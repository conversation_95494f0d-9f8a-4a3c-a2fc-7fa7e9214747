# Nginx configuration for Qanony Academy
# Place this file in /etc/nginx/sites-available/qanonyacademy.conf
# Then create symlink: sudo ln -s /etc/nginx/sites-available/qanonyacademy.conf /etc/nginx/sites-enabled/

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name qanonyacademy.com www.qanonyacademy.com;
    return 301 https://www.qanonyacademy.com$request_uri;
}

# Main HTTPS server block
server {
    listen 443 ssl http2;
    server_name www.qanonyacademy.com;
    
    # SSL Configuration (update paths after obtaining SSL certificates)
    ssl_certificate /etc/letsencrypt/live/www.qanonyacademy.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.qanonyacademy.com/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Root directory for React build
    root /home/<USER>/PreQanoony/preFinal/qanony/build;
    index index.html;
    
    # Client max body size for file uploads
    client_max_body_size 50M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Static files (React build)
    location / {
        try_files $uri $uri/ /index.html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Django API endpoints
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
    }
    
    # Django Admin
    location /admin/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
    }
    
    # Django static files
    location /static/ {
        alias /home/<USER>/PreQanoony/preFinal/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Django media files
    location /media/ {
        alias /home/<USER>/PreQanoony/preFinal/media/;
        expires 30d;
        add_header Cache-Control "public";
    }
    
    # WebSocket for Django Channels
    location /ws/ {
        proxy_pass http://127.0.0.1:8001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
    }
}

# Redirect non-www to www
server {
    listen 443 ssl http2;
    server_name qanonyacademy.com;
    
    ssl_certificate /etc/letsencrypt/live/www.qanonyacademy.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.qanonyacademy.com/privkey.pem;
    
    return 301 https://www.qanonyacademy.com$request_uri;
}
