#!/bin/bash

# Qanony Academy Deployment Script
# Run this script on the VPS server as root user

set -e  # Exit on any error

echo "🚀 Starting Qanony Academy Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="qanony"
PROJECT_DIR="/home/<USER>/PreQanoony/preFinal"
VENV_DIR="/home/<USER>/PreQanoony/venv"
DOMAIN="www.qanonyacademy.com"
SERVER_IP="************"

echo -e "${YELLOW}📋 Deployment Configuration:${NC}"
echo "Project: $PROJECT_NAME"
echo "Domain: $DOMAIN"
echo "Server IP: $SERVER_IP"
echo "Project Directory: $PROJECT_DIR"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

print_status "Step 1: Updating system packages..."
apt update && apt upgrade -y

print_status "Step 2: Installing required packages..."
apt install -y python3 python3-pip python3-venv postgresql postgresql-contrib redis-server nginx certbot python3-certbot-nginx git curl

print_status "Step 3: Creating qanony user..."
if ! id "qanony" &>/dev/null; then
    useradd -m -s /bin/bash qanony
    usermod -aG sudo qanony
    print_status "User 'qanony' created"
else
    print_warning "User 'qanony' already exists"
fi

print_status "Step 4: Setting up PostgreSQL..."
sudo -u postgres psql -c "CREATE USER platform_admin WITH PASSWORD 'CHANGE_THIS_TO_STRONG_PASSWORD';" || print_warning "User might already exist"
sudo -u postgres psql -c "CREATE DATABASE law_platform_db OWNER platform_admin;" || print_warning "Database might already exist"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE law_platform_db TO platform_admin;"

print_status "Step 5: Configuring Redis..."
systemctl enable redis-server
systemctl start redis-server

print_status "Step 6: Setting up project directory..."
mkdir -p /home/<USER>/PreQanoony
chown -R qanony:qanony /home/<USER>/PreQanoony

print_status "Step 7: Installing Nginx configuration..."
cp $PROJECT_DIR/deployment/nginx/qanonyacademy.conf /etc/nginx/sites-available/
ln -sf /etc/nginx/sites-available/qanonyacademy.conf /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

print_status "Step 8: Installing systemd services..."
cp $PROJECT_DIR/deployment/systemd/*.service /etc/systemd/system/
systemctl daemon-reload

print_status "Step 9: Obtaining SSL certificate..."
certbot --nginx -d $DOMAIN -d qanonyacademy.com --non-interactive --agree-tos --email <EMAIL>

print_status "Step 10: Testing Nginx configuration..."
nginx -t && systemctl reload nginx

print_status "Step 11: Enabling and starting services..."
systemctl enable nginx postgresql redis-server
systemctl enable qanony-django qanony-daphne qanony-celery

print_status "Step 12: Setting up firewall..."
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

echo ""
print_status "🎉 Basic server setup completed!"
echo ""
print_warning "Next steps to complete manually:"
echo "1. Copy your project files to $PROJECT_DIR"
echo "2. Create virtual environment: python3 -m venv $VENV_DIR"
echo "3. Install requirements: $VENV_DIR/bin/pip install -r $PROJECT_DIR/requirements.txt"
echo "4. Copy .env.production to .env.production in project directory"
echo "5. Run migrations: $VENV_DIR/bin/python $PROJECT_DIR/manage.py migrate"
echo "6. Collect static files: $VENV_DIR/bin/python $PROJECT_DIR/manage.py collectstatic"
echo "7. Create superuser: $VENV_DIR/bin/python $PROJECT_DIR/manage.py createsuperuser"
echo "8. Start services: systemctl start qanony-django qanony-daphne qanony-celery"
echo ""
print_status "Deployment script finished!"
