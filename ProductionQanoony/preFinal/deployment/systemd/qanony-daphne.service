[Unit]
Description=Qanony Academy Daphne (WebSocket) Server
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=exec
User=qanony
Group=qanony
WorkingDirectory=/home/<USER>/PreQanoony/preFinal
Environment=PATH=/home/<USER>/PreQanoony/venv/bin
EnvironmentFile=/home/<USER>/PreQanoony/preFinal/.env.production
ExecStart=/home/<USER>/PreQanoony/venv/bin/daphne -b 127.0.0.1 -p 8001 config.asgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal
SyslogIdentifier=qanony-daphne

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict

[Install]
WantedBy=multi-user.target
