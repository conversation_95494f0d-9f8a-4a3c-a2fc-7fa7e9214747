[Unit]
Description=Qanony Academy Django Application
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=exec
User=qanony
Group=qanony
WorkingDirectory=/home/<USER>/PreQanoony/preFinal
Environment=PATH=/home/<USER>/PreQanoony/venv/bin
EnvironmentFile=/home/<USER>/PreQanoony/preFinal/.env.production
ExecStart=/home/<USER>/PreQanoony/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:8000 config.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal
SyslogIdentifier=qanony-django

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ReadWritePaths=/home/<USER>/PreQanoony/preFinal/media /home/<USER>/PreQanoony/preFinal/staticfiles

[Install]
WantedBy=multi-user.target
