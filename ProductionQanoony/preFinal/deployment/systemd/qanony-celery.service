[Unit]
Description=Qanony Academy Celery Worker
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=exec
User=qanony
Group=qanony
WorkingDirectory=/home/<USER>/PreQanoony/preFinal
Environment=PATH=/home/<USER>/PreQanoony/venv/bin
EnvironmentFile=/home/<USER>/PreQanoony/preFinal/.env.production
ExecStart=/home/<USER>/PreQanoony/venv/bin/celery -A config worker --loglevel=info
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=qanony-celery

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict

[Install]
WantedBy=multi-user.target
