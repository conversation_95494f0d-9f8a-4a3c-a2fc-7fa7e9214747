# 🚀 Qanony Academy Deployment Checklist

## Server Information
- **Domain**: www.qanonyacademy.com
- **Server IP**: ************
- **SSL**: Let's Encrypt (auto-configured)

## Pre-Deployment Checklist

### ✅ Environment Setup
- [ ] `.env.production` configured with correct domain and IP
- [ ] Database password changed from default
- [ ] SECRET_KEY generated for production
- [ ] DEBUG=False set
- [ ] ALLOWED_HOSTS configured

### ✅ Security
- [ ] Strong database password set
- [ ] SSL certificates configured
- [ ] Security headers enabled in Nginx
- [ ] Firewall configured (ports 22, 80, 443)
- [ ] Non-root user created for application

### ✅ Services Configuration
- [ ] PostgreSQL installed and configured
- [ ] Redis installed and running
- [ ] Nginx configured with SSL
- [ ] Systemd services created
- [ ] Gunicorn configured for Django
- [ ] Daphne configured for WebSockets
- [ ] Celery configured for background tasks

### ✅ Application Setup
- [ ] Virtual environment created
- [ ] Requirements installed
- [ ] Database migrations applied
- [ ] Static files collected
- [ ] Media directories created
- [ ] Superuser created
- [ ] React app built for production

## Deployment Steps

### 1. Server Setup (as root)
```bash
# Copy deployment files to server
scp -r deployment/ root@************:/tmp/

# Run deployment script
ssh root@************
cd /tmp/deployment
chmod +x deploy.sh
./deploy.sh
```

### 2. Project Setup (as qanony user)
```bash
# Copy project files
scp -r preFinal/ qanony@************:/home/<USER>/PreQanoony/

# Setup project
ssh qanony@************
cd /home/<USER>/PreQanoony/preFinal/deployment
chmod +x setup_project.sh
./setup_project.sh
```

### 3. Start Services (as root)
```bash
ssh root@************
systemctl start qanony-django
systemctl start qanony-daphne
systemctl start qanony-celery
systemctl status qanony-django qanony-daphne qanony-celery
```

## Post-Deployment Verification

### ✅ Service Status
```bash
sudo systemctl status qanony-django
sudo systemctl status qanony-daphne
sudo systemctl status qanony-celery
sudo systemctl status nginx
sudo systemctl status postgresql
sudo systemctl status redis-server
```

### ✅ Website Testing
- [ ] https://www.qanonyacademy.com loads correctly
- [ ] Admin panel accessible: https://www.qanonyacademy.com/admin/
- [ ] API endpoints working: https://www.qanonyacademy.com/api/
- [ ] Static files loading correctly
- [ ] Media files uploading/downloading
- [ ] Email sending working
- [ ] WebSocket connections working
- [ ] SSL certificate valid

### ✅ Performance Testing
- [ ] Page load times acceptable
- [ ] Database queries optimized
- [ ] Static files cached properly
- [ ] Gzip compression working

## Troubleshooting

### View Logs
```bash
# Django application logs
sudo journalctl -u qanony-django -f

# Daphne WebSocket logs
sudo journalctl -u qanony-daphne -f

# Celery worker logs
sudo journalctl -u qanony-celery -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Common Issues
1. **502 Bad Gateway**: Check if Django service is running
2. **Static files not loading**: Run `collectstatic` and check Nginx config
3. **Database connection error**: Check PostgreSQL service and credentials
4. **SSL issues**: Check certificate paths in Nginx config
5. **WebSocket not working**: Check Daphne service and Nginx WebSocket config

## Maintenance

### Regular Tasks
- Monitor disk space
- Update SSL certificates (auto-renewed)
- Backup database regularly
- Monitor application logs
- Update dependencies periodically

### Backup Commands
```bash
# Database backup
sudo -u postgres pg_dump law_platform_db > backup_$(date +%Y%m%d).sql

# Media files backup
tar -czf media_backup_$(date +%Y%m%d).tar.gz /home/<USER>/PreQanoony/preFinal/media/
```

## Support Contacts
- **Domain**: www.qanonyacademy.com
- **Server**: ************
- **Email**: <EMAIL>
