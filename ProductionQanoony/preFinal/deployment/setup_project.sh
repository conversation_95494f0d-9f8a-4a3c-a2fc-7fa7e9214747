#!/bin/bash

# Qanony Academy Project Setup Script
# Run this script as the 'qanony' user after running deploy.sh as root

set -e  # Exit on any error

echo "🔧 Setting up Qanony Academy Project..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/home/<USER>/PreQanoony/preFinal"
VENV_DIR="/home/<USER>/PreQanoony/venv"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as qanony user
if [ "$USER" != "qanony" ]; then
    print_error "Please run this script as 'qanony' user"
    print_warning "Switch to qanony user: sudo su - qanony"
    exit 1
fi

print_status "Step 1: Creating virtual environment..."
python3 -m venv $VENV_DIR
source $VENV_DIR/bin/activate

print_status "Step 2: Upgrading pip..."
pip install --upgrade pip

print_status "Step 3: Installing Python requirements..."
pip install -r $PROJECT_DIR/requirements.txt

print_status "Step 4: Setting up environment variables..."
if [ ! -f "$PROJECT_DIR/.env.production" ]; then
    print_error ".env.production file not found!"
    print_warning "Please copy .env.production to $PROJECT_DIR/"
    exit 1
fi

print_status "Step 5: Running database migrations..."
cd $PROJECT_DIR
python manage.py migrate

print_status "Step 6: Collecting static files..."
python manage.py collectstatic --noinput

print_status "Step 7: Creating media directories..."
mkdir -p media/profile_images media/course_materials media/library_files

print_status "Step 8: Setting proper permissions..."
chmod -R 755 media/
chmod -R 755 staticfiles/

print_status "Step 9: Testing Django configuration..."
python manage.py check --deploy

print_status "Step 10: Testing email configuration..."
python manage.py send_test_email || print_warning "Email test failed - check email settings"

print_status "Step 11: Creating superuser (if needed)..."
echo "Do you want to create a superuser? (y/n)"
read -r create_superuser
if [ "$create_superuser" = "y" ]; then
    python manage.py createsuperuser
fi

print_status "Step 12: Testing React build..."
if [ -d "qanony/build" ]; then
    print_status "React build found"
else
    print_warning "React build not found - building now..."
    cd qanony
    npm install
    npm run build
    cd ..
fi

echo ""
print_status "🎉 Project setup completed!"
echo ""
print_warning "Final steps:"
echo "1. Start services as root:"
echo "   sudo systemctl start qanony-django"
echo "   sudo systemctl start qanony-daphne" 
echo "   sudo systemctl start qanony-celery"
echo ""
echo "2. Check service status:"
echo "   sudo systemctl status qanony-django"
echo "   sudo systemctl status qanony-daphne"
echo "   sudo systemctl status qanony-celery"
echo ""
echo "3. View logs if needed:"
echo "   sudo journalctl -u qanony-django -f"
echo ""
print_status "Setup script finished!"
