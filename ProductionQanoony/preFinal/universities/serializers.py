from rest_framework import serializers
from .models import University, AcademicYear

class UniversitySerializer(serializers.ModelSerializer):
    class Meta:
        model = University
        fields = ['id', 'name_ar', 'name_en', 'code', 'city', 'is_active', 'created_at']
        read_only_fields = ['id', 'created_at']

class AcademicYearSerializer(serializers.ModelSerializer):
    # university = serializers.PrimaryKeyRelatedField(queryset=University.objects.all())

    class Meta:
        model = AcademicYear
        fields = ['id', 'year_number', 'year_name_ar', 'year_name_en', 'is_active']
        read_only_fields = ['id'] 