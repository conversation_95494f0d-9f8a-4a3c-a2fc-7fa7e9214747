# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AcademicYear',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('year_number', models.IntegerField()),
                ('year_name_ar', models.CharField(max_length=100)),
                ('year_name_en', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='University',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name_ar', models.CharField(max_length=255)),
                ('name_en', models.CharField(max_length=255)),
                ('code', models.Char<PERSON>ield(max_length=20, unique=True)),
                ('city', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('is_active', models.<PERSON>olean<PERSON>ield(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
    ]
