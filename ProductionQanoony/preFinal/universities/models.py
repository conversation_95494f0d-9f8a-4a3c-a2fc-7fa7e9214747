from django.db import models
from django.utils import timezone

class University(models.Model):
    id = models.AutoField(primary_key=True)
    name_ar = models.CharField(max_length=255)
    name_en = models.CharField(max_length=255)
    code = models.CharField(max_length=20, unique=True)
    city = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return self.name_en

class AcademicYear(models.Model):
    id = models.AutoField(primary_key=True)
    # university = models.ForeignKey(University, on_delete=models.CASCADE, related_name='academic_years')
    year_number = models.IntegerField()
    year_name_ar = models.Char<PERSON>ield(max_length=100)
    year_name_en = models.Cha<PERSON><PERSON><PERSON>(max_length=100)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"Year {self.year_number}"

# Example fixture data for Egyptian universities with law schools (for later loading):
# [
#   {"name_ar": "جامعة القاهرة", "name_en": "Cairo University", "code": "CU", "city": "Cairo"},
#   {"name_ar": "جامعة عين شمس", "name_en": "Ain Shams University", "code": "ASU", "city": "Cairo"},
#   {"name_ar": "جامعة الإسكندرية", "name_en": "Alexandria University", "code": "AU", "city": "Alexandria"},
#   {"name_ar": "جامعة المنصورة", "name_en": "Mansoura University", "code": "MU", "city": "Mansoura"},
#   {"name_ar": "جامعة أسيوط", "name_en": "Assiut University", "code": "ASU2", "city": "Assiut"},
#   {"name_ar": "جامعة طنطا", "name_en": "Tanta University", "code": "TU", "city": "Tanta"},
#   {"name_ar": "جامعة الزقازيق", "name_en": "Zagazig University", "code": "ZU", "city": "Zagazig"},
#   {"name_ar": "جامعة بني سويف", "name_en": "Beni Suef University", "code": "BSU", "city": "Beni Suef"},
#   {"name_ar": "جامعة حلوان", "name_en": "Helwan University", "code": "HU", "city": "Cairo"},
#   {"name_ar": "جامعة جنوب الوادي", "name_en": "South Valley University", "code": "SVU", "city": "Qena"},
# ]
