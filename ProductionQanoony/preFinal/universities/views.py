from django.shortcuts import render
from rest_framework import viewsets, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import University, AcademicYear
from .serializers import UniversitySerializer, AcademicYearSerializer

# Create your views here.

class IsAdminOrReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user and request.user.is_staff

class UniversityViewSet(viewsets.ModelViewSet):
    queryset = University.objects.all()
    serializer_class = UniversitySerializer
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name_ar', 'name_en', 'code', 'city']
    ordering_fields = ['name_en', 'city', 'created_at']

    def get_queryset(self):
        if self.action in ['list', 'retrieve', 'active']:
            return University.objects.filter(is_active=True)
        return super().get_queryset()

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def active(self, request):
        """List all active universities (for registration)."""
        queryset = University.objects.filter(is_active=True)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class AcademicYearViewSet(viewsets.ModelViewSet):
    queryset = AcademicYear.objects.all()
    serializer_class = AcademicYearSerializer
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['year_name_ar', 'year_name_en']
    ordering_fields = ['year_number']

    def get_queryset(self):
        if self.action in ['list', 'retrieve', 'active']:
            return AcademicYear.objects.filter(is_active=True)
        return super().get_queryset()

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def active(self, request):
        """List all active academic years (for registration)."""
        queryset = AcademicYear.objects.filter(is_active=True).order_by('year_number')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
