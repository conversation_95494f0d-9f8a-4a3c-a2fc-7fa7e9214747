from django.contrib import admin
from .models import University, AcademicYear

@admin.register(University)
class UniversityAdmin(admin.ModelAdmin):
    list_display = ('name_en', 'name_ar', 'code', 'city', 'is_active', 'created_at')
    list_filter = ('is_active', 'city')
    search_fields = ('name_en', 'name_ar', 'code', 'city')
    ordering = ('name_en',)

@admin.register(AcademicYear)
class AcademicYearAdmin(admin.ModelAdmin):
    list_display = ('year_number', 'year_name_en', 'year_name_ar', 'is_active')
    list_filter = ('is_active', 'year_number')
    search_fields = ('year_name_en', 'year_name_ar')
    ordering = ('year_number',)
