# Universities API Documentation

## 1. List All Active Universities
- **Endpoint:** /api/universities/universities/active/
- **Method:** GET
- **Description:** List all active universities (for registration and public use).
- **Permissions:** AllowAny
- **Request Example:**
GET /api/universities/universities/active/
- **Response Example:**
200 OK
[
  {
    "id": 1,
    "name_ar": "جامعة القاهرة",
    "name_en": "Cairo University",
    "code": "CU",
    "city": "Cairo",
    "is_active": true,
    "created_at": "2024-06-01T12:00:00Z"
  },
  ...
]

## 2. List Universities (Admin)
- **Endpoint:** /api/universities/universities/
- **Method:** GET
- **Description:** List all universities (admin can see all, including inactive).
- **Permissions:** AllowAny (active only), IsAdmin for all
- **Request Example:**
GET /api/universities/universities/
- **Response Example:**
200 OK
[
  { ... }
]

## 3. Retrieve University Details
- **Endpoint:** /api/universities/universities/{id}/
- **Method:** GET
- **Description:** Retrieve details of a university.
- **Permissions:** AllowAny
- **Request Example:**
GET /api/universities/universities/1/
- **Response Example:**
200 OK
{
  "id": 1,
  "name_ar": "جامعة القاهرة",
  "name_en": "Cairo University",
  "code": "CU",
  "city": "Cairo",
  "is_active": true,
  "created_at": "2024-06-01T12:00:00Z"
}

## 4. Create/Update/Delete University (Admin)
- **Endpoint:** /api/universities/universities/
- **Method:** POST, PATCH, DELETE
- **Description:** Admin can create, update, or delete universities.
- **Permissions:** IsAdminUser
- **Request Example (POST):**
{
  "name_ar": "جامعة عين شمس",
  "name_en": "Ain Shams University",
  "code": "ASU",
  "city": "Cairo",
  "is_active": true
}
- **Response Example:**
201 CREATED
{
  "id": 2,
  ...
}

## 5. List All Academic Years (Updated - No University Dependency)
- **Endpoint:** /api/universities/academic-years/
- **Method:** GET
- **Description:** List all active academic years (independent of universities).
- **Permissions:** AllowAny
- **Request Example:**
GET /api/universities/academic-years/
- **Response Example:**
200 OK
[
  {
    "id": 1,
    "year_number": 1,
    "year_name_ar": "الفرقة الأولى",
    "year_name_en": "First Year",
    "is_active": true
  },
  ...
]

## 6. List Academic Years (Admin)
- **Endpoint:** /api/universities/academic-years/
- **Method:** GET
- **Description:** List all academic years (admin can see all, including inactive).
- **Permissions:** AllowAny (active only), IsAdmin for all
- **Request Example:**
GET /api/universities/academic-years/
- **Response Example:**
200 OK
[
  { ... }
]

## 7. Retrieve Academic Year Details
- **Endpoint:** /api/universities/academic-years/{id}/
- **Method:** GET
- **Description:** Retrieve details of an academic year.
- **Permissions:** AllowAny
- **Request Example:**
GET /api/universities/academic-years/1/
- **Response Example:**
200 OK
{
  "id": 1,
  "year_number": 1,
  "year_name_ar": "الفرقة الأولى",
  "year_name_en": "First Year",
  "is_active": true
}

## 8. Create/Update/Delete Academic Year (Admin)
- **Endpoint:** /api/universities/academic-years/
- **Method:** POST, PATCH, DELETE
- **Description:** Admin can create, update, or delete academic years.
- **Permissions:** IsAdminUser
- **Request Example (POST):**
{
  "year_number": 2,
  "year_name_ar": "الفرقة الثانية",
  "year_name_en": "Second Year",
  "is_active": true
}
- **Response Example:**
201 CREATED
{
  "id": 2,
  ...
} 