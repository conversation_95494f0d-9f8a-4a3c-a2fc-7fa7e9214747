from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import University, AcademicYear

User = get_user_model()

class UniversityAcademicYearTests(APITestCase):
    def setUp(self):
        self.admin = User.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='adminpass')
        self.student_user = User.objects.create_user(email='<EMAIL>', first_name='Student', last_name='User', password='studentpass', is_student=True)
        self.client = APIClient()
        self.university = University.objects.create(name_ar='جامعة القاهرة', name_en='Cairo University', code='CU', city='Cairo')
        self.year = AcademicYear.objects.create(university=self.university, year_number=1, year_name_ar='الفرقة الأولى', year_name_en='First Year')

    def test_list_universities(self):
        url = reverse('university-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)

    def test_retrieve_university(self):
        url = reverse('university-detail', args=[self.university.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name_en'], 'Cairo University')

    def test_list_academic_years(self):
        url = reverse('academicyear-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)

    def test_retrieve_academic_year(self):
        url = reverse('academicyear-detail', args=[self.year.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['year_name_en'], 'First Year')

    def test_admin_create_update_delete_university(self):
        self.client.force_authenticate(user=self.admin)
        # Create
        url = reverse('university-list')
        data = {'name_ar': 'جامعة عين شمس', 'name_en': 'Ain Shams University', 'code': 'ASU', 'city': 'Cairo', 'is_active': True}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        university_id = response.data['id']
        # Update
        url = reverse('university-detail', args=[university_id])
        response = self.client.patch(url, {'city': 'Giza'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['city'], 'Giza')
        # Delete
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_admin_create_update_delete_academic_year(self):
        self.client.force_authenticate(user=self.admin)
        # Create
        url = reverse('academicyear-list')
        data = {'university': self.university.id, 'year_number': 2, 'year_name_ar': 'الفرقة الثانية', 'year_name_en': 'Second Year', 'is_active': True}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        year_id = response.data['id']
        # Update
        url = reverse('academicyear-detail', args=[year_id])
        response = self.client.patch(url, {'year_name_en': 'Year 2'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['year_name_en'], 'Year 2')
        # Delete
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_list_universities_unauthenticated(self):
        # Public listing should work
        url = reverse('university-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_university_unauthorized(self):
        url = reverse('university-list')
        data = {'name_ar': 'جامعة حلوان', 'name_en': 'Helwan University', 'code': 'HU', 'city': 'Cairo', 'is_active': True}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_university_invalid(self):
        self.client.force_authenticate(user=self.admin)
        url = reverse('university-list')
        data = {'name_ar': '', 'name_en': '', 'code': '', 'city': '', 'is_active': True}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_retrieve_university_not_found(self):
        url = reverse('university-detail', args=[9999])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_university_forbidden(self):
        # Non-admin cannot update
        self.client.force_authenticate(user=self.student_user)
        url = reverse('university-detail', args=[self.university.id])
        response = self.client.patch(url, {'city': 'Giza'})
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_university_forbidden(self):
        # Non-admin cannot delete
        self.client.force_authenticate(user=self.student_user)
        url = reverse('university-detail', args=[self.university.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_academic_years_unauthenticated(self):
        url = reverse('academicyear-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_academic_year_unauthorized(self):
        url = reverse('academicyear-list')
        data = {'university': self.university.id, 'year_number': 3, 'year_name_ar': '', 'year_name_en': '', 'is_active': True}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_academic_year_invalid(self):
        self.client.force_authenticate(user=self.admin)
        url = reverse('academicyear-list')
        data = {'university': '', 'year_number': '', 'year_name_ar': '', 'year_name_en': '', 'is_active': True}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_retrieve_academic_year_not_found(self):
        url = reverse('academicyear-detail', args=[9999])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_academic_year_forbidden(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('academicyear-detail', args=[self.year.id])
        response = self.client.patch(url, {'year_name_en': 'Year X'})
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_academic_year_forbidden(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('academicyear-detail', args=[self.year.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
