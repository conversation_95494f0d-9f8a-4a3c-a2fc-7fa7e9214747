from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.http import FileResponse, HttpResponse
from django.utils import timezone
from .models import Subject, Semester, Lecture, Quiz, Question, Answer, QuizAttempt, Certificate, PrivateTutorSession
from .serializers import (
    SubjectSerializer, SemesterSerializer, LectureSerializer, QuizSerializer,
    QuestionSerializer, AnswerSerializer, QuizAttemptSerializer, CertificateSerializer, PrivateTutorSessionSerializer
)
from authentication.permissions import IsEmailVerified
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer
from reportlab.lib.enums import TA_CENTER
from django.utils import timezone
from ai_assistant.permissions import IsStaffOrActiveSubscriptionOrDenied
from rest_framework.permissions import IsAuthenticated
from notifications.models import Notification
from django.core.exceptions import ValidationError
from config.error_handlers import handle_api_errors, create_error_response, ErrorMessages

# Custom permission: Only instructors or admins can modify, students can only read
class IsInstructorOrAdmin(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user.is_authenticated and (getattr(request.user, 'is_instructor', False) or request.user.is_staff)

# Custom permission: Only students can take quizzes, download certificates, etc.
class IsStudent(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and getattr(request.user, 'is_student', False)

class SubjectViewSet(viewsets.ModelViewSet):
    queryset = Subject.objects.all()
    serializer_class = SubjectSerializer
    permission_classes = [IsEmailVerified, IsStaffOrActiveSubscriptionOrDenied]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [IsEmailVerified, IsInstructorOrAdmin, IsStaffOrActiveSubscriptionOrDenied]
        return super().get_permissions()

    def get_queryset(self):
        user = self.request.user
        queryset = Subject.objects.all()

        # Filter by semester if provided
        semester_id = self.request.query_params.get('semester')
        if semester_id:
            queryset = queryset.filter(semester_id=semester_id)

        # For students, filter by their academic year and only show active subjects
        if user.is_authenticated and not (user.is_staff or getattr(user, 'is_instructor', False)):
            queryset = queryset.filter(is_active=True)

            # Filter by student's academic year
            if hasattr(user, 'student_profile') and user.student_profile.academic_year:
                queryset = queryset.filter(academic_year=user.student_profile.academic_year)

        # Order by name_ar
        return queryset.order_by('name_ar')

class SemesterViewSet(viewsets.ModelViewSet):
    queryset = Semester.objects.all()
    serializer_class = SemesterSerializer
    permission_classes = [IsEmailVerified, IsStaffOrActiveSubscriptionOrDenied]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [IsEmailVerified, IsInstructorOrAdmin, IsStaffOrActiveSubscriptionOrDenied]
        return super().get_permissions()

    def get_queryset(self):
        user = self.request.user
        queryset = Semester.objects.all()
        
        # Staff and instructors can see all semesters
        if user.is_staff or getattr(user, 'is_instructor', False):
            return queryset
        
        # Students can only see published semesters for their academic year
        if hasattr(user, 'student_profile') and user.student_profile.academic_year:
            return queryset.filter(
                academic_year=user.student_profile.academic_year,
                is_published=True
            ).order_by('order')
        
        # For other users, show no semesters
        return Semester.objects.none()

class LectureViewSet(viewsets.ModelViewSet):
    queryset = Lecture.objects.all()
    serializer_class = LectureSerializer
    permission_classes = [IsEmailVerified, IsStaffOrActiveSubscriptionOrDenied]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [IsEmailVerified, IsInstructorOrAdmin, IsStaffOrActiveSubscriptionOrDenied]
        return super().get_permissions()

    def get_queryset(self):
        user = self.request.user
        queryset = Lecture.objects.all()
        
        # Filter by subject if provided
        subject_id = self.request.query_params.get('subject')
        if subject_id:
            queryset = queryset.filter(subject_id=subject_id)
        
        # For students, only show published lectures
        if user.is_authenticated and not (user.is_staff or getattr(user, 'is_instructor', False)):
            queryset = queryset.filter(is_published=True)
        
        # Order by order field
        return queryset.order_by('order')

    @handle_api_errors
    def create(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except ValidationError as e:
            return create_error_response(
                error_type='خطأ في البيانات',
                detail='البيانات المرسلة غير صحيحة. يرجى التحقق من جميع الحقول.',
                code='validation_error',
                status_code=status.HTTP_400_BAD_REQUEST
            )

    @handle_api_errors
    def update(self, request, *args, **kwargs):
        try:
            return super().update(request, *args, **kwargs)
        except ValidationError as e:
            return create_error_response(
                error_type='خطأ في تحديث البيانات',
                detail='البيانات المرسلة غير صحيحة. يرجى التحقق من جميع الحقول.',
                code='validation_error',
                status_code=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'], url_path='pdf-summary', permission_classes=[IsEmailVerified, IsStaffOrActiveSubscriptionOrDenied])
    def pdf_summary(self, request, pk=None):
        lecture = self.get_object()
        if lecture.pdf_summary:
            return Response({'url': lecture.pdf_summary.url})
        return Response({'detail': 'لا يوجد ملخص PDF'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'], url_path='download-pdf', permission_classes=[IsEmailVerified, IsStaffOrActiveSubscriptionOrDenied])
    def download_pdf(self, request, pk=None):
        lecture = self.get_object()
        if not lecture.pdf_summary:
            return Response({'detail': 'لا يوجد ملخص PDF'}, status=status.HTTP_404_NOT_FOUND)
        
        try:
            # فتح الملف للقراءة
            pdf_file = lecture.pdf_summary.open('rb')
            
            # إنشاء استجابة الملف
            response = FileResponse(pdf_file, content_type='application/pdf')
            
            # تعيين اسم الملف للتحميل
            filename = f"{lecture.title_ar.replace(' ', '_')}_ملخص.pdf"
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            return response
        except Exception as e:
            return Response({
                'detail': f'خطأ في تحميل الملف: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class QuizViewSet(viewsets.ModelViewSet):
    queryset = Quiz.objects.filter(is_active=True)
    serializer_class = QuizSerializer
    permission_classes = [IsEmailVerified, IsStaffOrActiveSubscriptionOrDenied]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [IsEmailVerified, IsInstructorOrAdmin]
        elif self.action in ['submit_answers', 'questions']:
            self.permission_classes = [IsEmailVerified, IsStaffOrActiveSubscriptionOrDenied]
        return super().get_permissions()

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        
        # Filter quizzes based on the student's enrolled subjects if they are a student
        if user.is_authenticated and hasattr(user, 'student_profile'):
            student_subjects = Subject.objects.filter(academic_year=user.student_profile.academic_year)
            return queryset.filter(lecture__subject__in=student_subjects)
        
        # Instructors/staff can see all quizzes
        return queryset

    @action(detail=True, methods=['get'], url_path='questions', permission_classes=[IsEmailVerified, IsStaffOrActiveSubscriptionOrDenied])
    def questions(self, request, pk=None):
        quiz = self.get_object()
        # Using QuestionSerializer to fetch questions and their answers
        questions = Question.objects.filter(quiz=quiz)
        serializer = QuestionSerializer(questions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], url_path='submit', permission_classes=[IsEmailVerified, IsStaffOrActiveSubscriptionOrDenied])
    def submit_answers(self, request, pk=None):
        quiz = self.get_object()
        user_answers = request.data.get('answers', []) # Expected format: [{"question_id": X, "answer_id": Y}]
        
        if not isinstance(user_answers, list):
            return Response({'detail': 'Invalid answers format.'}, status=status.HTTP_400_BAD_REQUEST)

        correct_answers_count = 0
        results = []
        
        quiz_questions = Question.objects.filter(quiz=quiz).prefetch_related('answers')

        for submission in user_answers:
            question_id = submission.get('question_id')
            user_answer_id = submission.get('answer_id')

            try:
                question = next(q for q in quiz_questions if q.id == question_id)
                correct_answer = next((a for a in question.answers.all() if a.is_correct), None)

                is_correct = correct_answer is not None and correct_answer.id == user_answer_id
                
                if is_correct:
                    correct_answers_count += 1
                
                # جلب نص الإجابة التي اختارها المستخدم
                user_answer_text = None
                if user_answer_id:
                    try:
                        user_answer_obj = next(a for a in question.answers.all() if a.id == user_answer_id)
                        user_answer_text = user_answer_obj.answer_text_ar
                    except StopIteration:
                        user_answer_text = "إجابة غير صحيحة"

                results.append({
                    'question_id': question.id,
                    'question_text': question.question_text_ar,
                    'user_answer': user_answer_text,
                    'user_answer_id': user_answer_id,
                    'correct_answer': correct_answer.answer_text_ar if correct_answer else None,
                    'correct_answer_id': correct_answer.id if correct_answer else None,
                    'is_correct': is_correct
                })
            except (StopIteration, Question.DoesNotExist):
                # Handle cases where a question_id from the submission doesn't exist in the quiz
                continue

        total_questions = quiz_questions.count()
        score = (correct_answers_count / total_questions * 100) if total_questions > 0 else 0

        # حفظ نتيجة الاختبار في قاعدة البيانات
        from students.models import StudentProfile
        try:
            student_profile = StudentProfile.objects.get(user=request.user)

            # إنشاء QuizAttempt لحفظ النتيجة
            quiz_attempt = QuizAttempt.objects.create(
                student=student_profile,
                quiz=quiz,
                score=correct_answers_count,
                total_points=total_questions,
                answers={str(submission['question_id']): submission['answer_id'] for submission in user_answers},
                completed_at=timezone.now()
            )

            # فحص إذا كانت الدرجة جيدة لإنشاء شهادة
            if score >= quiz.passing_score:
                from .utils import create_certificate_for_quiz
                certificate = create_certificate_for_quiz(student_profile, quiz, score)

                # إرسال إشعار بالشهادة
                if certificate:
                    from notifications.utils import create_notification_safe
                    create_notification_safe(
                        recipient=request.user,
                        subject_ar="تهانينا! حصلت على شهادة جديدة",
                        content_ar="مبروك {user_name}! لقد حصلت على شهادة في اختبار {quiz_title} بدرجة {score}%",
                        notification_type='in_app',
                        deduplication_minutes=60,
                        quiz_title=quiz.title_ar,
                        score=round(score, 2)
                    )

        except StudentProfile.DoesNotExist:
            pass  # المستخدم ليس طالب

        return Response({
            'score': round(score, 2),
            'correct_answers': correct_answers_count,
            'total_questions': total_questions,
            'questions': results,  # تغيير من results إلى questions للتوافق مع الفرونت إند
            'passed': score >= quiz.passing_score,
            'passing_score': quiz.passing_score
        })

class QuestionViewSet(viewsets.ModelViewSet):
    queryset = Question.objects.all()
    serializer_class = QuestionSerializer
    permission_classes = [IsEmailVerified, IsInstructorOrAdmin]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [IsEmailVerified, IsInstructorOrAdmin]
        return super().get_permissions()

    def get_queryset(self):
        queryset = Question.objects.all()
        quiz_id = self.request.query_params.get('quiz')
        if quiz_id:
            queryset = queryset.filter(quiz_id=quiz_id)
        return queryset

class AnswerViewSet(viewsets.ModelViewSet):
    queryset = Answer.objects.all()
    serializer_class = AnswerSerializer
    permission_classes = [IsEmailVerified, IsInstructorOrAdmin]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [IsEmailVerified, IsInstructorOrAdmin]
        return super().get_permissions()

    def get_queryset(self):
        queryset = Answer.objects.all()
        question_id = self.request.query_params.get('question')
        if question_id:
            queryset = queryset.filter(question_id=question_id)
        return queryset

class QuizAttemptViewSet(viewsets.ModelViewSet):
    queryset = QuizAttempt.objects.all()
    serializer_class = QuizAttemptSerializer
    permission_classes = [IsEmailVerified, IsInstructorOrAdmin]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [IsEmailVerified, IsStudent, IsInstructorOrAdmin]
        return super().get_permissions()

    def get_queryset(self):
        user = self.request.user
        if user.is_staff or getattr(user, 'is_instructor', False):
            return QuizAttempt.objects.all()
        elif hasattr(user, 'student_profile'):
            return QuizAttempt.objects.filter(student=user.student_profile)
        return QuizAttempt.objects.none()

    @action(detail=False, methods=['post'], url_path='take-quiz', permission_classes=[IsEmailVerified, IsStudent, IsInstructorOrAdmin])
    def take_quiz(self, request):
        # Stub for quiz taking logic
        return Response({'detail': 'Quiz attempt logic to be implemented.'}, status=status.HTTP_501_NOT_IMPLEMENTED)

class CertificateViewSet(viewsets.ModelViewSet):
    queryset = Certificate.objects.all()
    serializer_class = CertificateSerializer
    permission_classes = [IsEmailVerified, IsStudent, IsStaffOrActiveSubscriptionOrDenied]

    def get_queryset(self):
        user = self.request.user
        queryset = Certificate.objects.all()

        # For students, only show their own certificates
        if user.is_authenticated and hasattr(user, 'student_profile'):
            if not (user.is_staff or getattr(user, 'is_instructor', False)):
                queryset = queryset.filter(student=user.student_profile)

                # Filter by student's academic year for semester certificates
                if user.student_profile.academic_year:
                    from django.db.models import Q
                    queryset = queryset.filter(
                        Q(semester__academic_year=user.student_profile.academic_year) |
                        Q(subject__academic_year=user.student_profile.academic_year) |
                        Q(quiz__lecture__subject__academic_year=user.student_profile.academic_year)
                    )

        # Staff and instructors can see all certificates
        return queryset.order_by('-issued_date')

    @action(detail=True, methods=['get'], url_path='download', permission_classes=[IsEmailVerified, IsStudent, IsStaffOrActiveSubscriptionOrDenied])
    def download(self, request, pk=None):
        certificate = self.get_object()
        buffer = io.BytesIO()
        # PDF generation with ReportLab
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm, topMargin=2*cm, bottomMargin=2*cm)
        styles = getSampleStyleSheet()
        elements = []
        # Branding/Header
        header_style = styles['Title']
        header_style.alignment = TA_CENTER
        elements.append(Paragraph("Qanoony - قانوني", header_style))
        elements.append(Spacer(1, 1*cm))
        # Certificate Title
        cert_title_style = styles['Heading1']
        cert_title_style.alignment = TA_CENTER
        elements.append(Paragraph(certificate.get_certificate_title(), cert_title_style))
        elements.append(Spacer(1, 0.5*cm))
        # Student Name
        student_name = f"{certificate.student.user.first_name} {certificate.student.user.last_name}"
        elements.append(Paragraph(f"يُمنح الطالب: <b>{student_name}</b>", styles['Normal']))
        elements.append(Spacer(1, 0.3*cm))

        # محتوى الشهادة حسب النوع
        if certificate.certificate_type == 'quiz' and certificate.quiz:
            elements.append(Paragraph(f"لاجتيازه اختبار: <b>{certificate.quiz.title_ar}</b>", styles['Normal']))
            if certificate.score:
                elements.append(Paragraph(f"بدرجة: <b>{certificate.score}%</b>", styles['Normal']))
            elements.append(Paragraph(f"في مادة: <b>{certificate.quiz.lecture.subject.name_ar}</b>", styles['Normal']))
        elif certificate.certificate_type == 'subject' and certificate.subject:
            elements.append(Paragraph(f"لإتمامه مادة: <b>{certificate.subject.name_ar}</b>", styles['Normal']))
        elif certificate.certificate_type == 'semester' and certificate.semester:
            elements.append(Paragraph(f"لاستكماله الفصل الدراسي: <b>{certificate.semester.name_ar}</b>", styles['Normal']))

        elements.append(Spacer(1, 0.3*cm))
        # Issued Date
        issued_date = timezone.localtime(certificate.issued_date).strftime('%Y-%m-%d')
        elements.append(Paragraph(f"تاريخ الإصدار: {issued_date}", styles['Normal']))
        elements.append(Spacer(1, 1*cm))
        # Certificate ID
        elements.append(Paragraph(f"رمز الشهادة: {certificate.certificate_id}", styles['Normal']))
        elements.append(Spacer(1, 1*cm))
        # Footer/Notice
        notice = "هذه الشهادة صالحة للاستخدام الشخصي فقط وستُحذف بعد مغادرة الصفحة."
        notice_style = styles['Italic']
        notice_style.textColor = colors.red
        elements.append(Paragraph(notice, notice_style))
        doc.build(elements)
        buffer.seek(0)
        response = FileResponse(buffer, as_attachment=True, filename=f"certificate_{certificate.certificate_id}.pdf")
        return response

class PrivateTutorSessionViewSet(viewsets.ModelViewSet):
    serializer_class = PrivateTutorSessionSerializer
    permission_classes = [IsAuthenticated, IsEmailVerified]

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return PrivateTutorSession.objects.all()
        elif hasattr(user, 'student_profile'):
            queryset = PrivateTutorSession.objects.filter(student=user.student_profile)

            # Filter by student's academic year - only show sessions for subjects in their academic year
            if user.student_profile.academic_year:
                queryset = queryset.filter(subject__academic_year=user.student_profile.academic_year)

            return queryset
        elif user.is_instructor:
            return PrivateTutorSession.objects.filter(instructor=user)
        return PrivateTutorSession.objects.none()

    def perform_create(self, serializer):
        user = self.request.user
        
        # إضافة logging للتحقق من البيانات
        print("=== DEBUG: PrivateTutorSession Create ===")
        print(f"User: {user.email}, is_staff: {user.is_staff}")
        print(f"Request data: {self.request.data}")
        print(f"Validated data: {serializer.validated_data}")
        
        # إذا كان المستخدم أدمن، يمكنه إنشاء جلسة لأي طالب
        if user.is_staff:
            # الأدمن يرسل student_id في البيانات
            student_id = self.request.data.get('student_id')
            print(f"Student ID from request: {student_id}")
            if student_id:
                from students.models import StudentProfile
                try:
                    student = StudentProfile.objects.get(id=student_id)
                    print(f"Found student: {student.user.email}")
                    serializer.save(student=student, status='pending')
                except StudentProfile.DoesNotExist:
                    print(f"Student with ID {student_id} not found")
                    raise ValidationError('الطالب غير موجود')
            else:
                print("No student_id provided")
                raise ValidationError('معرف الطالب مطلوب')
        else:
            # الطلاب العاديين يمكنهم إنشاء جلسة لأنفسهم فقط
            print("Non-staff user, creating session for self")
            serializer.save(
                student=self.request.user.student_profile,
                status='pending'
            )
        
        print(f"Session created successfully: {serializer.instance.id}")
        
        # إنشاء إشعار للمدرس
        from notifications.utils import create_notification_safe
        create_notification_safe(
            recipient=serializer.instance.instructor,
            subject_ar='طلب جلسة خاصة جديد',
            content_ar='لديك طلب جلسة خاصة جديد من الطالب {student_name} في مادة {subject_name}',
            notification_type='in_app',
            deduplication_minutes=5,
            student_name=serializer.instance.student.user.get_full_name(),
            subject_name=serializer.instance.subject.name_ar
        )

    def perform_update(self, serializer):
        user = self.request.user
        
        # إضافة logging للتحقق من البيانات
        print("=== DEBUG: PrivateTutorSession Update ===")
        print(f"User: {user.email}, is_staff: {user.is_staff}")
        print(f"Request data: {self.request.data}")
        print(f"Validated data: {serializer.validated_data}")
        
        # إذا كان المستخدم أدمن، يمكنه تعديل أي جلسة
        if user.is_staff:
            # الأدمن يرسل student في البيانات
            student_id = self.request.data.get('student')
            print(f"Student ID from request: {student_id}")
            if student_id:
                from students.models import StudentProfile
                try:
                    student = StudentProfile.objects.get(id=student_id)
                    print(f"Found student: {student.user.email}")
                    serializer.save(student=student)
                except StudentProfile.DoesNotExist:
                    print(f"Student with ID {student_id} not found")
                    raise ValidationError('الطالب غير موجود')
            else:
                print("No student provided")
                raise ValidationError('معرف الطالب مطلوب')
        else:
            # المستخدمين العاديين يمكنهم تعديل جلساتهم فقط
            print("Non-staff user, updating session")
            serializer.save()
        
        print(f"Session updated successfully: {serializer.instance.id}")
        
        # إنشاء إشعار للمدرس إذا تم تغيير المدرس
        if 'instructor' in serializer.validated_data:
            Notification.objects.create(
                recipient=serializer.instance.instructor,
                subject_ar='تم تعديل جلسة خاصة',
                content_ar=f'تم تعديل جلسة خاصة للطالب {serializer.instance.student.user.get_full_name()} في مادة {serializer.instance.subject.name_ar}',
                notification_type='in_app',
                status='pending',
                scheduled_at=timezone.now()
            )

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        session = self.get_object()
        if not request.user.is_staff and request.user != session.instructor:
            return Response(
                {'detail': 'ليس لديك صلاحية لهذا الإجراء'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        meeting_link = request.data.get('meeting_link')
        if not meeting_link:
            return Response(
                {'detail': 'رابط الجلسة مطلوب'},
                status=status.HTTP_400_BAD_REQUEST
            )

        session.status = 'approved'
        session.meeting_link = meeting_link
        session.save()

        # إنشاء إشعار للطالب
        Notification.objects.create(
            recipient=session.student.user,
            subject_ar='تمت الموافقة على طلب الجلسة الخاصة',
            content_ar=f'تمت الموافقة على طلب جلستك الخاصة في مادة {session.subject.name_ar}. يمكنك الانضمام عبر الرابط المرفق',
            notification_type='in_app',
            status='pending',
            scheduled_at=timezone.now()
        )

        return Response({'status': 'approved'})

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        session = self.get_object()
        if not request.user.is_staff and request.user != session.instructor:
            return Response(
                {'detail': 'ليس لديك صلاحية لهذا الإجراء'},
                status=status.HTTP_403_FORBIDDEN
            )

        reason = request.data.get('reason', '')
        session.status = 'rejected'
        session.instructor_notes = reason
        session.save()

        # إنشاء إشعار للطالب
        Notification.objects.create(
            recipient=session.student.user,
            subject_ar='تم رفض طلب الجلسة الخاصة',
            content_ar=f'تم رفض طلب جلستك الخاصة في مادة {session.subject.name_ar}. السبب: {reason}',
            notification_type='in_app',
            status='pending',
            scheduled_at=timezone.now()
        )

        return Response({'status': 'rejected'}) 