from rest_framework import serializers
from .models import Subject, Semester, Lecture, Quiz, Question, Answer, QuizAttempt, Certificate, PrivateTutorSession
from universities.models import AcademicYear
from authentication.models import CustomUser
from students.models import StudentProfile
from universities.serializers import AcademicYearSerializer
from authentication.serializers import ProfileSerializer
from students.serializers import StudentProfileSerializer
from rest_framework.exceptions import PermissionDenied
import re

# دالة لاستخراج معرف اليوتيوب من الرابط الكامل
def extract_youtube_id(url):
    if not url:
        return None
    
    # إذا كان معرف فقط (بدون رابط)
    if len(url) <= 20 and 'youtube.com' not in url and 'youtu.be' not in url:
        return url
    
    # روابط youtube.com
    youtube_regex = r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})'
    match = re.match(youtube_regex, url)
    
    if match:
        return match.group(1)
    
    return None

# Subject Serializer
class SubjectSerializer(serializers.ModelSerializer):
    semester = serializers.PrimaryKeyRelatedField(queryset=Semester.objects.all())
    academic_year = serializers.PrimaryKeyRelatedField(queryset=AcademicYear.objects.all())
    lecture_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Subject
        fields = ['id', 'name_ar', 'name_en', 'code', 'description_ar', 'semester', 'academic_year', 'is_active', 'lecture_count']
        read_only_fields = ['id']
    
    def get_lecture_count(self, obj):
        user = self.context.get('request').user if self.context.get('request') else None
        queryset = obj.lectures.all()
        
        # For students, only count published lectures
        if user and not (user.is_staff or getattr(user, 'is_instructor', False)):
            queryset = queryset.filter(is_published=True)
        
        return queryset.count()

# Semester Serializer
class SemesterSerializer(serializers.ModelSerializer):
    academic_year = serializers.PrimaryKeyRelatedField(queryset=AcademicYear.objects.all())

    class Meta:
        model = Semester
        fields = ['id', 'title_ar', 'title_en', 'description_ar', 'academic_year', 'order', 'is_published', 'created_at']
        read_only_fields = ['id', 'created_at']

# Lecture Serializer
class LectureSerializer(serializers.ModelSerializer):
    subject = SubjectSerializer(read_only=True)
    subject_id = serializers.PrimaryKeyRelatedField(queryset=Subject.objects.all(), source='subject', write_only=True)
    pdf_summary_url = serializers.SerializerMethodField()
    pdf_summary_size = serializers.SerializerMethodField()

    class Meta:
        model = Lecture
        fields = ['id', 'subject', 'subject_id', 'title_ar', 'youtube_video_id', 'duration_minutes', 'order', 'pdf_summary', 'pdf_summary_url', 'pdf_summary_size', 'is_published', 'created_at']
        read_only_fields = ['id', 'created_at']

    def get_pdf_summary_url(self, obj):
        if obj.pdf_summary:
            return obj.pdf_summary.url
        return None

    def get_pdf_summary_size(self, obj):
        if obj.pdf_summary:
            return obj.pdf_summary.size
        return None

    def validate_youtube_video_id(self, value):
        if not value:
            raise serializers.ValidationError("معرف فيديو يوتيوب مطلوب")
        
        # استخراج المعرف من الرابط إذا كان رابط كامل
        extracted_id = extract_youtube_id(value)
        if extracted_id:
            return extracted_id
        
        # التحقق من أن المعرف صحيح (11 حرف)
        if len(value) != 11:
            raise serializers.ValidationError("معرف فيديو يوتيوب يجب أن يكون 11 حرف")
        
        # التحقق من أن المعرف يحتوي على أحرف صحيحة فقط
        if not re.match(r'^[a-zA-Z0-9_-]{11}$', value):
            raise serializers.ValidationError("معرف فيديو يوتيوب غير صحيح")
        
        return value

# Answer Serializer
class AnswerSerializer(serializers.ModelSerializer):
    question = serializers.PrimaryKeyRelatedField(queryset=Question.objects.all(), write_only=True)
    question_text_ar = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Answer
        fields = ['id', 'question', 'question_text_ar', 'answer_text_ar', 'is_correct', 'order']
        read_only_fields = ['id', 'question_text_ar']

    def get_question_text_ar(self, obj):
        return obj.question.question_text_ar if obj.question else None

# Question Serializer
class QuestionSerializer(serializers.ModelSerializer):
    answers = AnswerSerializer(many=True, read_only=True)
    quiz = serializers.PrimaryKeyRelatedField(queryset=Quiz.objects.all(), write_only=True)

    class Meta:
        model = Question
        fields = ['id', 'quiz', 'question_text_ar', 'question_type', 'order', 'points', 'answers']
        read_only_fields = ['id']

# Quiz Serializer
class QuizSerializer(serializers.ModelSerializer):
    lecture = LectureSerializer(read_only=True)
    lecture_id = serializers.PrimaryKeyRelatedField(queryset=Lecture.objects.all(), source='lecture', write_only=True)
    questions = QuestionSerializer(many=True, read_only=True)

    class Meta:
        model = Quiz
        fields = ['id', 'lecture', 'lecture_id', 'title_ar', 'instructions_ar', 'time_limit_minutes', 'passing_score', 'is_active', 'questions']
        read_only_fields = ['id']

# QuizAttempt Serializer
class QuizAttemptSerializer(serializers.ModelSerializer):
    student = StudentProfileSerializer(read_only=True)
    student_id = serializers.PrimaryKeyRelatedField(queryset=StudentProfile.objects.all(), source='student', write_only=True)
    quiz = QuizSerializer(read_only=True)
    quiz_id = serializers.PrimaryKeyRelatedField(queryset=Quiz.objects.all(), source='quiz', write_only=True)
    answers = serializers.JSONField()

    class Meta:
        model = QuizAttempt
        fields = ['id', 'student', 'student_id', 'quiz', 'quiz_id', 'score', 'total_points', 'started_at', 'completed_at', 'answers']
        read_only_fields = ['id', 'score', 'total_points', 'started_at', 'completed_at']

    def create(self, validated_data):
        request = self.context.get('request')
        if request and request.user != validated_data['student'].user:
            raise PermissionDenied('غير مسموح لك بإنشاء محاولة اختبار لطالب آخر.')
        answers = validated_data.get('answers', {})
        quiz = validated_data['quiz']
        score = 0
        total_points = 0
        # Calculate score if possible
        for question in quiz.questions.all():
            total_points += question.points
            ans_id = str(question.id)
            if ans_id in answers:
                try:
                    answer_obj = question.answers.get(id=answers[ans_id])
                    if answer_obj.is_correct:
                        score += question.points
                except Answer.DoesNotExist:
                    pass
        validated_data['score'] = score
        validated_data['total_points'] = total_points
        return super().create(validated_data)

    def validate(self, data):
        # Add custom validation for quiz answers here
        return data

# Certificate Serializer
class CertificateSerializer(serializers.ModelSerializer):
    student = StudentProfileSerializer(read_only=True)
    student_id = serializers.PrimaryKeyRelatedField(queryset=StudentProfile.objects.all(), source='student', write_only=True)
    semester = SemesterSerializer(read_only=True)
    semester_id = serializers.PrimaryKeyRelatedField(queryset=Semester.objects.all(), source='semester', write_only=True)

    class Meta:
        model = Certificate
        fields = ['id', 'student', 'student_id', 'semester', 'semester_id', 'certificate_id', 'issued_date']
        read_only_fields = ['id', 'issued_date']

class PrivateTutorSessionSerializer(serializers.ModelSerializer):
    student_name = serializers.SerializerMethodField()
    instructor_name = serializers.SerializerMethodField()
    subject_name = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    student_id = serializers.PrimaryKeyRelatedField(
        queryset=StudentProfile.objects.all(), 
        source='student', 
        write_only=True,
        required=False
    )
    instructor = serializers.PrimaryKeyRelatedField(
        queryset=CustomUser.objects.filter(is_instructor=True),
        required=True
    )

    class Meta:
        model = PrivateTutorSession
        fields = [
            'id', 'student', 'student_id', 'instructor', 'subject',
            'student_name', 'instructor_name', 'subject_name',
            'scheduled_at', 'duration_minutes', 'status',
            'status_display', 'meeting_link', 'student_notes',
            'instructor_notes', 'student_image', 'created_at', 'updated_at'
        ]
        read_only_fields = ['status_display', 'created_at', 'updated_at']

    def get_student_name(self, obj):
        return obj.student.user.get_full_name()

    def get_instructor_name(self, obj):
        return obj.instructor.get_full_name()

    def get_subject_name(self, obj):
        return obj.subject.name_ar

    def get_status_display(self, obj):
        return obj.get_status_display()

    def validate(self, data):
        request = self.context.get('request')
        if request and request.user.is_staff:
            # الأدمن يمكنه إنشاء أو تعديل جلسة لأي طالب
            if 'student' not in data:
                raise serializers.ValidationError("معرف الطالب مطلوب للأدمن")
        else:
            # الطلاب العاديين لا يمكنهم تحديد طالب آخر
            if 'student' in data and data['student'] != request.user.student_profile:
                raise serializers.ValidationError("لا يمكنك إنشاء جلسة لطالب آخر")
        
        return data

    def to_internal_value(self, data):
        # للأدمن، تأكد من أن student مطلوب في الإنشاء
        request = self.context.get('request')
        if request and request.user.is_staff and self.instance is None:  # فقط في الإنشاء
            if 'student' not in data or not data['student']:
                raise serializers.ValidationError({"student": "معرف الطالب مطلوب للأدمن"})
        
        return super().to_internal_value(data)
