# Courses API Documentation

## 1. Semesters
- **Endpoint:** /api/courses/semesters/
- **Methods:** GET, POST
- **Description:** List all semesters or create a new semester.
- **Permissions:**
  - GET: IsEmailVerified
  - POST: IsEmailVerified & IsInstructorOrAdmin
- **Sample Request (POST):**
{
  "title_ar": "الفصل الأول",
  "title_en": "First Semester",
  "description_ar": "شرح الفصل",
  "academic_year": 1,
  "order": 1,
  "is_published": true
}
- **Sample Response (GET):**
[
  { /api/courses/semesters/
    "id": 1,
    "title_ar": "الفصل الأول",
    "title_en": "First Semester",
    "description_ar": "شرح الفصل",
    "academic_year": 1,
    "order": 1,
    "is_published": true,
    "created_at": "2024-06-01T12:00:00Z"
  }
]

## 2. Subjects
- **Endpoint:** /api/courses/subjects/
- **Methods:** GET, POST
- **Description:** List all subjects for a semester or create a new subject.
- **Query Param:** `semester=<semester_id>` (مطلوب عند GET)
- **Permissions:**
  - GET: IsEmailVerified
  - POST: IsEmailVerified & IsInstructorOrAdmin
- **Sample Request (POST):**
{
  "name_ar": "مدني",
  "name_en": "Civil",
  "code": "CIV101",
  "description_ar": "شرح",
  "semester": 1,
  "academic_year": 1,
  "is_active": true
}
- **Sample Response (GET):**
[
  {
    "id": 1,
    "name_ar": "مدني",
    "name_en": "Civil",
    "code": "CIV101",
    "description_ar": "شرح",
    "semester": 1,
    "academic_year": 1,
    "is_active": true
  }
]

## 3. Courses
- **Endpoint:** /api/courses/courses/
- **Methods:** GET, POST
- **Description:** List all courses or create a new course (instructor/admin only).
- **Permissions:**
  - GET: IsEmailVerified
  - POST: IsEmailVerified & IsInstructorOrAdmin
- **Sample Request (POST):**
{
  "subject_id": 1,
  "title_ar": "قانون مدني",
  "title_en": "Civil Law",
  "description_ar": "شرح",
  "instructor_id": 2,
  "order": 1,
  "is_published": true
}
- **Sample Response (GET):**
[
  {
    "id": 1,
    "subject": {...},
    "title_ar": "قانون مدني",
    "title_en": "Civil Law",
    "description_ar": "شرح",
    "instructor": {...},
    "order": 1,
    "is_published": true,
    "created_at": "2024-06-01T12:00:00Z"
  }
]

## 4. Lectures
- **Endpoint:** /api/courses/lectures/
- **Methods:** GET, POST
- **Description:** List all lectures for a subject or create a new lecture.
- **Query Param:** `subject=<subject_id>` (مطلوب عند GET)
- **Permissions:**
  - GET: IsEmailVerified
  - POST: IsEmailVerified & IsInstructorOrAdmin
- **Sample Request (POST):**
{
  "subject_id": 1,
  "title_ar": "محاضرة 1",
  "youtube_video_id": "abc123",
  "duration_minutes": 60,
  "order": 1,
  "is_published": true
}
- **Sample Response (GET):**
[
  {
    "id": 1,
    "subject": {...},
    "title_ar": "محاضرة 1",
    "youtube_video_id": "abc123",
    "duration_minutes": 60,
    "order": 1,
    "pdf_summary": null,
    "pdf_summary_url": null,
    "is_published": true,
    "created_at": "2024-06-01T12:00:00Z"
  }
]
- **Custom Action:**
  - **Endpoint:** /api/courses/lectures/{id}/pdf-summary/
  - **Method:** GET
  - **Description:** Get the PDF summary for a lecture (if available).
  - **Permissions:** IsEmailVerified

## 5. Quizzes
- **Endpoint:** /api/courses/quizzes/
- **Methods:** GET, POST
- **Description:** List all quizzes or create a new quiz (instructor/admin only).
- **Permissions:**
  - GET: IsEmailVerified
  - POST: IsEmailVerified & IsInstructorOrAdmin
- **Sample Request (POST):**
{
  "lecture_id": 1,
  "title_ar": "اختبار 1",
  "instructions_ar": "أجب عن الأسئلة",
  "time_limit_minutes": 10,
  "passing_score": 50,
  "is_active": true
}
- **Sample Response (GET):**
[
  {
    "id": 1,
    "lecture": {...},
    "title_ar": "اختبار 1",
    "instructions_ar": "أجب عن الأسئلة",
    "time_limit_minutes": 10,
    "passing_score": 50,
    "is_active": true,
    "questions": [...]
  }
]

## 6. Questions
- **Endpoint:** /api/courses/questions/
- **Methods:** GET, POST
- **Description:** List all questions or create a new question (instructor/admin only).
- **Permissions:**
  - GET: IsEmailVerified
  - POST: IsEmailVerified & IsInstructorOrAdmin

## 7. Answers
- **Endpoint:** /api/courses/answers/
- **Methods:** GET, POST
- **Description:** List all answers or create a new answer (instructor/admin only).
- **Permissions:**
  - GET: IsEmailVerified
  - POST: IsEmailVerified & IsInstructorOrAdmin

## 8. Quiz Attempts
- **Endpoint:** /api/courses/quiz-attempts/
- **Methods:** GET, POST
- **Description:** List all quiz attempts (admin only) or create a new attempt (student only, for self).
- **Permissions:**
  - GET: IsEmailVerified & IsStudent (own attempts), IsAdminUser (all)
  - POST: IsEmailVerified & IsStudent (for self only)
- **Sample Request (POST):**
{
  "student_id": 1,
  "quiz_id": 1,
  "answers": {"1": 2}
}
- **Sample Response (POST):**
{
  "id": 1,
  "student": {...},
  "quiz": {...},
  "score": 10,
  "total_points": 10,
  "started_at": "2024-06-01T12:00:00Z",
  "completed_at": null,
  "answers": {"1": 2}
}

## 9. Certificates
- **Endpoint:** /api/courses/certificates/
- **Methods:** GET, POST
- **Description:** List all certificates (admin only) or create a new certificate (instructor/admin only).
- **Permissions:**
  - GET: IsEmailVerified & IsStudent (own), IsAdminUser (all)
  - POST: IsEmailVerified & IsInstructorOrAdmin
- **Custom Action:**
  - **Endpoint:** /api/courses/certificates/{id}/download/
  - **Method:** GET
  - **Description:** Download a dynamically generated PDF certificate. The file will be deleted after leaving the page.
  - **Permissions:** IsEmailVerified & IsStudent (own)
  - **Response:** PDF file with header: X-Notice: "سيتم حذف الشهادة بعد مغادرة الصفحة." 