# Private Tutor Session Endpoints

## List Sessions
GET /api/courses/private-tutor/
- **Permissions:** Authenticated, Email Verified
- **Description:** يعرض قائمة الجلسات الخاصة للمستخدم
  - للطلاب: يعرض جلساتهم فقط
  - للمدرسين: يعرض الجلسات المسندة إليهم
  - للمشرفين: يعرض كل الجلسات

## Create Session
POST /api/courses/private-tutor/
- **Permissions:** Authenticated, Email Verified
- **Fields:**
  - instructor (required): معرف المدرس
  - subject (required): معرف المادة
  - scheduled_at (required): موعد الجلسة
  - duration_minutes (optional): مدة الجلسة بالدقائق (افتراضياً 60)
  - student_notes (optional): ملاحظات الطالب

## Get Session Details
GET /api/courses/private-tutor/{id}/
- **Permissions:** Authenticated, Email Verified (مالك الجلسة أو المدرس أو المشرف)

## Update Session
PUT/PATCH /api/courses/private-tutor/{id}/
- **Permissions:** Authenticated, Email Verified (مالك الجلسة أو المدرس أو المشرف)
- **Fields:** نفس حقول الإنشاء

## Delete Session
DELETE /api/courses/private-tutor/{id}/
- **Permissions:** Authenticated, Email Verified (مالك الجلسة أو المشرف)

## Approve Session
POST /api/courses/private-tutor/{id}/approve/
- **Permissions:** Authenticated, Email Verified (المدرس أو المشرف)
- **Fields:**
  - meeting_link (required): رابط الجلسة

## Reject Session
POST /api/courses/private-tutor/{id}/reject/
- **Permissions:** Authenticated, Email Verified (المدرس أو المشرف)
- **Fields:**
  - reason (optional): سبب الرفض 