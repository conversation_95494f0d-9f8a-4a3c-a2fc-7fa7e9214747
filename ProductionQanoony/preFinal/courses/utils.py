import uuid
from django.utils import timezone
from .models import Certificate, Quiz, QuizAttempt
from students.models import StudentProfile


def create_certificate_for_quiz(student_profile, quiz, score):
    """
    إنشاء شهادة للطالب عند اجتياز اختبار بدرجة جيدة
    """
    try:
        # التحقق من عدم وجود شهادة مسبقة لنفس الاختبار
        existing_certificate = Certificate.objects.filter(
            student=student_profile,
            quiz=quiz,
            certificate_type='quiz'
        ).first()

        if existing_certificate:
            return existing_certificate

        # إنشاء معرف فريد للشهادة
        certificate_id = f"QUIZ-{student_profile.student_id}-{quiz.id}-{uuid.uuid4().hex[:8].upper()}"

        # إنشاء الشهادة
        certificate = Certificate.objects.create(
            student=student_profile,
            quiz=quiz,
            subject=quiz.lecture.subject,
            certificate_type='quiz',
            certificate_id=certificate_id,
            score=score
        )

        return certificate

    except Exception as e:
        print(f"Error creating certificate: {e}")
        return None


def get_student_semester_performance(student_profile, semester):
    """
    حساب أداء الطالب في فصل دراسي معين
    """
    try:
        # جلب جميع الاختبارات في هذا الفصل
        semester_quizzes = Quiz.objects.filter(
            lecture__subject__semester=semester,
            is_active=True
        )
        
        if not semester_quizzes.exists():
            return None
        
        # جلب محاولات الطالب في هذا الفصل
        student_attempts = QuizAttempt.objects.filter(
            student=student_profile,
            quiz__in=semester_quizzes,
            completed_at__isnull=False
        )
        
        if not student_attempts.exists():
            return None
        
        # حساب المتوسط
        total_score = 0
        total_possible = 0
        passed_quizzes = 0
        
        for attempt in student_attempts:
            quiz_score_percentage = (attempt.score / attempt.total_points * 100) if attempt.total_points > 0 else 0
            total_score += quiz_score_percentage
            total_possible += 100
            
            if quiz_score_percentage >= attempt.quiz.passing_score:
                passed_quizzes += 1
        
        average_score = total_score / len(student_attempts) if student_attempts else 0
        
        return {
            'average_score': round(average_score, 2),
            'total_quizzes': len(semester_quizzes),
            'attempted_quizzes': len(student_attempts),
            'passed_quizzes': passed_quizzes,
            'completion_rate': round((len(student_attempts) / len(semester_quizzes)) * 100, 2),
            'pass_rate': round((passed_quizzes / len(student_attempts)) * 100, 2) if student_attempts else 0
        }
        
    except Exception as e:
        print(f"Error calculating semester performance: {e}")
        return None


def check_certificate_eligibility(student_profile, semester):
    """
    فحص استحقاق الطالب للحصول على شهادة في فصل دراسي
    """
    performance = get_student_semester_performance(student_profile, semester)
    
    if not performance:
        return False
    
    # شروط الحصول على الشهادة:
    # 1. متوسط درجات 70% أو أكثر
    # 2. اجتياز 80% من الاختبارات على الأقل
    # 3. إكمال 80% من الاختبارات على الأقل
    
    return (
        performance['average_score'] >= 70 and
        performance['pass_rate'] >= 80 and
        performance['completion_rate'] >= 80
    )


def auto_generate_certificates():
    """
    دالة لإنشاء الشهادات التلقائية للطلاب المستحقين
    يمكن استدعاؤها من cron job أو task scheduler
    """
    from courses.models import Semester
    
    generated_certificates = []
    
    try:
        # جلب جميع الطلاب النشطين
        active_students = StudentProfile.objects.filter(
            user__is_active=True,
            user__is_student=True
        )
        
        # جلب جميع الفصول الدراسية
        semesters = Semester.objects.all()
        
        for student in active_students:
            for semester in semesters:
                # فحص إذا كان الطالب مستحق للشهادة
                if check_certificate_eligibility(student, semester):
                    # فحص عدم وجود شهادة مسبقة
                    existing_cert = Certificate.objects.filter(
                        student=student,
                        semester=semester
                    ).first()
                    
                    if not existing_cert:
                        # إنشاء الشهادة
                        certificate_id = f"CERT-{student.student_id}-{semester.id}-{uuid.uuid4().hex[:8].upper()}"
                        
                        certificate = Certificate.objects.create(
                            student=student,
                            semester=semester,
                            certificate_id=certificate_id
                        )
                        
                        generated_certificates.append(certificate)
                        
                        # إرسال إشعار
                        from notifications.utils import create_notification_safe
                        create_notification_safe(
                            recipient=student.user,
                            subject_ar="تهانينا! حصلت على شهادة إتمام الفصل الدراسي",
                            content_ar="مبروك {user_name}! لقد حصلت على شهادة إتمام {semester_name} بنجاح",
                            notification_type='in_app',
                            deduplication_minutes=1440,  # يوم واحد
                            semester_name=semester.name_ar
                        )
        
        return generated_certificates
        
    except Exception as e:
        print(f"Error in auto certificate generation: {e}")
        return []
