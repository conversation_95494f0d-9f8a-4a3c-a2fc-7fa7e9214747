from django.contrib import admin
from .models import Subject, Semester, Lecture, Quiz, Question, Answer, QuizAttempt, Certificate, PrivateTutorSession

class SuperuserOnlyAdmin(admin.ModelAdmin):
    def has_module_permission(self, request):
        return request.user.is_superuser
    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser
    def has_add_permission(self, request):
        return request.user.is_superuser
    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser
    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser

@admin.register(Subject)
class SubjectAdmin(SuperuserOnlyAdmin):
    list_display = ('name_en', 'name_ar', 'code', 'academic_year', 'is_active')
    search_fields = ('name_en', 'name_ar', 'code')
    list_filter = ('academic_year', 'is_active')
    ordering = ('academic_year', 'name_en')

@admin.register(Semester)
class SemesterAdmin(SuperuserOnlyAdmin):
    list_display = ('title_ar', 'title_en', 'academic_year', 'order', 'is_published', 'created_at')
    search_fields = ('title_ar', 'title_en')
    list_filter = ('academic_year', 'is_published')
    ordering = ('order',)

@admin.register(Lecture)
class LectureAdmin(SuperuserOnlyAdmin):
    list_display = ('title_ar', 'subject', 'order', 'has_pdf', 'is_published', 'created_at')
    search_fields = ('title_ar', 'subject__name_en')
    list_filter = ('subject', 'is_published')
    ordering = ('subject', 'order')
    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('subject', 'title_ar', 'order', 'duration_minutes')
        }),
        ('المحتوى', {
            'fields': ('youtube_video_id', 'pdf_summary'),
            'description': 'يمكنك إضافة معرف فيديو يوتيوب أو رابط كامل، وملف PDF ملخص'
        }),
        ('الإعدادات', {
            'fields': ('is_published',)
        }),
    )
    
    def has_pdf(self, obj):
        return bool(obj.pdf_summary)
    has_pdf.boolean = True
    has_pdf.short_description = 'PDF متاح'

@admin.register(Quiz)
class QuizAdmin(SuperuserOnlyAdmin):
    list_display = ('title_ar', 'lecture', 'is_active')
    search_fields = ('title_ar', 'lecture__title_ar')
    list_filter = ('lecture', 'is_active')
    ordering = ('lecture',)

@admin.register(Question)
class QuestionAdmin(SuperuserOnlyAdmin):
    list_display = ('question_text_ar', 'quiz', 'question_type', 'order', 'points')
    search_fields = ('question_text_ar', 'quiz__title_ar')
    list_filter = ('quiz', 'question_type')
    ordering = ('quiz', 'order')

@admin.register(Answer)
class AnswerAdmin(SuperuserOnlyAdmin):
    list_display = ('answer_text_ar', 'question', 'is_correct', 'order')
    search_fields = ('answer_text_ar', 'question__question_text_ar')
    list_filter = ('question', 'is_correct')
    ordering = ('question', 'order')

@admin.register(QuizAttempt)
class QuizAttemptAdmin(SuperuserOnlyAdmin):
    list_display = ('student', 'quiz', 'score', 'total_points', 'started_at', 'completed_at')
    search_fields = ('student__user__email', 'quiz__title_ar')
    list_filter = ('quiz',)
    ordering = ('quiz', 'started_at')

@admin.register(Certificate)
class CertificateAdmin(SuperuserOnlyAdmin):
    list_display = ('certificate_id', 'student', 'semester', 'issued_date')
    search_fields = ('certificate_id', 'student__user__email', 'semester__title_en')
    list_filter = ('semester', 'issued_date')
    ordering = ('-issued_date',)

@admin.register(PrivateTutorSession)
class PrivateTutorSessionAdmin(SuperuserOnlyAdmin):
    list_display = ('student', 'instructor', 'subject', 'scheduled_at', 'status', 'duration_minutes')
    search_fields = ('student__user__email', 'instructor__email', 'subject__name_ar')
    list_filter = ('status', 'subject', 'scheduled_at')
    ordering = ('-scheduled_at',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('معلومات الجلسة', {
            'fields': ('student', 'instructor', 'subject', 'scheduled_at', 'duration_minutes')
        }),
        ('تفاصيل الجلسة', {
            'fields': ('status', 'meeting_link', 'student_notes', 'instructor_notes')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    ) 