from rest_framework.routers import DefaultRouter
from .views import (
    SubjectViewSet, SemesterViewSet, LectureViewSet, QuizViewSet,
    QuestionViewSet, AnswerViewSet, QuizAttemptViewSet, CertificateViewSet,
    PrivateTutorSessionViewSet
)

router = DefaultRouter()
router.register(r'subjects', SubjectViewSet, basename='subject')
router.register(r'semesters', SemesterViewSet, basename='semester')
router.register(r'lectures', LectureViewSet, basename='lecture')
router.register(r'quizzes', QuizViewSet, basename='quiz')
router.register(r'questions', QuestionViewSet, basename='question')
router.register(r'answers', AnswerViewSet, basename='answer')
router.register(r'quiz-attempts', QuizAttemptViewSet, basename='quizattempt')
router.register(r'certificates', CertificateViewSet, basename='certificate')
router.register(r'private-tutor', PrivateTutorSessionViewSet, basename='private-tutor')

urlpatterns = router.urls 