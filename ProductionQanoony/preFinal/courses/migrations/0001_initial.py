# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('universities', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Answer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('answer_text_ar', models.CharField(max_length=255)),
                ('is_correct', models.BooleanField(default=False)),
                ('order', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='Certificate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('certificate_id', models.CharField(max_length=100, unique=True)),
                ('issued_date', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Lecture',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_ar', models.CharField(max_length=255)),
                ('youtube_video_id', models.CharField(max_length=100)),
                ('duration_minutes', models.IntegerField()),
                ('order', models.IntegerField()),
                ('pdf_summary', models.FileField(blank=True, null=True, upload_to='summaries/')),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='PrivateTutorSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_at', models.DateTimeField(verbose_name='موعد الجلسة')),
                ('duration_minutes', models.PositiveIntegerField(default=60, verbose_name='مدة الجلسة (دقائق)')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('approved', 'تمت الموافقة'), ('rejected', 'مرفوض'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('meeting_link', models.URLField(blank=True, null=True, verbose_name='رابط الجلسة')),
                ('student_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات الطالب')),
                ('instructor_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات المدرس')),
                ('student_image', models.ImageField(blank=True, null=True, upload_to='tutor_sessions/student_images/', verbose_name='صورة الطالب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'جلسة خاصة',
                'verbose_name_plural': 'الجلسات الخاصة',
                'ordering': ['-scheduled_at'],
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_text_ar', models.TextField()),
                ('question_type', models.CharField(choices=[('multiple_choice', 'اختيار من متعدد'), ('true_false', 'صح أو خطأ')], max_length=20)),
                ('order', models.IntegerField()),
                ('points', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_ar', models.CharField(max_length=255)),
                ('instructions_ar', models.TextField()),
                ('time_limit_minutes', models.IntegerField()),
                ('passing_score', models.IntegerField()),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='Semester',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_ar', models.CharField(max_length=255)),
                ('title_en', models.CharField(max_length=255)),
                ('description_ar', models.TextField()),
                ('order', models.IntegerField()),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='semesters', to='universities.academicyear')),
            ],
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name_ar', models.CharField(max_length=255)),
                ('name_en', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description_ar', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subjects', to='universities.academicyear')),
                ('semester', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subjects', to='courses.semester')),
            ],
        ),
        migrations.CreateModel(
            name='QuizAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.IntegerField()),
                ('total_points', models.IntegerField()),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('answers', models.JSONField(default=dict)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attempts', to='courses.quiz')),
            ],
        ),
    ]
