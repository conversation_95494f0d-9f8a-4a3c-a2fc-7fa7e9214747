from django.db import models
from universities.models import AcademicYear
from authentication.models import CustomUser
from students.models import StudentProfile
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
import re

# دالة لاستخراج معرف اليوتيوب من الرابط الكامل
def extract_youtube_id(url):
    if not url:
        return None
    
    # إذا كان معرف فقط (بدون رابط)
    if len(url) <= 20 and 'youtube.com' not in url and 'youtu.be' not in url:
        return url
    
    # روابط youtube.com
    youtube_regex = r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})'
    match = re.match(youtube_regex, url)
    
    if match:
        return match.group(1)
    
    return None


class Subject(models.Model):
    name_ar = models.CharField(max_length=255)
    name_en = models.CharField(max_length=255)
    code = models.CharField(max_length=50, unique=True)
    description_ar = models.TextField()
    semester = models.ForeignKey(
        'Semester', on_delete=models.CASCADE, related_name='subjects', null=True, blank=True)
    academic_year = models.ForeignKey(
        AcademicYear, on_delete=models.CASCADE, related_name='subjects')
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name_en} ({self.code}) - {self.semester.title_en if self.semester else ''}"


class Semester(models.Model):
    title_ar = models.CharField(max_length=255)
    title_en = models.CharField(max_length=255)
    description_ar = models.TextField()
    academic_year = models.ForeignKey(
        AcademicYear, on_delete=models.CASCADE, related_name='semesters')
    order = models.IntegerField()
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.title_en}"


class Lecture(models.Model):
    subject = models.ForeignKey(
        Subject, on_delete=models.CASCADE, related_name='lectures')
    title_ar = models.CharField(max_length=255)
    youtube_video_id = models.CharField(max_length=100)
    duration_minutes = models.IntegerField()
    order = models.IntegerField()
    pdf_summary = models.FileField(
        upload_to='summaries/', blank=True, null=True)
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def clean(self):
        from django.core.exceptions import ValidationError
        
        # التحقق من معرف اليوتيوب
        if self.youtube_video_id:
            # استخراج المعرف من الرابط إذا كان رابط كامل
            extracted_id = extract_youtube_id(self.youtube_video_id)
            if extracted_id:
                self.youtube_video_id = extracted_id
            
            # التحقق من أن المعرف صحيح (11 حرف)
            if len(self.youtube_video_id) != 11:
                raise ValidationError({
                    'youtube_video_id': _('معرف فيديو يوتيوب يجب أن يكون 11 حرف')
                })
            
            # التحقق من أن المعرف يحتوي على أحرف صحيحة فقط
            if not re.match(r'^[a-zA-Z0-9_-]{11}$', self.youtube_video_id):
                raise ValidationError({
                    'youtube_video_id': _('معرف فيديو يوتيوب غير صحيح')
                })
        
        # التحقق من نوع ملف PDF
        if self.pdf_summary:
            if not self.pdf_summary.name.endswith('.pdf'):
                raise ValidationError({
                    'pdf_summary': _('يجب أن يكون الملف بصيغة PDF')
                })

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title_ar} ({self.subject.name_en})"


class Quiz(models.Model):
    lecture = models.ForeignKey(
        Lecture, on_delete=models.CASCADE, related_name='quizzes')
    title_ar = models.CharField(max_length=255)
    instructions_ar = models.TextField()
    time_limit_minutes = models.IntegerField()
    passing_score = models.IntegerField()
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.title_ar} ({self.lecture.title_ar})"


class Question(models.Model):
    QUESTION_TYPE_CHOICES = [
        ('multiple_choice', 'اختيار من متعدد'),
        ('true_false', 'صح أو خطأ'),
    ]
    quiz = models.ForeignKey(
        Quiz, on_delete=models.CASCADE, related_name='questions')
    question_text_ar = models.TextField()
    question_type = models.CharField(
        max_length=20, choices=QUESTION_TYPE_CHOICES)
    order = models.IntegerField()
    points = models.IntegerField()

    def __str__(self):
        return f"{self.question_text_ar} ({self.quiz.title_ar})"


class Answer(models.Model):
    question = models.ForeignKey(
        Question, on_delete=models.CASCADE, related_name='answers')
    answer_text_ar = models.CharField(max_length=255)
    is_correct = models.BooleanField(default=False)
    order = models.IntegerField()

    def __str__(self):
        return f"{self.answer_text_ar} ({self.question.question_text_ar})"


class QuizAttempt(models.Model):
    student = models.ForeignKey(
        StudentProfile, on_delete=models.CASCADE, related_name='quiz_attempts')
    quiz = models.ForeignKey(
        Quiz, on_delete=models.CASCADE, related_name='attempts')
    score = models.IntegerField()
    total_points = models.IntegerField()
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    answers = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.student.user.email} - {self.quiz.title_ar}"


class Certificate(models.Model):
    CERTIFICATE_TYPE_CHOICES = [
        ('semester', 'شهادة إتمام فصل دراسي'),
        ('quiz', 'شهادة اجتياز اختبار'),
        ('subject', 'شهادة إتمام مادة'),
    ]

    student = models.ForeignKey(
        StudentProfile, on_delete=models.CASCADE, related_name='certificates')
    semester = models.ForeignKey(
        Semester, on_delete=models.CASCADE, related_name='certificates', null=True, blank=True)
    quiz = models.ForeignKey(
        Quiz, on_delete=models.CASCADE, related_name='certificates', null=True, blank=True)
    subject = models.ForeignKey(
        Subject, on_delete=models.CASCADE, related_name='certificates', null=True, blank=True)
    certificate_type = models.CharField(
        max_length=20, choices=CERTIFICATE_TYPE_CHOICES, default='semester')
    certificate_id = models.CharField(max_length=100, unique=True)
    score = models.FloatField(null=True, blank=True, help_text="الدرجة المحققة (للاختبارات)")
    issued_date = models.DateTimeField(auto_now_add=True)
    # The certificate file is generated dynamically and not stored permanently.

    def __str__(self):
        return f"{self.certificate_id} - {self.student.user.email} ({self.get_certificate_type_display()})"

    def get_certificate_title(self):
        """إرجاع عنوان الشهادة حسب النوع"""
        if self.certificate_type == 'quiz' and self.quiz:
            return f"شهادة اجتياز اختبار {self.quiz.title_ar}"
        elif self.certificate_type == 'subject' and self.subject:
            return f"شهادة إتمام مادة {self.subject.name_ar}"
        elif self.certificate_type == 'semester' and self.semester:
            return f"شهادة إتمام {self.semester.name_ar}"
        return "شهادة إنجاز"


class PrivateTutorSession(models.Model):
    STATUS_CHOICES = [
        ('pending', _('قيد الانتظار')),
        ('approved', _('تمت الموافقة')),
        ('rejected', _('مرفوض')),
        ('completed', _('مكتمل')),
        ('cancelled', _('ملغي')),
    ]

    student = models.ForeignKey('students.StudentProfile', on_delete=models.CASCADE,
                                related_name='tutor_sessions', verbose_name=_('الطالب'))
    instructor = models.ForeignKey('authentication.CustomUser', on_delete=models.CASCADE,
                                   related_name='teaching_sessions', verbose_name=_('المدرس'))
    subject = models.ForeignKey('Subject', on_delete=models.CASCADE,
                                related_name='private_sessions', verbose_name=_('المادة'))
    scheduled_at = models.DateTimeField(verbose_name=_('موعد الجلسة'))
    duration_minutes = models.PositiveIntegerField(
        default=60, verbose_name=_('مدة الجلسة (دقائق)'))
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name=_('الحالة'))
    meeting_link = models.URLField(
        null=True, blank=True, verbose_name=_('رابط الجلسة'))
    student_notes = models.TextField(
        null=True, blank=True, verbose_name=_('ملاحظات الطالب'))
    instructor_notes = models.TextField(
        null=True, blank=True, verbose_name=_('ملاحظات المدرس'))
    student_image = models.ImageField(
        upload_to='tutor_sessions/student_images/',
        null=True, blank=True, verbose_name=_('صورة الطالب')
    )
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name=_('تاريخ الإنشاء'))
    updated_at = models.DateTimeField(
        auto_now=True, verbose_name=_('تاريخ التحديث'))

    class Meta:
        verbose_name = _('جلسة خاصة')
        verbose_name_plural = _('الجلسات الخاصة')
        ordering = ['-scheduled_at']

    def __str__(self):
        return f"{self.student.user.get_full_name()} - {self.subject.name_ar} ({self.scheduled_at})" 