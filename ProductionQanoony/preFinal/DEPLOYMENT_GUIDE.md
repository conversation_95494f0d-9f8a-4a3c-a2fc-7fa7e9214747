# 🚀 دليل رفع منصة قانوني على السيرفر

## معلومات السيرفر
- **الدومين**: www.qanonyacademy.com
- **IP السيرفر**: ************
- **نظام التشغيل**: Ubuntu/Debian Linux
- **خادم الويب**: Nginx + SSL

---

## 📁 الملفات المطلوبة للرفع

### 1. مجلد المشروع الكامل:
```
preFinal/
├── ai_assistant/
├── authentication/
├── careers/
├── communication/
├── config/
├── courses/
├── deployment/          # ملفات الإعداد
├── email_verification/
├── library/
├── media/              # ملفات المستخدمين
├── notifications/
├── qanony/            # React Frontend
├── scheduling/
├── students/
├── subscriptions/
├── templates/
├── universities/
├── .env.production    # متغيرات البيئة
├── manage.py
└── requirements.txt
```

---

## 🔧 خطوات الرفع والإعداد

### الخطوة 1: رفع الملفات للسيرفر
```bash
# من جهازك المحلي
scp -r preFinal/ root@************:/tmp/qanony-project/
```

### الخطوة 2: الاتصال بالسيرفر
```bash
ssh root@************
```

### الخطوة 3: تشغيل سكريبت الإعداد الأساسي
```bash
cd /tmp/qanony-project/preFinal/deployment
chmod +x deploy.sh
./deploy.sh
```

**ما يحدث في هذه الخطوة:**
- تحديث النظام
- تثبيت PostgreSQL, Redis, Nginx
- إنشاء مستخدم qanony
- إعداد قاعدة البيانات
- تثبيت SSL certificates
- إعداد Firewall

### الخطوة 4: نسخ المشروع للمكان النهائي
```bash
# كـ root user
mkdir -p /home/<USER>/PreQanoony
cp -r /tmp/qanony-project/preFinal /home/<USER>/PreQanoony/
chown -R qanony:qanony /home/<USER>/PreQanoony
```

### الخطوة 5: التبديل لمستخدم qanony
```bash
su - qanony
cd /home/<USER>/PreQanoony/preFinal
```

### الخطوة 6: تشغيل سكريبت إعداد المشروع
```bash
chmod +x deployment/setup_project.sh
./deployment/setup_project.sh
```

**ما يحدث في هذه الخطوة:**
- إنشاء Virtual Environment
- تثبيت Python packages
- تطبيق Database migrations
- جمع Static files
- إنشاء Superuser
- اختبار الإعدادات

### الخطوة 7: بدء الخدمات (كـ root)
```bash
# العودة لـ root user
exit
systemctl start qanony-django
systemctl start qanony-daphne
systemctl start qanony-celery
systemctl enable qanony-django qanony-daphne qanony-celery
```

---

## ✅ التحقق من نجاح الرفع

### 1. فحص حالة الخدمات:
```bash
systemctl status qanony-django
systemctl status qanony-daphne
systemctl status qanony-celery
systemctl status nginx
```

### 2. فحص الموقع:
- **الصفحة الرئيسية**: https://www.qanonyacademy.com
- **لوحة الإدارة**: https://www.qanonyacademy.com/admin/
- **API**: https://www.qanonyacademy.com/api/

### 3. فحص الـ Logs:
```bash
# Django logs
journalctl -u qanony-django -f

# Nginx logs
tail -f /var/log/nginx/error.log
```

---

## 🔧 إعدادات مهمة بعد الرفع

### 1. تغيير كلمة مرور قاعدة البيانات:
```bash
sudo -u postgres psql
ALTER USER platform_admin PASSWORD 'كلمة_مرور_قوية_جديدة';
\q
```

### 2. تحديث .env.production:
```bash
nano /home/<USER>/PreQanoony/preFinal/.env.production
# غيّر DB_PASSWORD إلى كلمة المرور الجديدة
# API Keys موجودة بالفعل في الملف
```

### 3. إعادة تشغيل الخدمات:
```bash
systemctl restart qanony-django qanony-daphne qanony-celery
```

---

## 🛠️ استكشاف الأخطاء

### مشكلة: 502 Bad Gateway
```bash
# تحقق من Django service
systemctl status qanony-django
journalctl -u qanony-django -n 50
```

### مشكلة: Static files لا تعمل
```bash
# كـ qanony user
cd /home/<USER>/PreQanoony/preFinal
source /home/<USER>/PreQanoony/venv/bin/activate
python manage.py collectstatic --noinput
```

### مشكلة: Database connection
```bash
# تحقق من PostgreSQL
systemctl status postgresql
sudo -u postgres psql -c "\l"
```

---

## 📞 معلومات الدعم

### ملفات الإعداد المهمة:
- **Nginx**: `/etc/nginx/sites-available/qanonyacademy.conf`
- **Django Service**: `/etc/systemd/system/qanony-django.service`
- **Environment**: `/home/<USER>/PreQanoony/preFinal/.env.production`

### أوامر مفيدة:
```bash
# إعادة تشغيل جميع الخدمات
systemctl restart qanony-django qanony-daphne qanony-celery nginx

# مراقبة الـ logs
journalctl -f -u qanony-django -u qanony-daphne -u qanony-celery

# اختبار Nginx configuration
nginx -t

# إعادة تحميل Nginx
systemctl reload nginx
```

---

## 🎯 نصائح مهمة

1. **احتفظ بنسخة احتياطية** من قاعدة البيانات قبل أي تحديث
2. **راقب استخدام الذاكرة** والمعالج بانتظام
3. **حدّث SSL certificates** كل 3 أشهر (تلقائي مع Let's Encrypt)
4. **راجع الـ logs** بانتظام للتأكد من عدم وجود أخطاء

---

**🎉 بالتوفيق في رفع منصة قانوني!**
