from django.core import signing
from django.utils import timezone
from datetime import timedelta
import secrets

TOKEN_EXPIRY_HOURS = 24


def generate_token(user_id):
    # Use a random string plus user id for uniqueness
    raw_token = f"{user_id}-{secrets.token_urlsafe(32)}"
    return signing.dumps(raw_token)


def validate_token(token):
    try:
        raw_token = signing.loads(token, max_age=TOKEN_EXPIRY_HOURS * 3600)
        return raw_token
    except signing.BadSignature:
        return None
    except signing.SignatureExpired:
        return None 