from django.shortcuts import render
from rest_framework import status, views
from rest_framework.response import Response
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import EmailConfirmation
from .serializers import EmailConfirmationRequestSerializer, EmailConfirmationSerializer, EmailConfirmationResendSerializer
from .tokens import generate_token, validate_token
from .tasks import send_confirmation_email_task
from datetime import timedelta

User = get_user_model()

# Helper to keep backwards compatibility with old tests expecting this function
# Delegates to the Celery task implementation.
def send_confirmation_email(email: str, token: str) -> None:
    """Enqueue an email-confirmation message.

    A simple wrapper around ``send_confirmation_email_task`` that mirrors the
    original synchronous helper that existed before the Celery refactor. This
    keeps the public interface stable for tests and any other callers while we
    enjoy the benefits of asynchronous processing under the hood.
    """
    # Using ``delay`` schedules the task asynchronously; if Celery is not
    # running (e.g., in unit tests) this becomes a no-op thanks to the patching
    # logic in tests.
    send_confirmation_email_task.delay(email, token)

class RequestEmailConfirmationView(views.APIView):
    def post(self, request):
        serializer = EmailConfirmationRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data['email']
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({'detail': 'User not found.'}, status=status.HTTP_404_NOT_FOUND)
        token = generate_token(user.id)
        expires_at = timezone.now() + timedelta(hours=24)
        EmailConfirmation.objects.create(user=user, token=token, expires_at=expires_at)
        send_confirmation_email(user.email, token)
        return Response({'detail': 'Confirmation email sent.'}, status=status.HTTP_200_OK)

class ConfirmEmailView(views.APIView):
    def post(self, request):
        serializer = EmailConfirmationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        token = serializer.validated_data['token']
        raw_token = validate_token(token)
        if not raw_token:
            return Response({'detail': 'Invalid or expired token.'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            confirmation = EmailConfirmation.objects.get(token=token, is_confirmed=False)
        except EmailConfirmation.DoesNotExist:
            return Response({'detail': 'Invalid or already confirmed.'}, status=status.HTTP_400_BAD_REQUEST)
        confirmation.is_confirmed = True
        confirmation.save()
        confirmation.user.email_verified = True
        confirmation.user.save()
        return Response({'detail': 'Email confirmed.'}, status=status.HTTP_200_OK)

class ResendEmailConfirmationView(views.APIView):
    def post(self, request):
        serializer = EmailConfirmationResendSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data['email']
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({'detail': 'User not found.'}, status=status.HTTP_404_NOT_FOUND)
        token = generate_token(user.id)
        expires_at = timezone.now() + timedelta(hours=24)
        EmailConfirmation.objects.create(user=user, token=token, expires_at=expires_at)
        send_confirmation_email(user.email, token)
        return Response({'detail': 'Confirmation email resent.'}, status=status.HTTP_200_OK)
