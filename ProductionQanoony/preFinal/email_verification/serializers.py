from rest_framework import serializers
from .models import EmailConfirmation
from django.contrib.auth import get_user_model

User = get_user_model()

class EmailConfirmationRequestSerializer(serializers.Serializer):
    email = serializers.EmailField()

class EmailConfirmationSerializer(serializers.Serializer):
    token = serializers.CharField()

class EmailConfirmationResendSerializer(serializers.Serializer):
    email = serializers.EmailField() 