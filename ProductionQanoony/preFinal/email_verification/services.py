import requests
from django.conf import settings

def send_confirmation_email(email, token):
    url = "https://api.brevo.com/v3/smtp/email"
    frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')
    payload = {
        "sender": {"name": "منصة قانوني", "email": settings.DEFAULT_FROM_EMAIL},
        "to": [{"email": email}],
        "subject": "تأكيد البريد الإلكتروني - منصة قانوني",
        "htmlContent": f"""
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #FACC15;">مرحباً بك في منصة قانوني</h2>
            <p>يرجى النقر على الرابط أدناه لتأكيد بريدك الإلكتروني:</p>
            <a href='{frontend_url}/verify-email?token={token}'
               style="background-color: #FACC15; color: #1A1A1A; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0;">
               تأكيد البريد الإلكتروني
            </a>
            <p>إذا لم تقم بإنشاء حساب، يرجى تجاهل هذه الرسالة.</p>
            <hr style="margin: 30px 0;">
            <p style="color: #666; font-size: 12px;">منصة قانوني - أفضل منصة تعليمية لطلاب كلية الحقوق</p>
        </div>
        """
    }
    headers = {
        "api-key": settings.BREVO_API_KEY,
        "Content-Type": "application/json"
    }
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()