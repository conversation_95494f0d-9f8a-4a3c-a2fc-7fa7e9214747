from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from unittest.mock import patch
from .models import EmailConfirmation

User = get_user_model()

class EmailVerificationTests(APITestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.patcher = patch('email_verification.views.send_confirmation_email')
        cls.mock_send = cls.patcher.start()

    @classmethod
    def tearDownClass(cls):
        cls.patcher.stop()
        super().tearDownClass()

    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='pass', first_name='Test', last_name='User')

    def test_request_email_confirmation(self):
        url = reverse('email-confirmation-request')
        response = self.client.post(url, {'email': self.user.email})
        self.assertEqual(response.status_code, 200)
        self.assertTrue(EmailConfirmation.objects.filter(user=self.user).exists())
        self.mock_send.assert_called_once()
        self.mock_send.reset_mock()

    def test_resend_email_confirmation(self):
        url = reverse('email-confirmation-resend')
        response = self.client.post(url, {'email': self.user.email})
        self.assertEqual(response.status_code, 200)
        self.assertTrue(EmailConfirmation.objects.filter(user=self.user).exists())
        self.mock_send.assert_called_once()
        self.mock_send.reset_mock()

    def test_confirm_email(self):
        from .tokens import generate_token
        token = generate_token(self.user.id)
        EmailConfirmation.objects.create(user=self.user, token=token, expires_at='2099-01-01T00:00:00Z')
        url = reverse('email-confirmation-confirm')
        response = self.client.post(url, {'token': token})
        self.assertEqual(response.status_code, 200)
        self.user.refresh_from_db()
        self.assertTrue(self.user.email_verified)
