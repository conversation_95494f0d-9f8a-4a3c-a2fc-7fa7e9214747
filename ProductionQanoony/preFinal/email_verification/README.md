# Email Verification App

This app provides email confirmation functionality using Brevo (Sendinblue).

## Features
- Request email confirmation
- Confirm email via token
- Resend confirmation email
- Uses Brevo API for sending emails
- Token-based, secure, and async-ready (Celery)

## Setup
1. Add `email_verification` to `INSTALLED_APPS` in your Django settings.
2. Add the following to your `.env` or settings:

```
BREVO_API_KEY=your_brevo_api_key_here
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

3. Include the app's URLs in your project:

```
# config/urls.py
path('email-verification/', include('email_verification.urls')),
```

4. Run migrations:
```
python manage.py makemigrations email_verification
python manage.py migrate
```

## Usage
- POST `/email-verification/request/` with `{ "email": "<EMAIL>" }`
- POST `/email-verification/confirm/` with `{ "token": "..." }`
- POST `/email-verification/resend/` with `{ "email": "<EMAIL>" }`

## Celery
- To send emails asynchronously, use the `send_confirmation_email_task.delay(email, token)` in your workflow.
- **Start a worker:**
```
celery -A config worker -l info
```
- Make sure Redis is running as your broker.

## Customization
- Edit `services.py` and `templates/email_verification/confirmation_email.html` for custom email content. 