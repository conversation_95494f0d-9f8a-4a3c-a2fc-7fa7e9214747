from django.contrib import admin
from .models import Category, LegalDocument, DocumentAccess

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name_en', 'name_ar', 'parent', 'order', 'is_active')
    search_fields = ('name_en', 'name_ar')
    list_filter = ('is_active', 'parent')
    ordering = ('order', 'name_en')

@admin.register(LegalDocument)
class LegalDocumentAdmin(admin.ModelAdmin):
    list_display = ('title_en', 'title_ar', 'category', 'author', 'file_type', 'is_featured', 'download_count', 'uploaded_by', 'created_at')
    search_fields = ('title_en', 'title_ar', 'author', 'tags')
    list_filter = ('category', 'is_featured', 'file_type', 'author', 'uploaded_by')
    ordering = ('-created_at',)

@admin.register(DocumentAccess)
class DocumentAccessAdmin(admin.ModelAdmin):
    list_display = ('student', 'document', 'accessed_at', 'download_count')
    search_fields = ('student__student_id', 'document__title_en', 'document__title_ar')
    list_filter = ('accessed_at',)
    ordering = ('-accessed_at',)
