# Library API Documentation

## 1. Categories
- **Endpoint:** /api/library/categories/
- **Methods:** GET, POST
- **Description:** List all categories or create a new category (admin only).
- **Permissions:**
  - GET: Public
  - POST: Admin only
- **Sample Request (POST):**
{
  "name_ar": "قوانين عامة",
  "name_en": "General Laws",
  "order": 1,
  "is_active": true
}
- **Sample Response (GET):**
[
  {
    "id": 1,
    "name_ar": "قوانين عامة",
    "name_en": "General Laws",
    "parent": null,
    "order": 1,
    "is_active": true
  }
]

## 2. Legal Documents
- **Endpoint:** /api/library/documents/
- **Methods:** GET, POST
- **Description:** List all documents or upload a new document (instructor/admin only).
- **Permissions:**
  - GET: Public
  - POST: Instructor/Admin only
- **Sample Request (POST):**
{
  "title_ar": "دستور",
  "title_en": "Constitution",
  "category_id": 1,
  "description_ar": "شرح الدستور",
  "file_url": "http://example.com/test.pdf",
  "file_type": "pdf",
  "author": "مؤلف",
  "publication_date": "2024-06-01",
  "tags": "دستور,قانون",
  "is_featured": true
}
- **Sample Response (GET):**
[
  {
    "id": 1,
    "title_ar": "دستور",
    "title_en": "Constitution",
    "category": {...},
    "description_ar": "شرح الدستور",
    "file_url": "http://example.com/test.pdf",
    "file_type": "pdf",
    "author": "مؤلف",
    "publication_date": "2024-06-01",
    "tags": "دستور,قانون",
    "download_count": 0,
    "is_featured": true,
    "uploaded_by": {...},
    "created_at": "2024-06-01T12:00:00Z"
  }
]
- **Custom Actions:**
  - **Featured Documents:**
    - **Endpoint:** /api/library/documents/featured/
    - **Method:** GET
    - **Description:** List all featured documents.
    - **Permissions:** Public
  - **Search Documents:**
    - **Endpoint:** /api/library/documents/search/?q=term
    - **Method:** GET
    - **Description:** Search documents by title, author, or tags.
    - **Permissions:** Public
  - **Download Document:**
    - **Endpoint:** /api/library/documents/{id}/download/
    - **Method:** GET
    - **Description:** Download a document (authenticated students only, access tracked).
    - **Permissions:** IsEmailVerified & IsStudent
    - **Response:**
      {
        "file_url": "http://example.com/test.pdf",
        "detail": "تم تسجيل عملية التحميل"
      }

## 3. Document Access
- **Endpoint:** /api/library/access/
- **Methods:** GET
- **Description:** List all document access records (admin only).
- **Permissions:** Admin only
- **Sample Response (GET):**
[
  {
    "id": 1,
    "student": {...},
    "document": {...},
    "accessed_at": "2024-06-01T12:00:00Z",
    "download_count": 2
  }
] 