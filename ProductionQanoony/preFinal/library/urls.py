from rest_framework.routers import DefaultRouter
from .views import CategoryViewSet, LegalDocumentViewSet, DocumentAccessViewSet

router = DefaultRouter()
router.register(r'categories', CategoryViewSet, basename='category')
router.register(r'documents', LegalDocumentViewSet, basename='legaldocument')
router.register(r'access', DocumentAccessViewSet, basename='documentaccess')

urlpatterns = router.urls 