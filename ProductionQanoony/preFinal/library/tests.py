from django.test import TestCase
from rest_framework.test import APITestCase, APIClient
from django.urls import reverse
from django.contrib.auth import get_user_model
from students.models import StudentProfile
from authentication.models import CustomUser
from .models import Category, LegalDocument, DocumentAccess
from universities.models import University, AcademicYear
from django.utils import timezone
import tempfile
import datetime

User = get_user_model()

def get_temp_file(suffix='.pdf'):
    f = tempfile.NamedTemporaryFile(suffix=suffix, delete=False)
    f.write(b"%PDF-1.4 test file content")
    f.seek(0)
    return f

class LibraryAppTests(APITestCase):
    def setUp(self):
        # Users
        self.admin = User.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='adminpass')
        self.instructor = User.objects.create_user(email='<EMAIL>', first_name='Inst', last_name='Ructor', password='instructorpass', is_instructor=True)
        self.student = User.objects.create_user(email='<EMAIL>', first_name='Stud', last_name='Ent', password='studentpass', is_student=True)
        for u in [self.admin, self.instructor, self.student]:
            u.email_verified = True
            u.save()
        # University, AcademicYear, StudentProfile
        self.university = University.objects.create(name_ar='جامعة القاهرة', name_en='Cairo University', code='CU', city='Cairo')
        self.academic_year = AcademicYear.objects.create(university=self.university, year_number=1, year_name_ar='الفرقة الأولى', year_name_en='First Year')
        self.student_profile = StudentProfile.objects.create(user=self.student, university=self.university, academic_year=self.academic_year, student_id='S123')
        # Auth clients
        self.admin_client = APIClient()
        self.admin_client.force_authenticate(user=self.admin)
        self.instructor_client = APIClient()
        self.instructor_client.force_authenticate(user=self.instructor)
        self.student_client = APIClient()
        self.student_client.force_authenticate(user=self.student)

    def test_category_crud(self):
        url = reverse('category-list')
        data = {'name_ar': 'قوانين عامة', 'name_en': 'General Laws', 'order': 1, 'is_active': True}
        # Public can list
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        # Admin can create
        resp = self.admin_client.post(url, data)
        self.assertEqual(resp.status_code, 201)
        cat_id = resp.data['id']
        # Admin can update
        resp = self.admin_client.patch(reverse('category-detail', args=[cat_id]), {'name_ar': 'قوانين خاصة'})
        self.assertEqual(resp.status_code, 200)
        # Admin can delete
        resp = self.admin_client.delete(reverse('category-detail', args=[cat_id]))
        self.assertEqual(resp.status_code, 204)

    def test_document_upload_and_permissions(self):
        cat = Category.objects.create(name_ar='مدني', name_en='Civil', order=1, is_active=True)
        url = reverse('legaldocument-list')
        data = {
            'title_ar': 'دستور',
            'title_en': 'Constitution',
            'category_id': cat.id,
            'description_ar': 'شرح الدستور',
            'file_url': 'http://example.com/test.pdf',
            'file_type': 'pdf',
            'author': 'مؤلف',
            'publication_date': '2024-06-01',
            'tags': 'دستور,قانون',
            'is_featured': True
        }
        # Student cannot upload
        resp = self.student_client.post(url, data, format='multipart')
        self.assertEqual(resp.status_code, 403)
        # Instructor can upload
        resp = self.instructor_client.post(url, data, format='multipart')
        self.assertEqual(resp.status_code, 201)
        doc_id = resp.data['id']
        # Instructor can update
        resp = self.instructor_client.patch(reverse('legaldocument-detail', args=[doc_id]), {'title_ar': 'دستور محدث'})
        self.assertEqual(resp.status_code, 200)
        # Student can list
        resp = self.student_client.get(url)
        self.assertEqual(resp.status_code, 200)
        # Public can search
        resp = self.client.get(reverse('legaldocument-search') + '?q=دستور')
        self.assertEqual(resp.status_code, 200)
        # Featured endpoint
        resp = self.client.get(reverse('legaldocument-featured'))
        self.assertEqual(resp.status_code, 200)

    def test_document_download_and_access_tracking(self):
        cat = Category.objects.create(name_ar='جنائي', name_en='Criminal', order=2, is_active=True)
        temp_file = get_temp_file()
        doc = LegalDocument.objects.create(
            title_ar='قانون جنائي', title_en='Criminal Law', category=cat, description_ar='...',
            file_url=temp_file.name, file_type='pdf', author='مؤلف', publication_date='2024-06-01', tags='جنائي', is_featured=False, uploaded_by=self.instructor, download_count=0
        )
        url = reverse('legaldocument-download', args=[doc.id])
        # Student can download
        resp = self.student_client.get(url)
        self.assertEqual(resp.status_code, 200)
        self.assertIn('file_url', resp.data)
        self.assertIn('تم تسجيل عملية التحميل', resp.data['detail'])
        # Access tracking
        access = DocumentAccess.objects.filter(student=self.student_profile, document=doc).first()
        self.assertIsNotNone(access)
        self.assertEqual(access.download_count, 2)
        # Download again
        resp = self.student_client.get(url)
        access.refresh_from_db()
        self.assertEqual(access.download_count, 3)
        # Public cannot download
        resp = self.client.get(url)
        self.assertIn(resp.status_code, [401, 403])

    def test_permissions_enforced(self):
        cat = Category.objects.create(name_ar='مدني', name_en='Civil', order=1, is_active=True)
        temp_file = get_temp_file()
        doc = LegalDocument.objects.create(
            title_ar='قانون مدني', title_en='Civil Law', category=cat, description_ar='...',
            file_url=temp_file.name, file_type='pdf', author='مؤلف', publication_date='2024-06-01', tags='مدني', is_featured=False, uploaded_by=self.instructor, download_count=0
        )
        # Unauthenticated cannot upload
        url = reverse('legaldocument-list')
        resp = self.client.post(url, {'title_ar': 'x'}, format='multipart')
        self.assertEqual(resp.status_code, 401)
        # Student cannot delete
        resp = self.student_client.delete(reverse('legaldocument-detail', args=[doc.id]))
        self.assertEqual(resp.status_code, 403)
        # Instructor can delete
        resp = self.instructor_client.delete(reverse('legaldocument-detail', args=[doc.id]))
        self.assertIn(resp.status_code, [204, 404])

    def test_document_access_admin(self):
        cat = Category.objects.create(name_ar='جنائي', name_en='Criminal', order=2, is_active=True)
        temp_file = get_temp_file()
        doc = LegalDocument.objects.create(
            title_ar='قانون جنائي', title_en='Criminal Law', category=cat, description_ar='...',
            file_url=temp_file.name, file_type='pdf', author='مؤلف', publication_date='2024-06-01', tags='جنائي', is_featured=False, uploaded_by=self.instructor
        )
        access = DocumentAccess.objects.create(student=self.student_profile, document=doc, download_count=3)
        url = reverse('documentaccess-list')
        # Admin can list accesses
        resp = self.admin_client.get(url)
        self.assertEqual(resp.status_code, 200)
        # Student cannot list accesses
        resp = self.student_client.get(url)
        self.assertEqual(resp.status_code, 403)
