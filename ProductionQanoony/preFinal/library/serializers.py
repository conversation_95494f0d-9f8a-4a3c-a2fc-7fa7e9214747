from rest_framework import serializers
from .models import Category, LegalDocument, DocumentAccess
from authentication.serializers import ProfileSerializer
from students.serializers import StudentProfileSerializer
from students.models import StudentProfile

class CategorySerializer(serializers.ModelSerializer):
    parent = serializers.PrimaryKeyRelatedField(queryset=Category.objects.all(), allow_null=True, required=False)
    class Meta:
        model = Category
        fields = ['id', 'name_ar', 'name_en', 'parent', 'order', 'is_active']

class LegalDocumentSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    category_id = serializers.PrimaryKeyRelatedField(queryset=Category.objects.all(), source='category', write_only=True)
    uploaded_by = ProfileSerializer(read_only=True)
    file_url = serializers.FileField(required=False)

    class Meta:
        model = LegalDocument
        fields = [
            'id', 'title_ar', 'title_en', 'category', 'category_id', 'description_ar',
            'file_url', 'file_type', 'author', 'publication_date', 'tags',
            'download_count', 'is_featured', 'uploaded_by', 'created_at'
        ]
        read_only_fields = ['id', 'download_count', 'uploaded_by', 'created_at']

    def validate_file_type(self, value):
        allowed_types = ['pdf', 'doc', 'docx', 'txt']
        if value.lower() not in allowed_types:
            raise serializers.ValidationError('نوع الملف غير مدعوم')
        return value

class DocumentAccessSerializer(serializers.ModelSerializer):
    student = StudentProfileSerializer(read_only=True)
    student_id = serializers.PrimaryKeyRelatedField(queryset=StudentProfile.objects.all(), source='student', write_only=True)
    document = LegalDocumentSerializer(read_only=True)
    document_id = serializers.PrimaryKeyRelatedField(queryset=LegalDocument.objects.all(), source='document', write_only=True)

    class Meta:
        model = DocumentAccess
        fields = ['id', 'student', 'student_id', 'document', 'document_id', 'accessed_at', 'download_count']
        read_only_fields = ['id', 'accessed_at', 'download_count'] 