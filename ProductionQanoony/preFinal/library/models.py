from django.db import models
from authentication.models import CustomUser
from students.models import StudentProfile

# Create your models here.


class Category(models.Model):
    name_ar = models.CharField(max_length=255)
    name_en = models.CharField(max_length=255)
    parent = models.ForeignKey(
        'self', null=True, blank=True, on_delete=models.SET_NULL, related_name='children')
    order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name_en


class LegalDocument(models.Model):
    title_ar = models.CharField(max_length=255)
    title_en = models.CharField(max_length=255)
    category = models.ForeignKey(
        Category, on_delete=models.CASCADE, related_name='documents')
    description_ar = models.TextField()
    file_url = models.FileField(upload_to='library_files/')
    file_type = models.CharField(max_length=10)  # pdf, doc, etc.
    author = models.CharField(max_length=255)
    publication_date = models.DateField()
    tags = models.CharField(max_length=255, blank=True)
    download_count = models.IntegerField(default=0)
    is_featured = models.BooleanField(default=False)
    uploaded_by = models.ForeignKey(
        CustomUser, on_delete=models.SET_NULL, null=True, related_name='uploaded_documents')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title_en


class DocumentAccess(models.Model):
    student = models.ForeignKey(
        StudentProfile, on_delete=models.CASCADE, related_name='document_accesses')
    document = models.ForeignKey(
        LegalDocument, on_delete=models.CASCADE, related_name='accesses')
    accessed_at = models.DateTimeField(auto_now_add=True)
    download_count = models.IntegerField(default=1)

    def __str__(self):
        return f"{self.student.user.email} - {self.document.title_en}"


class LibraryCategory(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()

    def __str__(self):
        return self.name


class LibraryItem(models.Model):
    title = models.CharField(max_length=255)
    description = models.TextField()
    category = models.ForeignKey(LibraryCategory, on_delete=models.CASCADE, related_name='items')
    item_type = models.CharField(max_length=50, choices=[('book', 'Book'), ('summary', 'Summary'), ('exam', 'Exam')])
    file_url = models.FileField(upload_to='library_files/')
    uploaded_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    is_approved = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title
