from django.shortcuts import render
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>ars<PERSON>, FormParser
from django.db.models import Q
from .models import Category, LegalDocument, DocumentAccess
from .serializers import CategorySerializer, LegalDocumentSerializer, DocumentAccessSerializer
from authentication.permissions import IsEmailVerified
from rest_framework.permissions import IsAdminUser
from django.utils import timezone
from ai_assistant.permissions import IsStaffOrActiveSubscriptionOrDenied

# Custom permission: Only instructors/admins can modify, students can only read
class IsInstructorOrAdmin(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user.is_authenticated and (getattr(request.user, 'is_instructor', False) or request.user.is_staff)

# Custom permission: Only students can download
class IsStudent(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and getattr(request.user, 'is_student', False)

class CategoryViewSet(viewsets.ModelViewSet):
    queryset = Category.objects.filter(is_active=True)
    serializer_class = CategorySerializer
    permission_classes = [permissions.AllowAny, IsStaffOrActiveSubscriptionOrDenied]
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['order', 'name_en']

class LegalDocumentViewSet(viewsets.ModelViewSet):
    queryset = LegalDocument.objects.all()
    serializer_class = LegalDocumentSerializer
    parser_classes = [MultiPartParser, FormParser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title_ar', 'title_en', 'author', 'tags']
    ordering_fields = ['created_at', 'download_count', 'publication_date']

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsEmailVerified(), IsInstructorOrAdmin(), IsStaffOrActiveSubscriptionOrDenied()]
        if self.action in ['download']:
            return [IsEmailVerified(), IsStudent(), IsStaffOrActiveSubscriptionOrDenied()]
        return [permissions.AllowAny(), IsStaffOrActiveSubscriptionOrDenied()]

    def perform_create(self, serializer):
        serializer.save(uploaded_by=self.request.user)

    @action(detail=False, methods=['get'], url_path='featured')
    def featured(self, request):
        docs = LegalDocument.objects.filter(is_featured=True)
        serializer = self.get_serializer(docs, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='search')
    def search(self, request):
        q = request.query_params.get('q', '')
        docs = LegalDocument.objects.filter(
            Q(title_ar__icontains=q) | Q(title_en__icontains=q) | Q(author__icontains=q) | Q(tags__icontains=q)
        )
        serializer = self.get_serializer(docs, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path='download', permission_classes=[IsEmailVerified, IsStudent, IsStaffOrActiveSubscriptionOrDenied])
    def download(self, request, pk=None):
        doc = self.get_object()
        # Track access
        student_profile = getattr(request.user, 'student_profile', None)
        if student_profile:
            access, created = DocumentAccess.objects.get_or_create(student=student_profile, document=doc)
            access.download_count += 1
            access.accessed_at = timezone.now()
            access.save()
        doc.download_count += 1
        doc.save()
        # Return file URL (Cloudinary serves the file)
        return Response({'file_url': doc.file_url.url, 'detail': 'تم تسجيل عملية التحميل'} )

class DocumentAccessViewSet(viewsets.ModelViewSet):
    queryset = DocumentAccess.objects.all()
    serializer_class = DocumentAccessSerializer
    permission_classes = [IsAdminUser, IsStaffOrActiveSubscriptionOrDenied]
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['accessed_at', 'download_count']
