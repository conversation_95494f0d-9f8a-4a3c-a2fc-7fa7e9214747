from django.urls import path
from .views import (
    RegistrationView, EmailVerificationView, LoginView, LogoutView,
    ProfileView, PasswordResetRequestView, PasswordResetView,
    StudentApplicationView, StudentApplicationAdminActionView, StudentApplicationAdminListView,
    InstructorListView, InstructorDetailListView, InstructorProfileView, InstructorStatsView, InstructorSubjectsView, InstructorSessionsView, InstructorStudentsView, InstructorStudentNotesView, InstructorProfileImageUploadView, InstructorApplicationView, InstructorApplicationAdminActionView, InstructorApplicationAdminListView,
    DashboardStatsView, RecentActivitiesView, SecureStudentApplicationImageView, InstructorCreateView,
    PaymentMethodsPublicView, PaymentMethodsAdminView, ContactInfoView
)
from rest_framework_simplejwt.views import TokenRefreshView

urlpatterns = [
    path('register/', RegistrationView.as_view(), name='register'),
    path('verify-email/', EmailVerificationView.as_view(), name='verify-email'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('profile/', ProfileView.as_view(), name='profile'),
    path('password-reset/request/', PasswordResetRequestView.as_view(), name='password-reset-request'),
    path('password-reset/confirm/', PasswordResetView.as_view(), name='password-reset-confirm'),
    path('dashboard/stats/', DashboardStatsView.as_view(), name='dashboard-stats'),
    path('dashboard/recent-activities/', RecentActivitiesView.as_view(), name='recent-activities'),
    path('student-application/', StudentApplicationView.as_view(), name='student-application'),
    path('student-application/<int:pk>/<str:action>/', StudentApplicationAdminActionView.as_view(), name='student-application-admin-action'),
    path('student-applications/', StudentApplicationAdminListView.as_view(), name='student-application-list'),
    path('instructors/', InstructorListView.as_view(), name='instructor-list'),
    path('instructors/detailed/', InstructorDetailListView.as_view(), name='instructor-detail-list'),
    path('instructors/profile/', InstructorProfileView.as_view(), name='instructor-profile'),
    path('instructors/profile/image/', InstructorProfileImageUploadView.as_view(), name='instructor-profile-image'),
    path('instructors/stats/', InstructorStatsView.as_view(), name='instructor-stats'),
    path('instructors/subjects/', InstructorSubjectsView.as_view(), name='instructor-subjects'),
    path('instructors/sessions/', InstructorSessionsView.as_view(), name='instructor-sessions'),
    path('instructors/students/', InstructorStudentsView.as_view(), name='instructor-students'),
    path('instructors/students/notes/', InstructorStudentNotesView.as_view(), name='instructor-student-notes-all'),
    path('instructors/students/<int:student_id>/notes/', InstructorStudentNotesView.as_view(), name='instructor-student-notes'),
    path('instructors/create/', InstructorCreateView.as_view(), name='instructor-create'),
    path('instructor-application/', InstructorApplicationView.as_view(), name='instructor-application'),
    path('instructor-application/<int:pk>/<str:action>/', InstructorApplicationAdminActionView.as_view(), name='instructor-application-admin-action'),
    path('instructor-applications/', InstructorApplicationAdminListView.as_view(), name='instructor-application-list'),
    path('secure/student-application-image/<int:application_id>/', SecureStudentApplicationImageView.as_view(), name='secure-student-application-image'),

    # Payment Methods URLs
    path('payment-methods/', PaymentMethodsPublicView.as_view(), name='payment-methods-public'),
    path('payment-methods/admin/', PaymentMethodsAdminView.as_view(), name='payment-methods-admin'),
    path('contact-info/', ContactInfoView.as_view(), name='contact-info'),

    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
]