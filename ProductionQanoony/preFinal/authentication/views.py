from django.shortcuts import render
from rest_framework import generics, status, permissions, views
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import login, logout
from django.utils import timezone
from django.shortcuts import get_object_or_404
from datetime import timed<PERSON><PERSON>
from .models import CustomUser, EmailVerification, StudentApplication, InstructorApplication, PaymentMethods
from .serializers import (
    StudentRegistrationSerializer, RegistrationSerializer, LoginSerializer, ProfileSerializer,
    EmailVerificationSerializer, PasswordResetRequestSerializer, PasswordResetSerializer, StudentApplicationSerializer, InstructorListSerializer, InstructorDetailSerializer, InstructorApplicationSerializer, InstructorCreateSerializer, InstructorProfileSerializer, InstructorSubjectSerializer, InstructorSessionSerializer, InstructorStudentSerializer, InstructorStudentNoteSerializer, PaymentMethodsSerializer, PaymentMethodsPublicSerializer
)
from django.core.mail import send_mail
from django.conf import settings
import uuid
from rest_framework.exceptions import PermissionDenied
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from .permissions import IsInstructor, IsInstructorOrAdmin, IsOwnerOrAdmin, IsStudentOrAdmin, IsAdminOnly, IsActiveUser
from config.error_handlers import handle_api_errors, create_error_response, ErrorMessages

# Create your views here.

# Registration View
class RegistrationView(generics.CreateAPIView):
    serializer_class = StudentRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    parser_classes = [MultiPartParser, FormParser]

    def create(self, request, *args, **kwargs):
        print("🚀 Student Registration Request received")
        print(f"📋 Data keys: {list(request.data.keys())}")

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            application = serializer.save()
            return Response({
                'message': 'تم إرسال طلب التحاق بنجاح! سيتم مراجعته من قبل الإدارة.',
                'application_id': application.id,
                'user_email': application.user.email
            }, status=status.HTTP_201_CREATED)
        else:
            print(f"❌ Validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Email Verification Vie
class EmailVerificationView(views.APIView):
    permission_classes = [permissions.AllowAny]
    serializer_class = EmailVerificationSerializer

    @handle_api_errors
    def post(self, request):
        try:
            serializer = self.serializer_class(data=request.data)
            if not serializer.is_valid():
                return create_error_response(
                    error_type='بيانات غير صحيحة',
                    detail='رمز التحقق مطلوب.',
                    code='invalid_token',
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            token = serializer.validated_data['token']

            try:
                verification = EmailVerification.objects.get(token=token, is_used=False)
                if verification.created_at < timezone.now() - timedelta(hours=24):
                    return create_error_response(
                        error_type='رمز منتهي الصلاحية',
                        detail='انتهت صلاحية رمز التحقق. يرجى طلب رمز جديد.',
                        code='token_expired',
                        status_code=status.HTTP_400_BAD_REQUEST
                    )

                user = verification.user
                user.email_verified = True
                user.save()

                verification.is_used = True
                verification.save()

                return Response({
                    'success': True,
                    'message': 'تم تأكيد البريد الإلكتروني بنجاح',
                    'user': {
                        'email': user.email,
                        'email_verified': True
                    }
                }, status=status.HTTP_200_OK)

            except EmailVerification.DoesNotExist:
                return create_error_response(
                    error_type='رمز تحقق غير صحيح',
                    detail='رمز التحقق غير صحيح أو تم استخدامه بالفعل.',
                    code='invalid_token',
                    status_code=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return create_error_response(
                error_type='خطأ في تأكيد البريد الإلكتروني',
                detail='حدث خطأ أثناء تأكيد البريد الإلكتروني. يرجى المحاولة مرة أخرى.',
                code='verification_error',
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

# Login View (JWT)
class LoginView(views.APIView):
    permission_classes = [permissions.AllowAny]
    serializer_class = LoginSerializer

    @handle_api_errors
    def post(self, request):
        try:
            serializer = self.serializer_class(data=request.data)
            if not serializer.is_valid():
                return create_error_response(
                    error_type='بيانات دخول غير صحيحة',
                    detail='يرجى التحقق من البريد الإلكتروني وكلمة المرور.',
                    code='invalid_credentials',
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            user = serializer.validated_data['user']
            if not user.email_verified:
                return create_error_response(
                    error_type='البريد الإلكتروني غير مؤكد',
                    detail=ErrorMessages.EMAIL_NOT_VERIFIED,
                    code='email_not_verified',
                    status_code=status.HTTP_403_FORBIDDEN
                )

            refresh = RefreshToken.for_user(user)
            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'is_staff': user.is_staff,
                    'is_instructor': getattr(user, 'is_instructor', False),
                    'is_student': getattr(user, 'is_student', False),
                }
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return create_error_response(
                error_type='خطأ في تسجيل الدخول',
                detail='حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.',
                code='login_error',
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

# Logout View (JWT Blacklist or session-based)
class LogoutView(views.APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        logout(request)
        return Response({'detail': 'تم تسجيل الخروج بنجاح'}, status=status.HTTP_200_OK)

# Profile View
class ProfileView(generics.RetrieveUpdateAPIView):
    serializer_class = ProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        user = self.request.user
        obj = user
        # Only allow access to own profile or admin
        if not (user.is_staff or obj == self.request.user):
            raise PermissionDenied('غير مصرح لك بالوصول إلى هذا الملف الشخصي')
        return obj

# Password Reset Request View
class PasswordResetRequestView(views.APIView):
    permission_classes = [permissions.AllowAny]
    serializer_class = PasswordResetRequestSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data['email']
        user = CustomUser.objects.filter(email=email).first()
        if user:
            token = str(uuid.uuid4())
            EmailVerification.objects.create(user=user, token=token)
            send_mail(
                subject='إعادة تعيين كلمة المرور',
                message=f'رمز إعادة التعيين الخاص بك: {token}',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
            )
        return Response({'detail': 'إذا كان البريد الإلكتروني موجودًا، سيتم إرسال رمز إعادة التعيين'}, status=status.HTTP_200_OK)

# Password Reset View
class PasswordResetView(views.APIView):
    permission_classes = [permissions.AllowAny]
    serializer_class = PasswordResetSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        token = serializer.validated_data['token']
        new_password = serializer.validated_data['new_password']
        verification = get_object_or_404(EmailVerification, token=token, is_used=False)
        user = verification.user
        user.set_password(new_password)
        user.save()
        verification.is_used = True
        verification.save()
        return Response({'detail': 'تم إعادة تعيين كلمة المرور بنجاح'}, status=status.HTTP_200_OK)

class StudentApplicationView(generics.CreateAPIView, generics.RetrieveAPIView):
    serializer_class = StudentApplicationSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def get_object(self):
        return get_object_or_404(StudentApplication, user=self.request.user)

    def get(self, request, *args, **kwargs):
        try:
            return self.retrieve(request, *args, **kwargs)
        except:
            return Response({'detail': 'لم تقم بتقديم طلب بعد'}, status=status.HTTP_404_NOT_FOUND)

    def perform_create(self, serializer):
        user = self.request.user
        print(f"🚀 Creating student application for user: {user.email}")
        print(f"📋 User selected_plan_id: {user.selected_plan_id}")

        # الحصول على الخطة المختارة من المستخدم
        from subscriptions.models import SubscriptionPlan
        selected_plan = None
        if user.selected_plan_id:
            try:
                selected_plan = SubscriptionPlan.objects.get(id=user.selected_plan_id)
                print(f"✅ Found selected plan: {selected_plan.name}")
            except SubscriptionPlan.DoesNotExist:
                print(f"❌ Selected plan {user.selected_plan_id} not found")
                pass

        if not selected_plan:
            # إذا لم توجد خطة محفوظة، استخدم الخطة الأساسية
            selected_plan = SubscriptionPlan.objects.filter(is_active=True).first()
            print(f"📦 Using default plan: {selected_plan.name if selected_plan else 'No plan found'}")

        print(f"💾 Saving application with plan: {selected_plan}")
        try:
            serializer.save(user=user, selected_plan=selected_plan)
            print(f"✅ Student application created successfully")
        except Exception as e:
            print(f"❌ Error creating student application: {e}")
            raise

from rest_framework.views import APIView
from django.db.models import Count, Q
from datetime import datetime, timedelta
from subscriptions.models import Subscription, SubscriptionPlan
from students.models import StudentProfile
from courses.models import Subject, Lecture, Quiz
from ai_assistant.models import AIConversation, AIUsageLimit

class DashboardStatsView(APIView):
    permission_classes = [permissions.IsAdminUser]

    def get(self, request):
        """Get comprehensive dashboard statistics"""
        try:
            # Basic counts
            total_students = CustomUser.objects.filter(is_student=True).count()
            total_instructors = CustomUser.objects.filter(is_instructor=True).count()
            total_subjects = Subject.objects.count()
            total_lectures = Lecture.objects.count()
            total_quizzes = Quiz.objects.count()

            # Student applications stats
            student_apps = StudentApplication.objects.aggregate(
                total=Count('id'),
                pending=Count('id', filter=Q(is_approved=False, rejection_reason='')),
                approved=Count('id', filter=Q(is_approved=True)),
                rejected=Count('id', filter=~Q(rejection_reason=''))
            )

            # Instructor applications stats
            instructor_apps = InstructorApplication.objects.aggregate(
                total=Count('id'),
                pending=Count('id', filter=Q(is_approved=False, rejection_reason='')),
                approved=Count('id', filter=Q(is_approved=True)),
                rejected=Count('id', filter=~Q(rejection_reason=''))
            )

            # Subscription stats
            active_subscriptions = Subscription.objects.filter(
                status='active',
                end_date__gte=timezone.now().date()
            ).count()

            expired_subscriptions = Subscription.objects.filter(
                status='expired'
            ).count()

            # AI usage stats
            ai_conversations = AIConversation.objects.count()
            active_ai_users = AIConversation.objects.values('student').distinct().count()

            # Recent activity (last 30 days)
            thirty_days_ago = timezone.now() - timedelta(days=30)
            recent_registrations = CustomUser.objects.filter(
                date_joined__gte=thirty_days_ago
            ).count()

            recent_subscriptions = Subscription.objects.filter(
                created_at__gte=thirty_days_ago
            ).count()

            # Monthly growth data (last 6 months)
            monthly_data = []
            for i in range(6):
                month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
                month_end = month_start + timedelta(days=30)

                month_students = CustomUser.objects.filter(
                    is_student=True,
                    date_joined__gte=month_start,
                    date_joined__lt=month_end
                ).count()

                month_subscriptions = Subscription.objects.filter(
                    created_at__gte=month_start,
                    created_at__lt=month_end
                ).count()

                monthly_data.append({
                    'month': month_start.strftime('%Y-%m'),
                    'students': month_students,
                    'subscriptions': month_subscriptions
                })

            return Response({
                'basic_stats': {
                    'total_students': total_students,
                    'total_instructors': total_instructors,
                    'total_subjects': total_subjects,
                    'total_lectures': total_lectures,
                    'total_quizzes': total_quizzes,
                    'active_subscriptions': active_subscriptions,
                    'expired_subscriptions': expired_subscriptions,
                    'ai_conversations': ai_conversations,
                    'active_ai_users': active_ai_users
                },
                'applications': {
                    'students': student_apps,
                    'instructors': instructor_apps
                },
                'recent_activity': {
                    'new_registrations': recent_registrations,
                    'new_subscriptions': recent_subscriptions
                },
                'monthly_growth': monthly_data
            })

        except Exception as e:
            return Response(
                {'error': 'Failed to fetch dashboard stats', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RecentActivitiesView(APIView):
    """
    Get recent activities for admin dashboard
    """
    permission_classes = [permissions.IsAdminUser]

    def get(self, request):
        """Get recent activities from various models"""
        try:
            activities = []

            # Import models here to avoid circular imports
            from subscriptions.models import Subscription
            from ai_assistant.models import AIConversation

            # Recent user registrations (last 7 days)
            week_ago = timezone.now() - timedelta(days=7)
            recent_users = CustomUser.objects.filter(
                date_joined__gte=week_ago
            ).order_by('-date_joined')[:5]

            for user in recent_users:
                activities.append({
                    'id': f'user_{user.id}',
                    'type': 'user_registration',
                    'title': 'تسجيل مستخدم جديد',
                    'description': f'انضم {user.first_name} {user.last_name} إلى المنصة',
                    'timestamp': user.date_joined.isoformat(),
                    'icon': 'FaUserPlus',
                    'color': 'bg-blue-500'
                })

            # Recent student applications
            recent_student_apps = StudentApplication.objects.filter(
                created_at__gte=week_ago
            ).select_related('user').order_by('-created_at')[:5]

            for app in recent_student_apps:
                status_text = 'مقبول' if app.is_approved else ('مرفوض' if app.rejection_reason else 'قيد المراجعة')
                activities.append({
                    'id': f'student_app_{app.id}',
                    'type': 'student_application',
                    'title': f'طلب التحاق طالب - {status_text}',
                    'description': f'طلب من {app.user.first_name} {app.user.last_name}',
                    'timestamp': app.created_at.isoformat(),
                    'icon': 'FaGraduationCap',
                    'color': 'bg-green-500' if app.is_approved else ('bg-red-500' if app.rejection_reason else 'bg-yellow-500')
                })

            # Recent instructor applications
            recent_instructor_apps = InstructorApplication.objects.filter(
                created_at__gte=week_ago
            ).select_related('user').order_by('-created_at')[:5]

            for app in recent_instructor_apps:
                status_text = 'مقبول' if app.is_approved else ('مرفوض' if app.rejection_reason else 'قيد المراجعة')
                activities.append({
                    'id': f'instructor_app_{app.id}',
                    'type': 'instructor_application',
                    'title': f'طلب التحاق مدرس - {status_text}',
                    'description': f'طلب من {app.user.first_name} {app.user.last_name}',
                    'timestamp': app.created_at.isoformat(),
                    'icon': 'FaChalkboardTeacher',
                    'color': 'bg-green-600' if app.is_approved else ('bg-red-600' if app.rejection_reason else 'bg-yellow-600')
                })

            # Recent subscriptions
            recent_subscriptions = Subscription.objects.filter(
                created_at__gte=week_ago
            ).select_related('student__user', 'plan').order_by('-created_at')[:5]

            for sub in recent_subscriptions:
                activities.append({
                    'id': f'subscription_{sub.id}',
                    'type': 'subscription',
                    'title': 'اشتراك جديد',
                    'description': f'اشتراك {sub.plan.name} للطالب {sub.student.user.first_name if sub.student else "غير محدد"}',
                    'timestamp': sub.created_at.isoformat(),
                    'icon': 'FaMoneyBillWave',
                    'color': 'bg-green-500'
                })

            # Recent AI conversations
            recent_ai_conversations = AIConversation.objects.filter(
                created_at__gte=week_ago
            ).select_related('student__user').order_by('-created_at')[:5]

            for conv in recent_ai_conversations:
                activities.append({
                    'id': f'ai_conv_{conv.id}',
                    'type': 'ai_conversation',
                    'title': 'محادثة ذكية جديدة',
                    'description': f'محادثة حول {conv.get_context_type_display()} من {conv.student.user.first_name if conv.student else "غير محدد"}',
                    'timestamp': conv.created_at.isoformat(),
                    'icon': 'FaRobot',
                    'color': 'bg-purple-500'
                })

            # Sort all activities by timestamp (newest first)
            activities.sort(key=lambda x: x['timestamp'], reverse=True)

            # Return only the most recent 20 activities
            return Response(activities[:20])

        except Exception as e:
            return Response(
                {'error': 'Failed to fetch recent activities', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class StudentApplicationAdminActionView(APIView):
    permission_classes = [permissions.IsAdminUser]

    def post(self, request, pk, action):
        app = get_object_or_404(StudentApplication, pk=pk)
        if action == 'approve':
            if app.is_approved:
                return Response({'detail': 'تمت الموافقة بالفعل'}, status=status.HTTP_400_BAD_REQUEST)

            # تحديث حالة الطلب
            app.is_approved = True
            app.approved_at = timezone.now()
            app.approved_by = request.user
            app.rejection_reason = ''
            app.save()

            # تفعيل حالة الطالب
            app.user.is_student = True
            app.user.save()

            # إنشاء الاشتراك حسب الخطة المختارة
            from subscriptions.models import Subscription
            from django.utils import timezone
            from datetime import timedelta

            plan = app.selected_plan
            start_date = timezone.now().date()
            end_date = start_date + timedelta(days=plan.duration_days)

            Subscription.objects.create(
                user=app.user,
                plan=plan,
                start_date=start_date,
                end_date=end_date,
                status='active',
                is_active=True
            )

            # إنشاء حدود استخدام AI للطالب
            from ai_assistant.models import AIUsageLimit
            try:
                student_profile = app.user.student_profile
                AIUsageLimit.objects.get_or_create(
                    student=student_profile,
                    defaults={
                        'daily_limit': 10,
                        'monthly_limit': 100,
                        'daily_used': 0,
                        'monthly_used': 0,
                        'last_reset_date': timezone.now().date()
                    }
                )
            except:
                pass  # في حالة عدم وجود student profile

            # حذف صورة الإيصال بعد الموافقة (أمان)
            if app.payment_screenshot:
                app.payment_screenshot.delete(save=False)
                app.payment_screenshot = None
                app.save()

            # إرسال إشعار ترحيبي للطالب الجديد
            from notifications.utils import send_welcome_notification
            send_welcome_notification(app.user)

            return Response({'status': 'تمت الموافقة على الطلب وتم تفعيل الاشتراك'})
        elif action == 'reject':
            if app.is_approved:
                return Response({'detail': 'لا يمكن رفض طلب تمت الموافقة عليه'}, status=status.HTTP_400_BAD_REQUEST)
            reason = request.data.get('rejection_reason', '')
            app.is_approved = False
            app.approved_at = None
            app.approved_by = request.user
            app.rejection_reason = reason
            # Delete screenshot after rejection
            if app.payment_screenshot:
                app.payment_screenshot.delete(save=False)
                app.payment_screenshot = None
            app.save()
            return Response({'status': 'تم رفض الطلب', 'reason': reason})
        return Response({'detail': 'إجراء غير صالح'}, status=status.HTTP_400_BAD_REQUEST)

# New: List all student applications for admins
class StudentApplicationAdminListView(generics.ListAPIView):
    serializer_class = StudentApplicationSerializer
    permission_classes = [permissions.IsAdminUser]

    def get_queryset(self):
        return StudentApplication.objects.select_related('user').order_by('-created_at')

# List of active instructors (used by frontend booking form)
class InstructorListView(generics.ListAPIView):
    queryset = CustomUser.objects.filter(is_instructor=True, is_active=True)
    serializer_class = InstructorListSerializer
    permission_classes = [permissions.AllowAny]


# Detailed list of instructors for admin management
class InstructorDetailListView(generics.ListAPIView):
    queryset = CustomUser.objects.filter(is_instructor=True, is_active=True).select_related('instructor_application')
    serializer_class = InstructorDetailSerializer
    permission_classes = [permissions.IsAdminUser]


# Instructor Profile View - للمدرسين لعرض وتعديل ملفاتهم الشخصية
class InstructorProfileView(generics.RetrieveUpdateAPIView):
    """عرض وتعديل بيانات المدرس الحالي"""
    serializer_class = InstructorProfileSerializer
    permission_classes = [IsInstructor]

    def get_object(self):
        """جلب بيانات المدرس الحالي"""
        return self.request.user

    def update(self, request, *args, **kwargs):
        """تحديث البيانات مع رسالة نجاح"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': 'تم تحديث البيانات بنجاح',
                'data': serializer.data
            }, status=status.HTTP_200_OK)

        return Response({
            'error': 'خطأ في البيانات',
            'detail': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


# Instructor Stats View - إحصائيات المدرس الشاملة
class InstructorStatsView(views.APIView):
    """إحصائيات المدرس الشاملة"""
    permission_classes = [IsInstructor]

    def get(self, request):
        user = request.user

        try:
            # حساب الإحصائيات الأساسية
            stats = {
                'total_courses': self.get_total_courses(user),
                'active_courses': self.get_active_courses(user),
                'total_students': self.get_total_students(user),
                'total_lectures': self.get_total_lectures(user),
                'total_sessions': self.get_total_sessions(user),
                'completed_sessions': self.get_completed_sessions(user),
                'pending_sessions': self.get_pending_sessions(user),
                'total_messages': self.get_total_messages(user),
                'unread_messages': self.get_unread_messages(user),
                'this_month_sessions': self.get_this_month_sessions(user),
                'this_month_students': self.get_this_month_students(user),
                'average_rating': self.get_average_rating(user),
                'total_ratings': self.get_total_ratings(user),
                'recent_activity': self.get_recent_activity(user),
                'monthly_stats': self.get_monthly_stats(user)
            }

            return Response(stats, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'خطأ في جلب الإحصائيات',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_total_courses(self, user):
        """إجمالي الكورسات"""
        try:
            from courses.models import Subject
            return Subject.objects.filter(is_active=True).count()
        except Exception:
            return 0

    def get_active_courses(self, user):
        """الكورسات النشطة"""
        try:
            from courses.models import Subject
            return Subject.objects.filter(is_active=True).count()
        except Exception:
            return 0

    def get_total_students(self, user):
        """إجمالي الطلاب المرتبطين بالمدرس"""
        try:
            from communication.models import ChatRoom
            return ChatRoom.objects.filter(instructor=user).values('student').distinct().count()
        except Exception:
            return 0

    def get_total_lectures(self, user):
        """إجمالي المحاضرات"""
        try:
            from courses.models import Lecture
            return Lecture.objects.filter(is_published=True).count()
        except Exception:
            return 0

    def get_total_sessions(self, user):
        """إجمالي الجلسات"""
        try:
            from scheduling.models import LiveSession
            return LiveSession.objects.filter(instructor=user).count()
        except Exception:
            return 0

    def get_completed_sessions(self, user):
        """الجلسات المكتملة"""
        try:
            from scheduling.models import LiveSession
            return LiveSession.objects.filter(instructor=user, status='completed').count()
        except Exception:
            return 0

    def get_pending_sessions(self, user):
        """الجلسات المعلقة"""
        try:
            from scheduling.models import LiveSession
            return LiveSession.objects.filter(instructor=user, status='scheduled').count()
        except Exception:
            return 0

    def get_total_messages(self, user):
        """إجمالي الرسائل المرسلة"""
        try:
            from communication.models import Message
            return Message.objects.filter(sender=user).count()
        except Exception:
            return 0

    def get_unread_messages(self, user):
        """الرسائل غير المقروءة الواردة للمدرس"""
        try:
            from communication.models import Message
            return Message.objects.filter(
                room__instructor=user,
                is_read=False
            ).exclude(sender=user).count()
        except Exception:
            return 0

    def get_this_month_sessions(self, user):
        """جلسات هذا الشهر"""
        try:
            from scheduling.models import LiveSession
            from django.utils import timezone
            from datetime import datetime

            now = timezone.now()
            start_of_month = datetime(now.year, now.month, 1, tzinfo=now.tzinfo)

            return LiveSession.objects.filter(
                instructor=user,
                scheduled_date__gte=start_of_month,
                scheduled_date__lte=now
            ).count()
        except Exception:
            return 0

    def get_this_month_students(self, user):
        """الطلاب الجدد هذا الشهر"""
        try:
            from communication.models import ChatRoom
            from django.utils import timezone
            from datetime import datetime

            now = timezone.now()
            start_of_month = datetime(now.year, now.month, 1, tzinfo=now.tzinfo)

            return ChatRoom.objects.filter(
                instructor=user,
                created_at__gte=start_of_month,
                created_at__lte=now
            ).count()
        except Exception:
            return 0

    def get_average_rating(self, user):
        """متوسط التقييم"""
        try:
            from scheduling.models import SessionBooking
            from django.db.models import Avg

            avg_rating = SessionBooking.objects.filter(
                session__instructor=user,
                feedback_rating__isnull=False
            ).aggregate(avg_rating=Avg('feedback_rating'))

            return round(avg_rating['avg_rating'] or 0, 1)
        except Exception:
            return 0.0

    def get_total_ratings(self, user):
        """إجمالي التقييمات"""
        try:
            from scheduling.models import SessionBooking

            return SessionBooking.objects.filter(
                session__instructor=user,
                feedback_rating__isnull=False
            ).count()
        except Exception:
            return 0

    def get_recent_activity(self, user):
        """النشاط الحديث"""
        activities = []

        try:
            # آخر الجلسات المكتملة
            from scheduling.models import LiveSession
            recent_sessions = LiveSession.objects.filter(
                instructor=user,
                status='completed'
            ).order_by('-scheduled_date')[:3]

            for session in recent_sessions:
                activities.append({
                    'id': f'session_{session.id}',
                    'type': 'session_completed',
                    'title': 'جلسة مكتملة',
                    'description': f'جلسة {session.title_ar}',
                    'timestamp': session.scheduled_date.isoformat(),
                    'icon': 'session',
                    'color': 'green'
                })

            # آخر الرسائل المرسلة
            from communication.models import Message
            recent_messages = Message.objects.filter(
                sender=user
            ).order_by('-timestamp')[:3]

            for message in recent_messages:
                activities.append({
                    'id': f'message_{message.id}',
                    'type': 'new_message',
                    'title': 'رسالة مرسلة',
                    'description': f'رسالة في {message.room.name}',
                    'timestamp': message.timestamp.isoformat(),
                    'icon': 'message',
                    'color': 'blue'
                })

            # ترتيب حسب التاريخ وأخذ آخر 5
            activities.sort(key=lambda x: x['timestamp'], reverse=True)
            return activities[:5]

        except Exception as e:
            return []

    def get_monthly_stats(self, user):
        """إحصائيات شهرية للـ charts"""
        from django.utils import timezone
        from datetime import datetime, timedelta
        from scheduling.models import LiveSession
        from communication.models import ChatRoom

        try:
            now = timezone.now()
            months_data = []
            sessions_trend = []
            students_trend = []

            # آخر 6 شهور
            for i in range(5, -1, -1):
                month_start = datetime(now.year, now.month - i, 1, tzinfo=now.tzinfo)
                if month_start.month <= 0:
                    month_start = month_start.replace(year=month_start.year - 1, month=month_start.month + 12)

                if i == 0:
                    month_end = now
                else:
                    next_month = month_start.month + 1
                    next_year = month_start.year
                    if next_month > 12:
                        next_month = 1
                        next_year += 1
                    month_end = datetime(next_year, next_month, 1, tzinfo=now.tzinfo) - timedelta(days=1)

                # عدد الجلسات في هذا الشهر
                sessions_count = LiveSession.objects.filter(
                    instructor=user,
                    scheduled_date__gte=month_start,
                    scheduled_date__lte=month_end
                ).count()

                # عدد الطلاب الجدد في هذا الشهر
                students_count = ChatRoom.objects.filter(
                    instructor=user,
                    created_at__gte=month_start,
                    created_at__lte=month_end
                ).count()

                sessions_trend.append(sessions_count)
                students_trend.append(students_count)

                # اسم الشهر بالعربية
                month_names = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ]
                months_data.append(month_names[month_start.month - 1])

            return {
                'sessions_trend': sessions_trend,
                'students_trend': students_trend,
                'months': months_data
            }

        except Exception as e:
            return {
                'sessions_trend': [0, 0, 0, 0, 0, 0],
                'students_trend': [0, 0, 0, 0, 0, 0],
                'months': ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']
            }


# Instructor Subjects View - المواد التي يدرسها المدرس
class InstructorSubjectsView(generics.ListAPIView):
    """عرض قائمة المواد التي يدرسها المدرس"""
    serializer_class = InstructorSubjectSerializer
    permission_classes = [IsInstructor]

    def get_queryset(self):
        user = self.request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً للوصول إلى هذه المواد')

        from courses.models import Subject, PrivateTutorSession
        from scheduling.models import LiveSession
        from communication.models import ChatRoom
        from django.db.models import Q

        # البحث في المواد التي يدرسها المدرس من خلال:
        # 1. الجلسات الخاصة
        # 2. الجلسات المباشرة
        # 3. المحادثات

        subject_ids = set()

        # المواد من الجلسات الخاصة
        private_subjects = PrivateTutorSession.objects.filter(
            instructor=user
        ).values_list('subject_id', flat=True).distinct()
        subject_ids.update(private_subjects)

        # المواد من الجلسات المباشرة (إذا كان لها semester مرتبط)
        try:
            live_sessions = LiveSession.objects.filter(
                instructor=user,
                semester__isnull=False
            ).values_list('semester__subjects', flat=True).distinct()
            subject_ids.update(live_sessions)
        except Exception:
            # تجاهل الأخطاء في الجلسات المباشرة
            pass

        # المواد من المحادثات
        chat_subjects = ChatRoom.objects.filter(
            instructor=user,
            subject__isnull=False
        ).values_list('subject_id', flat=True).distinct()
        subject_ids.update(chat_subjects)

        # إذا لم توجد مواد، أرجع جميع المواد النشطة (للمدرسين الجدد)
        if not subject_ids:
            queryset = Subject.objects.filter(is_active=True)
        else:
            queryset = Subject.objects.filter(id__in=subject_ids, is_active=True)

        # دعم البحث مع معالجة Arabic encoding
        search = self.request.query_params.get('search', None)
        if search:
            try:
                # محاولة decode إذا كان النص مُرمز
                import urllib.parse
                decoded_search = urllib.parse.unquote(search)
                search_term = decoded_search if decoded_search != search else search
            except:
                search_term = search

            queryset = queryset.filter(
                Q(name_ar__icontains=search_term) |
                Q(name_en__icontains=search_term) |
                Q(code__icontains=search_term) |
                Q(description_ar__icontains=search_term)
            )

        # دعم الفلترة حسب الحالة
        status = self.request.query_params.get('status', None)
        if status == 'published':
            queryset = queryset.filter(semester__is_published=True)
        elif status == 'draft':
            queryset = queryset.filter(semester__is_published=False)

        # دعم الترتيب
        ordering = self.request.query_params.get('ordering', 'name_ar')
        if ordering in ['name_ar', '-name_ar', 'name_en', '-name_en', 'code', '-code']:
            queryset = queryset.order_by(ordering)
        else:
            queryset = queryset.order_by('name_ar')

        return queryset.select_related('semester', 'academic_year').distinct()

    def get_serializer_context(self):
        """إضافة المدرس للـ context"""
        context = super().get_serializer_context()
        context['instructor'] = self.request.user
        return context

    def list(self, request, *args, **kwargs):
        """تخصيص الاستجابة"""
        try:
            queryset = self.get_queryset()
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)

            return Response({
                'count': queryset.count(),
                'results': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'خطأ في جلب المواد',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Instructor Profile Image Upload View
class InstructorProfileImageUploadView(APIView):
    """رفع الصورة الشخصية للمدرس"""
    permission_classes = [IsInstructor]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        """رفع صورة شخصية جديدة للمدرس"""
        user = request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً لرفع الصورة الشخصية')

        try:
            # التحقق من وجود الملف
            if 'profile_image' not in request.FILES:
                return Response({
                    'error': 'ملف مطلوب',
                    'detail': 'يرجى اختيار صورة للرفع'
                }, status=status.HTTP_400_BAD_REQUEST)

            profile_image = request.FILES['profile_image']

            # استخدام validation functions الموجودة
            from subscriptions.utils import validate_image_file, compress_image

            # التحقق من صحة الصورة
            is_valid, error_message = validate_image_file(profile_image, max_size_mb=5)
            if not is_valid:
                return Response({
                    'error': 'صورة غير صحيحة',
                    'detail': error_message
                }, status=status.HTTP_400_BAD_REQUEST)

            # التحقق من أبعاد الصورة
            from PIL import Image
            try:
                image = Image.open(profile_image)
                width, height = image.size
                if width > 2000 or height > 2000:
                    return Response({
                        'error': 'أبعاد الصورة كبيرة',
                        'detail': 'الحد الأقصى للأبعاد 2000x2000 بكسل'
                    }, status=status.HTTP_400_BAD_REQUEST)
                profile_image.seek(0)  # إعادة تعيين مؤشر الملف
            except Exception as e:
                return Response({
                    'error': 'خطأ في قراءة الصورة',
                    'detail': 'لا يمكن قراءة الصورة المرفوعة'
                }, status=status.HTTP_400_BAD_REQUEST)

            # حذف الصورة القديمة إذا كانت موجودة
            if user.profile_image:
                try:
                    import os
                    if os.path.isfile(user.profile_image.path):
                        os.remove(user.profile_image.path)
                except Exception:
                    pass  # تجاهل الأخطاء في حذف الملف القديم

            # ضغط الصورة
            compressed_image = compress_image(
                profile_image,
                max_size=(1000, 1000),
                quality=85,
                format='JPEG'
            )

            # إنشاء اسم ملف آمن
            import uuid
            file_extension = 'jpg'
            new_filename = f"instructor_{user.id}_{uuid.uuid4().hex[:8]}.{file_extension}"
            compressed_image.name = new_filename

            # حفظ الصورة الجديدة
            user.profile_image = compressed_image
            user.save()

            # إرجاع URL الصورة الجديدة
            image_url = request.build_absolute_uri(user.profile_image.url) if user.profile_image else None

            return Response({
                'message': 'تم رفع الصورة بنجاح',
                'profile_image': image_url
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'خطأ في رفع الصورة',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request):
        """حذف الصورة الشخصية للمدرس"""
        user = request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً لحذف الصورة الشخصية')

        try:
            if user.profile_image:
                # حذف الملف من النظام
                try:
                    import os
                    if os.path.isfile(user.profile_image.path):
                        os.remove(user.profile_image.path)
                except Exception:
                    pass

                # حذف المرجع من قاعدة البيانات
                user.profile_image = None
                user.save()

                return Response({
                    'message': 'تم حذف الصورة بنجاح'
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'لا توجد صورة',
                    'detail': 'لا توجد صورة شخصية لحذفها'
                }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            return Response({
                'error': 'خطأ في حذف الصورة',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Instructor Sessions Management View
class InstructorSessionsView(generics.ListAPIView):
    """عرض وإدارة جلسات المدرس (Live + Private Sessions)"""
    serializer_class = InstructorSessionSerializer
    permission_classes = [IsInstructor]

    def get_queryset(self):
        user = self.request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً للوصول إلى الجلسات')

        # جمع الجلسات من النوعين
        from scheduling.models import LiveSession
        from courses.models import PrivateTutorSession
        from django.db.models import Q
        from itertools import chain

        # الجلسات المباشرة
        live_sessions = LiveSession.objects.filter(instructor=user).select_related('semester')

        # الجلسات الخاصة
        private_sessions = PrivateTutorSession.objects.filter(instructor=user).select_related('student__user', 'subject')

        # دمج النتائج
        all_sessions = list(chain(live_sessions, private_sessions))

        # الفلترة
        session_type = self.request.query_params.get('type', None)
        if session_type == 'live':
            all_sessions = [s for s in all_sessions if hasattr(s, 'title_ar')]
        elif session_type == 'private':
            all_sessions = [s for s in all_sessions if hasattr(s, 'scheduled_at')]

        # فلترة حسب الحالة
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            all_sessions = [s for s in all_sessions if s.status == status_filter]

        # فلترة حسب التاريخ
        date_from = self.request.query_params.get('date_from', None)
        date_to = self.request.query_params.get('date_to', None)

        if date_from:
            try:
                from datetime import datetime
                date_from_obj = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
                all_sessions = [s for s in all_sessions if
                              (hasattr(s, 'scheduled_date') and s.scheduled_date >= date_from_obj) or
                              (hasattr(s, 'scheduled_at') and s.scheduled_at >= date_from_obj)]
            except:
                pass

        if date_to:
            try:
                from datetime import datetime
                date_to_obj = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
                all_sessions = [s for s in all_sessions if
                              (hasattr(s, 'scheduled_date') and s.scheduled_date <= date_to_obj) or
                              (hasattr(s, 'scheduled_at') and s.scheduled_at <= date_to_obj)]
            except:
                pass

        # البحث
        search = self.request.query_params.get('search', None)
        if search:
            filtered_sessions = []
            for session in all_sessions:
                if hasattr(session, 'title_ar'):  # LiveSession
                    if search.lower() in session.title_ar.lower() or search.lower() in session.description_ar.lower():
                        filtered_sessions.append(session)
                else:  # PrivateTutorSession
                    if (search.lower() in session.subject.name_ar.lower() or
                        search.lower() in session.student.user.get_full_name().lower()):
                        filtered_sessions.append(session)
            all_sessions = filtered_sessions

        # الترتيب
        ordering = self.request.query_params.get('ordering', '-scheduled_date')
        if ordering in ['scheduled_date', '-scheduled_date', 'status', '-status']:
            reverse_order = ordering.startswith('-')
            field = ordering.lstrip('-')

            if field == 'scheduled_date':
                all_sessions.sort(
                    key=lambda x: x.scheduled_date if hasattr(x, 'scheduled_date') else x.scheduled_at,
                    reverse=reverse_order
                )
            elif field == 'status':
                all_sessions.sort(key=lambda x: x.status, reverse=reverse_order)

        return all_sessions

    def list(self, request, *args, **kwargs):
        """تخصيص الاستجابة مع الإحصائيات"""
        try:
            queryset = self.get_queryset()

            # حساب الإحصائيات
            stats = self.calculate_stats(queryset)

            # Pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                response = self.get_paginated_response(serializer.data)
                response.data['stats'] = stats
                return response

            serializer = self.get_serializer(queryset, many=True)

            return Response({
                'count': len(queryset),
                'sessions': serializer.data,
                'stats': stats
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'خطأ في جلب الجلسات',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def calculate_stats(self, sessions):
        """حساب إحصائيات الجلسات"""
        try:
            total_sessions = len(sessions)
            live_sessions = len([s for s in sessions if hasattr(s, 'title_ar')])
            private_sessions = len([s for s in sessions if hasattr(s, 'scheduled_at')])

            # إحصائيات الحالة
            scheduled = len([s for s in sessions if s.status == 'scheduled'])
            ongoing = len([s for s in sessions if s.status == 'ongoing'])
            completed = len([s for s in sessions if s.status == 'completed'])
            cancelled = len([s for s in sessions if s.status == 'cancelled'])
            pending = len([s for s in sessions if s.status == 'pending'])
            approved = len([s for s in sessions if s.status == 'approved'])

            # إجمالي المشاركين (للجلسات المباشرة فقط)
            total_participants = sum([
                s.bookings.count() for s in sessions
                if hasattr(s, 'title_ar') and hasattr(s, 'bookings')
            ])

            return {
                'total_sessions': total_sessions,
                'live_sessions': live_sessions,
                'private_sessions': private_sessions,
                'by_status': {
                    'scheduled': scheduled,
                    'ongoing': ongoing,
                    'completed': completed,
                    'cancelled': cancelled,
                    'pending': pending,
                    'approved': approved
                },
                'total_participants': total_participants
            }
        except Exception:
            return {
                'total_sessions': 0,
                'live_sessions': 0,
                'private_sessions': 0,
                'by_status': {
                    'scheduled': 0,
                    'ongoing': 0,
                    'completed': 0,
                    'cancelled': 0,
                    'pending': 0,
                    'approved': 0
                },
                'total_participants': 0
            }


# Instructor Students Management View
class InstructorStudentsView(generics.ListAPIView):
    """عرض وإدارة الطلاب المرتبطين بالمدرس"""
    serializer_class = InstructorStudentSerializer
    permission_classes = [IsInstructor]

    def get_queryset(self):
        user = self.request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً للوصول إلى الطلاب')

        from students.models import StudentProfile
        from communication.models import ChatRoom
        from courses.models import PrivateTutorSession
        from scheduling.models import SessionBooking
        from django.db.models import Q

        # جمع IDs الطلاب المرتبطين بالمدرس
        student_ids = set()

        # الطلاب من المحادثات
        chat_students = ChatRoom.objects.filter(
            instructor=user,
            student__isnull=False
        ).values_list('student_id', flat=True)
        student_ids.update(chat_students)

        # الطلاب من الجلسات الخاصة
        private_students = PrivateTutorSession.objects.filter(
            instructor=user
        ).values_list('student_id', flat=True)
        student_ids.update(private_students)

        # الطلاب من الجلسات المباشرة
        live_students = SessionBooking.objects.filter(
            session__instructor=user
        ).values_list('student_id', flat=True)
        student_ids.update(live_students)

        # جلب الطلاب
        queryset = StudentProfile.objects.filter(
            id__in=student_ids
        ).select_related('user', 'university', 'academic_year')

        # الفلترة والبحث
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(user__first_name__icontains=search) |
                Q(user__last_name__icontains=search) |
                Q(user__email__icontains=search) |
                Q(student_id__icontains=search)
            )

        # فلترة حسب النشاط
        activity = self.request.query_params.get('activity', None)
        if activity == 'active':
            # الطلاب النشطين (لديهم نشاط في آخر 30 يوم)
            from django.utils import timezone
            from datetime import timedelta
            thirty_days_ago = timezone.now() - timedelta(days=30)

            active_student_ids = set()

            # من الرسائل
            from communication.models import Message
            recent_message_students = Message.objects.filter(
                room__instructor=user,
                timestamp__gte=thirty_days_ago,
                sender__student_profile__isnull=False
            ).values_list('sender__student_profile__id', flat=True)
            active_student_ids.update(recent_message_students)

            # من الجلسات
            recent_session_students = PrivateTutorSession.objects.filter(
                instructor=user,
                updated_at__gte=thirty_days_ago
            ).values_list('student_id', flat=True)
            active_student_ids.update(recent_session_students)

            queryset = queryset.filter(id__in=active_student_ids)

        # فلترة حسب نوع الاتصال
        connection_type = self.request.query_params.get('connection_type', None)
        if connection_type == 'chat':
            chat_student_ids = ChatRoom.objects.filter(
                instructor=user
            ).values_list('student_id', flat=True)
            queryset = queryset.filter(id__in=chat_student_ids)
        elif connection_type == 'private_session':
            private_student_ids = PrivateTutorSession.objects.filter(
                instructor=user
            ).values_list('student_id', flat=True)
            queryset = queryset.filter(id__in=private_student_ids)
        elif connection_type == 'live_session':
            live_student_ids = SessionBooking.objects.filter(
                session__instructor=user
            ).values_list('student_id', flat=True)
            queryset = queryset.filter(id__in=live_student_ids)

        # الترتيب
        ordering = self.request.query_params.get('ordering', '-last_activity')
        if ordering == 'name':
            queryset = queryset.order_by('user__first_name', 'user__last_name')
        elif ordering == '-name':
            queryset = queryset.order_by('-user__first_name', '-user__last_name')
        elif ordering == 'created_at':
            queryset = queryset.order_by('created_at')
        elif ordering == '-created_at':
            queryset = queryset.order_by('-created_at')
        else:
            # الترتيب الافتراضي حسب آخر نشاط (سيتم في serializer)
            queryset = queryset.order_by('-created_at')

        return queryset

    def get_serializer_context(self):
        """إضافة المدرس للـ context"""
        context = super().get_serializer_context()
        context['instructor'] = self.request.user
        return context

    def list(self, request, *args, **kwargs):
        """تخصيص الاستجابة مع الإحصائيات"""
        try:
            queryset = self.get_queryset()

            # حساب الإحصائيات
            stats = self.calculate_stats(request.user, queryset)

            # Pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                response = self.get_paginated_response(serializer.data)
                response.data['stats'] = stats
                return response

            serializer = self.get_serializer(queryset, many=True)

            return Response({
                'count': len(queryset),
                'students': serializer.data,
                'stats': stats
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'خطأ في جلب الطلاب',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def calculate_stats(self, instructor, students):
        """حساب إحصائيات الطلاب"""
        try:
            from communication.models import ChatRoom
            from courses.models import PrivateTutorSession
            from scheduling.models import SessionBooking
            from django.utils import timezone
            from datetime import timedelta

            total_students = students.count()

            # الطلاب النشطين (آخر 30 يوم)
            thirty_days_ago = timezone.now() - timedelta(days=30)
            active_students = 0

            for student in students:
                # فحص النشاط
                has_recent_activity = (
                    ChatRoom.objects.filter(
                        instructor=instructor,
                        student=student,
                        last_message_at__gte=thirty_days_ago
                    ).exists() or
                    PrivateTutorSession.objects.filter(
                        instructor=instructor,
                        student=student,
                        updated_at__gte=thirty_days_ago
                    ).exists()
                )
                if has_recent_activity:
                    active_students += 1

            # الطلاب الجدد هذا الشهر
            start_of_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            new_this_month = 0

            for student in students:
                # أول اتصال مع المدرس
                first_chat = ChatRoom.objects.filter(
                    instructor=instructor,
                    student=student
                ).order_by('created_at').first()

                first_session = PrivateTutorSession.objects.filter(
                    instructor=instructor,
                    student=student
                ).order_by('created_at').first()

                first_contact = None
                if first_chat and first_session:
                    first_contact = min(first_chat.created_at, first_session.created_at)
                elif first_chat:
                    first_contact = first_chat.created_at
                elif first_session:
                    first_contact = first_session.created_at

                if first_contact and first_contact >= start_of_month:
                    new_this_month += 1

            # توزيع حسب السنة الأكاديمية
            by_academic_year = {}
            for student in students:
                year_name = student.academic_year.year_name_ar if student.academic_year else 'غير محدد'
                by_academic_year[year_name] = by_academic_year.get(year_name, 0) + 1

            # توزيع حسب نوع الاتصال
            chat_only = 0
            sessions_only = 0
            both = 0

            for student in students:
                has_chat = ChatRoom.objects.filter(instructor=instructor, student=student).exists()
                has_session = PrivateTutorSession.objects.filter(instructor=instructor, student=student).exists()

                if has_chat and has_session:
                    both += 1
                elif has_chat:
                    chat_only += 1
                elif has_session:
                    sessions_only += 1

            return {
                'total_students': total_students,
                'active_students': active_students,
                'new_this_month': new_this_month,
                'by_academic_year': by_academic_year,
                'by_connection_type': {
                    'chat_only': chat_only,
                    'sessions_only': sessions_only,
                    'both': both
                }
            }
        except Exception:
            return {
                'total_students': 0,
                'active_students': 0,
                'new_this_month': 0,
                'by_academic_year': {},
                'by_connection_type': {
                    'chat_only': 0,
                    'sessions_only': 0,
                    'both': 0
                }
            }


# Instructor Student Notes Management View
class InstructorStudentNotesView(APIView):
    """إدارة ملاحظات المدرس عن الطلاب"""
    permission_classes = [IsInstructor]

    def get(self, request, student_id=None):
        """جلب ملاحظات المدرس عن طالب محدد أو جميع الملاحظات"""
        user = request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً للوصول إلى الملاحظات')

        try:
            from .models import InstructorStudentNote
            from students.models import StudentProfile

            if student_id:
                # جلب ملاحظة طالب محدد
                try:
                    student = StudentProfile.objects.get(id=student_id)
                    note = InstructorStudentNote.objects.filter(
                        instructor=user,
                        student=student
                    ).first()

                    if note:
                        return Response({
                            'id': note.id,
                            'student_id': note.student.id,
                            'note': note.note,
                            'created_at': note.created_at,
                            'updated_at': note.updated_at
                        }, status=status.HTTP_200_OK)
                    else:
                        return Response({
                            'id': None,
                            'student_id': student_id,
                            'note': '',
                            'created_at': None,
                            'updated_at': None
                        }, status=status.HTTP_200_OK)

                except StudentProfile.DoesNotExist:
                    return Response({
                        'error': 'الطالب غير موجود'
                    }, status=status.HTTP_404_NOT_FOUND)
            else:
                # جلب جميع الملاحظات
                notes = InstructorStudentNote.objects.filter(
                    instructor=user
                ).select_related('student__user')

                notes_data = []
                for note in notes:
                    notes_data.append({
                        'id': note.id,
                        'student_id': note.student.id,
                        'student_name': note.student.user.get_full_name(),
                        'note': note.note,
                        'created_at': note.created_at,
                        'updated_at': note.updated_at
                    })

                return Response({
                    'count': len(notes_data),
                    'notes': notes_data
                }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'خطأ في جلب الملاحظات',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request, student_id):
        """إضافة أو تحديث ملاحظة عن طالب"""
        user = request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً لإضافة الملاحظات')

        try:
            from .models import InstructorStudentNote
            from students.models import StudentProfile

            # التحقق من وجود الطالب
            try:
                student = StudentProfile.objects.get(id=student_id)
            except StudentProfile.DoesNotExist:
                return Response({
                    'error': 'الطالب غير موجود'
                }, status=status.HTTP_404_NOT_FOUND)

            # التحقق من البيانات
            note_text = request.data.get('note', '').strip()
            if not note_text or len(note_text) < 5:
                return Response({
                    'error': 'يجب أن تكون الملاحظة أكثر من 5 أحرف'
                }, status=status.HTTP_400_BAD_REQUEST)

            if len(note_text) > 1000:
                return Response({
                    'error': 'الملاحظة طويلة جداً (الحد الأقصى 1000 حرف)'
                }, status=status.HTTP_400_BAD_REQUEST)

            # إنشاء أو تحديث الملاحظة
            note, created = InstructorStudentNote.objects.update_or_create(
                instructor=user,
                student=student,
                defaults={'note': note_text}
            )

            return Response({
                'id': note.id,
                'student_id': note.student.id,
                'note': note.note,
                'created_at': note.created_at,
                'updated_at': note.updated_at,
                'message': 'تم حفظ الملاحظة بنجاح' if not created else 'تم إنشاء الملاحظة بنجاح'
            }, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'خطأ في حفظ الملاحظة',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, student_id):
        """حذف ملاحظة عن طالب"""
        user = request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً لحذف الملاحظات')

        try:
            from .models import InstructorStudentNote
            from students.models import StudentProfile

            try:
                student = StudentProfile.objects.get(id=student_id)
                note = InstructorStudentNote.objects.get(
                    instructor=user,
                    student=student
                )
                note.delete()

                return Response({
                    'message': 'تم حذف الملاحظة بنجاح'
                }, status=status.HTTP_200_OK)

            except (StudentProfile.DoesNotExist, InstructorStudentNote.DoesNotExist):
                return Response({
                    'error': 'الملاحظة غير موجودة'
                }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            return Response({
                'error': 'خطأ في حذف الملاحظة',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Instructor Application View
class InstructorApplicationView(views.APIView):
    permission_classes = [AllowAny]
    parser_classes = [MultiPartParser, FormParser]

    def get(self, request):
        return Response({'detail': 'يمكنك التقديم كمدرس من خلال هذا النموذج.'}, status=status.HTTP_200_OK)

    def post(self, request):
        data = request.data.copy()
        print('DATA RECEIVED:', data)
        email = data.get('email')
        first_name = data.get('first_name')
        last_name = data.get('last_name')
        phone_number = data.get('phone_number')
        if not email or not first_name or not last_name:
            return Response({'detail': 'يجب إدخال الاسم والبريد الإلكتروني.'}, status=status.HTTP_400_BAD_REQUEST)
        # تحقق إذا كان هناك طلب سابق بنفس البريد
        if InstructorApplication.objects.filter(email=email).exists():
            return Response({'detail': 'لقد قمت بتقديم طلب بالفعل'}, status=status.HTTP_400_BAD_REQUEST)
        # احفظ الطلب بدون user
        serializer = InstructorApplicationSerializer(data={
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'phone_number': phone_number,
            'bio': data.get('bio', ''),
            'specialty': data.get('specialty', ''),
            'cv': data.get('cv'),
        }, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Admin approve/reject instructor application
class InstructorApplicationAdminActionView(APIView):
    permission_classes = [permissions.IsAdminUser]

    def post(self, request, pk, action):
        app = get_object_or_404(InstructorApplication, pk=pk)
        if action == 'approve':
            if app.is_approved:
                return Response({'detail': 'تمت الموافقة بالفعل'}, status=status.HTTP_400_BAD_REQUEST)
            # إذا لم يكن هناك user مرتبط بالطلب، أنشئه الآن
            if not app.user:
                from authentication.models import CustomUser
                password = 'instructor123'  # كلمة سر افتراضية
                user = CustomUser.objects.create_user(
                    email=app.email,
                    first_name=app.first_name,
                    last_name=app.last_name,
                    phone_number=app.phone_number,
                    is_instructor=True,
                    is_staff=True,
                    is_active=True
                )
                user.set_password(password)
                user.save()
                app.user = user
            app.is_approved = True
            app.approved_at = timezone.now()
            app.approved_by = request.user
            app.rejection_reason = ''
            app.save()
            # يمكن إرسال إيميل بالبيانات هنا
            return Response({'status': 'تمت الموافقة على الطلب'})
        elif action == 'reject':
            if app.is_approved:
                return Response({'detail': 'لا يمكن رفض طلب تمت الموافقة عليه'}, status=status.HTTP_400_BAD_REQUEST)
            reason = request.data.get('rejection_reason', '')
            app.is_approved = False
            app.approved_at = None
            app.approved_by = request.user
            app.rejection_reason = reason
            app.save()
            return Response({'status': 'تم رفض الطلب', 'reason': reason})
        return Response({'detail': 'إجراء غير صالح'}, status=status.HTTP_400_BAD_REQUEST)

# Admin: List all instructor applications
class InstructorApplicationAdminListView(generics.ListAPIView):
    serializer_class = InstructorApplicationSerializer
    permission_classes = [permissions.IsAdminUser]

    def get_queryset(self):
        return InstructorApplication.objects.select_related('user').order_by('-created_at')


class InstructorCreateView(generics.CreateAPIView):
    """
    Admin endpoint to create instructor directly
    Best Practice: Comprehensive error handling and logging
    """
    serializer_class = InstructorCreateSerializer
    permission_classes = [permissions.IsAdminUser]

    def create(self, request, *args, **kwargs):
        """إنشاء مدرس جديد مع معالجة شاملة للأخطاء"""
        print(f"🚀 Instructor creation request from admin: {request.user.email}")
        print(f"📋 Request data: {list(request.data.keys())}")

        try:
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                result = serializer.save()

                # استخراج النتائج
                user = result['user']
                password = result['password']
                email_sent = result['email_sent']

                # تسجيل العملية في النظام
                print(f"✅ Instructor created successfully: {user.email}")

                # إرجاع استجابة شاملة
                response_data = {
                    'success': True,
                    'message': 'تم إنشاء حساب المدرس بنجاح',
                    'instructor': {
                        'id': user.id,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'phone_number': user.phone_number,
                        'is_instructor': user.is_instructor,
                        'is_staff': user.is_staff,
                        'is_active': user.is_active,
                        'date_joined': user.date_joined
                    },
                    'credentials': {
                        'email': user.email,
                        'password': password,
                        'login_url': f"{getattr(settings, 'FRONTEND_URL', 'http://localhost:3001')}/login"
                    },
                    'notifications': {
                        'email_sent': email_sent,
                        'email_address': user.email if email_sent else None
                    },
                    'next_steps': [
                        'المدرس سيحصل على إيميل ببيانات الدخول' if email_sent else 'يرجى إرسال بيانات الدخول للمدرس يدوياً',
                        'المدرس يجب أن يغير كلمة السر في أول تسجيل دخول',
                        'المدرس له صلاحيات إدارية كاملة'
                    ]
                }

                return Response(response_data, status=status.HTTP_201_CREATED)
            else:
                print(f"❌ Validation errors: {serializer.errors}")
                return Response({
                    'success': False,
                    'message': 'خطأ في البيانات المدخلة',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            print(f"❌ Unexpected error creating instructor: {str(e)}")
            import traceback
            traceback.print_exc()

            return Response({
                'success': False,
                'message': 'حدث خطأ غير متوقع أثناء إنشاء المدرس',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SecureStudentApplicationImageView(APIView):
    """
    Secure view for serving student application payment screenshot images.
    Only authenticated admins can access the images.
    """
    permission_classes = [permissions.IsAdminUser]

    def get(self, request, application_id):
        """Serve student application payment screenshot image securely"""
        print(f"🖼️ Image request for application {application_id}")
        print(f"👤 User: {request.user.email if request.user.is_authenticated else 'Anonymous'}")
        print(f"🔑 Is Admin: {request.user.is_staff if request.user.is_authenticated else False}")

        try:
            # Get the application
            application = get_object_or_404(StudentApplication, id=application_id)
            print(f"✅ Found application: {application.id}")

            # Check if image exists
            if not application.payment_screenshot:
                print("❌ No payment screenshot found")
                return Response(
                    {'error': 'لا توجد صورة مرفقة مع هذا الطلب'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Import required modules for file serving
            from django.http import FileResponse, Http404
            from django.conf import settings
            import os
            import mimetypes

            # Get the file path
            file_path = application.payment_screenshot.path
            print(f"📁 File path: {file_path}")

            # Check if file exists on disk
            if not os.path.exists(file_path):
                print("❌ File does not exist on disk")
                return Response(
                    {'error': 'الملف غير موجود على الخادم'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Detect content type
            content_type, _ = mimetypes.guess_type(file_path)
            if not content_type:
                content_type = 'image/jpeg'  # Default fallback

            print(f"📄 Content type: {content_type}")
            print(f"✅ Serving file: {file_path}")

            # Serve the file
            response = FileResponse(
                open(file_path, 'rb'),
                content_type=content_type
            )
            response['Content-Disposition'] = f'inline; filename="payment_screenshot_{application_id}.jpg"'
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Headers'] = 'Authorization, Content-Type'
            return response

        except Exception as e:
            print(f"❌ Error serving image: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response(
                {'error': f'خطأ في عرض الصورة: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# ==================== Payment Methods Views ====================

class PaymentMethodsPublicView(generics.RetrieveAPIView):
    """عرض بيانات الدفع والتواصل للعامة (للفرونت إند)"""
    serializer_class = PaymentMethodsPublicSerializer
    permission_classes = [AllowAny]

    def get_object(self):
        """جلب البيانات النشطة"""
        payment_methods = PaymentMethods.get_active_instance()
        if not payment_methods:
            # إنشاء بيانات افتراضية إذا لم توجد
            payment_methods = PaymentMethods.objects.create(
                payment_instructions='للاشتراك يرجى رفع صورة تحويل بأحد الوسائل الآتية:',
                is_active=True
            )
        return payment_methods


class PaymentMethodsAdminView(generics.RetrieveUpdateAPIView):
    """إدارة بيانات الدفع والتواصل (للأدمن فقط)"""
    serializer_class = PaymentMethodsSerializer
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]

    def get_object(self):
        """جلب البيانات النشطة"""
        payment_methods = PaymentMethods.get_active_instance()
        if not payment_methods:
            # إنشاء بيانات افتراضية إذا لم توجد
            payment_methods = PaymentMethods.objects.create(
                payment_instructions='للاشتراك يرجى رفع صورة تحويل بأحد الوسائل الآتية:',
                is_active=True
            )
        return payment_methods

    def update(self, request, *args, **kwargs):
        """تحديث البيانات"""
        try:
            response = super().update(request, *args, **kwargs)
            return Response({
                'success': True,
                'message': 'تم تحديث بيانات الدفع والتواصل بنجاح',
                'data': response.data
            })
        except Exception as e:
            return Response({
                'success': False,
                'message': f'خطأ في تحديث البيانات: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)


class ContactInfoView(generics.RetrieveAPIView):
    """عرض بيانات الاتصال فقط (للفوتر)"""
    serializer_class = PaymentMethodsPublicSerializer
    permission_classes = [AllowAny]

    def get_object(self):
        """جلب البيانات النشطة"""
        return PaymentMethods.get_active_instance()

    def retrieve(self, request, *args, **kwargs):
        """عرض بيانات الاتصال فقط"""
        instance = self.get_object()
        if not instance:
            return Response({
                'phone_number': None,
                'email': None,
                'address': None,
                'working_hours': None,
                'facebook_url': None,
                'twitter_url': None,
                'instagram_url': None,
                'linkedin_url': None,
                'youtube_url': None,
            })

        # عرض بيانات الاتصال فقط
        data = {
            'phone_number': instance.phone_number,
            'email': instance.email,
            'address': instance.address,
            'working_hours': instance.working_hours,
            'facebook_url': instance.facebook_url,
            'twitter_url': instance.twitter_url,
            'instagram_url': instance.instagram_url,
            'linkedin_url': instance.linkedin_url,
            'youtube_url': instance.youtube_url,
        }

        return Response(data)
