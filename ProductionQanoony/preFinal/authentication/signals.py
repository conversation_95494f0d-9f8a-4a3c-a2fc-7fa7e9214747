from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import CustomUser, EmailVerification
from django.core.mail import send_mail
from django.conf import settings
import uuid

@receiver(post_save, sender=CustomUser)
def create_email_verification(sender, instance, created, **kwargs):
    if created and not instance.email_verified:
        token = str(uuid.uuid4())
        EmailVerification.objects.create(user=instance, token=token)
        send_mail(
            subject='تأكيد البريد الإلكتروني',
            message=f'رمز التحقق الخاص بك: {token}',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[instance.email],
        ) 