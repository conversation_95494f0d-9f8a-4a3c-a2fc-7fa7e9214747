from django.db import models
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin, BaseUserManager
from django.utils import timezone
import uuid
from django.utils.translation import gettext_lazy as _


class CustomUserManager(BaseUserManager):
    def create_user(self, email, first_name, last_name, password=None, **extra_fields):
        if not email:
            raise ValueError('Email is required')
        email = self.normalize_email(email)
        user = self.model(email=email, first_name=first_name,
                          last_name=last_name, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, first_name, last_name, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        extra_fields.setdefault('email_verified', True)  # Auto-verify email for superusers
        if extra_fields.get('is_staff') is not True:
            raise ValueError('المشرف يجب أن يكون is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('المشرف يجب أن يكون is_superuser=True')
        return self.create_user(email, first_name, last_name, password, **extra_fields)


class CustomUser(AbstractBaseUser, PermissionsMixin):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_student = models.BooleanField(default=False)
    is_instructor = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(blank=True, null=True)
    email_verified = models.BooleanField(default=False)
    profile_image = models.ImageField(
        upload_to='profile_images/', blank=True, null=True)
    selected_plan_id = models.IntegerField(null=True, blank=True, verbose_name='الخطة المختارة')

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']

    objects = CustomUserManager()

    def save(self, *args, **kwargs):
        if self.is_superuser:
            self.is_staff = True
            self.email_verified = True  # Auto-verify email for superusers
        super().save(*args, **kwargs)

    def __str__(self):
        return self.email


class EmailVerification(models.Model):
    user = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name='email_verifications')
    token = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_used = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.user.email} - {self.token}"


class StudentApplication(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE,
                                related_name='student_application', verbose_name='المستخدم')
    selected_plan = models.ForeignKey(
        'subscriptions.SubscriptionPlan',
        on_delete=models.CASCADE,
        verbose_name='الخطة المختارة',
        null=True,
        blank=True
    )
    payment_screenshot = models.ImageField(
        upload_to='student_applications/', verbose_name='صورة إيصال الدفع')
    is_approved = models.BooleanField(
        default=False, verbose_name='تمت الموافقة')
    approved_at = models.DateTimeField(
        null=True, blank=True, verbose_name='تاريخ الموافقة')
    approved_by = models.ForeignKey(
        CustomUser,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='approved_student_applications',
        verbose_name='تمت الموافقة بواسطة'
    )
    rejection_reason = models.TextField(blank=True, verbose_name='سبب الرفض')
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name='تاريخ التقديم')

    def __str__(self):
        return f"{self.user.email} - طلب التحاق طالب"


class InstructorApplication(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE,
                                related_name='instructor_application', verbose_name='المستخدم', blank=True, null=True)
    email = models.EmailField(verbose_name='البريد الإلكتروني', blank=True, null=True)
    first_name = models.CharField(max_length=50, verbose_name='الاسم الأول', blank=True, null=True)
    last_name = models.CharField(max_length=50, verbose_name='اسم العائلة', blank=True, null=True)
    bio = models.TextField(verbose_name='نبذة تعريفية')
    specialty = models.CharField(
        max_length=100, blank=True, verbose_name='التخصص')
    phone_number = models.CharField(
        max_length=20, blank=True, verbose_name='رقم الهاتف')
    cv = models.FileField(upload_to='instructor_applications/cv/',
                          blank=True, null=True, verbose_name='السيرة الذاتية')
    is_approved = models.BooleanField(
        default=False, verbose_name='تمت الموافقة')
    approved_at = models.DateTimeField(
        null=True, blank=True, verbose_name='تاريخ الموافقة')
    approved_by = models.ForeignKey(
        CustomUser,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='approved_instructor_applications',
        verbose_name='تمت الموافقة بواسطة'
    )
    rejection_reason = models.TextField(blank=True, verbose_name='سبب الرفض')
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name='تاريخ التقديم')

    def __str__(self):
        if self.user:
            return f"{self.user.email} - طلب تفعيل معلم"
        return f"{self.email} - طلب تفعيل معلم"


class InstructorStudentNote(models.Model):
    """ملاحظات المدرس عن الطلاب"""
    instructor = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='student_notes',
        limit_choices_to={'is_instructor': True},
        verbose_name='المدرس'
    )
    student = models.ForeignKey(
        'students.StudentProfile',
        on_delete=models.CASCADE,
        related_name='instructor_notes',
        verbose_name='الطالب'
    )
    note = models.TextField(max_length=1000, verbose_name='الملاحظة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        unique_together = ('instructor', 'student')
        ordering = ['-updated_at']
        verbose_name = 'ملاحظة مدرس عن طالب'
        verbose_name_plural = 'ملاحظات المدرسين عن الطلاب'

    def __str__(self):
        return f"{self.instructor.get_full_name()} -> {self.student.user.get_full_name()}"


class PaymentMethods(models.Model):
    """موديل بيانات الدفع والتواصل"""

    # بيانات طرق الدفع الإلكترونية
    instapay_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='رقم انستاباي'
    )
    instapay_enabled = models.BooleanField(
        default=True,
        verbose_name='تفعيل انستاباي'
    )

    vodafone_cash_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='رقم فودافون كاش'
    )
    vodafone_cash_enabled = models.BooleanField(
        default=True,
        verbose_name='تفعيل فودافون كاش'
    )

    # بيانات الحساب البنكي
    bank_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='اسم البنك'
    )
    account_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='رقم الحساب'
    )
    account_holder_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='اسم صاحب الحساب'
    )
    iban = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='الآيبان'
    )
    swift_code = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='كود السويفت'
    )
    bank_enabled = models.BooleanField(
        default=True,
        verbose_name='تفعيل الحساب البنكي'
    )

    # بيانات الاتصال (للفوتر)
    phone_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='رقم الهاتف'
    )
    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name='البريد الإلكتروني'
    )
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name='العنوان'
    )
    working_hours = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='ساعات العمل'
    )

    # صفحات التواصل الاجتماعي
    facebook_url = models.URLField(
        blank=True,
        null=True,
        verbose_name='رابط فيسبوك'
    )
    twitter_url = models.URLField(
        blank=True,
        null=True,
        verbose_name='رابط تويتر'
    )
    instagram_url = models.URLField(
        blank=True,
        null=True,
        verbose_name='رابط إنستجرام'
    )
    linkedin_url = models.URLField(
        blank=True,
        null=True,
        verbose_name='رابط لينكد إن'
    )
    youtube_url = models.URLField(
        blank=True,
        null=True,
        verbose_name='رابط يوتيوب'
    )

    # تعليمات الدفع
    payment_instructions = models.TextField(
        blank=True,
        null=True,
        verbose_name='تعليمات الدفع',
        default='للاشتراك يرجى رفع صورة تحويل بأحد الوسائل الآتية:'
    )

    # إعدادات
    is_active = models.BooleanField(
        default=True,
        verbose_name='فعال'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'بيانات الدفع والتواصل'
        verbose_name_plural = 'بيانات الدفع والتواصل'
        ordering = ['-updated_at']

    def __str__(self):
        return f"بيانات الدفع والتواصل - {self.updated_at.strftime('%Y-%m-%d')}"

    @classmethod
    def get_active_instance(cls):
        """جلب البيانات النشطة"""
        return cls.objects.filter(is_active=True).first()

    def save(self, *args, **kwargs):
        # التأكد من وجود instance واحد نشط فقط
        if self.is_active:
            PaymentMethods.objects.filter(is_active=True).exclude(pk=self.pk).update(is_active=False)
        super().save(*args, **kwargs)
