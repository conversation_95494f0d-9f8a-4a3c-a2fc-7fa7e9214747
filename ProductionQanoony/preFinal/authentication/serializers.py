from rest_framework import serializers
from django.contrib.auth import authenticate
from .models import CustomUser, EmailVerification, StudentApplication, InstructorApplication, PaymentMethods
from django.utils.translation import gettext_lazy as _
from subscriptions.models import Subscription, SubscriptionPlan
from .profile_serializers import ProfileSerializer
from students.serializers import StudentProfileSerializer
from students.models import StudentProfile
from universities.models import University, AcademicYear
from courses.models import Subject


# بدلاً من RegistrationSerializer المعقد، هنعمل StudentApplicationSerializer بسيط
class StudentRegistrationSerializer(serializers.Serializer):
    # بيانات المستخدم
    email = serializers.EmailField()
    first_name = serializers.CharField(max_length=50)
    last_name = serializers.CharField(max_length=50)
    phone_number = serializers.CharField(max_length=20)
    password = serializers.CharField(write_only=True, min_length=8)

    # بيانات الطالب
    university_id = serializers.PrimaryKeyRelatedField(
        queryset=University.objects.filter(is_active=True),
        write_only=True,
        required=True
    )
    academic_year_id = serializers.PrimaryKeyRelatedField(
        queryset=AcademicYear.objects.filter(is_active=True),
        write_only=True,
        required=True
    )

    # بيانات الاشتراك
    plan_id = serializers.PrimaryKeyRelatedField(
        queryset=SubscriptionPlan.objects.filter(is_active=True),
        write_only=True,
        required=True
    )
    payment_screenshot = serializers.ImageField(required=True)

    def validate_email(self, value):
        if CustomUser.objects.filter(email=value).exists():
            raise serializers.ValidationError("هذا البريد الإلكتروني مستخدم بالفعل")
        return value

    def create(self, validated_data):
        # استخراج البيانات
        email = validated_data['email']
        first_name = validated_data['first_name']
        last_name = validated_data['last_name']
        phone_number = validated_data['phone_number']
        password = validated_data['password']
        university = validated_data['university_id']
        academic_year = validated_data['academic_year_id']
        plan = validated_data['plan_id']
        payment_screenshot = validated_data['payment_screenshot']

        print(f"🚀 Creating student application for: {email}")

        # إنشاء المستخدم (غير مفعل)
        user = CustomUser.objects.create_user(
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone_number=phone_number,
            password=password,
            is_active=True,  # نشط لكن مش طالب
            is_student=False  # هيتفعل لما الأدمن يوافق
        )
        print(f"✅ Created user: {user.email}")

        # إنشاء StudentProfile
        from students.models import StudentProfile
        profile = StudentProfile.objects.create(
            user=user,
            university=university,
            academic_year=academic_year,
            student_id=f'STU{str(user.id)[:8].upper()}'
        )
        print(f"✅ Created profile: {profile.student_id}")

        # إنشاء طلب التحاق
        application = StudentApplication.objects.create(
            user=user,
            selected_plan=plan,
            payment_screenshot=payment_screenshot,
            is_approved=False  # منتظر موافقة الأدمن
        )
        print(f"✅ Created application: {application.id}")

        return application

# الـ RegistrationSerializer القديم - هنشيله
class RegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, min_length=8)

    class Meta:
        model = CustomUser
        fields = ('email', 'first_name', 'last_name', 'phone_number', 'password')

    def create(self, validated_data):
        password = validated_data.pop('password')
        user = CustomUser(**validated_data)
        user.set_password(password)
        user.is_active = True
        user.save()
        return user


class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)

    def validate(self, data):
        # Django's default ModelBackend expects "username" kwarg, but since
        # our CustomUser sets USERNAME_FIELD = 'email', we must pass "email".
        user = authenticate(email=data.get('email'),
                            password=data.get('password'))
        if not user:
            raise serializers.ValidationError(
                _('البريد الإلكتروني أو كلمة المرور غير صحيحة'))
        if not user.is_active:
            raise serializers.ValidationError(_('هذا الحساب غير نشط'))
        return {'user': user}


class ProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = ('id', 'email', 'first_name', 'last_name', 'phone_number', 'profile_image',
                  'is_student', 'is_instructor', 'is_staff', 'is_superuser', 'email_verified', 'date_joined', 'last_login')
        read_only_fields = ('email', 'is_student', 'is_instructor', 'is_staff', 'is_superuser',
                            'email_verified', 'date_joined', 'last_login')


class EmailVerificationSerializer(serializers.Serializer):
    token = serializers.CharField()


class PasswordResetRequestSerializer(serializers.Serializer):
    email = serializers.EmailField()


class PasswordResetSerializer(serializers.Serializer):
    token = serializers.CharField()
    new_password = serializers.CharField(write_only=True, min_length=8)


class StudentApplicationSerializer(serializers.ModelSerializer):
    user = ProfileSerializer(read_only=True)
    student_profile = StudentProfileSerializer(source='user.student_profile', read_only=True)
    selected_plan = serializers.PrimaryKeyRelatedField(read_only=True)
    selected_plan_name = serializers.CharField(source='selected_plan.name', read_only=True)
    selected_plan_price = serializers.DecimalField(source='selected_plan.price', max_digits=10, decimal_places=2, read_only=True)
    payment_screenshot = serializers.ImageField(required=True)
    is_approved = serializers.BooleanField(read_only=True)
    approved_at = serializers.DateTimeField(read_only=True)
    approved_by = serializers.PrimaryKeyRelatedField(read_only=True)
    rejection_reason = serializers.CharField(read_only=True)
    created_at = serializers.DateTimeField(read_only=True)

    class Meta:
        model = StudentApplication
        fields = '__all__'

    def validate(self, data):
        user = self.context['request'].user
        if StudentApplication.objects.filter(user=user).exists():
            raise serializers.ValidationError('لقد قمت بتقديم طلب بالفعل')
        return data


class InstructorListSerializer(serializers.ModelSerializer):
    """Minimal serializer used to list instructors for the Private-Tutor booking form"""
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = CustomUser
        fields = ('id', 'full_name')

    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}"


class InstructorDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for admin instructor management"""
    full_name = serializers.SerializerMethodField()
    specialty = serializers.SerializerMethodField()
    bio = serializers.SerializerMethodField()
    is_approved = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(source='date_joined', read_only=True)

    class Meta:
        model = CustomUser
        fields = (
            'id', 'email', 'first_name', 'last_name', 'full_name',
            'phone_number', 'specialty', 'bio', 'is_active', 'is_approved',
            'date_joined', 'created_at', 'last_login'
        )

    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}"

    def get_specialty(self, obj):
        # Try to get specialty from InstructorApplication
        try:
            app = getattr(obj, 'instructor_application', None)
            return app.specialty if app and app.specialty else '—'
        except:
            return '—'

    def get_bio(self, obj):
        # Try to get bio from InstructorApplication
        try:
            app = getattr(obj, 'instructor_application', None)
            return app.bio if app and app.bio else '—'
        except:
            return '—'

    def get_is_approved(self, obj):
        return obj.is_instructor and obj.is_active


class InstructorApplicationSerializer(serializers.ModelSerializer):
    user = ProfileSerializer(read_only=True)
    is_approved = serializers.BooleanField(read_only=True)
    approved_at = serializers.DateTimeField(read_only=True)
    approved_by = serializers.PrimaryKeyRelatedField(read_only=True)
    rejection_reason = serializers.CharField(read_only=True)
    created_at = serializers.DateTimeField(read_only=True)
    email = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    phone_number = serializers.CharField()

    class Meta:
        model = InstructorApplication
        fields = '__all__'

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if instance.user:
            data['email'] = instance.user.email
            data['first_name'] = instance.user.first_name
            data['last_name'] = instance.user.last_name
            data['phone_number'] = instance.user.phone_number
        else:
            data['email'] = instance.email
            data['first_name'] = instance.first_name
            data['last_name'] = instance.last_name
            data['phone_number'] = instance.phone_number
        return data

    def validate(self, data):
        # لا تتحقق من user ولا تربط الطلب بأي user
        return data


class InstructorProfileSerializer(serializers.ModelSerializer):
    """Serializer شامل لـ profile المدرس"""
    specialty = serializers.SerializerMethodField()
    bio = serializers.SerializerMethodField()

    class Meta:
        model = CustomUser
        fields = [
            'id', 'email', 'first_name', 'last_name', 'phone_number',
            'profile_image', 'specialty', 'bio', 'date_joined', 'last_login',
            'is_active', 'email_verified'
        ]
        read_only_fields = ['email', 'date_joined', 'last_login', 'is_active', 'email_verified']

    def get_specialty(self, obj):
        """جلب التخصص من InstructorApplication"""
        try:
            return obj.instructor_application.specialty if obj.instructor_application.specialty else ''
        except:
            return ''

    def get_bio(self, obj):
        """جلب النبذة التعريفية من InstructorApplication"""
        try:
            return obj.instructor_application.bio if obj.instructor_application.bio else ''
        except:
            return ''

    def validate_first_name(self, value):
        """التحقق من الاسم الأول"""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError('يجب أن يكون الاسم الأول أكثر من حرفين')
        return value.strip()

    def validate_last_name(self, value):
        """التحقق من اسم العائلة"""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError('يجب أن يكون اسم العائلة أكثر من حرفين')
        return value.strip()

    def validate_phone_number(self, value):
        """التحقق من رقم الهاتف"""
        if value and len(value.strip()) > 0:
            import re
            # تنسيق أرقام الهواتف السعودية والدولية
            phone_pattern = r'^(\+966|966|0)?[5][0-9]{8}$|^\+?[1-9]\d{1,14}$'
            if not re.match(phone_pattern, value.strip()):
                raise serializers.ValidationError('تنسيق رقم الهاتف غير صحيح')
            return value.strip()
        return value

    def update(self, instance, validated_data):
        """تحديث البيانات في الجدولين"""
        from django.utils import timezone

        try:
            # تحديث البيانات الأساسية في CustomUser
            for attr, value in validated_data.items():
                if attr not in ['specialty', 'bio']:
                    setattr(instance, attr, value)
            instance.save()

            # تحديث التخصص والسيرة في InstructorApplication
            specialty = self.context['request'].data.get('specialty')
            bio = self.context['request'].data.get('bio')

            if specialty is not None or bio is not None:
                try:
                    # محاولة الحصول على InstructorApplication الموجود
                    app = InstructorApplication.objects.get(user=instance)
                    if specialty is not None:
                        app.specialty = specialty[:100] if specialty else ''  # حد أقصى 100 حرف
                    if bio is not None:
                        app.bio = bio[:1000] if bio else ''  # حد أقصى 1000 حرف
                    app.save()
                except InstructorApplication.DoesNotExist:
                    # إنشاء جديد إذا لم يكن موجود
                    InstructorApplication.objects.create(
                        user=instance,
                        specialty=(specialty[:100] if specialty else '') or '',
                        bio=(bio[:1000] if bio else '') or '',
                        is_approved=True,
                        approved_at=timezone.now()
                    )

            return instance
        except Exception as e:
            raise serializers.ValidationError(f'خطأ في تحديث البيانات: {str(e)}')


class InstructorSubjectSerializer(serializers.ModelSerializer):
    """Serializer للمواد التي يدرسها المدرس مع معلومات إضافية"""
    semester_name = serializers.CharField(source='semester.title_ar', read_only=True)
    academic_year_name = serializers.CharField(source='academic_year.year_name_ar', read_only=True)
    lectures_count = serializers.SerializerMethodField()
    quizzes_count = serializers.SerializerMethodField()
    students_count = serializers.SerializerMethodField()

    class Meta:
        model = Subject
        fields = [
            'id', 'name_ar', 'name_en', 'code', 'description_ar',
            'semester_name', 'academic_year_name', 'lectures_count',
            'quizzes_count', 'students_count', 'is_active'
        ]

    def get_lectures_count(self, obj):
        """عدد المحاضرات في المادة"""
        return obj.lectures.count()

    def get_quizzes_count(self, obj):
        """عدد الاختبارات في المادة"""
        from courses.models import Quiz
        return Quiz.objects.filter(lecture__subject=obj).count()

    def get_students_count(self, obj):
        """عدد الطلاب المسجلين في المادة (تقدير)"""
        # يمكن حسابه من خلال الطلاب الذين لديهم محاولات اختبار في هذه المادة
        from courses.models import QuizAttempt
        return QuizAttempt.objects.filter(
            quiz__lecture__subject=obj
        ).values('student').distinct().count()


class InstructorSessionSerializer(serializers.Serializer):
    """Serializer موحد لجلسات المدرس (Live + Private)"""
    id = serializers.IntegerField()
    type = serializers.CharField()  # 'live_session' or 'private_session'
    title = serializers.CharField()
    description = serializers.CharField()
    scheduled_date = serializers.DateTimeField()
    duration_minutes = serializers.IntegerField()
    status = serializers.CharField()
    status_display = serializers.CharField()
    meeting_link = serializers.URLField(allow_null=True)
    created_at = serializers.DateTimeField()

    # معلومات إضافية حسب النوع
    max_participants = serializers.IntegerField(allow_null=True)  # للجلسات المباشرة
    participants_count = serializers.IntegerField(allow_null=True)  # للجلسات المباشرة
    student_name = serializers.CharField(allow_null=True)  # للجلسات الخاصة
    subject_name = serializers.CharField(allow_null=True)  # للجلسات الخاصة
    instructor_notes = serializers.CharField(allow_null=True)

    def to_representation(self, instance):
        """تحويل البيانات حسب نوع الجلسة"""
        if hasattr(instance, 'title_ar'):  # LiveSession
            return {
                'id': instance.id,
                'type': 'live_session',
                'title': instance.title_ar,
                'description': instance.description_ar,
                'scheduled_date': instance.scheduled_date,
                'duration_minutes': instance.duration_minutes,
                'status': instance.status,
                'status_display': instance.get_status_display(),
                'meeting_link': instance.meeting_link,
                'created_at': instance.created_at,
                'max_participants': instance.max_participants,
                'participants_count': instance.bookings.count(),
                'student_name': None,
                'subject_name': instance.semester.title_ar if instance.semester else None,
                'instructor_notes': None
            }
        else:  # PrivateTutorSession
            return {
                'id': instance.id,
                'type': 'private_session',
                'title': f'جلسة خاصة - {instance.subject.name_ar}',
                'description': instance.instructor_notes or 'جلسة خاصة',
                'scheduled_date': instance.scheduled_at,
                'duration_minutes': instance.duration_minutes,
                'status': instance.status,
                'status_display': instance.get_status_display(),
                'meeting_link': instance.meeting_link,
                'created_at': instance.created_at,
                'max_participants': None,
                'participants_count': None,
                'student_name': instance.student.user.get_full_name(),
                'subject_name': instance.subject.name_ar,
                'instructor_notes': instance.instructor_notes
            }


class InstructorStudentSerializer(serializers.ModelSerializer):
    """Serializer للطلاب المرتبطين بالمدرس مع معلومات شاملة"""

    # معلومات المستخدم الأساسية
    name = serializers.CharField(source='user.get_full_name', read_only=True)
    email = serializers.CharField(source='user.email', read_only=True)
    phone = serializers.CharField(source='user.phone_number', read_only=True)
    profile_image = serializers.SerializerMethodField()

    # معلومات أكاديمية
    university_name = serializers.CharField(source='university.name_ar', read_only=True)
    academic_year_name = serializers.CharField(source='academic_year.year_name_ar', read_only=True)

    # معلومات الاتصال مع المدرس
    connection_types = serializers.SerializerMethodField()
    subjects = serializers.SerializerMethodField()
    last_activity = serializers.SerializerMethodField()

    # إحصائيات
    stats = serializers.SerializerMethodField()

    class Meta:
        model = StudentProfile
        fields = [
            'id', 'name', 'email', 'phone', 'profile_image',
            'university_name', 'academic_year_name', 'student_id',
            'connection_types', 'subjects', 'last_activity', 'stats',
            'created_at'
        ]

    def get_profile_image(self, obj):
        """صورة الطالب الشخصية"""
        if obj.user.profile_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.user.profile_image.url)
        return None

    def get_connection_types(self, obj):
        """أنواع الاتصال مع المدرس"""
        instructor = self.context.get('instructor')
        if not instructor:
            return []

        connections = []

        # فحص المحادثات
        from communication.models import ChatRoom
        if ChatRoom.objects.filter(instructor=instructor, student=obj).exists():
            connections.append('chat')

        # فحص الجلسات الخاصة
        from courses.models import PrivateTutorSession
        if PrivateTutorSession.objects.filter(instructor=instructor, student=obj).exists():
            connections.append('private_session')

        # فحص الجلسات المباشرة
        from scheduling.models import SessionBooking
        if SessionBooking.objects.filter(
            session__instructor=instructor,
            student=obj
        ).exists():
            connections.append('live_session')

        return connections

    def get_subjects(self, obj):
        """المواد المرتبطة بالطالب مع المدرس"""
        instructor = self.context.get('instructor')
        if not instructor:
            return []

        subjects = set()

        # من المحادثات
        from communication.models import ChatRoom
        chat_subjects = ChatRoom.objects.filter(
            instructor=instructor,
            student=obj,
            subject__isnull=False
        ).values_list('subject__name_ar', flat=True)
        subjects.update(chat_subjects)

        # من الجلسات الخاصة
        from courses.models import PrivateTutorSession
        private_subjects = PrivateTutorSession.objects.filter(
            instructor=instructor,
            student=obj
        ).values_list('subject__name_ar', flat=True)
        subjects.update(private_subjects)

        return list(subjects)

    def get_last_activity(self, obj):
        """آخر نشاط للطالب مع المدرس"""
        instructor = self.context.get('instructor')
        if not instructor:
            return None

        from django.db.models import Q
        from communication.models import Message
        from courses.models import PrivateTutorSession
        from scheduling.models import SessionBooking

        activities = []

        # آخر رسالة
        try:
            last_message = Message.objects.filter(
                room__instructor=instructor,
                room__student=obj,
                sender=obj.user
            ).order_by('-timestamp').first()
            if last_message:
                activities.append(last_message.timestamp)
        except:
            pass

        # آخر جلسة خاصة
        try:
            last_private = PrivateTutorSession.objects.filter(
                instructor=instructor,
                student=obj
            ).order_by('-updated_at').first()
            if last_private:
                activities.append(last_private.updated_at)
        except:
            pass

        # آخر جلسة مباشرة
        try:
            last_booking = SessionBooking.objects.filter(
                session__instructor=instructor,
                student=obj
            ).order_by('-booked_at').first()
            if last_booking:
                activities.append(last_booking.booked_at)
        except:
            pass

        if activities:
            return max(activities)
        return obj.created_at

    def get_stats(self, obj):
        """إحصائيات الطالب مع المدرس"""
        instructor = self.context.get('instructor')
        if not instructor:
            return {}

        try:
            from communication.models import Message
            from courses.models import PrivateTutorSession
            from scheduling.models import SessionBooking
            from django.db.models import Avg

            # عدد الرسائل
            total_messages = Message.objects.filter(
                room__instructor=instructor,
                room__student=obj,
                sender=obj.user
            ).count()

            # عدد الجلسات الخاصة
            private_sessions = PrivateTutorSession.objects.filter(
                instructor=instructor,
                student=obj
            )
            total_private_sessions = private_sessions.count()
            completed_private_sessions = private_sessions.filter(status='completed').count()

            # عدد الجلسات المباشرة
            live_bookings = SessionBooking.objects.filter(
                session__instructor=instructor,
                student=obj
            )
            total_live_sessions = live_bookings.count()
            attended_live_sessions = live_bookings.filter(attendance_status='attended').count()

            # متوسط التقييم
            avg_rating = live_bookings.filter(
                feedback_rating__isnull=False
            ).aggregate(avg_rating=Avg('feedback_rating'))['avg_rating']

            return {
                'total_messages': total_messages,
                'total_private_sessions': total_private_sessions,
                'completed_private_sessions': completed_private_sessions,
                'total_live_sessions': total_live_sessions,
                'attended_live_sessions': attended_live_sessions,
                'avg_rating': round(avg_rating, 1) if avg_rating else None
            }
        except Exception:
            return {
                'total_messages': 0,
                'total_private_sessions': 0,
                'completed_private_sessions': 0,
                'total_live_sessions': 0,
                'attended_live_sessions': 0,
                'avg_rating': None
            }


class InstructorStudentNoteSerializer(serializers.Serializer):
    """Serializer لملاحظات المدرس عن الطلاب"""
    id = serializers.IntegerField(read_only=True)
    student_id = serializers.IntegerField()
    note = serializers.CharField(max_length=1000)
    created_at = serializers.DateTimeField(read_only=True)
    updated_at = serializers.DateTimeField(read_only=True)

    def validate_note(self, value):
        """التحقق من الملاحظة"""
        if not value or len(value.strip()) < 5:
            raise serializers.ValidationError('يجب أن تكون الملاحظة أكثر من 5 أحرف')
        return value.strip()


class InstructorCreateSerializer(serializers.Serializer):
    """
    Serializer for admin to create instructor directly
    Best Practice: Separate serializer for different use cases
    """
    email = serializers.EmailField()
    first_name = serializers.CharField(max_length=50)
    last_name = serializers.CharField(max_length=50)
    phone_number = serializers.CharField(max_length=20, required=False, allow_blank=True)
    specialty = serializers.CharField(max_length=100, required=False, allow_blank=True)
    bio = serializers.CharField(required=False, allow_blank=True)

    # Password options
    auto_generate_password = serializers.BooleanField(default=True)
    custom_password = serializers.CharField(
        min_length=8,
        required=False,
        allow_blank=True,
        help_text="اتركه فارغاً لتوليد كلمة سر تلقائية"
    )

    # Notification options
    send_email_notification = serializers.BooleanField(default=True)

    def validate_email(self, value):
        """تحقق من أن البريد الإلكتروني غير مستخدم"""
        if CustomUser.objects.filter(email=value).exists():
            raise serializers.ValidationError("هذا البريد الإلكتروني مستخدم بالفعل")
        return value

    def validate(self, data):
        """تحقق من كلمة السر"""
        auto_generate = data.get('auto_generate_password', True)
        custom_password = data.get('custom_password', '')

        if not auto_generate and not custom_password:
            raise serializers.ValidationError({
                'custom_password': 'يجب إدخال كلمة سر أو اختيار التوليد التلقائي'
            })

        return data

    def create(self, validated_data):
        """إنشاء مدرس جديد مع best practices"""
        import secrets
        import string
        from django.core.mail import send_mail
        from django.conf import settings
        from django.utils import timezone

        # استخراج البيانات
        email = validated_data['email']
        first_name = validated_data['first_name']
        last_name = validated_data['last_name']
        phone_number = validated_data.get('phone_number', '')
        specialty = validated_data.get('specialty', '')
        bio = validated_data.get('bio', '')
        auto_generate = validated_data.get('auto_generate_password', True)
        custom_password = validated_data.get('custom_password', '')
        send_email = validated_data.get('send_email_notification', True)

        print(f"🚀 Creating instructor: {email}")

        # توليد كلمة سر آمنة
        if auto_generate:
            # توليد كلمة سر قوية: 12 حرف مع أرقام ورموز
            alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
            password = ''.join(secrets.choice(alphabet) for i in range(12))
        else:
            password = custom_password

        print(f"🔐 Generated password for {email}")

        # إنشاء المستخدم
        user = CustomUser.objects.create_user(
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone_number=phone_number,
            password=password,
            is_instructor=True,
            is_staff=True,  # صلاحيات إدارية
            is_active=True,
            email_verified=True  # مدرس موثق تلقائياً
        )

        print(f"✅ Created user: {user.email}")

        # إنشاء سجل في InstructorApplication للتتبع (اختياري)
        try:
            InstructorApplication.objects.create(
                user=user,
                email=email,
                first_name=first_name,
                last_name=last_name,
                phone_number=phone_number,
                specialty=specialty,
                bio=bio,
                is_approved=True,
                approved_at=timezone.now(),
                approved_by=self.context['request'].user
            )
            print(f"✅ Created instructor application record")
        except Exception as e:
            print(f"⚠️ Could not create application record: {e}")

        # إرسال إيميل بالبيانات
        if send_email:
            try:
                self._send_welcome_email(user, password)
                print(f"📧 Welcome email sent to {email}")
            except Exception as e:
                print(f"⚠️ Could not send email: {e}")

        # إرجاع البيانات للأدمن
        return {
            'user': user,
            'password': password,
            'email_sent': send_email
        }

    def _send_welcome_email(self, user, password):
        """إرسال إيميل ترحيبي مع بيانات الدخول"""
        from django.conf import settings

        subject = "مرحباً بك كمدرس في منصتنا - بيانات الدخول"

        frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3001')

        message = f"""
مرحباً {user.first_name} {user.last_name},

تم إنشاء حسابك كمدرس في منصتنا بنجاح!

بيانات الدخول:
الإيميل: {user.email}
كلمة السر المؤقتة: {password}

⚠️ مهم جداً:
1. يرجى تسجيل الدخول وتغيير كلمة السر فوراً
2. احتفظ ببيانات الدخول في مكان آمن
3. لا تشارك كلمة السر مع أي شخص

رابط تسجيل الدخول: {frontend_url}/login

مع تحيات فريق المنصة
        """

        from django.core.mail import send_mail
        send_mail(
            subject=subject,
            message=message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[user.email],
            fail_silently=False,
        )


class PaymentMethodsSerializer(serializers.ModelSerializer):
    """Serializer لبيانات الدفع والتواصل"""

    class Meta:
        model = PaymentMethods
        fields = [
            # طرق الدفع الإلكترونية
            'instapay_number',
            'instapay_enabled',
            'vodafone_cash_number',
            'vodafone_cash_enabled',

            # بيانات الحساب البنكي
            'bank_name',
            'account_number',
            'account_holder_name',
            'iban',
            'swift_code',
            'bank_enabled',

            # بيانات الاتصال
            'phone_number',
            'email',
            'address',
            'working_hours',

            # صفحات التواصل الاجتماعي
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'linkedin_url',
            'youtube_url',

            # تعليمات الدفع
            'payment_instructions',

            # إعدادات
            'is_active',
            'updated_at'
        ]
        read_only_fields = ['updated_at']

    def to_representation(self, instance):
        """تخصيص البيانات المرجعة"""
        data = super().to_representation(instance)

        # إخفاء الأرقام الحساسة جزئياً للأمان
        if data.get('instapay_number'):
            number = data['instapay_number']
            if len(number) > 4:
                data['instapay_number_masked'] = number[:2] + '*' * (len(number) - 4) + number[-2:]
            else:
                data['instapay_number_masked'] = '*' * len(number)

        if data.get('vodafone_cash_number'):
            number = data['vodafone_cash_number']
            if len(number) > 4:
                data['vodafone_cash_number_masked'] = number[:2] + '*' * (len(number) - 4) + number[-2:]
            else:
                data['vodafone_cash_number_masked'] = '*' * len(number)

        if data.get('account_number'):
            number = data['account_number']
            if len(number) > 4:
                data['account_number_masked'] = number[:2] + '*' * (len(number) - 4) + number[-2:]
            else:
                data['account_number_masked'] = '*' * len(number)

        return data


class PaymentMethodsPublicSerializer(serializers.ModelSerializer):
    """Serializer عام لبيانات الدفع (للعرض في الفرونت إند)"""

    instapay_number_masked = serializers.SerializerMethodField()
    vodafone_cash_number_masked = serializers.SerializerMethodField()
    account_number_masked = serializers.SerializerMethodField()

    class Meta:
        model = PaymentMethods
        fields = [
            # طرق الدفع (مخفية جزئياً)
            'instapay_number_masked',
            'instapay_enabled',
            'vodafone_cash_number_masked',
            'vodafone_cash_enabled',

            # بيانات الحساب البنكي (مخفية جزئياً)
            'bank_name',
            'account_number_masked',
            'account_holder_name',
            'bank_enabled',

            # بيانات الاتصال
            'phone_number',
            'email',
            'address',
            'working_hours',

            # صفحات التواصل الاجتماعي
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'linkedin_url',
            'youtube_url',

            # تعليمات الدفع
            'payment_instructions',
        ]

    def get_instapay_number_masked(self, obj):
        """إخفاء رقم انستاباي جزئياً"""
        if not obj.instapay_number:
            return None
        number = obj.instapay_number
        if len(number) > 4:
            return number[:2] + '*' * (len(number) - 4) + number[-2:]
        return '*' * len(number)

    def get_vodafone_cash_number_masked(self, obj):
        """إخفاء رقم فودافون كاش جزئياً"""
        if not obj.vodafone_cash_number:
            return None
        number = obj.vodafone_cash_number
        if len(number) > 4:
            return number[:2] + '*' * (len(number) - 4) + number[-2:]
        return '*' * len(number)

    def get_account_number_masked(self, obj):
        """إخفاء رقم الحساب البنكي جزئياً"""
        if not obj.account_number:
            return None
        number = obj.account_number
        if len(number) > 4:
            return number[:2] + '*' * (len(number) - 4) + number[-2:]
        return '*' * len(number)
