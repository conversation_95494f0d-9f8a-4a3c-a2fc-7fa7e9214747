from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings

class Command(BaseCommand):
    help = 'Sends a test email using Brevo SMTP settings.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Email address to send test email to (default: DEFAULT_FROM_EMAIL)',
        )

    def handle(self, *args, **options):
        try:
            # Get recipient email from command argument or use default
            recipient_email = options.get('email') or settings.DEFAULT_FROM_EMAIL

            if not recipient_email:
                self.stdout.write(self.style.ERROR('No recipient email provided. Use --email or set DEFAULT_FROM_EMAIL.'))
                return

            # Check if all required settings are configured
            required_settings = ['EMAIL_HOST', 'EMAIL_HOST_USER', 'EMAIL_HOST_PASSWORD', 'BREVO_API_KEY']
            missing_settings = []

            for setting in required_settings:
                if not getattr(settings, setting, None):
                    missing_settings.append(setting)

            if missing_settings:
                self.stdout.write(self.style.ERROR(f'Missing required settings: {", ".join(missing_settings)}'))
                return

            self.stdout.write(self.style.WARNING(f"🚀 Sending test email to {recipient_email}..."))
            self.stdout.write(f"📧 SMTP Host: {settings.EMAIL_HOST}")
            self.stdout.write(f"📧 SMTP User: {settings.EMAIL_HOST_USER}")
            self.stdout.write(f"📧 SMTP Port: {settings.EMAIL_PORT}")
            self.stdout.write(f"📧 Use TLS: {settings.EMAIL_USE_TLS}")

            subject = 'اختبار البريد الإلكتروني - منصة قانوني'
            message = '''
مرحباً!

هذه رسالة اختبار للتأكد من أن إعدادات البريد الإلكتروني تعمل بشكل صحيح.

إعدادات Brevo SMTP:
- الخادم: smtp-relay.brevo.com
- المنفذ: 587
- التشفير: TLS

إذا وصلتك هذه الرسالة، فإن الإعدادات تعمل بنجاح! ✅

مع تحيات فريق منصة قانوني
            '''
            from_email = settings.DEFAULT_FROM_EMAIL

            send_mail(subject, message, from_email, [recipient_email])

            self.stdout.write(self.style.SUCCESS('✅ Test email sent successfully! Please check your inbox.'))
            self.stdout.write(self.style.SUCCESS(f'📬 Email sent from: {from_email}'))
            self.stdout.write(self.style.SUCCESS(f'📬 Email sent to: {recipient_email}'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Failed to send test email: {str(e)}'))
            self.stdout.write(self.style.ERROR('Please check your email settings in .env file.'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'An error occurred while sending the email: {e}')) 