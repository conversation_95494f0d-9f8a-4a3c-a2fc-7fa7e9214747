import random
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timed<PERSON><PERSON>, date
from faker import Faker
from django.db import models
from authentication.models import CustomUser
from universities.models import University, AcademicYear
from students.models import StudentProfile, StudentProgress
from courses.models import Subject, Semester, Lecture, Quiz, Question, Answer, QuizAttempt, Certificate
from library.models import LibraryCategory, LibraryItem
from careers.models import JobCategory, JobPosting, JobApplication
from communication.models import Chat<PERSON>oom, Message, ForumCategory, ForumTopic, ForumPost
from scheduling.models import LiveSession, SessionBooking, Calendar
from notifications.models import NotificationTemplate, Notification
from subscriptions.models import SubscriptionPlan, Subscription
from ai_assistant.models import AIConversation, AIMessage, AIPromptTemplate, AIUsageLimit, AIFeedback

fake = Faker('ar_SA')  # Arabic locale

class Command(BaseCommand):
    help = 'Populate database with fake data'

    def add_arguments(self, parser):
        parser.add_argument('--clear', action='store_true', help='Clear existing data first')

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing data...')
            self.clear_data()

        self.stdout.write('Creating fake data...')
        
        # Create data in order of dependencies
        self.create_universities()
        self.create_academic_years()
        self.create_subscription_plans()
        self.create_semesters()
        self.create_subjects()
        self.create_lectures()
        self.create_quizzes()
        self.stdout.write('✅ Finished creating quizzes, now creating questions...')
        self.create_questions_and_answers()
        self.stdout.write('✅ Finished creating questions, now creating library data...')
        self.create_library_data()
        self.stdout.write('✅ Finished creating library data, now creating career data...')
        self.create_career_data()
        self.stdout.write('✅ Finished creating career data, now creating templates...')
        self.create_notification_templates()
        self.create_ai_prompt_templates()
        
        # Create test users first
        self.create_test_users()

        # Get existing users to create related data
        users = CustomUser.objects.all()
        if users.exists():
            self.create_student_profiles(users)
            self.create_subscriptions(users)
            self.create_student_progress()
            self.create_quiz_attempts()
            self.create_certificates()
            self.create_communication_data(users)
            self.create_scheduling_data(users)
            self.create_notifications(users)
            self.create_ai_data(users)
            self.create_job_postings(users)
            self.create_job_applications(users)
            self.create_library_items(users)

        self.stdout.write(self.style.SUCCESS('Successfully populated database with fake data!'))

    def clear_data(self):
        """Clear all data except users"""
        models_to_clear = [
            AIFeedback, AIMessage, AIConversation, AIUsageLimit,
            Notification, SessionBooking, LiveSession, Calendar,
            ForumPost, ForumTopic, ForumCategory, Message, ChatRoom,
            JobApplication, Certificate, QuizAttempt, Answer, Question, Quiz,
            Lecture, Subject, Semester, StudentProgress, StudentProfile,
            Subscription, LibraryItem, LibraryCategory,
            JobPosting, JobCategory,
            AcademicYear, University, SubscriptionPlan, NotificationTemplate, AIPromptTemplate
        ]

        for model in models_to_clear:
            model.objects.all().delete()

    def create_universities(self):
        universities_data = [
            {"name_ar": "جامعة القاهرة", "name_en": "Cairo University", "code": "CU", "city": "القاهرة"},
            {"name_ar": "جامعة عين شمس", "name_en": "Ain Shams University", "code": "ASU", "city": "القاهرة"},
            {"name_ar": "جامعة الإسكندرية", "name_en": "Alexandria University", "code": "AU", "city": "الإسكندرية"},
            {"name_ar": "جامعة المنصورة", "name_en": "Mansoura University", "code": "MU", "city": "المنصورة"},
            {"name_ar": "جامعة أسيوط", "name_en": "Assiut University", "code": "ASUT", "city": "أسيوط"},
            {"name_ar": "جامعة طنطا", "name_en": "Tanta University", "code": "TU", "city": "طنطا"},
            {"name_ar": "جامعة الزقازيق", "name_en": "Zagazig University", "code": "ZU", "city": "الزقازيق"},
            {"name_ar": "جامعة بني سويف", "name_en": "Beni Suef University", "code": "BSU", "city": "بني سويف"},
            {"name_ar": "جامعة حلوان", "name_en": "Helwan University", "code": "HU", "city": "القاهرة"},
            {"name_ar": "جامعة جنوب الوادي", "name_en": "South Valley University", "code": "SVU", "city": "قنا"},
        ]
        
        for uni_data in universities_data:
            University.objects.get_or_create(**uni_data)
        
        self.stdout.write(f'Created {len(universities_data)} universities')

    def create_academic_years(self):
        years_data = [
            {"year_number": 1, "year_name_ar": "السنة الأولى", "year_name_en": "First Year"},
            {"year_number": 2, "year_name_ar": "السنة الثانية", "year_name_en": "Second Year"},
            {"year_number": 3, "year_name_ar": "السنة الثالثة", "year_name_en": "Third Year"},
            {"year_number": 4, "year_name_ar": "السنة الرابعة", "year_name_en": "Fourth Year"},
            {"year_number": 5, "year_name_ar": "السنة الخامسة", "year_name_en": "Fifth Year"},
        ]
        
        for year_data in years_data:
            AcademicYear.objects.get_or_create(**year_data)
        
        self.stdout.write(f'Created {len(years_data)} academic years')

    def create_subscription_plans(self):
        plans_data = [
            {
                "name": "الخطة الأساسية",
                "price": 99.00,
                "duration_days": 30,
                "is_active": True
            },
            {
                "name": "الخطة المتقدمة",
                "price": 199.00,
                "duration_days": 30,
                "is_active": True
            },
            {
                "name": "الخطة السنوية",
                "price": 1999.00,
                "duration_days": 365,
                "is_active": True
            }
        ]

        for plan_data in plans_data:
            SubscriptionPlan.objects.get_or_create(
                name=plan_data["name"],
                defaults=plan_data
            )
        
        self.stdout.write(f'Created {len(plans_data)} subscription plans')

    def create_semesters(self):
        academic_years = AcademicYear.objects.all()
        semesters_data = [
            {"title_ar": "الفصل الدراسي الأول", "title_en": "First Semester", "order": 1},
            {"title_ar": "الفصل الدراسي الثاني", "title_en": "Second Semester", "order": 2},
        ]

        count = 0
        for year in academic_years:
            for sem_data in semesters_data:
                Semester.objects.get_or_create(
                    academic_year=year,
                    order=sem_data["order"],
                    defaults={
                        "title_ar": f"{sem_data['title_ar']} - {year.year_name_ar}",
                        "title_en": f"{sem_data['title_en']} - {year.year_name_en}",
                        "description_ar": f"وصف {sem_data['title_ar']} للسنة {year.year_name_ar}",
                        "is_published": True
                    }
                )
                count += 1

        self.stdout.write(f'Created {count} semesters')

    def create_subjects(self):
        semesters = Semester.objects.all()
        subjects_data = [
            {"name_ar": "القانون المدني", "name_en": "Civil Law", "code": "LAW101"},
            {"name_ar": "القانون الجنائي", "name_en": "Criminal Law", "code": "LAW102"},
            {"name_ar": "القانون الدستوري", "name_en": "Constitutional Law", "code": "LAW103"},
            {"name_ar": "القانون التجاري", "name_en": "Commercial Law", "code": "LAW104"},
            {"name_ar": "القانون الإداري", "name_en": "Administrative Law", "code": "LAW105"},
            {"name_ar": "قانون العمل", "name_en": "Labor Law", "code": "LAW106"},
            {"name_ar": "القانون الدولي", "name_en": "International Law", "code": "LAW107"},
            {"name_ar": "قانون الأحوال الشخصية", "name_en": "Personal Status Law", "code": "LAW108"},
        ]

        count = 0
        for semester in semesters:
            # Add 3-4 subjects per semester
            selected_subjects = random.sample(subjects_data, random.randint(3, 4))
            for subj_data in selected_subjects:
                Subject.objects.get_or_create(
                    code=f"{subj_data['code']}-{semester.id}",
                    defaults={
                        "name_ar": subj_data["name_ar"],
                        "name_en": subj_data["name_en"],
                        "description_ar": f"مقرر {subj_data['name_ar']} - {semester.title_ar}",
                        "semester": semester,
                        "academic_year": semester.academic_year,
                        "is_active": True
                    }
                )
                count += 1

        self.stdout.write(f'Created {count} subjects')

    def create_lectures(self):
        subjects = Subject.objects.all()

        count = 0
        for subject in subjects:
            # Create 8-12 lectures per subject
            num_lectures = random.randint(8, 12)
            for i in range(1, num_lectures + 1):
                Lecture.objects.get_or_create(
                    subject=subject,
                    order=i,
                    defaults={
                        "title_ar": f"المحاضرة {i}: {fake.sentence(nb_words=4)}",
                        "youtube_video_id": "dQw4w9WgXcQ",  # Rick Roll as placeholder
                        "duration_minutes": random.randint(30, 90),
                        "is_published": True
                    }
                )
                count += 1

        self.stdout.write(f'Created {count} lectures')

    def create_quizzes(self):
        lectures = Lecture.objects.all()

        count = 0
        for lecture in lectures:
            # 30% chance of having a quiz for each lecture
            if random.random() < 0.3:
                Quiz.objects.get_or_create(
                    lecture=lecture,
                    title_ar=f"اختبار - {lecture.title_ar}",
                    defaults={
                        "instructions_ar": f"اختبار تقييمي في {lecture.title_ar}",
                        "time_limit_minutes": random.randint(30, 60),
                        "passing_score": random.randint(60, 80),
                        "is_active": True
                    }
                )
                count += 1

        self.stdout.write(f'Created {count} quizzes')

    def create_questions_and_answers(self):
        quizzes = Quiz.objects.all()
        self.stdout.write(f'Found {quizzes.count()} quizzes to process')

        question_count = 0
        answer_count = 0

        for quiz_index, quiz in enumerate(quizzes):
            if quiz_index % 10 == 0:  # Log every 10 quizzes
                self.stdout.write(f'Processing quiz {quiz_index + 1}/{quizzes.count()}')

            # Create 5-10 questions per quiz
            num_questions = random.randint(5, 10)
            for i in range(1, num_questions + 1):
                try:
                    question = Question.objects.create(
                        quiz=quiz,
                        question_text_ar=f"السؤال {i}: {fake.sentence()}؟",
                        question_type="multiple_choice",
                        points=random.randint(5, 15),
                        order=i
                    )
                    question_count += 1

                    # Create 4 answers per question (1 correct, 3 wrong)
                    for j in range(1, 5):
                        Answer.objects.create(
                            question=question,
                            answer_text_ar=f"الإجابة {j}: {fake.sentence(nb_words=3)}",
                            is_correct=(j == 1),  # First answer is correct
                            order=j
                        )
                        answer_count += 1
                except Exception as e:
                    self.stdout.write(f'Error creating question for quiz {quiz.id}: {str(e)}')
                    continue

        self.stdout.write(f'Created {question_count} questions and {answer_count} answers')

    def create_library_data(self):
        # Create library categories
        categories_data = [
            {"name": "القوانين الأساسية", "description": "مجموعة القوانين الأساسية في النظام القانوني"},
            {"name": "الأحكام القضائية", "description": "أحكام المحاكم والقرارات القضائية"},
            {"name": "المراجع القانونية", "description": "الكتب والمراجع القانونية المتخصصة"},
            {"name": "البحوث والدراسات", "description": "البحوث والدراسات القانونية الحديثة"},
        ]

        lib_categories = []
        for cat_data in categories_data:
            lib_cat, created = LibraryCategory.objects.get_or_create(
                name=cat_data["name"],
                defaults={
                    "description": cat_data["description"]
                }
            )
            lib_categories.append(lib_cat)

        # Create library items (skip for now since it needs uploaded_by user)
        count = 0
        # We'll create library items later when we have users

        self.stdout.write(f'Created {len(lib_categories)} library categories and {count} library items')

    def create_career_data(self):
        # Create job categories
        job_categories_data = [
            {"name_ar": "المحاماة", "name_en": "Legal Practice", "description_ar": "وظائف في مجال المحاماة"},
            {"name_ar": "القضاء", "name_en": "Judiciary", "description_ar": "وظائف في السلك القضائي"},
            {"name_ar": "الاستشارات القانونية", "name_en": "Legal Consulting", "description_ar": "وظائف الاستشارات القانونية"},
            {"name_ar": "الشركات", "name_en": "Corporate", "description_ar": "وظائف قانونية في الشركات"},
        ]

        job_categories = []
        for cat_data in job_categories_data:
            category, created = JobCategory.objects.get_or_create(
                name_en=cat_data["name_en"],
                defaults=cat_data
            )
            job_categories.append(category)

        # Create job postings (skip for now since it needs posted_by user)
        count = 0
        # We'll create job postings later when we have users

        self.stdout.write(f'Created {len(job_categories)} job categories and {count} job postings')

    def create_notification_templates(self):
        templates_data = [
            {
                "name": "welcome_student",
                "subject_ar": "مرحباً بك في المنصة",
                "content_ar": "مرحباً {user_name}، نرحب بك في منصة القانوني التعليمية",
                "notification_type": "in_app"
            },
            {
                "name": "quiz_reminder",
                "subject_ar": "تذكير بالاختبار",
                "content_ar": "لديك اختبار في مادة {subject_name} غداً",
                "notification_type": "in_app"
            },
            {
                "name": "new_lecture",
                "subject_ar": "محاضرة جديدة",
                "content_ar": "تم إضافة محاضرة جديدة في مادة {subject_name}",
                "notification_type": "in_app"
            }
        ]

        for template_data in templates_data:
            NotificationTemplate.objects.get_or_create(
                name=template_data["name"],
                defaults=template_data
            )

        self.stdout.write(f'Created {len(templates_data)} notification templates')

    def create_ai_prompt_templates(self):
        templates_data = [
            {
                "name": "legal_summary",
                "template_text": "لخص هذا النص القانوني: {text}",
                "category": "legal_summary",
                "is_active": True
            },
            {
                "name": "law_explanation",
                "template_text": "اشرح هذا القانون بطريقة مبسطة: {law_text}",
                "category": "law_explanation",
                "is_active": True
            },
            {
                "name": "quiz_help",
                "template_text": "ساعدني في فهم هذا السؤال: {question}",
                "category": "quiz_help",
                "is_active": True
            }
        ]

        for template_data in templates_data:
            AIPromptTemplate.objects.get_or_create(
                name=template_data["name"],
                defaults=template_data
            )

        self.stdout.write(f'Created {len(templates_data)} AI prompt templates')

    def create_test_users(self):
        """Create test users for the system"""
        users_created = 0

        # Create admin user
        if not CustomUser.objects.filter(email='<EMAIL>').exists():
            admin = CustomUser.objects.create_user(
                email='<EMAIL>',
                first_name='أحمد',
                last_name='الإداري',
                password='admin123',
                is_staff=True,
                is_superuser=True,
                email_verified=True
            )
            users_created += 1

        # Create instructor users
        for i in range(3):
            email = f'instructor{i+1}@preqanoony.com'
            if not CustomUser.objects.filter(email=email).exists():
                instructor = CustomUser.objects.create_user(
                    email=email,
                    first_name=fake.first_name(),
                    last_name=fake.last_name(),
                    password='instructor123',
                    is_instructor=True,
                    email_verified=True
                )
                users_created += 1

        # Create student users
        for i in range(10):
            email = f'student{i+1}@preqanoony.com'
            if not CustomUser.objects.filter(email=email).exists():
                student = CustomUser.objects.create_user(
                    email=email,
                    first_name=fake.first_name(),
                    last_name=fake.last_name(),
                    password='student123',
                    is_student=True,
                    email_verified=True
                )
                users_created += 1

        self.stdout.write(f'Created {users_created} test users')

    def create_student_profiles(self, users):
        universities = University.objects.all()
        academic_years = AcademicYear.objects.all()

        count = 0
        for user in users.filter(is_student=True):
            if not hasattr(user, 'student_profile'):
                StudentProfile.objects.create(
                    user=user,
                    university=random.choice(universities),
                    academic_year=random.choice(academic_years),
                    student_id=f"STU{random.randint(100000, 999999)}"
                )
                count += 1

        self.stdout.write(f'Created {count} student profiles')

    def create_subscriptions(self, users):
        plans = SubscriptionPlan.objects.all()

        count = 0
        for user in users:
            # 70% chance of having a subscription
            if random.random() < 0.7:
                plan = random.choice(plans)
                start_date = timezone.now().date() - timedelta(days=random.randint(0, 30))
                Subscription.objects.get_or_create(
                    user=user,
                    defaults={
                        "plan": plan,
                        "start_date": start_date,
                        "end_date": start_date + timedelta(days=plan.duration_days),
                        "status": random.choice(['active', 'expired', 'cancelled']),
                        "is_active": random.choice([True, False])
                    }
                )
                count += 1

        self.stdout.write(f'Created {count} subscriptions')

    def create_student_progress(self):
        students = StudentProfile.objects.all()
        semesters = Semester.objects.all()

        count = 0
        for student in students:
            # Create progress for 1-2 semesters per student
            selected_semesters = random.sample(list(semesters), min(2, len(semesters)))
            for semester in selected_semesters:
                total_lectures = random.randint(20, 40)
                StudentProgress.objects.create(
                    student=student,
                    semester=semester,
                    completed_lectures=random.randint(0, total_lectures),
                    total_lectures=total_lectures,
                    quiz_scores={
                        "quiz1": random.randint(60, 100),
                        "quiz2": random.randint(60, 100),
                        "midterm": random.randint(50, 95)
                    }
                )
                count += 1

        self.stdout.write(f'Created {count} student progress records')

    def create_quiz_attempts(self):
        students = StudentProfile.objects.all()
        quizzes = Quiz.objects.all()

        count = 0
        for student in students:
            # Each student attempts 3-5 random quizzes
            selected_quizzes = random.sample(list(quizzes), min(random.randint(3, 5), len(quizzes)))
            for quiz in selected_quizzes:
                # Calculate total points from questions
                total_points = quiz.questions.aggregate(total=models.Sum('points'))['total'] or 100
                score = random.randint(30, total_points)
                QuizAttempt.objects.create(
                    student=student,
                    quiz=quiz,
                    score=score,
                    total_points=total_points,
                    completed_at=timezone.now() - timedelta(days=random.randint(1, 30)),
                    answers={"q1": "a", "q2": "b", "q3": "c"}  # Simplified
                )
                count += 1

        self.stdout.write(f'Created {count} quiz attempts')

    def create_certificates(self):
        students = StudentProfile.objects.all()
        semesters = Semester.objects.all()

        count = 0
        for student in students:
            # 30% chance of having certificates
            if random.random() < 0.3:
                semester = random.choice(semesters)
                Certificate.objects.create(
                    student=student,
                    semester=semester,
                    certificate_id=f"CERT{random.randint(100000, 999999)}"
                )
                count += 1

        self.stdout.write(f'Created {count} certificates')

    def create_communication_data(self, users):
        # Create forum categories (need subjects first)
        subjects = Subject.objects.all()
        if not subjects.exists():
            self.stdout.write('No subjects found, skipping forum categories')
            return

        forum_categories = []
        for subject in subjects[:3]:  # Create forum categories for first 3 subjects
            category, created = ForumCategory.objects.get_or_create(
                subject=subject,
                defaults={
                    "name_ar": f"منتدى {subject.name_ar}",
                    "description_ar": f"منتدى مناقشة مادة {subject.name_ar}",
                    "order": len(forum_categories) + 1,
                    "is_active": True
                }
            )
            forum_categories.append(category)

        # Create forum topics and posts
        topic_count = 0
        post_count = 0

        for category in forum_categories:
            for i in range(3, 6):  # 3-5 topics per category
                author = random.choice(users)
                topic = ForumTopic.objects.create(
                    title_ar=f"موضوع {i}: {fake.sentence()}",
                    content_ar=fake.text(max_nb_chars=300),
                    category=category,
                    author=author,
                    is_pinned=random.choice([True, False])
                )
                topic_count += 1

                # Create 2-5 posts per topic
                for j in range(2, 6):
                    ForumPost.objects.create(
                        topic=topic,
                        content_ar=fake.text(max_nb_chars=200),
                        author=random.choice(users)
                    )
                    post_count += 1

        self.stdout.write(f'Created {len(forum_categories)} forum categories, {topic_count} topics, {post_count} posts')

    def create_scheduling_data(self, users):
        instructors = users.filter(is_instructor=True)
        students = users.filter(is_student=True)
        semesters = Semester.objects.all()

        # Create calendar events for users
        calendar_count = 0
        for user in users:
            # Create 2-3 calendar events per user
            for i in range(2, 4):
                event_date = timezone.now() + timedelta(days=random.randint(1, 30), hours=random.randint(9, 17))
                Calendar.objects.create(
                    user=user,
                    title_ar=f"حدث {i}: {fake.sentence(nb_words=3)}",
                    description_ar=fake.text(max_nb_chars=200),
                    event_date=event_date,
                    event_type=random.choice(['live_class', 'quiz', 'subscription_expiry']),
                    is_reminder_sent=random.choice([True, False])
                )
                calendar_count += 1

        # Create live sessions
        session_count = 0
        for instructor in instructors:
            for i in range(2, 5):  # 2-4 sessions per instructor
                scheduled_date = timezone.now() + timedelta(days=random.randint(1, 30), hours=random.randint(9, 17))
                LiveSession.objects.create(
                    title_ar=f"جلسة مباشرة: {fake.sentence(nb_words=3)}",
                    description_ar=fake.text(max_nb_chars=200),
                    instructor=instructor,
                    scheduled_date=scheduled_date,
                    duration_minutes=random.randint(60, 120),
                    max_participants=random.randint(20, 50),
                    meeting_link=f"https://meet.example.com/session-{random.randint(1000, 9999)}",
                    status=random.choice(['scheduled', 'ongoing', 'completed', 'cancelled']),
                    semester=random.choice(semesters) if semesters else None
                )
                session_count += 1

        self.stdout.write(f'Created {calendar_count} calendar events and {session_count} live sessions')

    def create_notifications(self, users):
        templates = NotificationTemplate.objects.all()

        count = 0
        for user in users:
            # Create 2-5 notifications per user
            for i in range(2, 6):
                template = random.choice(templates) if templates else None
                Notification.objects.create(
                    recipient=user,
                    subject_ar=f"إشعار {i}: {fake.sentence(nb_words=3)}",
                    content_ar=fake.text(max_nb_chars=150),
                    notification_type=random.choice(['email', 'in_app', 'sms']),
                    template=template,
                    status=random.choice(['pending', 'sent', 'failed'])
                )
                count += 1

        self.stdout.write(f'Created {count} notifications')

    def create_ai_data(self, users):
        students = StudentProfile.objects.all()
        semesters = Semester.objects.all()

        # Create AI usage limits for students
        usage_count = 0
        for student in students:
            AIUsageLimit.objects.get_or_create(
                student=student,
                defaults={
                    "daily_limit": random.randint(10, 30),
                    "monthly_limit": random.randint(100, 300),
                    "daily_used": random.randint(0, 10),
                    "monthly_used": random.randint(0, 50),
                    "last_reset_date": timezone.now().date()
                }
            )
            usage_count += 1

        # Create AI conversations
        conversation_count = 0
        message_count = 0

        for student in students[:10]:  # Only for first 10 students to avoid too much data
            for i in range(1, 3):  # 1-2 conversations per student
                conversation = AIConversation.objects.create(
                    student=student,
                    title_ar=f"محادثة {i}: {fake.sentence(nb_words=3)}",
                    context_type=random.choice(['legal_question', 'civil_law', 'criminal_law', 'general']),
                    semester=random.choice(semesters) if semesters else None,
                    is_active=True
                )
                conversation_count += 1

                # Create 2-5 messages per conversation
                for j in range(2, 6):
                    # User message
                    AIMessage.objects.create(
                        conversation=conversation,
                        message_type='user',
                        content_ar=fake.sentence() + "؟",
                        tokens_used=random.randint(10, 50),
                        response_time_seconds=0.1
                    )
                    message_count += 1

                    # Assistant message
                    AIMessage.objects.create(
                        conversation=conversation,
                        message_type='assistant',
                        content_ar=fake.text(max_nb_chars=200),
                        tokens_used=random.randint(50, 200),
                        response_time_seconds=random.uniform(1.0, 5.0)
                    )
                    message_count += 1

        self.stdout.write(f'Created {usage_count} usage limits, {conversation_count} conversations, {message_count} messages')

    def create_job_applications(self, users):
        students = users.filter(is_student=True)
        job_postings = JobPosting.objects.all()

        count = 0
        for student in students:
            # 30% chance of applying to jobs
            if random.random() < 0.3 and job_postings:
                # Apply to 1-2 jobs
                selected_jobs = random.sample(list(job_postings), min(random.randint(1, 2), len(job_postings)))
                for job in selected_jobs:
                    try:
                        student_profile = student.student_profile
                        JobApplication.objects.create(
                            job_posting=job,
                            student=student_profile,
                            cover_letter_ar=fake.text(max_nb_chars=300),
                            status=random.choice(['pending', 'accepted', 'rejected']),
                            applied_at=timezone.now() - timedelta(days=random.randint(1, 30))
                        )
                        count += 1
                    except:
                        pass  # Skip if student doesn't have profile

        self.stdout.write(f'Created {count} job applications')

    def create_library_items(self, users):
        lib_categories = LibraryCategory.objects.all()

        count = 0
        for lib_cat in lib_categories:
            for i in range(5, 10):  # 5-9 items per category
                LibraryItem.objects.create(
                    title=f"{fake.sentence(nb_words=3)} - {lib_cat.name}",
                    description=fake.text(max_nb_chars=300),
                    category=lib_cat,
                    item_type=random.choice(['book', 'summary', 'exam']),
                    uploaded_by=random.choice(users),
                    is_approved=random.choice([True, False])
                )
                count += 1

        self.stdout.write(f'Created {count} library items')

    def create_job_postings(self, users):
        job_categories = JobCategory.objects.all()

        count = 0
        for category in job_categories:
            for i in range(3, 6):  # 3-5 jobs per category
                JobPosting.objects.create(
                    title_ar=f"{fake.job()} - {category.name_ar}",
                    description_ar=fake.text(max_nb_chars=500),
                    company_name=fake.company(),
                    location=random.choice(["القاهرة", "الإسكندرية", "الجيزة", "المنصورة"]),
                    job_type=random.choice(['internship', 'training', 'full_time']),
                    category=category,
                    requirements_ar=fake.text(max_nb_chars=200),
                    salary_range=f"{random.randint(3000, 15000)} - {random.randint(15000, 30000)} جنيه",
                    application_deadline=timezone.now().date() + timedelta(days=random.randint(7, 60)),
                    contact_email=fake.email(),
                    contact_phone=fake.phone_number(),
                    posted_by=random.choice(users),
                    is_active=True
                )
                count += 1

        self.stdout.write(f'Created {count} job postings')
