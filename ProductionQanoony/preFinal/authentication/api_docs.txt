# Authentication API Documentation

## 1. Registration
- **Endpoint:** /api/auth/register/
- **Method:** POST
- **Description:** Register a new user and send email verification token.
- **Permissions:** AllowAny
- **Request Example:**
{
  "email": "<EMAIL>",
  "first_name": "User",
  "last_name": "Test",
  "phone_number": "01000000000",
  "password": "TestPass1234",
  "profile_image": null
}
- **Response Example:**
201 CREATED
{
  "email": "<EMAIL>",
  "first_name": "User",
  "last_name": "Test",
  "phone_number": "01000000000",
  "profile_image": null
}

## 2. Email Verification
- **Endpoint:** /api/auth/verify-email/
- **Method:** POST
- **Description:** Verify user email using token.
- **Permissions:** AllowAny (only for unverified users)
- **Request Example:**
{
  "token": "verification-token"
}
- **Response Example:**
200 OK
{
  "detail": "تم تأكيد البريد الإلكتروني بنجاح"
}

## 3. Login
- **Endpoint:** /api/auth/login/
- **Method:** POST
- **Description:** Login with email and password (JWT).
- **Permissions:** AllowAny
- **Request Example:**
{
  "email": "<EMAIL>",
  "password": "TestPass1234"
}
- **Response Example:**
200 OK
{
  "refresh": "<refresh_token>",
  "access": "<access_token>"
}

## 4. Logout
- **Endpoint:** /api/auth/logout/
- **Method:** POST
- **Description:** Logout user (JWT/session-based).
- **Permissions:** IsAuthenticated
- **Request Example:**
POST (no body)
- **Response Example:**
200 OK
{
  "detail": "تم تسجيل الخروج بنجاح"
}

## 5. Profile
- **Endpoint:** /api/auth/profile/
- **Method:** GET, PATCH
- **Description:** View or update user profile.
- **Permissions:** IsAuthenticated
- **Request Example (PATCH):**
{
  "first_name": "Updated"
}
- **Response Example (GET):**
200 OK
{
  "id": "<uuid>",
  "email": "<EMAIL>",
  "first_name": "Updated",
  "last_name": "Test",
  "phone_number": "01000000000",
  "profile_image": null,
  "is_student": false,
  "is_instructor": false,
  "email_verified": true,
  "date_joined": "2024-06-01T12:00:00Z",
  "last_login": null
}

## 6. Password Reset Request
- **Endpoint:** /api/auth/password-reset/request/
- **Method:** POST
- **Description:** Request password reset (sends token to email).
- **Permissions:** AllowAny
- **Request Example:**
{
  "email": "<EMAIL>"
}
- **Response Example:**
200 OK
{
  "detail": "إذا كان البريد الإلكتروني موجودًا، سيتم إرسال رمز إعادة التعيين"
}

## 7. Password Reset Confirm
- **Endpoint:** /api/auth/password-reset/confirm/
- **Method:** POST
- **Description:** Reset password using token.
- **Permissions:** AllowAny
- **Request Example:**
{
  "token": "reset-token",
  "new_password": "NewPass1234"
}
- **Response Example:**
200 OK
{
  "detail": "تم إعادة تعيين كلمة المرور بنجاح"
}

## 8. Student Application (طلب التحاق طالب)

### Submit or View Application
- **Endpoint:** /api/auth/student-application/
- **Methods:**
  - POST: Submit a new student application (requires payment screenshot)
  - GET: View your application status
- **Permissions:** IsAuthenticated
- **Request Example (POST):**
```
Content-Type: multipart/form-data
{
  "payment_screenshot": <image file>
}
```
- **Response Example (POST):**
201 CREATED
```
{
  "id": 1,
  "user": 1,
  "payment_screenshot": "http://.../media/student_applications/xyz.png",
  "is_approved": false,
  "approved_at": null,
  "approved_by": null,
  "rejection_reason": "",
  "created_at": "2025-06-13T23:06:03.425442Z"
}
```
- **Response Example (GET):**
200 OK
```
{
  "id": 1,
  "user": 1,
  "payment_screenshot": "http://.../media/student_applications/xyz.png",
  "is_approved": false,
  "approved_at": null,
  "approved_by": null,
  "rejection_reason": "",
  "created_at": "2025-06-13T23:06:03.425442Z"
}
```
- **Error Example (Duplicate):**
400 BAD REQUEST
```
{"non_field_errors": ["لقد قمت بتقديم طلب بالفعل"]}
```

### Admin Approve/Reject Application
- **Endpoint:** /api/auth/student-application/<pk>/<action>/
  - `<pk>`: Application ID
  - `<action>`: `approve` or `reject`
- **Method:** POST
- **Permissions:** IsAdminUser
- **Request Example (Approve):**
```
POST /api/auth/student-application/1/approve/
```
- **Request Example (Reject):**
```
POST /api/auth/student-application/1/reject/
{
  "rejection_reason": "مرفوض"
}
```
- **Response Example (Approve):**
200 OK
```
{"status": "تمت الموافقة على الطلب"}
```
- **Response Example (Reject):**
200 OK
```
{"status": "تم رفض الطلب", "reason": "مرفوض"}
```
- **Behavior:**
  - After approval, the student's `is_student` flag is set to true and the screenshot is deleted.
  - After rejection, the screenshot is deleted and the rejection reason is saved. 