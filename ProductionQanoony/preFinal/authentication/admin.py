from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import CustomUser, EmailVerification, StudentApplication, InstructorApplication, PaymentMethods

@admin.register(CustomUser)
class CustomUserAdmin(BaseUserAdmin):
    list_display = ('email', 'first_name', 'last_name', 'is_student', 'is_instructor', 'email_verified', 'is_active', 'is_staff')
    list_filter = ('is_student', 'is_instructor', 'email_verified', 'is_active', 'is_staff')
    search_fields = ('email', 'first_name', 'last_name')
    ordering = ('email',)
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'phone_number', 'profile_image')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'is_student', 'is_instructor', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
        ('Verification', {'fields': ('email_verified',)}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'first_name', 'last_name', 'password1', 'password2', 'is_student', 'is_instructor', 'is_active', 'is_staff', 'is_superuser'),
        }),
    )

@admin.register(EmailVerification)
class EmailVerificationAdmin(admin.ModelAdmin):
    list_display = ('user', 'token', 'created_at', 'is_used')
    list_filter = ('is_used', 'created_at')
    search_fields = ('user__email', 'token')

@admin.register(StudentApplication)
class StudentApplicationAdmin(admin.ModelAdmin):
    list_display = ('user', 'created_at', 'is_approved', 'approved_by', 'approved_at', 'payment_screenshot_thumb')
    list_filter = ('is_approved', 'created_at', 'approved_at')
    search_fields = ('user__email',)
    ordering = ('-created_at',)

    def payment_screenshot_thumb(self, obj):
        if obj.payment_screenshot:
            return f'<img src="{obj.payment_screenshot.url}" width="60" height="60" />'
        return '—'
    payment_screenshot_thumb.short_description = 'إيصال الدفع'
    payment_screenshot_thumb.allow_tags = True

@admin.register(InstructorApplication)
class InstructorApplicationAdmin(admin.ModelAdmin):
    list_display = ('id', 'email', 'first_name', 'last_name', 'phone_number', 'specialty', 'is_approved', 'approved_by', 'approved_at', 'created_at')
    list_filter = ('is_approved', 'created_at', 'approved_at')
    search_fields = ('email', 'first_name', 'last_name', 'phone_number', 'specialty')
    ordering = ('-created_at',)


@admin.register(PaymentMethods)
class PaymentMethodsAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'is_active', 'instapay_enabled', 'vodafone_cash_enabled', 'bank_enabled', 'updated_at')
    list_filter = ('is_active', 'instapay_enabled', 'vodafone_cash_enabled', 'bank_enabled', 'updated_at')
    search_fields = ('instapay_number', 'vodafone_cash_number', 'bank_name', 'account_number')
    ordering = ('-updated_at',)

    fieldsets = (
        ('طرق الدفع الإلكترونية', {
            'fields': (
                ('instapay_number', 'instapay_enabled'),
                ('vodafone_cash_number', 'vodafone_cash_enabled'),
            )
        }),
        ('بيانات الحساب البنكي', {
            'fields': (
                'bank_name',
                ('account_number', 'account_holder_name'),
                ('iban', 'swift_code'),
                'bank_enabled',
            )
        }),
        ('بيانات الاتصال', {
            'fields': (
                ('phone_number', 'email'),
                'address',
                'working_hours',
            )
        }),
        ('صفحات التواصل الاجتماعي', {
            'fields': (
                'facebook_url',
                'twitter_url',
                'instagram_url',
                'linkedin_url',
                'youtube_url',
            )
        }),
        ('تعليمات الدفع', {
            'fields': ('payment_instructions',)
        }),
        ('إعدادات', {
            'fields': ('is_active',)
        }),
    )

    def has_add_permission(self, request):
        # السماح بإضافة instance واحد فقط
        if PaymentMethods.objects.exists():
            return False
        return super().has_add_permission(request)

    def has_delete_permission(self, request, obj=None):
        # منع حذف البيانات
        return False
