from rest_framework import serializers
from .models import CustomUser

class ProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = (
            'id', 'email', 'first_name', 'last_name', 'phone_number', 'profile_image',
            'is_student', 'is_instructor', 'email_verified', 'date_joined', 'last_login'
        )
        read_only_fields = (
            'email', 'is_student', 'is_instructor',
            'email_verified', 'date_joined', 'last_login'
        ) 