from rest_framework import permissions


class IsEmailVerified(permissions.BasePermission):
    """Allows access only to users with verified email."""
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.email_verified


class IsUnverified(permissions.BasePermission):
    """Allows access only to users who are not yet email verified."""
    def has_permission(self, request, view):
        return request.user.is_authenticated and not request.user.email_verified


class IsInstructor(permissions.BasePermission):
    """
    Permission class للتحقق من أن المستخدم مدرس مفعل
    يسمح للمدرسين المفعلين فقط
    """
    message = 'يجب أن تكون مدرساً مفعلاً للوصول إلى هذا المحتوى'

    def has_permission(self, request, view):
        """التحقق من صلاحيات المستخدم"""
        if not request.user or not request.user.is_authenticated:
            self.message = 'يجب تسجيل الدخول أولاً'
            return False

        if not request.user.is_active:
            self.message = 'حسابك غير مفعل، يرجى التواصل مع الإدارة'
            return False

        if not request.user.is_instructor:
            self.message = 'يجب أن تكون مدرساً للوصول إلى هذا المحتوى'
            return False

        return True


class IsInstructorOrAdmin(permissions.BasePermission):
    """
    Permission class للتحقق من أن المستخدم مدرس أو أدمن
    يسمح للمدرسين والأدمن فقط
    """
    message = 'يجب أن تكون مدرساً أو مديراً للوصول إلى هذا المحتوى'

    def has_permission(self, request, view):
        """التحقق من صلاحيات المستخدم"""
        if not request.user or not request.user.is_authenticated:
            self.message = 'يجب تسجيل الدخول أولاً'
            return False

        if not request.user.is_active:
            self.message = 'حسابك غير مفعل، يرجى التواصل مع الإدارة'
            return False

        # يسمح للمدرسين أو الأدمن
        if request.user.is_instructor or request.user.is_staff:
            return True

        self.message = 'يجب أن تكون مدرساً أو مديراً للوصول إلى هذا المحتوى'
        return False


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Permission class للتحقق من ملكية الكائن
    يسمح للمالك أو الأدمن فقط
    """
    message = 'ليس لديك صلاحية للوصول إلى هذا المحتوى'

    def has_permission(self, request, view):
        """التحقق من صلاحيات المستخدم الأساسية"""
        if not request.user or not request.user.is_authenticated:
            self.message = 'يجب تسجيل الدخول أولاً'
            return False

        if not request.user.is_active:
            self.message = 'حسابك غير مفعل، يرجى التواصل مع الإدارة'
            return False

        return True

    def has_object_permission(self, request, view, obj):
        """التحقق من ملكية الكائن المحدد"""
        # الأدمن يمكنه الوصول لكل شيء
        if request.user.is_staff:
            return True

        # التحقق من الملكية حسب نوع الكائن
        if hasattr(obj, 'user'):
            # إذا كان الكائن له user field
            return obj.user == request.user
        elif hasattr(obj, 'instructor'):
            # إذا كان الكائن له instructor field
            return obj.instructor == request.user
        elif hasattr(obj, 'student'):
            # إذا كان الكائن له student field وهو مدرس
            if request.user.is_instructor:
                # التحقق من أن الطالب مرتبط بالمدرس
                from communication.models import ChatRoom
                from courses.models import PrivateTutorSession
                from scheduling.models import SessionBooking

                # فحص المحادثات
                if ChatRoom.objects.filter(instructor=request.user, student=obj.student).exists():
                    return True

                # فحص الجلسات الخاصة
                if PrivateTutorSession.objects.filter(instructor=request.user, student=obj.student).exists():
                    return True

                # فحص الجلسات المباشرة
                if SessionBooking.objects.filter(session__instructor=request.user, student=obj.student).exists():
                    return True

            return obj.student.user == request.user
        else:
            # إذا كان الكائن هو المستخدم نفسه
            return obj == request.user

        self.message = 'ليس لديك صلاحية للوصول إلى هذا المحتوى'
        return False


class IsStudentOrAdmin(permissions.BasePermission):
    """
    Permission class للتحقق من أن المستخدم طالب أو أدمن
    يسمح للطلاب والأدمن فقط
    """
    message = 'يجب أن تكون طالباً أو مديراً للوصول إلى هذا المحتوى'

    def has_permission(self, request, view):
        """التحقق من صلاحيات المستخدم"""
        if not request.user or not request.user.is_authenticated:
            self.message = 'يجب تسجيل الدخول أولاً'
            return False

        if not request.user.is_active:
            self.message = 'حسابك غير مفعل، يرجى التواصل مع الإدارة'
            return False

        # يسمح للطلاب أو الأدمن
        if request.user.is_student or request.user.is_staff:
            return True

        self.message = 'يجب أن تكون طالباً أو مديراً للوصول إلى هذا المحتوى'
        return False


class IsAdminOnly(permissions.BasePermission):
    """
    Permission class للأدمن فقط
    يسمح للأدمن فقط
    """
    message = 'يجب أن تكون مديراً للوصول إلى هذا المحتوى'

    def has_permission(self, request, view):
        """التحقق من صلاحيات المستخدم"""
        if not request.user or not request.user.is_authenticated:
            self.message = 'يجب تسجيل الدخول أولاً'
            return False

        if not request.user.is_active:
            self.message = 'حسابك غير مفعل، يرجى التواصل مع الإدارة'
            return False

        if not request.user.is_staff:
            self.message = 'يجب أن تكون مديراً للوصول إلى هذا المحتوى'
            return False

        return True


class IsActiveUser(permissions.BasePermission):
    """
    Permission class للمستخدمين المفعلين فقط
    يسمح للمستخدمين المفعلين فقط
    """
    message = 'حسابك غير مفعل، يرجى التواصل مع الإدارة'

    def has_permission(self, request, view):
        """التحقق من صلاحيات المستخدم"""
        if not request.user or not request.user.is_authenticated:
            self.message = 'يجب تسجيل الدخول أولاً'
            return False

        if not request.user.is_active:
            self.message = 'حسابك غير مفعل، يرجى التواصل مع الإدارة'
            return False

        return True