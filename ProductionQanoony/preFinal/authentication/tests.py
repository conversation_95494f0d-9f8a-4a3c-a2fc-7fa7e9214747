from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from .models import CustomUser, EmailVerification, StudentApplication
from django.test.utils import override_settings
from django.core.files.uploadedfile import SimpleUploadedFile
from io import BytesIO
from PIL import Image

# Create your tests here.

@override_settings(DEFAULT_FILE_STORAGE='django.core.files.storage.FileSystemStorage')
class AuthenticationTests(APITestCase):
    def setUp(self):
        self.register_url = reverse('register')
        self.verify_email_url = reverse('verify-email')
        self.login_url = reverse('login')
        self.profile_url = reverse('profile')
        self.password_reset_request_url = reverse('password-reset-request')
        self.password_reset_confirm_url = reverse('password-reset-confirm')
        self.user_data = {
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'phone_number': '01000000000',
            'password': 'TestPass1234',
        }

    def create_valid_image(self):
        image_io = BytesIO()
        image = Image.new('RGB', (1, 1), color='white')
        image.save(image_io, format='PNG')
        image_io.seek(0)
        return SimpleUploadedFile("receipt.png", image_io.read(), content_type="image/png")

    def test_registration(self):
        response = self.client.post(self.register_url, self.user_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(CustomUser.objects.filter(email=self.user_data['email']).exists())

    def test_email_verification(self):
        self.client.post(self.register_url, self.user_data)
        user = CustomUser.objects.get(email=self.user_data['email'])
        token = EmailVerification.objects.filter(user=user).first().token
        response = self.client.post(self.verify_email_url, {'token': token})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        user.refresh_from_db()
        self.assertTrue(user.email_verified)

    def test_login(self):
        self.client.post(self.register_url, self.user_data)
        user = CustomUser.objects.get(email=self.user_data['email'])
        user.email_verified = True
        user.save()
        response = self.client.post(self.login_url, {'email': self.user_data['email'], 'password': self.user_data['password']})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)

    def test_profile_update(self):
        self.client.post(self.register_url, self.user_data)
        user = CustomUser.objects.get(email=self.user_data['email'])
        user.email_verified = True
        user.save()
        login_response = self.client.post(self.login_url, {'email': self.user_data['email'], 'password': self.user_data['password']})
        token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + token)
        response = self.client.patch(self.profile_url, {'first_name': 'Updated'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        user.refresh_from_db()
        self.assertEqual(user.first_name, 'Updated')

    def test_profile_update_unauthorized(self):
        # Not authenticated
        user = CustomUser.objects.create_user(email='<EMAIL>', first_name='Other', last_name='User', password='OtherPass1234', is_student=True)
        response = self.client.patch(self.profile_url, {'first_name': 'Hacker'})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_profile_update_forbidden(self):
        # Authenticated as another user
        self.client.post(self.register_url, self.user_data)
        user2 = CustomUser.objects.create_user(email='<EMAIL>', first_name='Other', last_name='User', password='OtherPass1234', is_student=True, email_verified=True)
        login_response = self.client.post(self.login_url, {'email': '<EMAIL>', 'password': 'OtherPass1234'})
        token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + token)
        response = self.client.patch(self.profile_url, {'first_name': 'Hacker'})
        # Should be 200 OK because only the authenticated user's profile is updated
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_login_invalid_credentials(self):
        response = self.client.post(self.login_url, {'email': '<EMAIL>', 'password': 'wrongpass'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_email_verification_invalid_token(self):
        response = self.client.post(self.verify_email_url, {'token': 'invalidtoken'})
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_password_reset(self):
        self.client.post(self.register_url, self.user_data)
        user = CustomUser.objects.get(email=self.user_data['email'])
        user.email_verified = True
        user.save()
        # Request reset
        response = self.client.post(self.password_reset_request_url, {'email': self.user_data['email']})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        token = EmailVerification.objects.filter(user=user, is_used=False).last().token
        # Confirm reset
        response = self.client.post(self.password_reset_confirm_url, {'token': token, 'new_password': 'NewPass1234'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        user.refresh_from_db()
        self.assertTrue(user.check_password('NewPass1234'))

    def test_password_reset_invalid_email(self):
        response = self.client.post(self.password_reset_request_url, {'email': '<EMAIL>'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)  # Should not reveal if email exists

    def test_password_reset_invalid_token(self):
        response = self.client.post(self.password_reset_confirm_url, {'token': 'invalidtoken', 'new_password': 'NewPass1234'})
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_profile_get_unauthenticated(self):
        response = self.client.get(self.profile_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_profile_get_nonexistent(self):
        # Authenticated but no profile (simulate by deleting user)
        self.client.post(self.register_url, self.user_data)
        user = CustomUser.objects.get(email=self.user_data['email'])
        user.email_verified = True
        user.save()
        login_response = self.client.post(self.login_url, {'email': self.user_data['email'], 'password': self.user_data['password']})
        token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + token)
        user.delete()
        response = self.client.get(self.profile_url)
        self.assertIn(response.status_code, [status.HTTP_404_NOT_FOUND, status.HTTP_401_UNAUTHORIZED])

    def test_student_application_submission(self):
        self.client.post(self.register_url, self.user_data)
        user = CustomUser.objects.get(email=self.user_data['email'])
        user.email_verified = True
        user.is_student = True
        user.save()
        login_response = self.client.post(self.login_url, {'email': self.user_data['email'], 'password': self.user_data['password']})
        token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + token)
        url = reverse('student-application')
        img = self.create_valid_image()
        data = {'payment_screenshot': img}
        response = self.client.post(url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(StudentApplication.objects.filter(user=user).exists())

    def test_student_application_duplicate(self):
        self.client.post(self.register_url, self.user_data)
        user = CustomUser.objects.get(email=self.user_data['email'])
        user.email_verified = True
        user.is_student = True
        user.save()
        login_response = self.client.post(self.login_url, {'email': self.user_data['email'], 'password': self.user_data['password']})
        token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + token)
        url = reverse('student-application')
        img = self.create_valid_image()
        data = {'payment_screenshot': img}
        self.client.post(url, data, format='multipart')
        img2 = self.create_valid_image()
        data2 = {'payment_screenshot': img2}
        response = self.client.post(url, data2, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_student_application_admin_approve(self):
        # Create student and application
        self.client.post(self.register_url, self.user_data)
        user = CustomUser.objects.get(email=self.user_data['email'])
        user.email_verified = True
        user.is_student = True
        user.save()
        login_response = self.client.post(self.login_url, {'email': self.user_data['email'], 'password': self.user_data['password']})
        token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + token)
        url = reverse('student-application')
        img = self.create_valid_image()
        data = {'payment_screenshot': img}
        self.client.post(url, data, format='multipart')
        app = StudentApplication.objects.get(user=user)
        # Create admin
        admin = CustomUser.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='AdminPass1234')
        self.client.force_authenticate(user=admin)
        action_url = reverse('student-application-admin-action', args=[app.pk, 'approve'])
        response = self.client.post(action_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        app.refresh_from_db()
        self.assertTrue(app.is_approved)
        self.assertTrue(app.user.is_student)
        self.assertFalse(app.payment_screenshot)

    def test_student_application_admin_reject(self):
        # Create student and application
        self.client.post(self.register_url, self.user_data)
        user = CustomUser.objects.get(email=self.user_data['email'])
        user.email_verified = True
        user.is_student = True
        user.save()
        login_response = self.client.post(self.login_url, {'email': self.user_data['email'], 'password': self.user_data['password']})
        token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + token)
        url = reverse('student-application')
        img = self.create_valid_image()
        data = {'payment_screenshot': img}
        self.client.post(url, data, format='multipart')
        app = StudentApplication.objects.get(user=user)
        # Create admin
        admin = CustomUser.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='AdminPass1234')
        self.client.force_authenticate(user=admin)
        action_url = reverse('student-application-admin-action', args=[app.pk, 'reject'])
        response = self.client.post(action_url, {'rejection_reason': 'مرفوض'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        app.refresh_from_db()
        self.assertFalse(app.is_approved)
        self.assertEqual(app.rejection_reason, 'مرفوض')
        self.assertFalse(app.payment_screenshot)
