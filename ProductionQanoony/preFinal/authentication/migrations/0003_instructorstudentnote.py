# Generated by Django 4.2.7 on 2025-07-14 15:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('students', '0001_initial'),
        ('authentication', '0002_customuser_selected_plan_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='InstructorStudentNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField(max_length=1000, verbose_name='الملاحظة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('instructor', models.ForeignKey(limit_choices_to={'is_instructor': True}, on_delete=django.db.models.deletion.CASCADE, related_name='student_notes', to=settings.AUTH_USER_MODEL, verbose_name='المدرس')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instructor_notes', to='students.studentprofile', verbose_name='الطالب')),
            ],
            options={
                'verbose_name': 'ملاحظة مدرس عن طالب',
                'verbose_name_plural': 'ملاحظات المدرسين عن الطلاب',
                'ordering': ['-updated_at'],
                'unique_together': {('instructor', 'student')},
            },
        ),
    ]
