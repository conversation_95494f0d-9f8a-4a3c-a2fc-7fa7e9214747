# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('first_name', models.Char<PERSON>ield(max_length=50)),
                ('last_name', models.Char<PERSON>ield(max_length=50)),
                ('phone_number', models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                ('is_active', models.<PERSON><PERSON><PERSON><PERSON>ield(default=True)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_student', models.BooleanField(default=False)),
                ('is_instructor', models.BooleanField(default=False)),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now)),
                ('last_login', models.DateTimeField(blank=True, null=True)),
                ('email_verified', models.BooleanField(default=False)),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='profile_images/')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StudentApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_screenshot', models.ImageField(upload_to='student_applications/', verbose_name='صورة إيصال الدفع')),
                ('is_approved', models.BooleanField(default=False, verbose_name='تمت الموافقة')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('rejection_reason', models.TextField(blank=True, verbose_name='سبب الرفض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقديم')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_student_applications', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='student_application', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
        ),
        migrations.CreateModel(
            name='InstructorApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('first_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='اسم العائلة')),
                ('bio', models.TextField(verbose_name='نبذة تعريفية')),
                ('specialty', models.CharField(blank=True, max_length=100, verbose_name='التخصص')),
                ('phone_number', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('cv', models.FileField(blank=True, null=True, upload_to='instructor_applications/cv/', verbose_name='السيرة الذاتية')),
                ('is_approved', models.BooleanField(default=False, verbose_name='تمت الموافقة')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('rejection_reason', models.TextField(blank=True, verbose_name='سبب الرفض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقديم')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_instructor_applications', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='instructor_application', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
        ),
        migrations.CreateModel(
            name='EmailVerification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=255, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_used', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_verifications', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
