# Generated by Django 4.2.7 on 2025-07-14 17:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0003_instructorstudentnote'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentMethods',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('instapay_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم انستاباي')),
                ('instapay_enabled', models.BooleanField(default=True, verbose_name='تفعيل انستاباي')),
                ('vodafone_cash_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم فودافون كاش')),
                ('vodafone_cash_enabled', models.BooleanField(default=True, verbose_name='تفعيل فودافون كاش')),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم البنك')),
                ('account_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحساب')),
                ('account_holder_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم صاحب الحساب')),
                ('iban', models.CharField(blank=True, max_length=50, null=True, verbose_name='الآيبان')),
                ('swift_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='كود السويفت')),
                ('bank_enabled', models.BooleanField(default=True, verbose_name='تفعيل الحساب البنكي')),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('working_hours', models.CharField(blank=True, max_length=100, null=True, verbose_name='ساعات العمل')),
                ('facebook_url', models.URLField(blank=True, null=True, verbose_name='رابط فيسبوك')),
                ('twitter_url', models.URLField(blank=True, null=True, verbose_name='رابط تويتر')),
                ('instagram_url', models.URLField(blank=True, null=True, verbose_name='رابط إنستجرام')),
                ('linkedin_url', models.URLField(blank=True, null=True, verbose_name='رابط لينكد إن')),
                ('youtube_url', models.URLField(blank=True, null=True, verbose_name='رابط يوتيوب')),
                ('payment_instructions', models.TextField(blank=True, default='للاشتراك يرجى رفع صورة تحويل بأحد الوسائل الآتية:', null=True, verbose_name='تعليمات الدفع')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'بيانات الدفع والتواصل',
                'verbose_name_plural': 'بيانات الدفع والتواصل',
                'ordering': ['-updated_at'],
            },
        ),
    ]
