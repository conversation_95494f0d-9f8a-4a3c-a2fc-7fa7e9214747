import os
import uuid
from PIL import Image, ImageOps
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.conf import settings
from io import BytesIO
import logging

logger = logging.getLogger(__name__)

def compress_image(image_file, max_size=(1920, 1080), quality=85, format='JPEG'):
    """
    Compress and resize an uploaded image file.
    
    Args:
        image_file: Django UploadedFile object
        max_size: Tuple of (width, height) for maximum dimensions
        quality: JPEG quality (1-100)
        format: Output format ('JPEG', 'PNG', 'WEBP')
    
    Returns:
        ContentFile: Compressed image as Django ContentFile
    """
    try:
        # Open the image
        image = Image.open(image_file)
        
        # Convert RGBA to RGB if saving as JPEG
        if format == 'JPEG' and image.mode in ('RGBA', 'LA', 'P'):
            # Create a white background
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        
        # Auto-orient the image based on EXIF data
        image = ImageOps.exif_transpose(image)
        
        # Resize if larger than max_size
        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # Save to BytesIO
        output = BytesIO()
        save_kwargs = {'format': format, 'optimize': True}
        
        if format == 'JPEG':
            save_kwargs['quality'] = quality
        elif format == 'PNG':
            save_kwargs['compress_level'] = 6
        elif format == 'WEBP':
            save_kwargs['quality'] = quality
            save_kwargs['method'] = 6
        
        image.save(output, **save_kwargs)
        output.seek(0)
        
        # Generate new filename
        original_name = os.path.splitext(image_file.name)[0]
        extension = format.lower()
        if extension == 'jpeg':
            extension = 'jpg'
        
        new_filename = f"{original_name}_compressed_{uuid.uuid4().hex[:8]}.{extension}"
        
        return ContentFile(output.getvalue(), name=new_filename)
        
    except Exception as e:
        logger.error(f"Error compressing image {image_file.name}: {e}")
        # Return original file if compression fails
        return image_file

def get_file_size_mb(file_obj):
    """Get file size in megabytes."""
    if hasattr(file_obj, 'size'):
        return file_obj.size / (1024 * 1024)
    return 0

def validate_image_file(file_obj, max_size_mb=10):
    """
    Validate uploaded image file.
    
    Args:
        file_obj: Django UploadedFile object
        max_size_mb: Maximum file size in megabytes
    
    Returns:
        tuple: (is_valid, error_message)
    """
    # Check file size
    size_mb = get_file_size_mb(file_obj)
    if size_mb > max_size_mb:
        return False, f"حجم الملف كبير جداً. الحد الأقصى {max_size_mb} ميجابايت"
    
    # Check file type
    allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if file_obj.content_type not in allowed_types:
        return False, "نوع الملف غير مدعوم. يرجى رفع صورة (JPG, PNG, GIF, WEBP)"
    
    # Try to open with PIL to validate it's a real image
    try:
        image = Image.open(file_obj)
        image.verify()
        file_obj.seek(0)  # Reset file pointer
        return True, None
    except Exception:
        return False, "الملف المرفوع ليس صورة صحيحة"

def generate_secure_filename(original_filename, user_id=None):
    """
    Generate a secure filename for uploaded files.
    
    Args:
        original_filename: Original filename
        user_id: User ID for additional security
    
    Returns:
        str: Secure filename
    """
    # Get file extension
    name, ext = os.path.splitext(original_filename)
    
    # Generate unique identifier
    unique_id = uuid.uuid4().hex
    
    # Include user_id if provided
    if user_id:
        filename = f"renewal_{user_id}_{unique_id}{ext}"
    else:
        filename = f"renewal_{unique_id}{ext}"
    
    return filename

def cleanup_old_renewal_files(days_old=30):
    """
    Clean up old renewal screenshot files.
    
    Args:
        days_old: Delete files older than this many days
    
    Returns:
        tuple: (deleted_count, total_size_freed_mb)
    """
    from django.utils import timezone
    from datetime import timedelta
    from .models import Subscription
    
    deleted_count = 0
    total_size_freed = 0
    
    try:
        # Get cutoff date
        cutoff_date = timezone.now() - timedelta(days=days_old)
        
        # Find old subscriptions with renewal screenshots
        old_subscriptions = Subscription.objects.filter(
            renewal_requested_at__lt=cutoff_date,
            renewal_screenshot__isnull=False
        ).exclude(renewal_screenshot='')
        
        for subscription in old_subscriptions:
            try:
                file_path = subscription.renewal_screenshot.path
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    os.remove(file_path)
                    total_size_freed += file_size
                    deleted_count += 1
                    logger.info(f"Deleted old renewal file: {file_path}")
                
                # Clear the field in database
                subscription.renewal_screenshot = None
                subscription.save(update_fields=['renewal_screenshot'])
                
            except Exception as e:
                logger.error(f"Error deleting file for subscription {subscription.id}: {e}")
        
        total_size_freed_mb = total_size_freed / (1024 * 1024)
        logger.info(f"Cleanup completed: {deleted_count} files deleted, {total_size_freed_mb:.2f} MB freed")
        
        return deleted_count, total_size_freed_mb
        
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        return 0, 0
