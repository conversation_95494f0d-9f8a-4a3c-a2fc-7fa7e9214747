from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, Http404, FileResponse
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from .models import SubscriptionPlan, Subscription
from .serializers import SubscriptionPlanSerializer, SubscriptionRenewalRequestSerializer, SubscriptionRenewalAdminActionSerializer
from rest_framework import generics
from django.db.models import Count, Sum, Q
from datetime import datetime, timedelta
from django.utils import timezone
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.parsers import <PERSON>PartParser, FormParser
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.contrib.auth.models import AnonymousUser
from notifications.models import Notification
from django.utils.translation import gettext as _
from .utils import compress_image, validate_image_file
from config.error_handlers import handle_api_errors, create_error_response, ErrorMessages
import logging
import os
import mimetypes

logger = logging.getLogger(__name__)

class QueryParameterJWTAuthentication(JWTAuthentication):
    """
    JWT Authentication that accepts token from query parameter or Authorization header
    """
    def authenticate(self, request):
        # Try normal header authentication first
        header_auth = super().authenticate(request)
        if header_auth is not None:
            return header_auth

        # Try query parameter authentication
        token = request.GET.get('token')
        if token is None:
            return None

        try:
            # Remove 'Bearer ' prefix if present
            if token.startswith('Bearer '):
                token = token[7:]

            validated_token = self.get_validated_token(token)
            user = self.get_user(validated_token)
            logger.info(f"Query parameter authentication successful for user {user.id}")
            return (user, validated_token)
        except TokenError as e:
            logger.warning(f"Query parameter authentication failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in query parameter authentication: {e}")
            return None

# Create your views here.

class SubscriptionPlanListView(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        plans = SubscriptionPlan.objects.filter(is_active=True)
        serializer = SubscriptionPlanSerializer(plans, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

class SubscriptionRenewalRequestView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    @handle_api_errors
    def post(self, request):
        logger.info(f"Renewal request from user {request.user.id}")
        logger.info(f"Request data: {request.data}")
        logger.info(f"Request files: {list(request.FILES.keys())}")

        try:
            # Find the user's latest active or expired subscription
            subscription = Subscription.objects.filter(user=request.user).order_by('-end_date').first()
            if not subscription:
                return create_error_response(
                    error_type='لا يوجد اشتراك',
                    detail='لا يوجد اشتراك لطلب التجديد.',
                    code='no_subscription',
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Validate the uploaded image first
            renewal_screenshot = request.FILES.get('renewal_screenshot')
            logger.info(f"Renewal screenshot file: {renewal_screenshot}")

            if not renewal_screenshot:
                logger.error("No renewal_screenshot file found in request")
                return create_error_response(
                    error_type='صورة مطلوبة',
                    detail='صورة إثبات الدفع مطلوبة.',
                    code='image_required',
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            logger.info(f"File details: name={renewal_screenshot.name}, size={renewal_screenshot.size}, content_type={renewal_screenshot.content_type}")

            # Validate image file
            is_valid, error_message = validate_image_file(renewal_screenshot, max_size_mb=10)
            if not is_valid:
                logger.error(f"Image validation failed: {error_message}")
                return create_error_response(
                    error_type='صورة غير صحيحة',
                    detail=error_message,
                    code='invalid_image',
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Validate serializer data (without the image for now)
            data_without_image = request.data.copy()
            serializer = SubscriptionRenewalRequestSerializer(subscription, data=data_without_image, context={'request': request}, partial=True)

            if not serializer.is_valid():
                logger.error(f"Serializer validation failed: {serializer.errors}")
                return create_error_response(
                    error_type='بيانات غير صحيحة',
                    detail='بيانات الطلب غير صحيحة.',
                    code='validation_error',
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Compress the image after validation
            try:
                compressed_image = compress_image(renewal_screenshot, max_size=(1920, 1080), quality=85)
                logger.info(f"Image compressed for user {request.user.id}: {renewal_screenshot.name}")
            except Exception as e:
                logger.error(f"Image compression failed for user {request.user.id}: {e}")
                compressed_image = renewal_screenshot  # Use original if compression fails

            # Set renewal fields directly on the model
            subscription.plan = serializer.validated_data['plan']
            subscription.renewal_screenshot = compressed_image
            subscription.renewal_requested_at = timezone.now()
            subscription.renewal_status = 'pending'
            subscription.status = 'pending_renewal'
            subscription.save()

            logger.info(f"Renewal request saved successfully for user {request.user.id}")
            return Response({
                'success': True,
                'message': 'تم إرسال طلب التجديد بنجاح. سيتم مراجعته من قبل الإدارة.',
                'renewal_status': 'pending'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Unexpected error in renewal request: {e}")
            return create_error_response(
                error_type='خطأ في النظام',
                detail='حدث خطأ أثناء معالجة طلب التجديد. يرجى المحاولة مرة أخرى.',
                code='system_error',
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class SubscriptionRenewalStatusView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get the user's latest subscription with renewal request
        subscription = Subscription.objects.filter(
            user=request.user,
            renewal_status__in=['pending', 'approved', 'denied']
        ).order_by('-renewal_requested_at').first()

        # Also check if user has any subscription at all
        has_any_subscription = Subscription.objects.filter(user=request.user).exists()

        if not subscription:
            # Return empty response instead of 404 to avoid frontend errors
            return Response({
                'status': None,
                'requested_at': None,
                'reviewed_at': None,
                'rejection_reason': None,
                'plan_name': None,
                'has_subscription': has_any_subscription,
                'message': 'لا يوجد طلب تجديد حالياً'
            }, status=status.HTTP_200_OK)

        return Response({
            'status': subscription.renewal_status,
            'requested_at': subscription.renewal_requested_at,
            'reviewed_at': subscription.renewal_reviewed_at,
            'rejection_reason': subscription.rejection_reason,
            'plan_name': subscription.plan.name if subscription.plan else None,
            'has_subscription': True,
        }, status=status.HTTP_200_OK)

class SubscriptionRenewalAdminActionView(APIView):
    permission_classes = [IsAdminUser]

    def post(self, request, pk):
        subscription = Subscription.objects.filter(pk=pk, renewal_status='pending').first()
        if not subscription:
            return Response({'detail': 'لا يوجد طلب تجديد قيد المراجعة لهذا الاشتراك.'}, status=status.HTTP_404_NOT_FOUND)
        serializer = SubscriptionRenewalAdminActionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        action = serializer.validated_data['action']
        if action == 'approve':
            from datetime import timedelta
            subscription.start_date = timezone.now().date()
            subscription.end_date = subscription.start_date + timedelta(days=subscription.plan.duration_days)
            subscription.status = 'active'
            subscription.renewal_status = 'approved'
            subscription.renewal_reviewed_at = timezone.now()
            subscription.renewal_reviewed_by = request.user
            subscription.is_active = True
            subscription.renewal_screenshot = None
            subscription.save()

            # إرسال إشعار الموافقة باستخدام الـ utility الجديد
            from notifications.utils import send_subscription_renewal_notification
            send_subscription_renewal_notification(subscription, approved=True)
            return Response({'detail': 'تمت الموافقة على التجديد وتم تمديد الاشتراك.'}, status=status.HTTP_200_OK)
        elif action == 'deny':
            subscription.renewal_status = 'denied'
            subscription.status = 'denied'
            subscription.renewal_reviewed_at = timezone.now()
            subscription.renewal_reviewed_by = request.user
            denial_reason = serializer.validated_data.get('denial_reason', '')
            subscription.rejection_reason = denial_reason
            subscription.save()

            # إرسال إشعار الرفض باستخدام الـ utility الجديد
            from notifications.utils import send_subscription_renewal_notification
            send_subscription_renewal_notification(subscription, approved=False)
            return Response({'detail': 'تم رفض طلب التجديد.', 'reason': denial_reason}, status=status.HTTP_200_OK)
        return Response({'detail': 'إجراء غير صالح.'}, status=status.HTTP_400_BAD_REQUEST)


# Admin Views
class SubscriptionPlanAdminView(APIView):
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get all subscription plans for admin"""
        plans = SubscriptionPlan.objects.all().order_by('duration_days')
        serializer = SubscriptionPlanSerializer(plans, many=True)
        return Response(serializer.data)

    def post(self, request):
        """Create new subscription plan"""
        serializer = SubscriptionPlanSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SubscriptionPlanAdminDetailView(APIView):
    permission_classes = [IsAdminUser]

    def get(self, request, pk):
        """Get specific subscription plan"""
        try:
            plan = SubscriptionPlan.objects.get(pk=pk)
            serializer = SubscriptionPlanSerializer(plan)
            return Response(serializer.data)
        except SubscriptionPlan.DoesNotExist:
            return Response({'error': 'Plan not found'}, status=status.HTTP_404_NOT_FOUND)

    def put(self, request, pk):
        """Update subscription plan"""
        try:
            plan = SubscriptionPlan.objects.get(pk=pk)
            serializer = SubscriptionPlanSerializer(plan, data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except SubscriptionPlan.DoesNotExist:
            return Response({'error': 'Plan not found'}, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request, pk):
        """Delete subscription plan"""
        try:
            plan = SubscriptionPlan.objects.get(pk=pk)
            # Check if plan has active subscriptions
            active_subs = Subscription.objects.filter(plan=plan, status='active').count()
            if active_subs > 0:
                return Response(
                    {'error': f'Cannot delete plan with {active_subs} active subscriptions'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            plan.delete()
            return Response({'message': 'Plan deleted successfully'}, status=status.HTTP_204_NO_CONTENT)
        except SubscriptionPlan.DoesNotExist:
            return Response({'error': 'Plan not found'}, status=status.HTTP_404_NOT_FOUND)


class SubscriptionAdminListView(APIView):
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get all subscriptions for admin with filtering"""
        subscriptions = Subscription.objects.select_related('user', 'plan', 'renewal_reviewed_by').all()

        # Filter by status
        status_filter = request.GET.get('status')
        if status_filter:
            subscriptions = subscriptions.filter(status=status_filter)

        # Filter by renewal status
        renewal_status = request.GET.get('renewal_status')
        if renewal_status:
            subscriptions = subscriptions.filter(renewal_status=renewal_status)

        # Search by user email
        search = request.GET.get('search')
        if search:
            subscriptions = subscriptions.filter(
                Q(user__email__icontains=search) |
                Q(user__first_name__icontains=search) |
                Q(user__last_name__icontains=search)
            )

        # Order by latest first
        subscriptions = subscriptions.order_by('-created_at')

        # Serialize data
        data = []
        for sub in subscriptions:
            data.append({
                'id': sub.id,
                'user': {
                    'id': sub.user.id,
                    'email': sub.user.email,
                    'first_name': sub.user.first_name,
                    'last_name': sub.user.last_name,
                },
                'plan': {
                    'id': sub.plan.id if sub.plan else None,
                    'name': sub.plan.name if sub.plan else 'No Plan',
                    'duration_days': sub.plan.duration_days if sub.plan else 0,
                    'price': str(sub.plan.price) if sub.plan else '0.00',
                } if sub.plan else None,
                'start_date': sub.start_date,
                'end_date': sub.end_date,
                'status': sub.status,
                'renewal_status': sub.renewal_status,
                'renewal_requested_at': sub.renewal_requested_at,
                'renewal_screenshot': sub.renewal_screenshot.url if sub.renewal_screenshot else None,
                'renewal_reviewed_at': sub.renewal_reviewed_at,
                'renewal_reviewed_by': {
                    'email': sub.renewal_reviewed_by.email,
                    'name': f"{sub.renewal_reviewed_by.first_name} {sub.renewal_reviewed_by.last_name}"
                } if sub.renewal_reviewed_by else None,
                'rejection_reason': sub.rejection_reason,
                'is_active': sub.is_active,
                'created_at': sub.created_at,
                'updated_at': sub.updated_at,
            })

        return Response(data)


class SubscriptionStatsView(APIView):
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get subscription statistics for admin dashboard"""
        try:
            # Basic counts
            total_subscriptions = Subscription.objects.count()
            active_subscriptions = Subscription.objects.filter(status='active').count()
            expired_subscriptions = Subscription.objects.filter(status='expired').count()
            pending_renewals = Subscription.objects.filter(renewal_status='pending').count()

            # Revenue calculations - sum of all active subscriptions
            total_revenue = Subscription.objects.filter(
                status='active'
            ).aggregate(
                total=Sum('plan__price')
            )['total'] or 0

            # Monthly revenue (last 6 months)
            monthly_revenue = []
            for i in range(6):
                month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
                month_end = month_start + timedelta(days=30)

                month_revenue = Subscription.objects.filter(
                    renewal_reviewed_at__gte=month_start,
                    renewal_reviewed_at__lt=month_end,
                    renewal_status='approved'
                ).aggregate(
                    total=Sum('plan__price')
                )['total'] or 0

                monthly_revenue.append({
                    'month': month_start.strftime('%Y-%m'),
                    'revenue': float(month_revenue)
                })

            # Plan popularity
            plan_stats = SubscriptionPlan.objects.annotate(
                active_count=Count('subscriptions', filter=Q(subscriptions__status='active')),
                total_count=Count('subscriptions')
            ).values('id', 'name', 'price', 'active_count', 'total_count')

            return Response({
                'basic_stats': {
                    'total_subscriptions': total_subscriptions,
                    'active_subscriptions': active_subscriptions,
                    'expired_subscriptions': expired_subscriptions,
                    'pending_renewals': pending_renewals,
                    'total_revenue': float(total_revenue),
                },
                'monthly_revenue': monthly_revenue,
                'plan_stats': list(plan_stats)
            })

        except Exception as e:
            return Response(
                {'error': 'Failed to fetch subscription stats', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SecureRenewalImageView(APIView):
    """
    Secure view for serving renewal screenshot images.
    Only authenticated users (admins or the image owner) can access the images.
    """
    authentication_classes = [QueryParameterJWTAuthentication]
    permission_classes = [IsAuthenticated]

    @handle_api_errors
    def get(self, request, subscription_id):
        """Serve renewal screenshot image securely"""
        try:
            # Get the subscription
            subscription = get_object_or_404(Subscription, id=subscription_id)

            # Check permissions: admin or owner
            if not (request.user.is_staff or request.user == subscription.user):
                logger.warning(f"Unauthorized image access attempt: User {request.user.id} tried to access subscription {subscription_id}")
                return create_error_response(
                    error_type='وصول غير مصرح',
                    detail='ليس لديك صلاحية لعرض هذه الصورة',
                    code='permission_denied',
                    status_code=status.HTTP_403_FORBIDDEN
                )

            # Check if image exists
            if not subscription.renewal_screenshot:
                return create_error_response(
                    error_type='صورة غير موجودة',
                    detail='لا توجد صورة مرفقة لهذا الطلب',
                    code='image_not_found',
                    status_code=status.HTTP_404_NOT_FOUND
                )

            # Get the file path
            image_path = subscription.renewal_screenshot.path

            if not os.path.exists(image_path):
                logger.error(f"Image file missing for subscription {subscription_id}: {image_path}")
                return create_error_response(
                    error_type='ملف غير موجود',
                    detail='الملف غير موجود على الخادم',
                    code='file_not_found',
                    status_code=status.HTTP_404_NOT_FOUND
                )

            # Determine content type
            content_type, _ = mimetypes.guess_type(image_path)
            if not content_type:
                content_type = 'application/octet-stream'

            # Log access for security
            logger.info(f"Secure image access: User {request.user.id} accessed subscription {subscription_id} image")

            # Return the file
            return FileResponse(
                open(image_path, 'rb'),
                content_type=content_type,
                filename=os.path.basename(image_path)
            )

        except Exception as e:
            logger.error(f"Error serving secure image for subscription {subscription_id}: {e}")
            return create_error_response(
                error_type='خطأ في تحميل الصورة',
                detail='حدث خطأ أثناء تحميل الصورة',
                code='image_load_error',
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
