from celery import shared_task
from django.utils import timezone
from datetime import timedelta
from .models import Subscription
from notifications.models import Notification
from django.utils.translation import gettext as _
from celery.utils.log import get_task_logger
from django.db import transaction
# from notifications.utils import send_subscription_expiry_notification  # To be implemented

logger = get_task_logger(__name__)

@shared_task
def notify_subscriptions_expiring_soon():
    target_date = timezone.now().date() + timedelta(days=7)
    expiring_subs = Subscription.objects.filter(end_date=target_date, is_active=True)
    for sub in expiring_subs:
        from notifications.utils import create_notification_safe
        create_notification_safe(
            recipient=sub.user,
            subject_ar=_('تنبيه انتهاء الاشتراك'),
            content_ar=_('مرحباً {user_name}، اشتراكك سينتهي بعد أسبوع في تاريخ {end_date}'),
            notification_type='in_app',
            deduplication_minutes=1440,  # منع التكرار لمدة 24 ساعة
            end_date=sub.end_date
        )

@shared_task
def deactivate_expired_subscriptions():
    """
    Deactivate all expired subscriptions and associated user accounts.
    Sends a single in-app notification per user if not already sent for this expiry event.
    Idempotent and safe to retry.
    """
    today = timezone.now().date()
    expired_subs = Subscription.objects.select_related('user').filter(end_date__lt=today, is_active=True)
    logger.info(f"Found {expired_subs.count()} expired subscriptions to process.")
    for sub in expired_subs:
        try:
            with transaction.atomic():
                # Idempotency: skip if already deactivated
                if not sub.is_active:
                    continue
                sub.is_active = False
                sub.status = 'expired'
                sub.save()
                # إزالة صفة الطالب عند انتهاء الاشتراك (لكن المستخدم يبقى نشط)
                user = sub.user
                if user.is_student:
                    user.is_student = False  # إزالة صفة الطالب
                    # user.is_active يبقى True عشان يقدر يجدد الاشتراك
                    user.save()
                    logger.info(f"Removed student status from user {user.email} due to subscription expiry")
                # إرسال إشعار انتهاء الاشتراك مع منع التكرار
                from notifications.utils import create_notification_safe
                create_notification_safe(
                    recipient=user,
                    subject_ar=_('انتهاء الاشتراك'),
                    content_ar=_('مرحباً {user_name}، انتهى اشتراكك بتاريخ {end_date}. تم إيقاف وصولك للمحتوى. يرجى تجديد الاشتراك للمتابعة.'),
                    notification_type='in_app',
                    deduplication_minutes=1440,  # منع التكرار لمدة 24 ساعة
                    end_date=sub.end_date
                )
                logger.info(f"Deactivated subscription {sub.pk} and user {user.pk}.")
        except Exception as e:
            logger.error(f"Failed to deactivate subscription {sub.pk}: {e}")


@shared_task
def cleanup_old_renewal_screenshots():
    """
    Clean up old renewal screenshot files.
    Removes files older than 30 days to save storage space.
    """
    from .utils import cleanup_old_renewal_files

    try:
        deleted_count, size_freed_mb = cleanup_old_renewal_files(days_old=30)
        logger.info(f"Renewal screenshots cleanup completed: {deleted_count} files deleted, {size_freed_mb:.2f} MB freed")
        return {
            'deleted_count': deleted_count,
            'size_freed_mb': size_freed_mb,
            'status': 'success'
        }
    except Exception as e:
        logger.error(f"Error during renewal screenshots cleanup: {e}")
        return {
            'deleted_count': 0,
            'size_freed_mb': 0,
            'status': 'error',
            'error': str(e)
        }