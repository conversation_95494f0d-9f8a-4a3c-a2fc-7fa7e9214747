from django.contrib import admin
from .models import Subscription, SubscriptionPlan

@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = ('name', 'duration_days', 'price', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active', 'duration_days')

@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = (
        'user', 'plan', 'start_date', 'end_date', 'status', 'renewal_status', 'is_active', 'created_at'
    )
    search_fields = ('user__email', 'plan__name')
    list_filter = ('status', 'renewal_status', 'is_active', 'plan', 'start_date', 'end_date')
