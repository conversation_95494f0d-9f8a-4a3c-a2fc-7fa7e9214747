from django.urls import path
from .views import (
    SubscriptionPlanListView, SubscriptionRenewalRequestView, SubscriptionRenewalAdminActionView,
    SubscriptionRenewalStatusView, SubscriptionPlanAdminView, SubscriptionPlanAdminDetailView,
    SubscriptionAdminListView, SubscriptionStatsView, SecureRenewalImageView
)

app_name = 'subscriptions'

urlpatterns = [
    # Public endpoints
    path('plans/', SubscriptionPlanListView.as_view(), name='subscription-plan-list'),
    path('renewal-requests/', SubscriptionRenewalRequestView.as_view(), name='subscription-renewal-request'),
    path('renewal-requests/me/', SubscriptionRenewalStatusView.as_view(), name='subscription-renewal-status'),
    path('renewal-action/<int:pk>/', SubscriptionRenewalAdminActionView.as_view(), name='subscription-renewal-admin-action'),

    # Admin endpoints
    path('admin/plans/', SubscriptionPlanAdminView.as_view(), name='subscription-plan-admin'),
    path('admin/plans/<int:pk>/', SubscriptionPlanAdminDetailView.as_view(), name='subscription-plan-admin-detail'),
    path('admin/subscriptions/', SubscriptionAdminListView.as_view(), name='subscription-admin-list'),
    path('admin/stats/', SubscriptionStatsView.as_view(), name='subscription-stats'),

    # Secure file serving
    path('secure/image/<int:subscription_id>/', SecureRenewalImageView.as_view(), name='secure-renewal-image'),
]