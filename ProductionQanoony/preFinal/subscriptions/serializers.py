from rest_framework import serializers
from .models import Subscription, SubscriptionPlan

class SubscriptionPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionPlan
        fields = ['id', 'name', 'duration_days', 'price', 'is_active']

class SubscriptionSerializer(serializers.ModelSerializer):
    plan = SubscriptionPlanSerializer(read_only=True)
    plan_id = serializers.PrimaryKeyRelatedField(queryset=SubscriptionPlan.objects.all(), source='plan', write_only=True)
    user_email = serializers.EmailField(source='user.email', read_only=True)

    class Meta:
        model = Subscription
        fields = [
            'id', 'user', 'user_email', 'plan', 'plan_id', 'start_date', 'end_date', 'status',
            'renewal_requested_at', 'renewal_screenshot', 'renewal_status', 'renewal_reviewed_at', 'renewal_reviewed_by',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['status', 'is_active', 'created_at', 'updated_at', 'renewal_status', 'renewal_reviewed_at', 'renewal_reviewed_by']

class SubscriptionRenewalRequestSerializer(serializers.ModelSerializer):
    plan_id = serializers.PrimaryKeyRelatedField(queryset=SubscriptionPlan.objects.filter(is_active=True), source='plan', write_only=True)
    renewal_screenshot = serializers.ImageField(required=False)  # سنتعامل معها في الـ view

    class Meta:
        model = Subscription
        fields = ['plan_id', 'renewal_screenshot']

    def validate(self, data):
        user = self.context['request'].user
        # Only allow one pending renewal at a time
        if Subscription.objects.filter(user=user, renewal_status='pending').exists():
            raise serializers.ValidationError('لديك طلب تجديد قيد المراجعة بالفعل.')
        return data

    def validate_renewal_screenshot(self, value):
        """تحقق من صحة الصورة المرفوعة"""
        if value:
            # فحص نوع الملف
            if not value.content_type.startswith('image/'):
                raise serializers.ValidationError('يجب أن يكون الملف صورة.')

            # فحص حجم الملف (10 ميجابايت كحد أقصى)
            if value.size > 10 * 1024 * 1024:
                raise serializers.ValidationError('حجم الصورة يجب أن يكون أقل من 10 ميجابايت.')

            # فحص امتداد الملف
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
            file_extension = value.name.lower().split('.')[-1] if '.' in value.name else ''
            if f'.{file_extension}' not in allowed_extensions:
                raise serializers.ValidationError('نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, BMP, WebP.')

        return value

class SubscriptionRenewalAdminActionSerializer(serializers.Serializer):
    action = serializers.ChoiceField(choices=[('approve', 'Approve'), ('deny', 'Deny')])
    denial_reason = serializers.CharField(required=False, allow_blank=True) 