# Subscriptions App

This app manages student subscriptions and expiry notifications.

## Features
- Tracks subscription periods for each user
- Admin interface for managing subscriptions
- Celery task to notify students 7 days before their subscription ends

## Model
- `Subscription`: user, start_date, end_date, is_active, created_at, updated_at

## Celery Task
- `notify_subscriptions_expiring_soon`: Notifies students exactly 7 days before their subscription ends (to be scheduled as a periodic task)

## Setup
1. Add `subscriptions` to `INSTALLED_APPS` in your Django settings.
2. Run migrations:
   ```
   python manage.py makemigrations subscriptions
   python manage.py migrate
   ```
3. Register the periodic task in your Celery beat schedule (see project celery config). 