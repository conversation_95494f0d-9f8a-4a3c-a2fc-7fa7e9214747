## Background Task: Deactivate Expired Subscriptions

**Task:** `deactivate_expired_subscriptions`
- **Type:** Celery periodic/background task
- **Description:**
    - Deactivates all subscriptions whose `end_date` is in the past and are still marked as active.
    - Sets `is_active=False` and `status='expired'` on the subscription.
    - Deactivates the associated user account (`user.is_active=False`).
    - Creates an in-app notification for the user (via the notifications app) with subject "انتهاء الاشتراك" and a message indicating their account has been deactivated due to expiry.
- **Integration:**
    - Should be scheduled to run daily (via Celery beat).
    - Integrates with the notifications app for in-app alerts.

## Endpoint: Admin Renewal Approval/Denial Notifications

**Endpoint:** `/api/subscriptions/admin/renewal-action/<pk>/`
- **Method:** POST
- **Permissions:** Admin only
- **Description:**
    - When an admin approves a renewal, the user receives an in-app notification with subject "تجديد الاشتراك: تمت الموافقة" and a message indicating the new expiry date.
    - When an admin denies a renewal, the user receives an in-app notification with subject "تجديد الاشتراك: تم الرفض" and a message including the denial reason.
- **Notification Fields:**
    - `recipient`: The user whose subscription was reviewed.
    - `subject_ar`: Arabic subject line as above.
    - `content_ar`: Arabic message as above.
    - `notification_type`: 'in_app'
    - `status`: 'pending'
    - `scheduled_at`: Time of notification creation. 