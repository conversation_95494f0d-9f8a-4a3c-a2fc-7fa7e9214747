from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
from authentication.models import CustomUser
from .models import Subscription, SubscriptionPlan
from .tasks import notify_subscriptions_expiring_soon
from notifications.models import Notification
from subscriptions.tasks import deactivate_expired_subscriptions
from django.urls import reverse
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model

# Create your tests here.

class SubscriptionNotificationTests(TestCase):
    def setUp(self):
        self.user = CustomUser.objects.create_user(email='<EMAIL>', first_name='Stu', last_name='Dent', password='pass')
        self.today = timezone.now().date()
        self.sub = Subscription.objects.create(
            user=self.user,
            start_date=self.today - timedelta(days=20),
            end_date=self.today + timedelta(days=7),
            is_active=True
        )

    def test_notify_subscriptions_expiring_soon(self):
        notify_subscriptions_expiring_soon()
        notifications = Notification.objects.filter(recipient=self.user, subject_ar__contains='انتهاء الاشتراك')
        self.assertEqual(notifications.count(), 1)
        self.sub.refresh_from_db()
        self.assertTrue(self.sub.is_active)

class SubscriptionTaskTests(TestCase):
    """Test Celery tasks for subscription expiry and deactivation."""
    def setUp(self):
        self.user = get_user_model().objects.create_user(
            email='<EMAIL>',
            password='pass',
            first_name='Expired',
            last_name='User',
        )
        self.plan = SubscriptionPlan.objects.create(name='Test', duration_days=30, price=100, is_active=True)
        self.expired_sub = Subscription.objects.create(
            user=self.user,
            plan=self.plan,
            start_date=timezone.now().date() - timezone.timedelta(days=40),
            end_date=timezone.now().date() - timezone.timedelta(days=10),
            status='active',
            is_active=True,
        )

    def test_deactivate_expired_subscriptions(self):
        deactivate_expired_subscriptions()
        self.expired_sub.refresh_from_db()
        self.user.refresh_from_db()
        self.assertFalse(self.expired_sub.is_active)
        self.assertEqual(self.expired_sub.status, 'expired')
        self.assertFalse(self.user.is_active)
        notif = Notification.objects.filter(recipient=self.user, subject_ar__contains='انتهاء الاشتراك').first()
        self.assertIsNotNone(notif)
        self.assertIn('تم إيقاف حسابك', notif.content_ar)

    def test_already_deactivated_user(self):
        self.user.is_active = False
        self.user.save()
        deactivate_expired_subscriptions()
        self.expired_sub.refresh_from_db()
        self.user.refresh_from_db()
        self.assertFalse(self.expired_sub.is_active)
        self.assertFalse(self.user.is_active)

    def test_multiple_expired_subscriptions_one_user(self):
        expired_sub2 = Subscription.objects.create(
            user=self.user,
            plan=self.plan,
            start_date=timezone.now().date() - timezone.timedelta(days=80),
            end_date=timezone.now().date() - timezone.timedelta(days=50),
            status='active',
            is_active=True,
        )
        deactivate_expired_subscriptions()
        expired_sub2.refresh_from_db()
        self.assertFalse(expired_sub2.is_active)
        notifs = Notification.objects.filter(recipient=self.user, subject_ar__contains='انتهاء الاشتراك')
        self.assertEqual(notifs.count(), 2)  # Each expiry gets a notification with its date

    def test_notification_deduplication(self):
        # Run once to create notification
        deactivate_expired_subscriptions()
        notif_count_1 = Notification.objects.filter(recipient=self.user, subject_ar__contains='انتهاء الاشتراك').count()
        # Run again, should not duplicate notification for same expiry
        deactivate_expired_subscriptions()
        notif_count_2 = Notification.objects.filter(recipient=self.user, subject_ar__contains='انتهاء الاشتراك').count()
        self.assertEqual(notif_count_1, notif_count_2)

class SubscriptionRenewalNotificationTests(TestCase):
    def setUp(self):
        self.admin = get_user_model().objects.create_superuser(
            email='<EMAIL>',
            password='pass',
            first_name='Admin',
            last_name='User',
        )
        self.user = get_user_model().objects.create_user(
            email='<EMAIL>',
            password='pass',
            first_name='Renew',
            last_name='User',
        )
        self.plan = SubscriptionPlan.objects.create(name='Test', duration_days=30, price=100, is_active=True)
        self.sub = Subscription.objects.create(
            user=self.user,
            plan=self.plan,
            start_date=timezone.now().date() - timezone.timedelta(days=30),
            end_date=timezone.now().date() - timezone.timedelta(days=1),
            status='active',
            is_active=True,
            renewal_status='pending',
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.admin)

    def test_notification_on_renewal_approval(self):
        url = reverse('subscriptions:admin-renewal-action', args=[self.sub.pk])
        data = {'action': 'approve'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)
        notif = Notification.objects.filter(recipient=self.user, subject_ar__contains='تمت الموافقة').first()
        self.assertIsNotNone(notif)
        self.assertIn('تمت الموافقة على طلب تجديد اشتراكك', notif.content_ar)

    def test_notification_on_renewal_denial(self):
        url = reverse('subscriptions:admin-renewal-action', args=[self.sub.pk])
        data = {'action': 'deny', 'denial_reason': 'مستند غير واضح'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)
        notif = Notification.objects.filter(recipient=self.user, subject_ar__contains='تم الرفض').first()
        self.assertIsNotNone(notif)
        self.assertIn('تم رفض طلب تجديد اشتراكك', notif.content_ar)
