from django.db import models
from authentication.models import CustomUser
import os
from .utils import generate_secure_filename

def renewal_screenshot_upload_path(instance, filename):
    """Generate secure upload path for renewal screenshots."""
    secure_filename = generate_secure_filename(filename, instance.user.id)
    return f'subscription_renewals/{instance.user.id}/{secure_filename}'

class SubscriptionPlan(models.Model):
    name = models.CharField(max_length=50)
    duration_days = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=8, decimal_places=2)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.duration_days} days)"

class Subscription(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('pending_renewal', 'Pending Renewal'),
        ('denied', 'Denied'),
    ]
    RENEWAL_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('denied', 'Denied'),
    ]
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='subscriptions')
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.PROTECT, related_name='subscriptions', null=True, blank=True)
    start_date = models.DateField()
    end_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    renewal_requested_at = models.DateTimeField(null=True, blank=True)
    renewal_screenshot = models.ImageField(upload_to=renewal_screenshot_upload_path, null=True, blank=True)
    renewal_status = models.CharField(max_length=20, choices=RENEWAL_STATUS_CHOICES, default='pending')
    renewal_reviewed_at = models.DateTimeField(null=True, blank=True)
    renewal_reviewed_by = models.ForeignKey(CustomUser, null=True, blank=True, related_name='reviewed_renewals', on_delete=models.SET_NULL)
    rejection_reason = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.email} - {self.plan.name} ({self.start_date} - {self.end_date}) [{self.status}]"
