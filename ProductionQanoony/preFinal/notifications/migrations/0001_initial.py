# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('subject_ar', models.CharField(max_length=255, verbose_name='الموضوع (عربي)')),
                ('content_ar', models.TextField(verbose_name='المحتوى (عربي)')),
                ('notification_type', models.CharField(choices=[('email', 'Email'), ('in_app', 'In-App'), ('sms', 'SMS')], max_length=20, verbose_name='نوع الإشعار')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject_ar', models.CharField(max_length=255, verbose_name='الموضوع (عربي)')),
                ('content_ar', models.TextField(verbose_name='المحتوى (عربي)')),
                ('notification_type', models.CharField(choices=[('email', 'Email'), ('in_app', 'In-App'), ('sms', 'SMS')], max_length=20, verbose_name='نوع الإشعار')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('sent', 'تم الإرسال'), ('failed', 'فشل')], default='pending', max_length=20, verbose_name='الحالة')),
                ('scheduled_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الجدولة')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستلم')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notifications', to='notifications.notificationtemplate', verbose_name='قالب الإشعار')),
            ],
        ),
    ]
