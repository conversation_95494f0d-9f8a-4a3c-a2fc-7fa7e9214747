from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from authentication.models import CustomUser
from .models import NotificationTemplate, Notification
from django.test.utils import override_settings
from django.utils import timezone

# Create your tests here.

@override_settings(DEFAULT_FILE_STORAGE='django.core.files.storage.FileSystemStorage')
class NotificationsTests(APITestCase):
    def setUp(self):
        self.admin = CustomUser.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='AdminPass1234')
        self.user = CustomUser.objects.create_user(email='<EMAIL>', first_name='User', last_name='Test', password='UserPass1234', is_student=True)
        self.template = NotificationTemplate.objects.create(
            name='Test Template',
            subject_ar='موضوع',
            content_ar='محتوى',
            notification_type='in_app',
            is_active=True
        )

    def test_template_management(self):
        self.client.force_authenticate(user=self.admin)
        url = reverse('notificationtemplate-list')
        data = {
            'name': 'New Template',
            'subject_ar': 'موضوع جديد',
            'content_ar': 'محتوى جديد',
            'notification_type': 'email',
            'is_active': True
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(NotificationTemplate.objects.filter(name='New Template').exists())

    def test_send_notification(self):
        self.client.force_authenticate(user=self.admin)
        url = reverse('notification-send')
        data = {
            'recipients': [str(self.user.id)],
            'template_id': self.template.id
        }
        response = self.client.post(url, data, content_type='application/json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(Notification.objects.filter(recipient=self.user).exists())

    def test_user_notification_center(self):
        Notification.objects.create(
            recipient=self.user,
            template=self.template,
            subject_ar='تنبيه',
            content_ar='رسالة',
            notification_type='in_app',
            status='sent',
            created_at=timezone.now()
        )
        self.client.force_authenticate(user=self.user)
        url = reverse('notification-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)

    def test_mark_notification_as_read(self):
        notif = Notification.objects.create(
            recipient=self.user,
            template=self.template,
            subject_ar='تنبيه',
            content_ar='رسالة',
            notification_type='in_app',
            status='sent',
            created_at=timezone.now()
        )
        self.client.force_authenticate(user=self.user)
        url = reverse('notification-mark-as-read', args=[notif.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        notif.refresh_from_db()
        self.assertIsNotNone(notif.read_at)
