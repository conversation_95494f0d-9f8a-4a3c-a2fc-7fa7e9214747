# Notifications API Documentation

## 1. Notification Templates

### List Templates
- **Endpoint:** /api/notifications/templates/
- **Method:** GET
- **Description:** List all notification templates (admin only).
- **Permissions:** IsAdminUser
- **Response Example:**
```
200 OK
[
  {
    "id": 1,
    "name": "Test Template",
    "subject_ar": "موضوع",
    "content_ar": "محتوى",
    "notification_type": "in_app",
    "is_active": true
  }
]
```

### Create Template
- **Endpoint:** /api/notifications/templates/
- **Method:** POST
- **Description:** Create a new notification template (admin only).
- **Permissions:** IsAdminUser
- **Request Example:**
```
{
  "name": "New Template",
  "subject_ar": "موضوع جديد",
  "content_ar": "محتوى جديد",
  "notification_type": "email",
  "is_active": true
}
```
- **Response Example:**
```
201 CREATED
{
  "id": 2,
  "name": "New Template",
  "subject_ar": "موضوع جديد",
  "content_ar": "محتوى جديد",
  "notification_type": "email",
  "is_active": true
}
```

### Update/Delete Template
- **Endpoint:** /api/notifications/templates/{id}/
- **Method:** PATCH/DELETE
- **Description:** Update or delete a notification template (admin only).
- **Permissions:** IsAdminUser

---

## 2. Notifications

### List Notifications
- **Endpoint:** /api/notifications/
- **Method:** GET
- **Description:** List notifications for the authenticated user (admin sees all).
- **Permissions:** IsAuthenticated
- **Response Example:**
```
200 OK
[
  {
    "id": 1,
    "recipient": "<user_id>",
    "template": {...},
    "subject_ar": "تنبيه",
    "content_ar": "رسالة",
    "notification_type": "in_app",
    "status": "sent",
    "scheduled_at": null,
    "sent_at": null,
    "read_at": null,
    "created_at": "2025-06-13T23:06:03.425442Z"
  }
]
```

### Mark Notification as Read
- **Endpoint:** /api/notifications/{id}/mark_as_read/
- **Method:** POST
- **Description:** Mark a notification as read (user only).
- **Permissions:** IsAuthenticated
- **Response Example:**
```
200 OK
{"status": "تمت القراءة"}
```

### Notification History
- **Endpoint:** /api/notifications/history/
- **Method:** GET
- **Description:** List all notifications for the authenticated user.
- **Permissions:** IsAuthenticated

### Send Notification
- **Endpoint:** /api/notifications/send/
- **Method:** POST
- **Description:** Send a notification to one or more users (admin only).
- **Permissions:** IsAdminUser
- **Request Example:**
```
{
  "recipients": ["<user_id>"],
  "template_id": 1
}
```
- **Response Example:**
```
200 OK
{"status": "تم إرسال الإشعارات", "count": 1}
```

---

## Summary of Endpoints & Restrictions

| Endpoint                                 | Method | Permissions      | Description                                 |
|------------------------------------------|--------|------------------|---------------------------------------------|
| /api/notifications/templates/            | GET    | Admin only       | List notification templates                 |
| /api/notifications/templates/            | POST   | Admin only       | Create notification template                |
| /api/notifications/templates/{id}/       | PATCH  | Admin only       | Update notification template                |
| /api/notifications/templates/{id}/       | DELETE | Admin only       | Delete notification template                |
| /api/notifications/                      | GET    | Authenticated    | List user notifications (admin: all)        |
| /api/notifications/{id}/mark_as_read/    | POST   | Authenticated    | Mark notification as read                   |
| /api/notifications/history/              | GET    | Authenticated    | List all user notifications                 |
| /api/notifications/send/                 | POST   | Admin only       | Send notification to users                  |

---

All user-facing messages are in Arabic. All API documentation and code are in English. 