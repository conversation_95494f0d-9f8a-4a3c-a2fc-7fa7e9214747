"""
Notification utilities for template processing and deduplication
"""
from django.utils import timezone
from .models import Notification


def get_user_display_name(user):
    """
    Get user display name for notifications
    """
    if user.first_name:
        return user.first_name
    elif user.last_name:
        return user.last_name
    else:
        # استخدام الجزء الأول من الإيميل كاسم
        return user.email.split('@')[0]


def process_notification_template(template_content, user=None, **kwargs):
    """
    Process notification template with variables
    """
    if not template_content:
        return template_content
    
    # متغيرات افتراضية
    variables = {}
    
    if user:
        variables['user_name'] = get_user_display_name(user)
        variables['user_email'] = user.email
        variables['user_full_name'] = user.get_full_name() if hasattr(user, 'get_full_name') else get_user_display_name(user)
    
    # إضافة متغيرات إضافية
    variables.update(kwargs)
    
    # استبدال المتغيرات في النص
    try:
        return template_content.format(**variables)
    except KeyError as e:
        # في حالة وجود متغير غير معرف، إرجاع النص كما هو
        print(f"Warning: Template variable {e} not found")
        return template_content


def create_notification_safe(recipient, subject_ar, content_ar, notification_type='in_app', 
                           template=None, deduplication_minutes=5, **template_vars):
    """
    Create notification with deduplication and template processing
    """
    # معالجة المتغيرات في المحتوى
    processed_content = process_notification_template(content_ar, user=recipient, **template_vars)
    processed_subject = process_notification_template(subject_ar, user=recipient, **template_vars)
    
    # فحص التكرار
    if deduplication_minutes > 0:
        time_threshold = timezone.now() - timezone.timedelta(minutes=deduplication_minutes)
        existing_notification = Notification.objects.filter(
            recipient=recipient,
            subject_ar=processed_subject,
            created_at__gte=time_threshold
        ).exists()
        
        if existing_notification:
            print(f"Duplicate notification prevented for user {recipient.email}")
            return None
    
    # إنشاء الإشعار
    notification = Notification.objects.create(
        recipient=recipient,
        template=template,
        subject_ar=processed_subject,
        content_ar=processed_content,
        notification_type=notification_type,
        status='pending',
        scheduled_at=timezone.now(),
    )
    
    return notification


def send_welcome_notification(user):
    """
    Send welcome notification to new user
    """
    from .models import NotificationTemplate
    
    try:
        template = NotificationTemplate.objects.get(name='welcome_student', is_active=True)
        return create_notification_safe(
            recipient=user,
            subject_ar=template.subject_ar,
            content_ar=template.content_ar,
            notification_type=template.notification_type,
            template=template,
            deduplication_minutes=60  # منع التكرار لمدة ساعة
        )
    except NotificationTemplate.DoesNotExist:
        # إنشاء إشعار افتراضي إذا لم يوجد template
        return create_notification_safe(
            recipient=user,
            subject_ar="مرحباً بك في المنصة",
            content_ar="مرحباً {user_name}، نرحب بك في منصة القانوني التعليمية",
            notification_type='in_app',
            deduplication_minutes=60
        )


def send_subscription_renewal_notification(subscription, approved=True):
    """
    Send subscription renewal notification
    """
    if approved:
        return create_notification_safe(
            recipient=subscription.user,
            subject_ar="تجديد الاشتراك: تمت الموافقة",
            content_ar="مرحباً {user_name}، تمت الموافقة على طلب تجديد اشتراكك. تم تمديد الاشتراك حتى {end_date}.",
            notification_type='in_app',
            deduplication_minutes=5,
            end_date=subscription.end_date
        )
    else:
        return create_notification_safe(
            recipient=subscription.user,
            subject_ar="تجديد الاشتراك: تم الرفض",
            content_ar="مرحباً {user_name}، نأسف لإبلاغك أنه تم رفض طلب تجديد اشتراكك. يرجى التواصل مع الإدارة للمزيد من التفاصيل.",
            notification_type='in_app',
            deduplication_minutes=5
        )
