from rest_framework import serializers
from .models import NotificationTemplate, Notification
from django.utils import timezone

class NotificationTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationTemplate
        fields = '__all__'

    def validate_notification_type(self, value):
        allowed = [choice[0] for choice in NotificationTemplate.NOTIFICATION_TYPE_CHOICES]
        if value not in allowed:
            raise serializers.ValidationError('نوع الإشعار غير صالح')
        return value

class NotificationSerializer(serializers.ModelSerializer):
    template = NotificationTemplateSerializer(read_only=True)
    template_id = serializers.PrimaryKeyRelatedField(queryset=NotificationTemplate.objects.all(), source='template', write_only=True, required=False)

    class Meta:
        model = Notification
        fields = '__all__'
        read_only_fields = ('status', 'sent_at', 'read_at', 'created_at')

    def validate_notification_type(self, value):
        allowed = [choice[0] for choice in NotificationTemplate.NOTIFICATION_TYPE_CHOICES]
        if value not in allowed:
            raise serializers.ValidationError('نوع الإشعار غير صالح')
        return value

    def validate_scheduled_at(self, value):
        if value and value < timezone.now():
            raise serializers.ValidationError('لا يمكن جدولة الإشعار في الماضي')
        return value 