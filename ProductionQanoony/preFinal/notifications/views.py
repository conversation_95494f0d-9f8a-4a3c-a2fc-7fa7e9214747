from django.shortcuts import render
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import NotificationTemplate, Notification
from .serializers import NotificationTemplateSerializer, NotificationSerializer
from django.utils import timezone
from authentication.models import CustomUser

# Create your views here.

class IsAdminOrReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated
        return request.user.is_staff

class NotificationTemplateViewSet(viewsets.ModelViewSet):
    queryset = NotificationTemplate.objects.all().order_by('id')  # ترتيب بـ id بدلاً من created_at
    serializer_class = NotificationTemplateSerializer
    permission_classes = [permissions.IsAdminUser]
    filterset_fields = ['notification_type', 'is_active']
    search_fields = ['name', 'subject_ar', 'content_ar']

class NotificationViewSet(viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    filterset_fields = ['notification_type', 'status', 'created_at']
    search_fields = ['subject_ar', 'content_ar']

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return Notification.objects.all().order_by('-created_at')  # إضافة ترتيب لحل مشكلة pagination
        return Notification.objects.filter(recipient=user).order_by('-created_at')  # إضافة ترتيب

    def get_permissions(self):
        if self.action in ['list', 'retrieve', 'mark_as_read', 'history']:
            return [permissions.IsAuthenticated()]
        return [permissions.IsAdminUser()]

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def mark_as_read(self, request, pk=None):
        notification = self.get_object()

        # Allow admin to mark any notification as read, regular users only their own
        if not request.user.is_staff and notification.recipient != request.user:
            return Response({'detail': 'غير مصرح لك'}, status=status.HTTP_403_FORBIDDEN)

        if not notification.read_at:
            notification.read_at = timezone.now()
            notification.save()
        return Response({'status': 'تمت القراءة'})

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def history(self, request):
        notifications = Notification.objects.filter(recipient=request.user).order_by('-created_at')
        serializer = self.get_serializer(notifications, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], permission_classes=[permissions.IsAdminUser])
    def send(self, request):
        """Send a notification to a user or users using a template or custom content."""
        data = request.data
        recipients = data.get('recipients')  # list of user IDs
        template_id = data.get('template_id')
        subject_ar = data.get('subject_ar')
        content_ar = data.get('content_ar')
        notification_type = data.get('notification_type')
        scheduled_at = data.get('scheduled_at')
        if not recipients:
            return Response({'detail': 'يجب تحديد المستلمين'}, status=status.HTTP_400_BAD_REQUEST)
        users = CustomUser.objects.filter(id__in=recipients)
        template = None
        if template_id:
            try:
                template = NotificationTemplate.objects.get(id=template_id)
            except NotificationTemplate.DoesNotExist:
                return Response({'detail': 'قالب الإشعار غير موجود'}, status=status.HTTP_400_BAD_REQUEST)
        notifications = []
        for user in users:
            from .utils import create_notification_safe
            notif = create_notification_safe(
                recipient=user,
                subject_ar=subject_ar or (template.subject_ar if template else ''),
                content_ar=content_ar or (template.content_ar if template else ''),
                notification_type=notification_type or (template.notification_type if template else 'in_app'),
                template=template,
                deduplication_minutes=1  # منع التكرار لمدة دقيقة واحدة للإرسال اليدوي
            )
            if notif:  # إضافة فقط إذا تم إنشاء الإشعار (لم يكن مكرر)
                notifications.append(notif)
        return Response({'status': 'تم إرسال الإشعارات', 'count': len(notifications)})
