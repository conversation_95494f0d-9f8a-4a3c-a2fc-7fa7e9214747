from django.db import models
from authentication.models import CustomUser

# Create your models here.

class NotificationTemplate(models.Model):
    NOTIFICATION_TYPE_CHOICES = [
        ('email', 'Email'),
        ('in_app', 'In-App'),
        ('sms', 'SMS'),
    ]
    name = models.CharField(max_length=100, verbose_name='اسم القالب')
    subject_ar = models.CharField(max_length=255, verbose_name='الموضوع (عربي)')
    content_ar = models.TextField(verbose_name='المحتوى (عربي)')
    notification_type = models.Char<PERSON><PERSON>(max_length=20, choices=NOTIFICATION_TYPE_CHOICES, verbose_name='نوع الإشعار')
    is_active = models.BooleanField(default=True, verbose_name='نشط')

    def __str__(self):
        return f"{self.name} ({self.notification_type})"

class Notification(models.Model):
    STATUS_CHOICES = [
        ('pending', 'قيد الانتظار'),
        ('sent', 'تم الإرسال'),
        ('failed', 'فشل'),
    ]
    recipient = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='notifications', verbose_name='المستلم')
    template = models.ForeignKey(NotificationTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='notifications', verbose_name='قالب الإشعار')
    subject_ar = models.CharField(max_length=255, verbose_name='الموضوع (عربي)')
    content_ar = models.TextField(verbose_name='المحتوى (عربي)')
    notification_type = models.CharField(max_length=20, choices=NotificationTemplate.NOTIFICATION_TYPE_CHOICES, verbose_name='نوع الإشعار')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='الحالة')
    scheduled_at = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ الجدولة')
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ الإرسال')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ القراءة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def __str__(self):
        return f"{self.subject_ar} -> {self.recipient.email}"
