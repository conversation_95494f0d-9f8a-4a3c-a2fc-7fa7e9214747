from django.contrib import admin
from .models import NotificationTemplate, Notification

@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'notification_type', 'is_active')
    search_fields = ('name', 'subject_ar', 'content_ar')
    list_filter = ('notification_type', 'is_active')
    ordering = ('-id',)

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('recipient', 'subject_ar', 'notification_type', 'status', 'scheduled_at', 'sent_at', 'read_at', 'created_at')
    search_fields = ('recipient__email', 'subject_ar', 'content_ar')
    list_filter = ('notification_type', 'status', 'created_at', 'scheduled_at', 'sent_at', 'read_at')
    ordering = ('-created_at',)
