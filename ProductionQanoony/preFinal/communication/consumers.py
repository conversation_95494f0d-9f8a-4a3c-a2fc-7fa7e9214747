import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Chat<PERSON>oom, Message, CustomUser

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f'chat_{self.room_id}'
        self.user = self.scope['user']

        # Only allow authenticated users who are part of the room
        if self.user.is_anonymous or not await self.is_user_in_room():
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        await self.accept()

    async def disconnect(self, close_code):
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

    async def receive(self, text_data):
        data = json.loads(text_data)
        message = data.get('message')
        
        if not message or self.user.is_anonymous:
            return

        # Save message to DB
        msg_obj = await self.create_message(message)

        # Broadcast message to room group (excluding sender)
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message',
                'id': str(msg_obj.id),
                'message': msg_obj.content,
                'sender_id': str(self.user.id),
                'sender_email': self.user.email,
                'sender_name': f"{self.user.first_name} {self.user.last_name}",
                'timestamp': str(msg_obj.timestamp),
                'message_type': msg_obj.message_type,
                'exclude_sender': self.channel_name,  # Exclude sender from receiving
            }
        )

    async def chat_message(self, event):
        # Don't send message back to sender
        if event.get('exclude_sender') == self.channel_name:
            return

        # Send message to WebSocket
        # Remove 'type' and 'exclude_sender' from event before sending to client
        message_data = {k: v for k, v in event.items() if k not in ['type', 'exclude_sender']}
        await self.send(text_data=json.dumps(message_data))

    @database_sync_to_async
    def is_user_in_room(self):
        try:
            room = ChatRoom.objects.get(id=self.room_id)

            # Check if user is a participant
            if room.participants.filter(id=self.user.id).exists():
                return True

            # For instructor-student chats, check specific permissions
            if room.room_type == 'instructor_student':
                # Check if user is the instructor in this chat
                if room.instructor and room.instructor.id == self.user.id:
                    return True

                # Check if user is the student in this chat
                if room.student and room.student.user.id == self.user.id:
                    return True

            # Admin can access all rooms
            if getattr(self.user, 'is_staff', False):
                return True

            return False
        except ChatRoom.DoesNotExist:
            return False

    @database_sync_to_async
    def create_message(self, content):
        from django.utils import timezone
        room = ChatRoom.objects.get(id=self.room_id)

        # Create message
        message = Message.objects.create(
            room=room,
            sender=self.user,
            content=content,
            message_type='text'
        )

        # Update room's last_message_at
        room.last_message_at = timezone.now()
        room.save(update_fields=['last_message_at'])

        return message