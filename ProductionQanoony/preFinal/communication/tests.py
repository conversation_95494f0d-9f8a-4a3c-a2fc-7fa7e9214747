from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import ChatRoom, Message, ForumCategory, ForumTopic, ForumPost
from courses.models import Subject
from universities.models import AcademicYear, University
from channels.testing import WebsocketCommunicator
from config.asgi import application
import json
import asyncio

User = get_user_model()

class CommunicationTests(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='pass', first_name='User', last_name='Test', email_verified=True)
        self.moderator = User.objects.create_user(email='<EMAIL>', password='pass', first_name='Mod', last_name='Test', is_staff=True, email_verified=True)
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.university = University.objects.create(name_ar='جامعة', name_en='University', code='UNI', city='Cairo')
        self.academic_year = AcademicYear.objects.create(university=self.university, year_number=1, year_name_ar='سنة أولى', year_name_en='First Year')
        self.subject = Subject.objects.create(name_ar='مادة', name_en='Subject', code='SUBJ1', description_ar='وصف', academic_year=self.academic_year)

    def test_chatroom_creation(self):
        url = reverse('chatroom-list')
        data = {'name': 'Test Room', 'room_type': 'group', 'created_by': self.user.id, 'participants': [self.user.id]}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Room')

    def test_chatroom_join(self):
        room = ChatRoom.objects.create(name='Room', room_type='group', created_by=self.user)
        url = reverse('chatroom-join', args=[room.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn(self.user, room.participants.all())

    def test_message_creation(self):
        room = ChatRoom.objects.create(name='Room', room_type='group', created_by=self.user)
        room.participants.add(self.user)
        url = reverse('message-list')
        data = {'room': room.id, 'sender': self.user.id, 'content': 'Hello', 'message_type': 'text'}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['content'], 'Hello')

    def test_forum_category_topic_post_creation(self):
        category = ForumCategory.objects.create(subject=self.subject, name_ar='تصنيف', description_ar='وصف', order=1)
        topic_url = reverse('forumtopic-list')
        topic_data = {'category': category.id, 'title_ar': 'موضوع', 'content_ar': 'محتوى', 'author': self.user.id}
        topic_response = self.client.post(topic_url, topic_data, format='json')
        self.assertEqual(topic_response.status_code, status.HTTP_201_CREATED)
        topic_id = topic_response.data['id']
        post_url = reverse('forumpost-list')
        post_data = {'topic': topic_id, 'content_ar': 'رد', 'author': self.user.id}
        post_response = self.client.post(post_url, post_data, format='json')
        self.assertEqual(post_response.status_code, status.HTTP_201_CREATED)

    def test_forum_topic_pin_lock_permissions(self):
        category = ForumCategory.objects.create(subject=self.subject, name_ar='تصنيف', description_ar='وصف', order=1)
        topic = ForumTopic.objects.create(category=category, title_ar='موضوع', content_ar='محتوى', author=self.user)
        url_pin = reverse('forumtopic-pin', args=[topic.id])
        url_lock = reverse('forumtopic-lock', args=[topic.id])
        # Non-moderator
        response = self.client.post(url_pin)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        # Moderator
        self.client.force_authenticate(user=self.moderator)
        response = self.client.post(url_pin)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        topic.refresh_from_db()
        self.assertTrue(topic.is_pinned)
        response = self.client.post(url_lock)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        topic.refresh_from_db()
        self.assertTrue(topic.is_locked)

    def test_forum_post_approval_permissions(self):
        category = ForumCategory.objects.create(subject=self.subject, name_ar='تصنيف', description_ar='وصف', order=1)
        topic = ForumTopic.objects.create(category=category, title_ar='موضوع', content_ar='محتوى', author=self.user)
        post = ForumPost.objects.create(topic=topic, content_ar='رد', author=self.user)
        # Non-moderator
        url = reverse('forumpost-approve', args=[post.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        # Moderator
        self.client.force_authenticate(user=self.moderator)
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        post.refresh_from_db()
        self.assertTrue(post.is_approved)

    def test_permissions_enforcement(self):
        self.client.logout()
        url = reverse('chatroom-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_multiple_users_chatroom_and_message(self):
        user2 = User.objects.create_user(email='<EMAIL>', password='pass', first_name='User2', last_name='Test', email_verified=True)
        room = ChatRoom.objects.create(name='Multi Room', room_type='group', created_by=self.user)
        room.participants.add(self.user, user2)
        url = reverse('message-list')
        data = {'room': room.id, 'sender': user2.id, 'content': 'Hi from user2', 'message_type': 'text'}
        self.client.force_authenticate(user=user2)
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['content'], 'Hi from user2')
        self.assertEqual(response.data['sender'], user2.id)

    def test_message_read_status(self):
        room = ChatRoom.objects.create(name='Read Room', room_type='group', created_by=self.user)
        room.participants.add(self.user)
        msg = Message.objects.create(room=room, sender=self.user, content='Read me', message_type='text')
        self.assertFalse(msg.is_read)
        # Simulate marking as read
        msg.is_read = True
        msg.save()
        self.assertTrue(Message.objects.get(id=msg.id).is_read)

    def test_forum_topic_view_count(self):
        category = ForumCategory.objects.create(subject=self.subject, name_ar='تصنيف', description_ar='وصف', order=1)
        topic = ForumTopic.objects.create(category=category, title_ar='موضوع', content_ar='محتوى', author=self.user)
        self.assertEqual(topic.view_count, 0)
        # Simulate a view
        topic.view_count += 1
        topic.save()
        self.assertEqual(ForumTopic.objects.get(id=topic.id).view_count, 1)

    def test_invalid_chatroom_creation(self):
        url = reverse('chatroom-list')
        data = {'name': '', 'room_type': 'invalid', 'created_by': 999, 'participants': []}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_unauthorized_forum_post(self):
        self.client.logout()
        category = ForumCategory.objects.create(subject=self.subject, name_ar='تصنيف', description_ar='وصف', order=1)
        topic = ForumTopic.objects.create(category=category, title_ar='موضوع', content_ar='محتوى', author=self.user)
        post_url = reverse('forumpost-list')
        post_data = {'topic': topic.id, 'content_ar': 'رد', 'author': self.user.id}
        response = self.client.post(post_url, post_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @classmethod
    def tearDownClass(cls):
        # Clean up async loop for Channels tests
        try:
            asyncio.get_event_loop().close()
        except Exception:
            pass

class CommunicationChannelsTests(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(email='<EMAIL>', password='pass', first_name='Ws', last_name='User', email_verified=True)
        cls.room = ChatRoom.objects.create(name='WS Room', room_type='group', created_by=cls.user)
        cls.room.participants.add(cls.user)

    async def test_websocket_chat(self):
        communicator = WebsocketCommunicator(application, f'/ws/chat/{self.room.id}/')
        communicator.scope['user'] = self.user
        connected, _ = await communicator.connect()
        self.assertTrue(connected)
        await communicator.send_json_to({"message": "Hello WS!", "message_type": "text"})
        response = await communicator.receive_json_from()
        self.assertEqual(response['message'], 'Hello WS!')
        self.assertEqual(response['sender_id'], str(self.user.id))
        self.assertTrue(response['notification'])
        await communicator.disconnect() 