from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedel<PERSON>
from .models import ChatRoom, Message, ForumCategory, ForumTopic, ForumPost
from .serializers import (
    ChatRoomSerializer, MessageSerializer, ForumCategorySerializer,
    ForumTopicSerializer, ForumPostSerializer
)
from .permissions import IsAuthenticated, IsModeratorOrAdmin
from ai_assistant.permissions import IsStaffOrActiveSubscriptionOrDenied

class ChatRoomViewSet(viewsets.ModelViewSet):
    queryset = ChatRoom.objects.all().order_by('-created_at')  # إضافة ترتيب لحل مشكلة pagination
    serializer_class = ChatRoomSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'room_type']

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        # For now, allow all authenticated users to create chat rooms
        # Later we can add subscription checks if needed
        return [permissions.IsAuthenticated()]

    def get_queryset(self):
        user = self.request.user

        if user.is_staff:
            # Admin can see all chat rooms
            return ChatRoom.objects.all()
        elif getattr(user, 'is_instructor', False):
            # Instructor can see their chats with students
            return ChatRoom.objects.filter(
                Q(room_type='instructor_student', instructor=user) |
                Q(participants=user)
            ).distinct()
        elif getattr(user, 'is_student', False):
            # Student can see their chats with instructors
            return ChatRoom.objects.filter(
                Q(room_type='instructor_student', student__user=user) |
                Q(participants=user)
            ).distinct()
        else:
            # Other users can see rooms they participate in
            return ChatRoom.objects.filter(participants=user)

    def perform_create(self, serializer):
        """
        Set the created_by field to the current user and add them as a participant.
        """
        chat_room = serializer.save(created_by=self.request.user)
        chat_room.participants.add(self.request.user)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated, IsStaffOrActiveSubscriptionOrDenied])
    def join(self, request, pk=None):
        room = self.get_object()
        room.participants.add(request.user)
        return Response({'status': 'joined'}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['post'])
    def create_instructor_chat(self, request):
        """Create a new instructor-student chat room"""
        from students.models import StudentProfile
        from courses.models import Subject

        # Only students can create instructor chats
        if not getattr(request.user, 'is_student', False):
            return Response({
                'error': 'فقط الطلاب يمكنهم إنشاء محادثات مع المدرسين'
            }, status=status.HTTP_403_FORBIDDEN)

        instructor_id = request.data.get('instructor_id')
        subject_id = request.data.get('subject_id')  # Optional
        title = request.data.get('title', '')

        if not instructor_id:
            return Response({
                'error': 'معرف المدرس مطلوب'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get instructor
            from authentication.models import CustomUser
            instructor = CustomUser.objects.get(id=instructor_id, is_instructor=True)

            # Get student profile
            student_profile = StudentProfile.objects.get(user=request.user)

            # Get subject if provided
            subject = None
            if subject_id:
                subject = Subject.objects.get(id=subject_id)

            # Check if chat already exists
            existing_chat = ChatRoom.objects.filter(
                room_type='instructor_student',
                student=student_profile,
                instructor=instructor,
                subject=subject
            ).first()

            if existing_chat:
                return Response({
                    'message': 'محادثة موجودة بالفعل مع هذا المدرس',
                    'chat_room': ChatRoomSerializer(existing_chat).data
                }, status=status.HTTP_200_OK)

            # Create new chat room
            chat_room = ChatRoom.objects.create(
                name=title or f"محادثة مع {instructor.first_name} {instructor.last_name}",
                room_type='instructor_student',
                created_by=request.user,
                student=student_profile,
                instructor=instructor,
                subject=subject
            )

            # Add participants
            chat_room.participants.add(request.user, instructor)

            return Response(
                ChatRoomSerializer(chat_room).data,
                status=status.HTTP_201_CREATED
            )

        except CustomUser.DoesNotExist:
            return Response({
                'error': 'المدرس غير موجود'
            }, status=status.HTTP_404_NOT_FOUND)
        except StudentProfile.DoesNotExist:
            return Response({
                'error': 'ملف الطالب غير موجود'
            }, status=status.HTTP_404_NOT_FOUND)
        except Subject.DoesNotExist:
            return Response({
                'error': 'الموضوع غير موجود'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'حدث خطأ في إنشاء المحادثة',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class MessageViewSet(viewsets.ModelViewSet):
    queryset = Message.objects.all()
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['content']
    ordering_fields = ['timestamp']
    ordering = ['timestamp']  # Default ordering by timestamp

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.request.user.is_staff:
            # Admin users can access all data
            permission_classes = [permissions.IsAuthenticated]
        else:
            # Regular users need active subscription
            permission_classes = [IsAuthenticated, IsStaffOrActiveSubscriptionOrDenied]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Filter messages based on room and user permissions
        """
        user = self.request.user
        queryset = Message.objects.all()

        # Filter by room if specified
        room_id = self.request.query_params.get('room', None)
        if room_id:
            queryset = queryset.filter(room_id=room_id)

        # Apply user-based filtering
        if user.is_staff:
            # Admin can see all messages
            pass
        elif getattr(user, 'is_instructor', False):
            # Instructor can see messages from their rooms
            queryset = queryset.filter(
                Q(room__instructor=user) |
                Q(room__participants=user)
            ).distinct()
        elif getattr(user, 'is_student', False):
            # Student can see messages from their rooms
            queryset = queryset.filter(
                Q(room__student__user=user) |
                Q(room__participants=user)
            ).distinct()
        else:
            # Other users can only see messages from rooms they participate in
            queryset = queryset.filter(room__participants=user)

        return queryset.select_related('sender', 'room').order_by('timestamp')

    # Real-time send/receive will be handled via Django Channels consumers

class ForumCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ForumCategory.objects.all()
    serializer_class = ForumCategorySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.request.user.is_staff:
            # Admin users can access all data
            permission_classes = [permissions.IsAuthenticated]
        else:
            # Regular users need active subscription
            permission_classes = [IsAuthenticated, IsStaffOrActiveSubscriptionOrDenied]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'], url_path='by_subject')
    def by_subject(self, request):
        subject_id = request.query_params.get('subject')
        if not subject_id:
            return Response({'detail': 'يرجى تحديد رقم المادة (subject).'}, status=400)
        categories = ForumCategory.objects.filter(subject_id=subject_id, is_active=True)
        serializer = self.get_serializer(categories, many=True)
        return Response(serializer.data)

class ForumTopicViewSet(viewsets.ModelViewSet):
    queryset = ForumTopic.objects.all()
    serializer_class = ForumTopicSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['title_ar', 'content_ar']

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.request.user.is_staff:
            # Admin users can access all data
            permission_classes = [permissions.IsAuthenticated]
        else:
            # Regular users need active subscription
            permission_classes = [IsAuthenticated, IsStaffOrActiveSubscriptionOrDenied]
        return [permission() for permission in permission_classes]

    @action(detail=True, methods=['post'], permission_classes=[IsModeratorOrAdmin, IsStaffOrActiveSubscriptionOrDenied])
    def pin(self, request, pk=None):
        topic = self.get_object()
        topic.is_pinned = True
        topic.save()
        return Response({'status': 'pinned'})

    @action(detail=True, methods=['post'], permission_classes=[IsModeratorOrAdmin, IsStaffOrActiveSubscriptionOrDenied])
    def lock(self, request, pk=None):
        topic = self.get_object()
        topic.is_locked = True
        topic.save()
        return Response({'status': 'locked'})

class ForumPostViewSet(viewsets.ModelViewSet):
    queryset = ForumPost.objects.all()
    serializer_class = ForumPostSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['content_ar']

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.request.user.is_staff:
            # Admin users can access all data
            permission_classes = [permissions.IsAuthenticated]
        else:
            # Regular users need active subscription
            permission_classes = [IsAuthenticated, IsStaffOrActiveSubscriptionOrDenied]
        return [permission() for permission in permission_classes]

    @action(detail=True, methods=['post'], permission_classes=[IsModeratorOrAdmin, IsStaffOrActiveSubscriptionOrDenied])
    def approve(self, request, pk=None):
        post = self.get_object()
        post.is_approved = True
        post.save()
        return Response({'status': 'approved'})


class CommunicationStatsView(APIView):
    """
    Admin view for communication statistics
    """
    permission_classes = [permissions.IsAdminUser]

    def get(self, request):
        """Get communication statistics for admin dashboard"""
        try:
            # Basic counts
            total_chat_rooms = ChatRoom.objects.count()
            active_chat_rooms = ChatRoom.objects.filter(is_active=True).count()
            total_messages = Message.objects.count()
            unread_messages = Message.objects.filter(is_read=False).count()

            # Forum stats
            total_forum_categories = ForumCategory.objects.count()
            active_forum_categories = ForumCategory.objects.filter(is_active=True).count()
            total_forum_topics = ForumTopic.objects.count()
            pinned_topics = ForumTopic.objects.filter(is_pinned=True).count()
            locked_topics = ForumTopic.objects.filter(is_locked=True).count()

            # Forum posts
            total_forum_posts = ForumPost.objects.count()
            pending_posts = ForumPost.objects.filter(is_approved=False).count()
            approved_posts = ForumPost.objects.filter(is_approved=True).count()

            # Recent activity (last 7 days)
            week_ago = timezone.now() - timedelta(days=7)
            recent_messages = Message.objects.filter(timestamp__gte=week_ago).count()
            recent_topics = ForumTopic.objects.filter(created_at__gte=week_ago).count()
            recent_posts = ForumPost.objects.filter(created_at__gte=week_ago).count()

            # Chat room types distribution
            room_types = ChatRoom.objects.values('room_type').annotate(
                count=Count('id')
            ).order_by('room_type')

            # Message types distribution
            message_types = Message.objects.values('message_type').annotate(
                count=Count('id')
            ).order_by('message_type')

            # Most active chat rooms (by message count)
            active_rooms = ChatRoom.objects.annotate(
                message_count=Count('messages')
            ).order_by('-message_count')[:5]

            # Most viewed forum topics
            popular_topics = ForumTopic.objects.order_by('-view_count')[:5]

            return Response({
                'basic_stats': {
                    'total_chat_rooms': total_chat_rooms,
                    'active_chat_rooms': active_chat_rooms,
                    'total_messages': total_messages,
                    'unread_messages': unread_messages,
                    'total_forum_categories': total_forum_categories,
                    'active_forum_categories': active_forum_categories,
                    'total_forum_topics': total_forum_topics,
                    'pinned_topics': pinned_topics,
                    'locked_topics': locked_topics,
                    'total_forum_posts': total_forum_posts,
                    'pending_posts': pending_posts,
                    'approved_posts': approved_posts,
                },
                'recent_activity': {
                    'recent_messages': recent_messages,
                    'recent_topics': recent_topics,
                    'recent_posts': recent_posts,
                },
                'distributions': {
                    'room_types': list(room_types),
                    'message_types': list(message_types),
                },
                'top_content': {
                    'active_rooms': [
                        {
                            'id': room.id,
                            'name': room.name,
                            'message_count': room.message_count,
                            'room_type': room.room_type
                        } for room in active_rooms
                    ],
                    'popular_topics': [
                        {
                            'id': topic.id,
                            'title_ar': topic.title_ar,
                            'view_count': topic.view_count,
                            'category': topic.category.name_ar if topic.category else None
                        } for topic in popular_topics
                    ]
                }
            })

        except Exception as e:
            return Response(
                {'error': 'Failed to fetch communication stats', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )