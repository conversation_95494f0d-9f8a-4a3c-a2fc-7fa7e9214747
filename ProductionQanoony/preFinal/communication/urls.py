from django.urls import path
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import (
    ChatRoomViewSet, MessageViewSet, ForumCategoryViewSet,
    ForumTopicViewSet, ForumPostViewSet, CommunicationStatsView
)

router = DefaultRouter()
router.register(r'chatrooms', ChatRoomViewSet)
router.register(r'messages', MessageViewSet)
router.register(r'forum-categories', ForumCategoryViewSet)
router.register(r'forum-topics', ForumTopicViewSet)
router.register(r'forum-posts', ForumPostViewSet)

urlpatterns = router.urls + [
    path('admin/stats/', CommunicationStatsView.as_view(), name='communication-stats'),
]