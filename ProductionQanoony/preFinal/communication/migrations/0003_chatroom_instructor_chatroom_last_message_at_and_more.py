# Generated by Django 4.2.7 on 2025-07-09 15:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('students', '0001_initial'),
        ('courses', '0002_initial'),
        ('communication', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='chatroom',
            name='instructor',
            field=models.ForeignKey(blank=True, help_text='المدرس في المحادثة', limit_choices_to={'is_instructor': True}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='instructor_chats', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='chatroom',
            name='last_message_at',
            field=models.DateTimeField(blank=True, help_text='وقت آخر رسالة', null=True),
        ),
        migrations.AddField(
            model_name='chatroom',
            name='student',
            field=models.ForeignKey(blank=True, help_text='الطالب في المحادثة', null=True, on_delete=django.db.models.deletion.CASCADE, to='students.studentprofile'),
        ),
        migrations.AddField(
            model_name='chatroom',
            name='subject',
            field=models.ForeignKey(blank=True, help_text='الموضوع المرتبط بالمحادثة (اختياري)', null=True, on_delete=django.db.models.deletion.SET_NULL, to='courses.subject'),
        ),
    ]
