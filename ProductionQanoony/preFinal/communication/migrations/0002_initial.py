# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('communication', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='forumcategory',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='forum_categories', to='courses.subject'),
        ),
        migrations.AddField(
            model_name='communicationnotification',
            name='recipient',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='communication_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستلم'),
        ),
        migrations.AddField(
            model_name='chatroom',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_chatrooms', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='chatroom',
            name='participants',
            field=models.ManyToManyField(related_name='chatrooms', to=settings.AUTH_USER_MODEL),
        ),
    ]
