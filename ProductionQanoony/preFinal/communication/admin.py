from django.contrib import admin
from .models import ChatRoom, Message, ForumCategory, ForumTopic, ForumPost

class ChatRoomAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'room_type', 'created_by', 'created_at', 'is_active')
    search_fields = ('name', 'created_by__email')
    list_filter = ('room_type', 'is_active')
    filter_horizontal = ('participants',)

class MessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'room', 'sender', 'message_type', 'timestamp', 'is_read')
    search_fields = ('room__name', 'sender__email', 'content')
    list_filter = ('message_type', 'is_read')

class ForumCategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name_ar', 'subject', 'order', 'is_active')
    search_fields = ('name_ar', 'subject__name')
    list_filter = ('is_active',)

class ForumTopicAdmin(admin.ModelAdmin):
    list_display = ('id', 'title_ar', 'category', 'author', 'created_at', 'is_pinned', 'is_locked', 'view_count')
    search_fields = ('title_ar', 'author__email', 'category__name_ar')
    list_filter = ('is_pinned', 'is_locked', 'category')

class ForumPostAdmin(admin.ModelAdmin):
    list_display = ('id', 'topic', 'author', 'created_at', 'is_approved')
    search_fields = ('topic__title_ar', 'author__email', 'content_ar')
    list_filter = ('is_approved', 'topic')

admin.site.register(ChatRoom, ChatRoomAdmin)
admin.site.register(Message, MessageAdmin)
admin.site.register(ForumCategory, ForumCategoryAdmin)
admin.site.register(ForumTopic, ForumTopicAdmin)
admin.site.register(ForumPost, ForumPostAdmin) 