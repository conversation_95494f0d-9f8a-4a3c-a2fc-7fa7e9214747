from rest_framework import serializers
from .models import Chat<PERSON>oom, Message, ForumCategory, ForumTopic, ForumPost
from django.contrib.auth import get_user_model

User = get_user_model()

class ChatRoomSerializer(serializers.ModelSerializer):
    participants = serializers.PrimaryKeyRelatedField(many=True, queryset=User.objects.all(), required=False)
    created_by = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), required=False)

    # Add detailed info for instructor-student chats
    instructor_name = serializers.SerializerMethodField()
    student_name = serializers.SerializerMethodField()
    subject_name = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()

    class Meta:
        model = ChatRoom
        fields = '__all__'
        read_only_fields = ('created_at', 'is_active', 'last_message_at')

    def get_instructor_name(self, obj):
        if obj.instructor:
            return f"{obj.instructor.first_name} {obj.instructor.last_name}"
        return None

    def get_student_name(self, obj):
        if obj.student:
            return f"{obj.student.user.first_name} {obj.student.user.last_name}"
        return None

    def get_subject_name(self, obj):
        if obj.subject:
            return obj.subject.name_ar
        return None

    def get_last_message(self, obj):
        last_msg = obj.messages.order_by('-timestamp').first()
        if last_msg:
            return {
                'content': last_msg.content,
                'sender': last_msg.sender.first_name,
                'timestamp': last_msg.timestamp
            }
        return None

    def get_unread_count(self, obj):
        # Get current user from context
        request = self.context.get('request')
        if request and request.user:
            return obj.messages.filter(
                is_read=False
            ).exclude(sender=request.user).count()
        return 0

class MessageSerializer(serializers.ModelSerializer):
    sender = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())
    file_attachment = serializers.FileField(required=False, allow_null=True)

    class Meta:
        model = Message
        fields = '__all__'

    def validate(self, data):
        message_type = data.get('message_type')
        file_attachment = data.get('file_attachment')
        content = data.get('content')
        if message_type == 'text' and not content:
            raise serializers.ValidationError('Text messages must have content.')
        if message_type in ['file', 'image'] and not file_attachment:
            raise serializers.ValidationError('File or image messages must have an attachment.')
        return data

class ForumPostSerializer(serializers.ModelSerializer):
    author = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())

    class Meta:
        model = ForumPost
        fields = '__all__'

class ForumTopicSerializer(serializers.ModelSerializer):
    author = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())
    posts = ForumPostSerializer(many=True, read_only=True)

    class Meta:
        model = ForumTopic
        fields = '__all__'

class ForumCategorySerializer(serializers.ModelSerializer):
    topics = ForumTopicSerializer(many=True, read_only=True)

    class Meta:
        model = ForumCategory
        fields = '__all__' 