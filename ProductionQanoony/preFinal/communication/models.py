from django.db import models
from django.conf import settings
from authentication.models import CustomUser


class ChatRoom(models.Model):
    ROOM_TYPE_CHOICES = [
        ('private', 'Private'),
        ('group', 'Group'),
        ('instructor_student', 'Instructor-Student'),
    ]
    name = models.CharField(max_length=255)
    room_type = models.CharField(max_length=32, choices=ROOM_TYPE_CHOICES)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_chatrooms')
    participants = models.ManyToManyField(
        settings.AUTH_USER_MODEL, related_name='chatrooms')
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    # New fields for instructor-student chats
    subject = models.ForeignKey(
        'courses.Subject', on_delete=models.SET_NULL, null=True, blank=True,
        help_text="الموضوع المرتبط بالمحادثة (اختياري)")
    student = models.ForeignKey(
        'students.StudentProfile', on_delete=models.CASCADE, null=True, blank=True,
        help_text="الطالب في المحادثة")
    instructor = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
        related_name='instructor_chats', null=True, blank=True,
        limit_choices_to={'is_instructor': True},
        help_text="المدرس في المحادثة")
    last_message_at = models.DateTimeField(null=True, blank=True,
        help_text="وقت آخر رسالة")

    def __str__(self):
        return self.name


class Message(models.Model):
    MESSAGE_TYPE_CHOICES = [
        ('text', 'Text'),
        ('file', 'File'),
        ('image', 'Image'),
    ]
    room = models.ForeignKey(
        ChatRoom, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='sent_messages')
    content = models.TextField(blank=True)
    message_type = models.CharField(
        max_length=16, choices=MESSAGE_TYPE_CHOICES)
    file_attachment = models.FileField(
        upload_to='attachments/', blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    edited_at = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"{self.sender} in {self.room}: {self.content[:30]}"


class ForumCategory(models.Model):
    subject = models.ForeignKey(
        'courses.Subject', on_delete=models.CASCADE, related_name='forum_categories')
    name_ar = models.CharField(max_length=255)
    description_ar = models.TextField()
    order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name_ar


class ForumTopic(models.Model):
    category = models.ForeignKey(
        ForumCategory, on_delete=models.CASCADE, related_name='topics')
    title_ar = models.CharField(max_length=255)
    content_ar = models.TextField()
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='forum_topics')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_pinned = models.BooleanField(default=False)
    is_locked = models.BooleanField(default=False)
    view_count = models.IntegerField(default=0)

    def __str__(self):
        return self.title_ar


class ForumPost(models.Model):
    topic = models.ForeignKey(
        ForumTopic, on_delete=models.CASCADE, related_name='posts')
    content_ar = models.TextField()
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='forum_posts')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_approved = models.BooleanField(default=False)

    def __str__(self):
        return f"Post by {self.author} on {self.topic}"


class CommunicationNotification(models.Model):
    recipient = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                  related_name='communication_notifications', verbose_name='المستلم')
    message = models.TextField(verbose_name='الرسالة')
    is_read = models.BooleanField(default=False, verbose_name='تمت القراءة')
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name='تاريخ الإنشاء')
    link = models.URLField(blank=True, null=True, verbose_name='رابط ذو صلة')

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'إشعار (التواصل)'
        verbose_name_plural = 'الإشعارات (التواصل)'

    def __str__(self):
        return f"إشعار إلى {self.recipient.email}: {self.message[:30]}"
