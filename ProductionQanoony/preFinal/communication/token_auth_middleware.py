import jwt
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from channels.db import database_sync_to_async
from channels.middleware import BaseMiddleware

User = get_user_model()

@database_sync_to_async
def get_user(user_id):
    try:
        return User.objects.get(id=user_id)
    except User.DoesNotExist:
        return AnonymousUser()

class TokenAuthMiddleware(BaseMiddleware):
    def __init__(self, inner):
        super().__init__(inner)

    async def __call__(self, scope, receive, send):
        try:
            # Extract token from query string
            query_string = scope.get('query_string', b'').decode('utf-8')
            query_params = dict(qp.split('=') for qp in query_string.split('&'))
            token = query_params.get('token', None)

            if token:
                # Decode the token to get user_id
                decoded_data = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
                user_id = decoded_data.get('user_id')
                
                # Get the user from the database
                scope['user'] = await get_user(user_id)
            else:
                scope['user'] = AnonymousUser()
        except (jwt.ExpiredSignatureError, jwt.DecodeError, ValueError):
            scope['user'] = AnonymousUser()
        
        return await super().__call__(scope, receive, send)

def TokenAuthMiddlewareStack(inner):
    return TokenAuthMiddleware(inner) 