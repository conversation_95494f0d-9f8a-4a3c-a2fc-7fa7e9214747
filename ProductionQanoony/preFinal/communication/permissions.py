from rest_framework import permissions

class IsAuthenticated(permissions.BasePermission):
    """Allows access only to authenticated users."""
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated

class IsModeratorOrAdmin(permissions.BasePermission):
    """Allows access only to moderators (is_staff) or admins (is_superuser)."""
    def has_permission(self, request, view):
        return request.user and (request.user.is_staff or request.user.is_superuser) 