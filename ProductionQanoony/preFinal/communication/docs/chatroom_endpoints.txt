# ChatRoom Endpoints

## List Chat Rooms
- URL: /api/communication/chatrooms/
- Method: GET
- Description: List all chat rooms.
- Permissions: Authenticated users
- Request Example:
    GET /api/communication/chatrooms/
- Response Example:
    [
        {
            "id": 1,
            "name": "Test Room",
            "room_type": "group",
            "created_by": 2,
            "participants": [2],
            "created_at": "2024-06-01T12:00:00Z",
            "is_active": true
        }
    ]

## Create Chat Room
- URL: /api/communication/chatrooms/
- Method: POST
- Description: Create a new chat room.
- Permissions: Authenticated users
- Request Example:
    {
        "name": "Test Room",
        "room_type": "group",
        "created_by": 2,
        "participants": [2]
    }
- Response Example:
    {
        "id": 1,
        "name": "Test Room",
        "room_type": "group",
        "created_by": 2,
        "participants": [2],
        "created_at": "2024-06-01T12:00:00Z",
        "is_active": true
    }

## Join Chat Room
- URL: /api/communication/chatrooms/{id}/join/
- Method: POST
- Description: Join a chat room as a participant.
- Permissions: Authenticated users
- Request Example:
    POST /api/communication/chatrooms/1/join/
- Response Example:
    { "status": "joined" } 