# Forum Endpoints

## List Forum Categories
- URL: /api/communication/forum-categories/
- Method: GET
- Description: List all forum categories.
- Permissions: Authenticated users

## List Forum Topics
- URL: /api/communication/forum-topics/
- Method: GET
- Description: List all forum topics.
- Permissions: Authenticated users

## Create Forum Topic
- URL: /api/communication/forum-topics/
- Method: POST
- Description: Create a new forum topic.
- Permissions: Authenticated users
- Request Example:
    {
        "category": 1,
        "title_ar": "موضوع",
        "content_ar": "محتوى",
        "author": 2
    }
- Response Example:
    {
        "id": 1,
        "category": 1,
        "title_ar": "موضوع",
        "content_ar": "محتوى",
        "author": 2,
        "created_at": "2024-06-01T12:00:00Z",
        "updated_at": "2024-06-01T12:00:00Z",
        "is_pinned": false,
        "is_locked": false,
        "view_count": 0
    }

## Pin Forum Topic
- URL: /api/communication/forum-topics/{id}/pin/
- Method: POST
- Description: Pin a forum topic (moderators/admins only).
- Permissions: Moderators/Admins
- Request Example:
    POST /api/communication/forum-topics/1/pin/
- Response Example:
    { "status": "pinned" }

## Lock Forum Topic
- URL: /api/communication/forum-topics/{id}/lock/
- Method: POST
- Description: Lock a forum topic (moderators/admins only).
- Permissions: Moderators/Admins
- Request Example:
    POST /api/communication/forum-topics/1/lock/
- Response Example:
    { "status": "locked" }

## List Forum Posts
- URL: /api/communication/forum-posts/
- Method: GET
- Description: List all forum posts.
- Permissions: Authenticated users

## Create Forum Post
- URL: /api/communication/forum-posts/
- Method: POST
- Description: Create a new forum post.
- Permissions: Authenticated users
- Request Example:
    {
        "topic": 1,
        "content_ar": "رد",
        "author": 2
    }
- Response Example:
    {
        "id": 1,
        "topic": 1,
        "content_ar": "رد",
        "author": 2,
        "created_at": "2024-06-01T12:00:00Z",
        "updated_at": "2024-06-01T12:00:00Z",
        "is_approved": false
    }

## Approve Forum Post
- URL: /api/communication/forum-posts/{id}/approve/
- Method: POST
- Description: Approve a forum post (moderators/admins only).
- Permissions: Moderators/Admins
- Request Example:
    POST /api/communication/forum-posts/1/approve/
- Response Example:
    { "status": "approved" } 