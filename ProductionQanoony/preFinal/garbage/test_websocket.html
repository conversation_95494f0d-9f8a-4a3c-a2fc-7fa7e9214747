<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Chat Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #messages { border: 1px solid #ccc; height: 300px; overflow-y: scroll; padding: 10px; margin: 10px 0; }
        .message { margin: 5px 0; padding: 5px; background: #f0f0f0; border-radius: 5px; }
        .my-message { background: #007bff; color: white; text-align: right; }
        input[type="text"] { width: 70%; padding: 10px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .error { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>WebSocket Chat Test</h1>
    
    <div>
        <label>Room ID: </label>
        <input type="number" id="roomId" value="3" placeholder="Enter room ID">
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
    </div>
    
    <div id="status" class="status disconnected">Disconnected</div>
    
    <div id="messages"></div>
    
    <div>
        <input type="text" id="messageInput" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
        <button onclick="sendMessage()">Send</button>
    </div>

    <script>
        let chatSocket = null;
        let roomId = null;

        function connect() {
            roomId = document.getElementById('roomId').value;
            if (!roomId) {
                alert('Please enter a room ID');
                return;
            }

            // Close existing connection if any
            if (chatSocket) {
                chatSocket.close();
            }

            // Create WebSocket connection
            const wsScheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
            const wsPath = `${wsScheme}://${window.location.host}/ws/chat/${roomId}/`;
            
            updateStatus('Connecting...', 'error');
            
            chatSocket = new WebSocket(wsPath);

            chatSocket.onopen = function(e) {
                console.log('WebSocket connected');
                updateStatus('Connected to room ' + roomId, 'connected');
            };

            chatSocket.onmessage = function(e) {
                const data = JSON.parse(e.data);
                console.log('Message received:', data);
                addMessage(data.message, data.sender, false);
            };

            chatSocket.onclose = function(e) {
                console.log('WebSocket closed');
                updateStatus('Disconnected', 'disconnected');
                chatSocket = null;
            };

            chatSocket.onerror = function(e) {
                console.error('WebSocket error:', e);
                updateStatus('Connection error', 'error');
            };
        }

        function disconnect() {
            if (chatSocket) {
                chatSocket.close();
            }
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) {
                alert('Please enter a message');
                return;
            }

            if (!chatSocket || chatSocket.readyState !== WebSocket.OPEN) {
                alert('Not connected to chat');
                return;
            }

            chatSocket.send(JSON.stringify({
                'message': message
            }));

            addMessage(message, 'You', true);
            messageInput.value = '';
        }

        function addMessage(message, sender, isMyMessage) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message' + (isMyMessage ? ' my-message' : '');
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<strong>${sender}:</strong> ${message} <small>(${timestamp})</small>`;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(message, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + className;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Auto-connect on page load
        window.onload = function() {
            // You can uncomment this to auto-connect
            // connect();
        };
    </script>
</body>
</html>
