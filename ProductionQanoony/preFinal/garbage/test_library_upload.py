#!/usr/bin/env python3
"""
اختبار نظام رفع الملفات في المكتبة
"""

import requests
import os
from datetime import date

# إعدادات الاختبار
API_BASE_URL = "http://localhost:8000"
TEST_FILE_PATH = "test_document.pdf"

def create_test_file():
    """إنشاء ملف اختبار"""
    with open(TEST_FILE_PATH, 'w') as f:
        f.write("هذا ملف اختبار للمكتبة")
    print(f"تم إنشاء ملف الاختبار: {TEST_FILE_PATH}")

def test_document_upload():
    """اختبار رفع وثيقة جديدة"""
    print("🧪 اختبار رفع وثيقة جديدة...")
    
    # بيانات الوثيقة
    data = {
        'title_ar': 'وثيقة اختبار',
        'title_en': 'Test Document',
        'category_id': 1,  # يجب أن تكون فئة موجودة
        'description_ar': 'هذه وثيقة اختبار لنظام رفع الملفات',
        'file_type': 'pdf',
        'author': 'مطور النظام',
        'publication_date': str(date.today()),
        'tags': 'اختبار,نظام',
        'is_featured': False
    }
    
    # رفع الملف
    with open(TEST_FILE_PATH, 'rb') as f:
        files = {'file_url': f}
        
        # يجب إضافة token المصادقة هنا
        headers = {
            'Authorization': 'Bearer YOUR_ACCESS_TOKEN_HERE'
        }
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/api/library/documents/",
                data=data,
                files=files,
                headers=headers
            )
            
            if response.status_code == 201:
                print("✅ تم رفع الوثيقة بنجاح!")
                print(f"📄 معرف الوثيقة: {response.json().get('id')}")
                return response.json()
            else:
                print(f"❌ فشل في رفع الوثيقة: {response.status_code}")
                print(f"📝 الرسالة: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return None

def cleanup():
    """تنظيف ملفات الاختبار"""
    if os.path.exists(TEST_FILE_PATH):
        os.remove(TEST_FILE_PATH)
        print(f"🧹 تم حذف ملف الاختبار: {TEST_FILE_PATH}")

def main():
    """تشغيل الاختبارات"""
    print("🚀 بدء اختبار نظام رفع الملفات في المكتبة")
    print("=" * 50)
    
    try:
        # إنشاء ملف اختبار
        create_test_file()
        
        # اختبار رفع الوثيقة
        result = test_document_upload()
        
        if result:
            print("\n✅ جميع الاختبارات نجحت!")
        else:
            print("\n❌ بعض الاختبارات فشلت!")
            
    finally:
        # تنظيف
        cleanup()
        
    print("=" * 50)
    print("🏁 انتهاء الاختبارات")

if __name__ == "__main__":
    main()
