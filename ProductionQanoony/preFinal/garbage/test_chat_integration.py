#!/usr/bin/env python3
"""
🧪 سكريبت اختبار شامل لنظام المحادثات
يختبر التكامل بين Backend APIs والفرونت إند
"""

import os
import sys
import django
import requests
import json
import time
from datetime import datetime

# إعداد Django
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

try:
    django.setup()
    from authentication.models import CustomUser
    from communication.models import ChatRoom, Message
    from rest_framework_simplejwt.tokens import RefreshToken
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    sys.exit(1)

# إعدادات الاختبار
API_BASE_URL = "http://localhost:8000"
TEST_RESULTS = []

def log_test(test_name, success, details=""):
    """تسجيل نتائج الاختبار"""
    status = "✅ PASS" if success else "❌ FAIL"
    timestamp = datetime.now().strftime("%H:%M:%S")
    result = f"[{timestamp}] {status} {test_name}"
    if details:
        result += f" - {details}"
    print(result)
    TEST_RESULTS.append({
        'test': test_name,
        'success': success,
        'details': details,
        'timestamp': timestamp
    })

def get_admin_token():
    """الحصول على token للأدمن"""
    try:
        admin = CustomUser.objects.filter(is_staff=True).first()
        if not admin:
            log_test("Get Admin Token", False, "No admin user found")
            return None
        
        token = RefreshToken.for_user(admin)
        access_token = str(token.access_token)
        log_test("Get Admin Token", True, f"Token for {admin.email}")
        return access_token, admin
    except Exception as e:
        log_test("Get Admin Token", False, str(e))
        return None

def get_student_token():
    """الحصول على token لطالب"""
    try:
        student = CustomUser.objects.filter(is_student=True).first()
        if not student:
            log_test("Get Student Token", False, "No student user found")
            return None
        
        token = RefreshToken.for_user(student)
        access_token = str(token.access_token)
        log_test("Get Student Token", True, f"Token for {student.email}")
        return access_token, student
    except Exception as e:
        log_test("Get Student Token", False, str(e))
        return None

def test_instructors_api():
    """اختبار API المدرسين"""
    try:
        response = requests.get(f"{API_BASE_URL}/api/auth/instructors/")
        
        if response.status_code == 200:
            data = response.json()
            instructors = data.get('results', [])
            count = len(instructors)
            log_test("Instructors API", True, f"Found {count} instructors")
            return instructors
        else:
            log_test("Instructors API", False, f"Status: {response.status_code}")
            return []
    except Exception as e:
        log_test("Instructors API", False, str(e))
        return []

def test_chatrooms_api(token):
    """اختبار API المحادثات"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f"{API_BASE_URL}/api/communication/chatrooms/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            chatrooms = data if isinstance(data, list) else data.get('results', [])
            count = len(chatrooms)
            log_test("ChatRooms API", True, f"Found {count} chatrooms")
            return chatrooms
        else:
            log_test("ChatRooms API", False, f"Status: {response.status_code}")
            return []
    except Exception as e:
        log_test("ChatRooms API", False, str(e))
        return []

def test_create_chatroom(token, instructor_id):
    """اختبار إنشاء محادثة جديدة"""
    try:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'name': f'اختبار محادثة - {datetime.now().strftime("%H:%M:%S")}',
            'room_type': 'instructor_student',
            'instructor': instructor_id
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/communication/chatrooms/", 
            headers=headers,
            json=data
        )
        
        if response.status_code == 201:
            chatroom = response.json()
            log_test("Create ChatRoom", True, f"Created room ID: {chatroom.get('id')}")
            return chatroom
        else:
            log_test("Create ChatRoom", False, f"Status: {response.status_code}, Response: {response.text}")
            return None
    except Exception as e:
        log_test("Create ChatRoom", False, str(e))
        return None

def test_send_message(token, room_id, sender_user):
    """اختبار إرسال رسالة"""
    try:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

        data = {
            'room': room_id,
            'sender': str(sender_user.id),
            'content': f'رسالة اختبار - {datetime.now().strftime("%H:%M:%S")}',
            'message_type': 'text'
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/communication/messages/", 
            headers=headers,
            json=data
        )
        
        if response.status_code == 201:
            message = response.json()
            log_test("Send Message", True, f"Message ID: {message.get('id')}")
            return message
        else:
            log_test("Send Message", False, f"Status: {response.status_code}, Response: {response.text}")
            return None
    except Exception as e:
        log_test("Send Message", False, str(e))
        return None

def test_get_messages(token, room_id):
    """اختبار جلب الرسائل"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(
            f"{API_BASE_URL}/api/communication/messages/?room={room_id}",
            headers=headers
        )

        if response.status_code == 200:
            messages = response.json()
            count = len(messages) if isinstance(messages, list) else len(messages.get('results', []))
            log_test("Get Messages", True, f"Found {count} messages")
            return messages
        else:
            log_test("Get Messages", False, f"Status: {response.status_code}")
            return []
    except Exception as e:
        log_test("Get Messages", False, str(e))
        return []

def check_student_subscription(student_user):
    """فحص subscription الطالب"""
    try:
        from subscriptions.models import Subscription
        from django.utils import timezone

        # فحص subscriptions الطالب
        active_subs = Subscription.objects.filter(
            user=student_user,
            status='active',
            end_date__gte=timezone.now().date()
        )

        count = active_subs.count()
        if count > 0:
            sub = active_subs.first()
            log_test("Check Student Subscription", True,
                    f"Student {student_user.email} has {count} active subscription(s), ends: {sub.end_date}")
            return sub
        else:
            # إنشاء subscription جديد
            from subscriptions.models import SubscriptionPlan
            from datetime import timedelta

            plan = SubscriptionPlan.objects.first()
            if not plan:
                plan = SubscriptionPlan.objects.create(
                    name='Test Plan',
                    price=100.00,
                    duration_days=30,
                    is_active=True
                )

            subscription = Subscription.objects.create(
                user=student_user,
                plan=plan,
                status='active',
                start_date=timezone.now().date(),
                end_date=timezone.now().date() + timedelta(days=30)
            )

            log_test("Check Student Subscription", True, f"Created new subscription for {student_user.email}")
            return subscription

    except Exception as e:
        log_test("Check Student Subscription", False, str(e))
        return None

def test_database_consistency():
    """اختبار تناسق قاعدة البيانات"""
    try:
        from subscriptions.models import Subscription
        from django.utils import timezone

        # عدد المدرسين
        instructors_count = CustomUser.objects.filter(is_instructor=True, is_active=True).count()

        # عدد المحادثات
        chatrooms_count = ChatRoom.objects.count()

        # عدد الرسائل
        messages_count = Message.objects.count()

        # فحص subscriptions
        active_subs = Subscription.objects.filter(
            status='active',
            is_active=True,
            end_date__gte=timezone.now().date()
        ).count()

        log_test("Database Consistency", True,
                f"Instructors: {instructors_count}, ChatRooms: {chatrooms_count}, Messages: {messages_count}, Active Subs: {active_subs}")

        return {
            'instructors': instructors_count,
            'chatrooms': chatrooms_count,
            'messages': messages_count,
            'active_subscriptions': active_subs
        }
    except Exception as e:
        log_test("Database Consistency", False, str(e))
        return None

def print_summary():
    """طباعة ملخص النتائج"""
    print("\n" + "="*60)
    print("📊 CHAT INTEGRATION TEST SUMMARY")
    print("="*60)
    
    total_tests = len(TEST_RESULTS)
    passed_tests = sum(1 for result in TEST_RESULTS if result['success'])
    failed_tests = total_tests - passed_tests
    
    print(f"📈 Total Tests: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if failed_tests > 0:
        print("\n❌ FAILED TESTS:")
        for result in TEST_RESULTS:
            if not result['success']:
                print(f"  - {result['test']}: {result['details']}")
    
    print("\n" + "="*60)

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 Starting Chat Integration Tests...")
    print("="*60)
    
    # 1. اختبار قاعدة البيانات
    db_stats = test_database_consistency()
    
    # 2. اختبار الحصول على tokens
    admin_result = get_admin_token()
    student_result = get_student_token()
    
    if not admin_result or not student_result:
        print("❌ Cannot proceed without admin and student tokens")
        print_summary()
        return
    
    admin_token, admin_user = admin_result
    student_token, student_user = student_result

    # 2.5. فحص subscription الطالب
    subscription = check_student_subscription(student_user)

    # 3. اختبار API المدرسين
    instructors = test_instructors_api()
    
    if not instructors:
        print("❌ Cannot proceed without instructors")
        print_summary()
        return
    
    # 4. اختبار APIs مع student token
    chatrooms = test_chatrooms_api(student_token)
    
    # 5. اختبار إنشاء محادثة جديدة
    first_instructor = instructors[0]
    new_chatroom = test_create_chatroom(student_token, first_instructor['id'])
    
    if new_chatroom:
        room_id = new_chatroom['id']
        
        # 6. اختبار إرسال رسالة
        new_message = test_send_message(student_token, room_id, student_user)
        
        # 7. اختبار جلب الرسائل
        messages = test_get_messages(student_token, room_id)
    
    # 8. اختبار قاعدة البيانات مرة أخرى
    final_db_stats = test_database_consistency()
    
    # طباعة الملخص
    print_summary()
    
    # نصائح للفرونت إند
    print("\n🎯 FRONTEND TESTING RECOMMENDATIONS:")
    print("1. Open browser to http://localhost:3000")
    print("2. Login as student")
    print("3. Go to 'الرسائل' tab")
    print("4. Click 'محادثة جديدة'")
    print("5. Select an instructor and send a message")
    print("6. Check if WebSocket real-time works")

if __name__ == "__main__":
    main()
