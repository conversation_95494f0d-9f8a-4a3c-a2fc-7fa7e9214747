<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Chat Test - PreQanoony</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .login-section { background: #f8f9fa; }
        .chat-section { background: #fff; }
        #messages { border: 1px solid #ccc; height: 300px; overflow-y: scroll; padding: 10px; margin: 10px 0; }
        .message { margin: 5px 0; padding: 5px; background: #f0f0f0; border-radius: 5px; }
        .my-message { background: #007bff; color: white; text-align: right; }
        input[type="text"], input[type="email"], input[type="password"] { width: 70%; padding: 10px; margin: 5px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; margin: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .error { background: #fff3cd; color: #856404; }
        .success { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار WebSocket - نظام المحادثات</h1>
        
        <!-- Login Section -->
        <div class="section login-section">
            <h3>تسجيل الدخول</h3>
            <div>
                <input type="email" id="email" placeholder="البريد الإلكتروني" value="<EMAIL>">
                <input type="password" id="password" placeholder="كلمة المرور" value="test123">
                <button onclick="login()">تسجيل الدخول</button>
            </div>
            <div id="loginStatus" class="status disconnected">غير مسجل</div>
        </div>

        <!-- Chat Section -->
        <div class="section chat-section">
            <h3>المحادثة</h3>
            <div>
                <label>رقم المحادثة: </label>
                <input type="number" id="roomId" value="3" placeholder="رقم المحادثة">
                <button onclick="connect()">اتصال</button>
                <button onclick="disconnect()">قطع الاتصال</button>
            </div>
            
            <div id="chatStatus" class="status disconnected">غير متصل</div>
            
            <div id="messages"></div>
            
            <div>
                <input type="text" id="messageInput" placeholder="اكتب رسالتك..." onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()">إرسال</button>
            </div>
        </div>
    </div>

    <script>
        let chatSocket = null;
        let roomId = null;
        let authToken = null;

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                updateLoginStatus('يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
                return;
            }

            try {
                const response = await fetch('/api/auth/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.access;
                    updateLoginStatus(`مرحباً ${data.user.first_name} ${data.user.last_name}`, 'success');
                } else {
                    updateLoginStatus(`خطأ: ${data.error || data.detail}`, 'error');
                }
            } catch (error) {
                updateLoginStatus(`خطأ في الاتصال: ${error.message}`, 'error');
            }
        }

        function connect() {
            if (!authToken) {
                updateChatStatus('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            roomId = document.getElementById('roomId').value;
            if (!roomId) {
                updateChatStatus('يرجى إدخال رقم المحادثة', 'error');
                return;
            }

            // Close existing connection if any
            if (chatSocket) {
                chatSocket.close();
            }

            // Create WebSocket connection with auth token
            const wsScheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
            const wsPath = `${wsScheme}://${window.location.host}/ws/chat/${roomId}/?token=${authToken}`;
            
            updateChatStatus('جاري الاتصال...', 'error');
            
            chatSocket = new WebSocket(wsPath);

            chatSocket.onopen = function(e) {
                console.log('WebSocket connected');
                updateChatStatus('متصل بالمحادثة ' + roomId, 'connected');
            };

            chatSocket.onmessage = function(e) {
                const data = JSON.parse(e.data);
                console.log('Message received:', data);
                addMessage(data.message, data.sender_name || 'مجهول', false);
            };

            chatSocket.onclose = function(e) {
                console.log('WebSocket closed');
                updateChatStatus('انقطع الاتصال', 'disconnected');
                chatSocket = null;
            };

            chatSocket.onerror = function(e) {
                console.error('WebSocket error:', e);
                updateChatStatus('خطأ في الاتصال', 'error');
            };
        }

        function disconnect() {
            if (chatSocket) {
                chatSocket.close();
            }
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) {
                alert('يرجى إدخال رسالة');
                return;
            }

            if (!chatSocket || chatSocket.readyState !== WebSocket.OPEN) {
                alert('غير متصل بالمحادثة');
                return;
            }

            chatSocket.send(JSON.stringify({
                'message': message
            }));

            // Add message locally for sender to see their own message
            addMessage(message, 'أنت', true);
            messageInput.value = '';
        }

        function addMessage(message, sender, isMyMessage) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message' + (isMyMessage ? ' my-message' : '');
            
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            messageDiv.innerHTML = `<strong>${sender}:</strong> ${message} <small>(${timestamp})</small>`;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateLoginStatus(message, className) {
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + className;
        }

        function updateChatStatus(message, className) {
            const statusDiv = document.getElementById('chatStatus');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + className;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
    </script>
</body>
</html>
