// Debug script to check user authentication status
// Run this in browser console

console.log('🔍 Debugging User Authentication...\n');

// Check localStorage
console.log('📦 LocalStorage Data:');
console.log('- access token:', localStorage.getItem('access'));
console.log('- refresh token:', localStorage.getItem('refresh'));
console.log('- user data:', localStorage.getItem('user'));

// Parse user data
const userData = localStorage.getItem('user');
if (userData) {
  try {
    const user = JSON.parse(userData);
    console.log('\n👤 Parsed User Data:');
    console.log('- ID:', user.id);
    console.log('- Email:', user.email);
    console.log('- First Name:', user.first_name);
    console.log('- Last Name:', user.last_name);
    console.log('- Is Admin:', user.is_admin);
    console.log('- Is Student:', user.is_student);
    console.log('- Is Instructor:', user.is_instructor);
    console.log('- Is Active:', user.is_active);
    console.log('- Full User Object:', user);
  } catch (e) {
    console.log('❌ Error parsing user data:', e);
  }
} else {
  console.log('❌ No user data found in localStorage');
}

// Check if tokens are valid
const token = localStorage.getItem('access');
if (token) {
  try {
    // Decode JWT token (basic check)
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    const decoded = JSON.parse(jsonPayload);
    console.log('\n🔑 Token Info:');
    console.log('- User ID:', decoded.user_id);
    console.log('- Expires:', new Date(decoded.exp * 1000));
    console.log('- Is Expired:', Date.now() >= decoded.exp * 1000);
    console.log('- Full Token Payload:', decoded);
  } catch (e) {
    console.log('❌ Error decoding token:', e);
  }
}

// Test API call
console.log('\n🧪 Testing API Call...');
fetch('http://localhost:8000/api/auth/instructors/profile/', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('access')}`,
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('API Response Status:', response.status);
  return response.json();
})
.then(data => {
  console.log('API Response Data:', data);
})
.catch(error => {
  console.log('API Error:', error);
});

console.log('\n📋 Next Steps:');
console.log('1. If no user data: Login as instructor');
console.log('2. If is_instructor is false: Login with instructor account');
console.log('3. If token expired: Refresh or login again');
console.log('4. If API fails: Check backend server');
