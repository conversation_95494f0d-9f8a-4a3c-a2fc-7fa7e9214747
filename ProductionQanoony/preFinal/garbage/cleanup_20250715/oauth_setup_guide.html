<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل إعداد OAuth2 - Google & Facebook</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #ea4335 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .provider {
            margin-bottom: 50px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .provider-header {
            padding: 20px;
            font-size: 1.5em;
            font-weight: bold;
            color: white;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .google-header {
            background: linear-gradient(135deg, #4285f4, #34a853);
        }
        
        .facebook-header {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
        }
        
        .github-header {
            background: linear-gradient(135deg, #333, #666);
        }
        
        .provider-content {
            padding: 30px;
        }
        
        .step {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        
        .step h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .step ol {
            margin-right: 20px;
        }
        
        .step li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
        
        .url-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            direction: ltr;
            text-align: left;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .icon {
            width: 30px;
            height: 30px;
        }
        
        .timeline {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
        }
        
        .timeline h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }
        
        .timeline-number {
            background: #4285f4;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: #4285f4;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #3367d6;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 دليل إعداد OAuth2</h1>
            <p>خطوات تفصيلية لإعداد تسجيل الدخول بـ Google و Facebook و GitHub</p>
        </div>
        
        <div class="content">
            <!-- Google OAuth -->
            <div class="provider">
                <div class="provider-header google-header">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Google OAuth2
                </div>
                <div class="provider-content">
                    <div class="step">
                        <h3>الخطوة 1: إنشاء مشروع في Google Cloud Console</h3>
                        <ol>
                            <li>اذهب إلى: <a href="https://console.cloud.google.com/" target="_blank">https://console.cloud.google.com/</a></li>
                            <li>اضغط على "Select a project" ثم "New Project"</li>
                            <li>اسم المشروع: <strong>PreQanoony OAuth</strong></li>
                            <li>اضغط "Create"</li>
                        </ol>
                    </div>
                    
                    <div class="step">
                        <h3>الخطوة 2: تفعيل Google APIs</h3>
                        <ol>
                            <li>في القائمة الجانبية: <strong>APIs & Services → Library</strong></li>
                            <li>ابحث عن: <strong>"Google+ API"</strong> أو <strong>"Google Identity"</strong></li>
                            <li>اضغط <strong>Enable</strong></li>
                        </ol>
                    </div>
                    
                    <div class="step">
                        <h3>الخطوة 3: إنشاء OAuth Credentials</h3>
                        <ol>
                            <li>اذهب إلى: <strong>APIs & Services → Credentials</strong></li>
                            <li>اضغط: <strong>"+ CREATE CREDENTIALS" → OAuth client ID</strong></li>
                            <li>اختر: <strong>Web application</strong></li>
                            <li>الاسم: <strong>PreQanoony Web Client</strong></li>
                            <li>أضف <strong>Authorized JavaScript origins</strong>:</li>
                        </ol>
                        <div class="url-list">
http://localhost:3000<br>
http://localhost:8000<br>
https://yourdomain.com (للإنتاج)
                        </div>
                        <ol start="6">
                            <li>أضف <strong>Authorized redirect URIs</strong>:</li>
                        </ol>
                        <div class="url-list">
http://localhost:3000/auth/google/callback<br>
http://localhost:8000/accounts/google/login/callback/<br>
https://yourdomain.com/auth/google/callback (للإنتاج)
                        </div>
                    </div>
                    
                    <div class="step">
                        <h3>الخطوة 4: OAuth Consent Screen</h3>
                        <ol>
                            <li>اذهب إلى: <strong>APIs & Services → OAuth consent screen</strong></li>
                            <li>اختر: <strong>External</strong></li>
                            <li>املأ البيانات المطلوبة:
                                <ul>
                                    <li>App name: <strong>PreQanoony</strong></li>
                                    <li>User support email: <strong><EMAIL></strong></li>
                                    <li>Developer contact: <strong><EMAIL></strong></li>
                                </ul>
                            </li>
                            <li>اضغط <strong>Save and Continue</strong></li>
                        </ol>
                    </div>
                    
                    <div class="success">
                        <strong>✅ انسخ المفاتيح:</strong><br>
                        <div class="code-block">
Client ID: 123456789-abcdefg.apps.googleusercontent.com
Client Secret: GOCSPX-abcdefghijklmnop
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Facebook OAuth -->
            <div class="provider">
                <div class="provider-header facebook-header">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    Facebook OAuth2
                </div>
                <div class="provider-content">
                    <div class="step">
                        <h3>الخطوة 1: إنشاء Facebook App</h3>
                        <ol>
                            <li>اذهب إلى: <a href="https://developers.facebook.com/" target="_blank">https://developers.facebook.com/</a></li>
                            <li>اضغط: <strong>"My Apps" → "Create App"</strong></li>
                            <li>اختر: <strong>"Consumer"</strong> أو <strong>"Business"</strong></li>
                            <li>اسم التطبيق: <strong>PreQanoony</strong></li>
                            <li>البريد الإلكتروني: <strong><EMAIL></strong></li>
                            <li>اضغط <strong>Create App</strong></li>
                        </ol>
                    </div>
                    
                    <div class="step">
                        <h3>الخطوة 2: إضافة Facebook Login</h3>
                        <ol>
                            <li>في Dashboard: اضغط <strong>"+ Add Product"</strong></li>
                            <li>ابحث عن: <strong>Facebook Login</strong></li>
                            <li>اضغط <strong>Set Up</strong></li>
                        </ol>
                    </div>
                    
                    <div class="step">
                        <h3>الخطوة 3: تكوين Facebook Login</h3>
                        <ol>
                            <li>اذهب إلى: <strong>Facebook Login → Settings</strong></li>
                            <li>أضف <strong>Valid OAuth Redirect URIs</strong>:</li>
                        </ol>
                        <div class="url-list">
http://localhost:3000/auth/facebook/callback<br>
http://localhost:8000/accounts/facebook/login/callback/<br>
https://yourdomain.com/auth/facebook/callback (للإنتاج)
                        </div>
                        <ol start="3">
                            <li>اضغط <strong>Save Changes</strong></li>
                        </ol>
                    </div>
                    
                    <div class="step">
                        <h3>الخطوة 4: Basic Settings</h3>
                        <ol>
                            <li>اذهب إلى: <strong>Settings → Basic</strong></li>
                            <li>App Domains: <strong>localhost</strong></li>
                            <li>Privacy Policy URL: <strong>http://localhost:3000/privacy</strong></li>
                            <li>Terms of Service URL: <strong>http://localhost:3000/terms</strong></li>
                        </ol>
                    </div>
                    
                    <div class="success">
                        <strong>✅ انسخ المفاتيح:</strong><br>
                        <div class="code-block">
App ID: 1234567890123456
App Secret: abcdefghijklmnopqrstuvwxyz123456
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- GitHub OAuth (البديل الأسهل) -->
            <div class="provider">
                <div class="provider-header github-header">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                    GitHub OAuth2 (البديل الأسهل)
                </div>
                <div class="provider-content">
                    <div class="warning">
                        <strong>💡 لماذا GitHub أسهل؟</strong><br>
                        • لا يحتاج موافقة مسبقة<br>
                        • إعداد أبسط<br>
                        • لا يحتاج privacy policy<br>
                        • يعمل فوراً
                    </div>
                    
                    <div class="step">
                        <h3>خطوات GitHub OAuth:</h3>
                        <ol>
                            <li>اذهب إلى: <a href="https://github.com/settings/developers" target="_blank">https://github.com/settings/developers</a></li>
                            <li>اضغط: <strong>"New OAuth App"</strong></li>
                            <li>املأ البيانات:
                                <ul>
                                    <li>Application name: <strong>PreQanoony</strong></li>
                                    <li>Homepage URL: <strong>http://localhost:3000</strong></li>
                                    <li>Authorization callback URL: <strong>http://localhost:3000/auth/github/callback</strong></li>
                                </ul>
                            </li>
                            <li>اضغط <strong>Register application</strong></li>
                            <li>انسخ: <strong>Client ID & Client Secret</strong></li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <!-- Timeline -->
            <div class="timeline">
                <h3>🎯 ترتيب التنفيذ المقترح:</h3>
                <div class="timeline-item">
                    <div class="timeline-number">1</div>
                    <div>ابدأ بـ <strong>GitHub OAuth</strong> (الأسهل والأسرع)</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-number">2</div>
                    <div>أضف <strong>Google OAuth</strong> (الأكثر استخداماً)</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-number">3</div>
                    <div>أضف <strong>Facebook OAuth</strong> (للاكتمال)</div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-number">4</div>
                    <div>اختبر جميع الطرق وتأكد من عملها</div>
                </div>
            </div>
            
            <!-- Environment Variables -->
            <div class="step">
                <h3>🔧 Environment Variables المطلوبة:</h3>
                <p><strong>Backend (.env):</strong></p>
                <div class="code-block">
GOOGLE_OAUTH2_CLIENT_ID=your_google_client_id
GOOGLE_OAUTH2_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
                </div>
                
                <p><strong>Frontend (.env):</strong></p>
                <div class="code-block">
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id
REACT_APP_FACEBOOK_APP_ID=your_facebook_app_id
REACT_APP_GITHUB_CLIENT_ID=your_github_client_id
                </div>
            </div>
        </div>
        
        <div class="footer">
            <h3>🚀 جاهز للبدء؟</h3>
            <p>بعد إكمال الإعداد، ستحصل على جميع المفاتيح المطلوبة لتشغيل OAuth2</p>
            <a href="#" class="btn">العودة للمشروع</a>
        </div>
    </div>
</body>
</html>
