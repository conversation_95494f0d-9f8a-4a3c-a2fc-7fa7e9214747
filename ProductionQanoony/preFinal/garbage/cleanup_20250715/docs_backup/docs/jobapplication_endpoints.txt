# JobApplication Endpoints

## List Job Applications
- URL: /api/careers/applications/
- Method: GET
- Description: List job applications. Students see their own, employers see applications for their jobs, admins see all.
- Permissions: Authenticated (role-based)
- Request Example:
    GET /api/careers/applications/
- Response Example:
    [
        {"id": 1, "student": {"id": 3, "user": {"email": "<EMAIL>"}}, "job": {"id": 1, "title_ar": "مبرمج"}, "cover_letter": "...", "resume": "https://res.cloudinary.com/.../resume.pdf", "applied_at": "2024-06-01T12:00:00Z", "status": "applied"}
    ]

## Retrieve Job Application
- URL: /api/careers/applications/{id}/
- Method: GET
- Description: Retrieve a job application by ID.
- Permissions: Student (own), employer (for their jobs), admin (any)
- Request Example:
    GET /api/careers/applications/1/
- Response Example:
    {"id": 1, "student": {"id": 3, "user": {"email": "<EMAIL>"}}, "job": {"id": 1, "title_ar": "مبرمج"}, "cover_letter": "...", "resume": "https://res.cloudinary.com/.../resume.pdf", "applied_at": "2024-06-01T12:00:00Z", "status": "applied"}

## Create Job Application
- URL: /api/careers/applications/
- Method: POST
- Description: Apply for a job (student only, upload resume and cover letter).
- Permissions: Authenticated student
- Request Example:
    {"job_id": 1, "cover_letter": "أرغب في التقديم...", "resume": "<file>"}
- Response Example:
    {"id": 2, "student": {"id": 3, "user": {"email": "<EMAIL>"}}, "job": {"id": 1, "title_ar": "مبرمج"}, "cover_letter": "أرغب في التقديم...", "resume": "https://res.cloudinary.com/.../resume.pdf", "applied_at": "2024-06-01T12:00:00Z", "status": "applied"}

## Update Job Application
- URL: /api/careers/applications/{id}/
- Method: PUT/PATCH
- Description: Update a job application (admin only, e.g., status).
- Permissions: Admin only
- Request Example:
    {"status": "accepted"}
- Response Example:
    {"id": 1, "status": "accepted", ...}

## Delete Job Application
- URL: /api/careers/applications/{id}/
- Method: DELETE
- Description: Delete a job application (admin only).
- Permissions: Admin only
- Request Example:
    DELETE /api/careers/applications/2/
- Response Example:
    204 No Content 