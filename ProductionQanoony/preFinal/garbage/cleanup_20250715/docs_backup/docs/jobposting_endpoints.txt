# JobPosting Endpoints

## List Job Postings
- URL: /api/careers/jobs/
- Method: GET
- Description: List all active job postings. Supports search and ordering.
- Permissions: Public
- Request Example:
    GET /api/careers/jobs/?search=مبرمج
- Response Example:
    [
        {"id": 1, "title_ar": "مبرمج", "company_name": "TechCorp", "category": {"id": 1, "name_en": "IT", "name_ar": "تقنية", "description_ar": "desc", "is_active": true}, "job_type": "full_time", "location": "Cairo", "description_ar": "برمجة", "requirements_ar": "Python", "salary_range": "10k-15k", "application_deadline": "2030-12-31", "contact_email": "<EMAIL>", "contact_phone": "0100000000", "is_active": true, "posted_by": {"id": 2, "email": "<EMAIL>"}, "created_at": "2024-06-01T12:00:00Z"}
    ]

## Retrieve Job Posting
- URL: /api/careers/jobs/{id}/
- Method: GET
- Description: Retrieve a job posting by ID.
- Permissions: Public
- Request Example:
    GET /api/careers/jobs/1/
- Response Example:
    {"id": 1, "title_ar": "مبرمج", "company_name": "TechCorp", "category": {"id": 1, "name_en": "IT", "name_ar": "تقنية", "description_ar": "desc", "is_active": true}, "job_type": "full_time", "location": "Cairo", "description_ar": "برمجة", "requirements_ar": "Python", "salary_range": "10k-15k", "application_deadline": "2030-12-31", "contact_email": "<EMAIL>", "contact_phone": "0100000000", "is_active": true, "posted_by": {"id": 2, "email": "<EMAIL>"}, "created_at": "2024-06-01T12:00:00Z"}

## Create Job Posting
- URL: /api/careers/jobs/
- Method: POST
- Description: Create a new job posting.
- Permissions: Employer/Admin only
- Request Example:
    {"title_ar": "مدير", "company_name": "ManageIt", "category_id": 1, "job_type": "full_time", "location": "Cairo", "description_ar": "إدارة", "requirements_ar": "خبرة", "salary_range": "20k-25k", "application_deadline": "2031-01-01", "contact_email": "<EMAIL>", "contact_phone": "0133333333", "is_active": true}
- Response Example:
    {"id": 3, "title_ar": "مدير", "company_name": "ManageIt", "category": {"id": 1, "name_en": "IT", "name_ar": "تقنية", "description_ar": "desc", "is_active": true}, "job_type": "full_time", "location": "Cairo", "description_ar": "إدارة", "requirements_ar": "خبرة", "salary_range": "20k-25k", "application_deadline": "2031-01-01", "contact_email": "<EMAIL>", "contact_phone": "0133333333", "is_active": true, "posted_by": {"id": 2, "email": "<EMAIL>"}, "created_at": "2024-06-01T12:00:00Z"}

## Update Job Posting
- URL: /api/careers/jobs/{id}/
- Method: PUT/PATCH
- Description: Update a job posting.
- Permissions: Employer/Admin only (owner or admin)
- Request Example:
    {"title_ar": "مبرمج أول", "company_name": "TechCorp", "category_id": 1, "job_type": "full_time", "location": "Cairo", "description_ar": "برمجة متقدمة", "requirements_ar": "Python", "salary_range": "12k-18k", "application_deadline": "2030-12-31", "contact_email": "<EMAIL>", "contact_phone": "0100000000", "is_active": true}
- Response Example:
    {"id": 1, "title_ar": "مبرمج أول", "company_name": "TechCorp", "category": {"id": 1, "name_en": "IT", "name_ar": "تقنية", "description_ar": "desc", "is_active": true}, "job_type": "full_time", "location": "Cairo", "description_ar": "برمجة متقدمة", "requirements_ar": "Python", "salary_range": "12k-18k", "application_deadline": "2030-12-31", "contact_email": "<EMAIL>", "contact_phone": "0100000000", "is_active": true, "posted_by": {"id": 2, "email": "<EMAIL>"}, "created_at": "2024-06-01T12:00:00Z"}

## Delete Job Posting
- URL: /api/careers/jobs/{id}/
- Method: DELETE
- Description: Delete a job posting.
- Permissions: Employer/Admin only (owner or admin)
- Request Example:
    DELETE /api/careers/jobs/3/
- Response Example:
    204 No Content

## My Jobs (Employer/Admin)
- URL: /api/careers/jobs/my-jobs/
- Method: GET
- Description: List jobs posted by the current employer/admin.
- Permissions: Employer/Admin only
- Request Example:
    GET /api/careers/jobs/my-jobs/
- Response Example:
    [
        {"id": 1, "title_ar": "مبرمج", ...}
    ] 