# Communication App API & WebSocket Documentation

---

## REST API Endpoints

### Chat Rooms
- **List Chat Rooms**
  - URL: `/api/communication/chatrooms/`
  - Method: GET
  - Permissions: Authenticated users
- **Create Chat Room**
  - URL: `/api/communication/chatrooms/`
  - Method: POST
  - Permissions: Authenticated users
- **Join Chat Room**
  - URL: `/api/communication/chatrooms/{id}/join/`
  - Method: POST
  - Permissions: Authenticated users

### Messages
- **List Messages**
  - URL: `/api/communication/messages/`
  - Method: GET
  - Permissions: Authenticated users
- **Send Message**
  - URL: `/api/communication/messages/`
  - Method: POST
  - Permissions: Authenticated users

### Forum
- **List Forum Categories**
  - URL: `/api/communication/forum-categories/`
  - Method: GET
  - Permissions: Authenticated users
- **List Forum Topics**
  - URL: `/api/communication/forum-topics/`
  - Method: GET
  - Permissions: Authenticated users
- **Create Forum Topic**
  - URL: `/api/communication/forum-topics/`
  - Method: POST
  - Permissions: Authenticated users
- **Pin/Lock Forum Topic**
  - URL: `/api/communication/forum-topics/{id}/pin/` or `/lock/`
  - Method: POST
  - Permissions: Moderators/Admins
- **List Forum Posts**
  - URL: `/api/communication/forum-posts/`
  - Method: GET
  - Permissions: Authenticated users
- **Create Forum Post**
  - URL: `/api/communication/forum-posts/`
  - Method: POST
  - Permissions: Authenticated users
- **Approve Forum Post**
  - URL: `/api/communication/forum-posts/{id}/approve/`
  - Method: POST
  - Permissions: Moderators/Admins

---

## WebSocket Endpoints

### Real-Time Chat
- **Connect to Chat Room**
  - URL: `ws://<host>/ws/chat/<room_id>/`
  - Auth: JWT or session (user must be authenticated)
  - On connect: User joins the chat room group.

#### Send Message (Client → Server)
```
{
  "message": "Hello!",
  "message_type": "text"  // or "file", "image"
}
```

#### Receive Message (Server → Client)
```
{
  "id": 12,
  "message": "Hello!",
  "sender_id": 2,
  "sender_email": "<EMAIL>",
  "timestamp": "2024-06-01T12:00:00Z",
  "message_type": "text",
  "notification": true
}
```
- All participants in the room receive the message and notification in real time.

---

## Permissions Summary
- **Authenticated users:** Can join chat rooms, send/receive messages, participate in forums.
- **Moderators/Admins:** Can pin/lock topics, approve posts.

---

## Notes
- File/image messages require file upload via REST API, then reference in WebSocket payload (future enhancement).
- All user-facing messages should be in Arabic.
- For more details, see the codebase or extend this file as needed. 