# Message Endpoints

## List Messages
- URL: /api/communication/messages/
- Method: GET
- Description: List all messages (optionally filter by room).
- Permissions: Authenticated users
- Request Example:
    GET /api/communication/messages/?room=1
- Response Example:
    [
        {
            "id": 1,
            "room": 1,
            "sender": 2,
            "content": "Hello",
            "message_type": "text",
            "file_attachment": null,
            "timestamp": "2024-06-01T12:00:00Z",
            "is_read": false,
            "edited_at": null
        }
    ]

## Create Message
- URL: /api/communication/messages/
- Method: POST
- Description: Send a new message (text, file, or image).
- Permissions: Authenticated users
- Request Example:
    {
        "room": 1,
        "sender": 2,
        "content": "Hello",
        "message_type": "text"
    }
- Response Example:
    {
        "id": 1,
        "room": 1,
        "sender": 2,
        "content": "Hello",
        "message_type": "text",
        "file_attachment": null,
        "timestamp": "2024-06-01T12:00:00Z",
        "is_read": false,
        "edited_at": null
    } 