.main-footer {
  background-color: #1a1a1a;
  padding: 15px 0;
  color: #fff;
  border-top: 1px solid #2d2d2d;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  direction: rtl;
}

.footer-section {
  display: flex;
  gap: 20px;
}

.footer-section.links {
  display: flex;
  gap: 20px;
}

.footer-section.links a {
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.footer-section.links a:hover {
  color: #60a5fa;
}

.footer-section.contact {
  display: flex;
  gap: 20px;
  font-size: 14px;
}

.footer-section.contact span {
  color: #888;
}

.footer-section.social .social-icons {
  display: flex;
  gap: 15px;
}

.footer-section.social a {
  color: #fff;
  font-size: 18px;
  transition: color 0.2s ease;
}

.footer-section.social a:hover {
  color: #60a5fa;
}

@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 15px;
  }

  .footer-section {
    justify-content: center;
  }

  .footer-section.links {
    flex-wrap: wrap;
    justify-content: center;
  }
} 