// Test script for Instructor Profile APIs
// Run this in browser console or as a Node.js script

const API_BASE = 'http://localhost:8000/api/auth';

// Get auth token from localStorage (for browser testing)
const getAuthHeaders = () => {
  const token = localStorage.getItem('access');
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
};

// Test functions
const testInstructorAPIs = {
  
  // Test 1: Get Instructor Profile
  async testGetProfile() {
    console.log('🧪 Testing GET /api/auth/instructors/profile/');
    try {
      const response = await fetch(`${API_BASE}/instructors/profile/`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      
      console.log('Status:', response.status);
      const data = await response.json();
      console.log('Response:', data);
      
      if (response.ok) {
        console.log('✅ GET Profile - SUCCESS');
        return data;
      } else {
        console.log('❌ GET Profile - FAILED');
        console.log('Error:', data);
      }
    } catch (error) {
      console.log('❌ GET Profile - ERROR:', error);
    }
  },

  // Test 2: Update Instructor Profile
  async testUpdateProfile() {
    console.log('🧪 Testing PATCH /api/auth/instructors/profile/');
    
    const updateData = {
      first_name: 'أحمد',
      last_name: 'محمد',
      phone_number: '+966501234567',
      specialty: 'القانون المدني والتجاري',
      bio: 'مدرس قانون متخصص في القانون المدني والتجاري مع خبرة 10 سنوات في التدريس.'
    };
    
    try {
      const response = await fetch(`${API_BASE}/instructors/profile/`, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        body: JSON.stringify(updateData)
      });
      
      console.log('Status:', response.status);
      const data = await response.json();
      console.log('Response:', data);
      
      if (response.ok) {
        console.log('✅ PATCH Profile - SUCCESS');
        return data;
      } else {
        console.log('❌ PATCH Profile - FAILED');
        console.log('Error:', data);
      }
    } catch (error) {
      console.log('❌ PATCH Profile - ERROR:', error);
    }
  },

  // Test 3: Get Instructor Stats
  async testGetStats() {
    console.log('🧪 Testing GET /api/auth/instructors/stats/');
    try {
      const response = await fetch(`${API_BASE}/instructors/stats/`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      
      console.log('Status:', response.status);
      const data = await response.json();
      console.log('Response:', data);
      
      if (response.ok) {
        console.log('✅ GET Stats - SUCCESS');
        return data;
      } else {
        console.log('❌ GET Stats - FAILED');
        console.log('Error:', data);
      }
    } catch (error) {
      console.log('❌ GET Stats - ERROR:', error);
    }
  },

  // Test 4: Get Instructor Subjects
  async testGetSubjects() {
    console.log('🧪 Testing GET /api/auth/instructors/subjects/');
    try {
      const response = await fetch(`${API_BASE}/instructors/subjects/`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      
      console.log('Status:', response.status);
      const data = await response.json();
      console.log('Response:', data);
      
      if (response.ok) {
        console.log('✅ GET Subjects - SUCCESS');
        return data;
      } else {
        console.log('❌ GET Subjects - FAILED');
        console.log('Error:', data);
      }
    } catch (error) {
      console.log('❌ GET Subjects - ERROR:', error);
    }
  },

  // Test 5: Get Instructor Students
  async testGetStudents() {
    console.log('🧪 Testing GET /api/auth/instructors/students/');
    try {
      const response = await fetch(`${API_BASE}/instructors/students/`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      
      console.log('Status:', response.status);
      const data = await response.json();
      console.log('Response:', data);
      
      if (response.ok) {
        console.log('✅ GET Students - SUCCESS');
        return data;
      } else {
        console.log('❌ GET Students - FAILED');
        console.log('Error:', data);
      }
    } catch (error) {
      console.log('❌ GET Students - ERROR:', error);
    }
  },

  // Test 6: Upload Profile Image (requires file input)
  async testUploadImage(file) {
    console.log('🧪 Testing POST /api/auth/instructors/profile/image/');
    
    if (!file) {
      console.log('❌ No file provided for image upload test');
      return;
    }
    
    try {
      const formData = new FormData();
      formData.append('profile_image', file);
      
      const response = await fetch(`${API_BASE}/instructors/profile/image/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access')}`
        },
        body: formData
      });
      
      console.log('Status:', response.status);
      const data = await response.json();
      console.log('Response:', data);
      
      if (response.ok) {
        console.log('✅ POST Image - SUCCESS');
        return data;
      } else {
        console.log('❌ POST Image - FAILED');
        console.log('Error:', data);
      }
    } catch (error) {
      console.log('❌ POST Image - ERROR:', error);
    }
  },

  // Test 7: Get Instructor Sessions
  async testGetSessions() {
    console.log('🧪 Testing GET /api/auth/instructors/sessions/');
    try {
      const response = await fetch(`${API_BASE}/instructors/sessions/`, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      
      console.log('Status:', response.status);
      const data = await response.json();
      console.log('Response:', data);
      
      if (response.ok) {
        console.log('✅ GET Sessions - SUCCESS');
        return data;
      } else {
        console.log('❌ GET Sessions - FAILED');
        console.log('Error:', data);
      }
    } catch (error) {
      console.log('❌ GET Sessions - ERROR:', error);
    }
  },

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting Instructor APIs Test Suite...\n');
    
    const results = {};
    
    results.profile = await this.testGetProfile();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.stats = await this.testGetStats();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.subjects = await this.testGetSubjects();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.students = await this.testGetStudents();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.sessions = await this.testGetSessions();
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Test update profile (optional)
    // results.updateProfile = await this.testUpdateProfile();
    // console.log('\n' + '='.repeat(50) + '\n');
    
    console.log('🏁 Test Suite Completed!');
    console.log('Results Summary:', results);
    
    return results;
  }
};

// Usage instructions
console.log(`
📋 Instructor APIs Test Suite
=============================

To run tests:
1. Make sure you're logged in as an instructor
2. Open browser console
3. Run: testInstructorAPIs.runAllTests()

Individual tests:
- testInstructorAPIs.testGetProfile()
- testInstructorAPIs.testGetStats()
- testInstructorAPIs.testGetSubjects()
- testInstructorAPIs.testGetStudents()
- testInstructorAPIs.testGetSessions()
- testInstructorAPIs.testUpdateProfile()

For image upload test:
- Create file input: const input = document.createElement('input'); input.type = 'file'; input.accept = 'image/*'; input.click();
- Then: testInstructorAPIs.testUploadImage(selectedFile)
`);

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testInstructorAPIs;
}

// Make available globally in browser
if (typeof window !== 'undefined') {
  window.testInstructorAPIs = testInstructorAPIs;
}
