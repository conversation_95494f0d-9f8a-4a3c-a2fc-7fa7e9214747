#!/usr/bin/env python3
"""
Test script for image upload functionality
"""

import os
import sys
import django
import requests
import json
from io import BytesIO
from PIL import Image
import tempfile

# Add the project directory to Python path
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from subscriptions.models import Subscription, SubscriptionPlan
from authentication.models import CustomUser
from datetime import date, timedelta

User = get_user_model()

def create_test_image():
    """Create a test image for upload"""
    # Create a simple test image with some content
    img = Image.new('RGB', (800, 600), color='red')

    # Add some text to make it more realistic
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    draw.text((50, 50), "Test Payment Screenshot", fill='white')
    draw.rectangle([100, 100, 700, 500], outline='white', width=3)

    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
    img.save(temp_file.name, 'JPEG', quality=95)
    temp_file.close()

    return temp_file.name

def setup_test_data():
    """Setup test user and subscription"""
    print("🔧 Setting up test data...")
    
    # Get or create test user
    user, created = CustomUser.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Test',
            'last_name': 'User',
            'is_active': True,
            'is_student': True
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.email_verified = True  # Skip email verification for test
        user.save()
        print(f"✅ Created test user: {user.email}")
    else:
        # Make sure email is verified
        user.email_verified = True
        user.save()
        print(f"✅ Using existing user: {user.email}")
    
    # Get or create subscription plan
    plan = SubscriptionPlan.objects.filter(is_active=True).first()
    if not plan:
        plan = SubscriptionPlan.objects.create(
            name='Test Plan',
            duration_days=30,
            price=100.00,
            is_active=True
        )
        print(f"✅ Created test plan: {plan.name}")
    else:
        print(f"✅ Using existing plan: {plan.name}")
    
    # Create or get subscription
    subscription, created = Subscription.objects.get_or_create(
        user=user,
        defaults={
            'plan': plan,
            'start_date': date.today(),
            'end_date': date.today() + timedelta(days=plan.duration_days),
            'status': 'active'
        }
    )
    
    if created:
        print(f"✅ Created subscription for user: {user.email}")
    else:
        print(f"✅ Using existing subscription for user: {user.email}")
    
    return user, plan

def get_auth_token(user):
    """Get authentication token for user"""
    print("🔑 Getting authentication token...")
    
    login_url = 'http://127.0.0.1:8000/api/auth/login/'
    login_data = {
        'email': user.email,
        'password': 'testpass123'
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            print("✅ Authentication successful!")
            return token_data['access']
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_image_upload(token, plan_id):
    """Test image upload functionality"""
    print("📤 Testing image upload...")
    
    # Create test image
    image_path = create_test_image()
    
    try:
        upload_url = 'http://127.0.0.1:8000/api/subscriptions/renewal-requests/'
        headers = {'Authorization': f'Bearer {token}'}
        
        # Verify file exists and get info
        if not os.path.exists(image_path):
            print(f"❌ Image file not found: {image_path}")
            return False

        file_size = os.path.getsize(image_path)
        print(f"📤 Uploading image: {file_size} bytes")

        with open(image_path, 'rb') as img_file:
            files = {'renewal_screenshot': ('test_payment.jpg', img_file, 'image/jpeg')}
            data = {'plan_id': plan_id}

            response = requests.post(upload_url, headers=headers, files=files, data=data)

            if response.status_code == 200:
                print("✅ Image uploaded successfully!")
                print(f"📋 Response: {response.json()}")
                return True
            else:
                print(f"❌ Upload failed: {response.status_code}")
                print(f"📋 Response: {response.text}")
                return False

    except Exception as e:
        print(f"❌ Upload error: {e}")
        return False
    finally:
        # Clean up test image
        if os.path.exists(image_path):
            os.unlink(image_path)

def main():
    """Main test function"""
    print("🚀 Starting image upload test...")
    
    # Setup test data
    user, plan = setup_test_data()
    
    # Get auth token
    token = get_auth_token(user)
    if not token:
        print("❌ Failed to get authentication token")
        return
    
    # Test image upload
    success = test_image_upload(token, plan.id)
    
    if success:
        print("🎉 Image upload test completed successfully!")
    else:
        print("💥 Image upload test failed!")

if __name__ == '__main__':
    main()
