#!/usr/bin/env python3
"""
🧪 اختبار عرض وجلب الرسائل
"""

import os
import sys
import django
import requests
import json

# إعداد Django
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

try:
    django.setup()
    from authentication.models import CustomUser
    from communication.models import ChatRoom, Message
    from rest_framework_simplejwt.tokens import RefreshToken
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    sys.exit(1)

API_BASE_URL = "http://localhost:8000"

def get_student_token():
    """الحصول على token لطالب"""
    try:
        student = CustomUser.objects.filter(is_student=True).first()
        if not student:
            print("❌ No student found")
            return None
        
        token = RefreshToken.for_user(student)
        access_token = str(token.access_token)
        print(f"✅ Got token for student: {student.email}")
        return access_token, student
    except Exception as e:
        print(f"❌ Error getting student token: {e}")
        return None

def test_get_chatrooms(token):
    """اختبار جلب المحادثات"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f"{API_BASE_URL}/api/communication/chatrooms/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            chatrooms = data if isinstance(data, list) else data.get('results', [])
            print(f"✅ ChatRooms API: Found {len(chatrooms)} chatrooms")
            
            for room in chatrooms:
                print(f"  - Room {room['id']}: {room['name']}")
            
            return chatrooms
        else:
            print(f"❌ ChatRooms API failed: {response.status_code}")
            print(f"Response: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Error testing chatrooms: {e}")
        return []

def test_get_messages(token, room_id):
    """اختبار جلب رسائل محادثة معينة"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f"{API_BASE_URL}/api/communication/messages/?room={room_id}", headers=headers)
        
        if response.status_code == 200:
            messages = response.json()
            messages_list = messages if isinstance(messages, list) else messages.get('results', [])
            print(f"✅ Messages API for room {room_id}: Found {len(messages_list)} messages")
            
            # عرض آخر 5 رسائل
            for msg in messages_list[-5:]:
                timestamp = msg.get('timestamp', 'No timestamp')
                sender_id = msg.get('sender', 'Unknown')
                content = msg.get('content', 'No content')[:50]
                print(f"  - [{timestamp}] Sender {sender_id}: {content}...")
            
            return messages_list
        else:
            print(f"❌ Messages API failed for room {room_id}: {response.status_code}")
            print(f"Response: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Error testing messages for room {room_id}: {e}")
        return []

def test_messages_with_pagination(token, room_id):
    """اختبار جلب الرسائل مع pagination"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        
        # اختبار مع limit
        response = requests.get(f"{API_BASE_URL}/api/communication/messages/?room={room_id}&limit=5", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Messages with pagination: {response.status_code}")
            print(f"Response structure: {list(data.keys()) if isinstance(data, dict) else 'List'}")
            
            if isinstance(data, dict):
                print(f"  - Count: {data.get('count', 'N/A')}")
                print(f"  - Next: {data.get('next', 'N/A')}")
                print(f"  - Previous: {data.get('previous', 'N/A')}")
                print(f"  - Results: {len(data.get('results', []))}")
            
            return data
        else:
            print(f"❌ Pagination test failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error testing pagination: {e}")
        return None

def test_message_ordering(token, room_id):
    """اختبار ترتيب الرسائل"""
    try:
        headers = {'Authorization': f'Bearer {token}'}
        
        # اختبار ترتيب تصاعدي
        response_asc = requests.get(f"{API_BASE_URL}/api/communication/messages/?room={room_id}&ordering=timestamp", headers=headers)
        
        # اختبار ترتيب تنازلي  
        response_desc = requests.get(f"{API_BASE_URL}/api/communication/messages/?room={room_id}&ordering=-timestamp", headers=headers)
        
        if response_asc.status_code == 200 and response_desc.status_code == 200:
            asc_data = response_asc.json()
            desc_data = response_desc.json()
            
            asc_messages = asc_data if isinstance(asc_data, list) else asc_data.get('results', [])
            desc_messages = desc_data if isinstance(desc_data, list) else desc_data.get('results', [])
            
            print(f"✅ Message ordering test:")
            print(f"  - Ascending: {len(asc_messages)} messages")
            print(f"  - Descending: {len(desc_messages)} messages")
            
            if asc_messages and desc_messages:
                print(f"  - First message (asc): {asc_messages[0].get('timestamp', 'No timestamp')}")
                print(f"  - First message (desc): {desc_messages[0].get('timestamp', 'No timestamp')}")
            
            return True
        else:
            print(f"❌ Ordering test failed: asc={response_asc.status_code}, desc={response_desc.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing ordering: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 Testing Messages Display & Fetching...")
    print("="*60)
    
    # 1. الحصول على token
    result = get_student_token()
    if not result:
        print("❌ Cannot proceed without student token")
        return
    
    token, student = result
    
    # 2. جلب المحادثات
    chatrooms = test_get_chatrooms(token)
    
    if not chatrooms:
        print("❌ No chatrooms found")
        return
    
    # 3. اختبار جلب الرسائل لكل محادثة
    for room in chatrooms:
        room_id = room['id']
        print(f"\n🔍 Testing room {room_id}: {room['name']}")
        print("-" * 40)
        
        # جلب الرسائل العادي
        messages = test_get_messages(token, room_id)
        
        if messages:
            # اختبار pagination
            test_messages_with_pagination(token, room_id)
            
            # اختبار ترتيب الرسائل
            test_message_ordering(token, room_id)
    
    print("\n" + "="*60)
    print("🎯 Messages Display Test Complete!")

if __name__ == "__main__":
    main()
