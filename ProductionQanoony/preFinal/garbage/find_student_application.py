#!/usr/bin/env python3
"""
Find specific student application
"""
import os
import sys

# Add project to path
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')

# Set minimal environment variables
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
os.environ['SECRET_KEY'] = 'temp-key'
os.environ['DEBUG'] = 'True'

# Import Django and setup
import django
from django.conf import settings

# Configure Django settings manually to avoid dotenv
if not settings.configured:
    settings.configure(
        DEBUG=True,
        SECRET_KEY='temp-key-for-search',
        DATABASES={
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': '/home/<USER>/projects/PreQanoony/preFinal/db.sqlite3',
            }
        },
        INSTALLED_APPS=[
            'django.contrib.auth',
            'django.contrib.contenttypes',
            'authentication',
            'students',
            'subscriptions',
            'universities',
        ],
        USE_TZ=True,
    )

django.setup()

from authentication.models import CustomUser, StudentApplication, InstructorApplication
from students.models import StudentProfile

def search_applications():
    """Search for student applications"""
    print("🔍 Searching for student applications...")
    print("=" * 50)
    
    # Search for specific email
    target_email = "<EMAIL>"
    print(f"🎯 Looking for: {target_email}")
    
    # Check if user exists
    try:
        user = CustomUser.objects.get(email=target_email)
        print(f"✅ User found: {user.email}")
        print(f"   - ID: {user.id}")
        print(f"   - Name: {user.first_name} {user.last_name}")
        print(f"   - Active: {user.is_active}")
        print(f"   - Student: {user.is_student}")
        print(f"   - Created: {user.date_joined}")
        
        # Check for student application
        try:
            app = StudentApplication.objects.get(user=user)
            print(f"✅ Student Application found:")
            print(f"   - ID: {app.id}")
            print(f"   - Plan: {app.selected_plan}")
            print(f"   - Approved: {app.is_approved}")
            print(f"   - Rejection: {app.rejection_reason}")
            print(f"   - Created: {app.created_at}")
            print(f"   - Screenshot: {app.payment_screenshot}")
        except StudentApplication.DoesNotExist:
            print("❌ No Student Application found for this user")
            
        # Check for student profile
        try:
            profile = StudentProfile.objects.get(user=user)
            print(f"✅ Student Profile found:")
            print(f"   - ID: {profile.id}")
            print(f"   - Student ID: {profile.student_id}")
            print(f"   - University: {profile.university}")
            print(f"   - Academic Year: {profile.academic_year}")
        except StudentProfile.DoesNotExist:
            print("❌ No Student Profile found for this user")
            
    except CustomUser.DoesNotExist:
        print(f"❌ User {target_email} not found")
    
    print("\n" + "=" * 50)
    print("📊 Overall Statistics:")
    
    # Count all applications
    total_users = CustomUser.objects.count()
    total_student_apps = StudentApplication.objects.count()
    total_instructor_apps = InstructorApplication.objects.count()
    total_profiles = StudentProfile.objects.count()
    
    print(f"   Total Users: {total_users}")
    print(f"   Student Applications: {total_student_apps}")
    print(f"   Instructor Applications: {total_instructor_apps}")
    print(f"   Student Profiles: {total_profiles}")
    
    # Show all student applications
    if total_student_apps > 0:
        print(f"\n📋 All Student Applications:")
        for app in StudentApplication.objects.all():
            status = "✅ Approved" if app.is_approved else ("❌ Rejected" if app.rejection_reason else "⏳ Pending")
            print(f"   - {app.user.email} | {status} | Created: {app.created_at.strftime('%Y-%m-%d %H:%M')}")
    
    # Show all users with student in email
    student_users = CustomUser.objects.filter(email__icontains='student')
    if student_users.exists():
        print(f"\n👥 Users with 'student' in email:")
        for user in student_users:
            print(f"   - {user.email} | Active: {user.is_active} | Student: {user.is_student}")

if __name__ == "__main__":
    try:
        search_applications()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
