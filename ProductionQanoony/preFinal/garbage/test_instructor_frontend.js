// Frontend Components Test Suite for Instructor Profile
// Run this in browser console after navigating to /instructor-profile

const testInstructorFrontend = {
  
  // Test 1: Check if page loads correctly
  testPageLoad() {
    console.log('🧪 Testing Page Load...');
    
    const checks = {
      pageTitle: document.title.includes('قانوني') || document.title.includes('Instructor'),
      headerExists: !!document.querySelector('[data-testid="instructor-header"], .bg-gradient-to-r'),
      tabsExist: !!document.querySelector('[role="tablist"], .border-b'),
      contentArea: !!document.querySelector('.max-w-6xl, .container'),
      loadingStates: document.querySelectorAll('.animate-pulse, .animate-spin').length
    };
    
    console.log('Page Load Results:', checks);
    return checks;
  },

  // Test 2: Check tabs functionality
  testTabsNavigation() {
    console.log('🧪 Testing Tabs Navigation...');
    
    const tabs = document.querySelectorAll('button[role="tab"], nav button');
    const results = {
      tabsFound: tabs.length,
      tabsClickable: 0,
      activeTab: null
    };
    
    tabs.forEach((tab, index) => {
      if (tab.onclick || tab.addEventListener) {
        results.tabsClickable++;
      }
      if (tab.classList.contains('border-yellow-500') || tab.classList.contains('text-yellow-600')) {
        results.activeTab = tab.textContent.trim();
      }
    });
    
    console.log('Tabs Navigation Results:', results);
    return results;
  },

  // Test 3: Check profile header component
  testProfileHeader() {
    console.log('🧪 Testing Profile Header...');
    
    const header = document.querySelector('.bg-gradient-to-r, [data-testid="instructor-header"]');
    const results = {
      headerExists: !!header,
      profileImage: !!document.querySelector('img[alt*=""], .rounded-full img'),
      editButton: !!document.querySelector('button:contains("تعديل"), button[title*="تعديل"]'),
      cameraButton: !!document.querySelector('button[title*="تغيير"], .fa-camera'),
      instructorName: null,
      instructorEmail: null
    };
    
    // Try to extract instructor info
    const nameElement = document.querySelector('h1, .text-3xl, .text-4xl');
    if (nameElement) {
      results.instructorName = nameElement.textContent.trim();
    }
    
    const emailElement = document.querySelector('[href^="mailto:"], .fa-envelope + span');
    if (emailElement) {
      results.instructorEmail = emailElement.textContent.trim();
    }
    
    console.log('Profile Header Results:', results);
    return results;
  },

  // Test 4: Check stats cards
  testStatsCards() {
    console.log('🧪 Testing Stats Cards...');
    
    const statsCards = document.querySelectorAll('.shadow-lg, .bg-white');
    const results = {
      cardsFound: statsCards.length,
      hasNumbers: 0,
      hasIcons: 0,
      cardTitles: []
    };
    
    statsCards.forEach(card => {
      // Check for numbers
      const numbers = card.querySelectorAll('.text-3xl, .font-bold');
      if (numbers.length > 0) {
        results.hasNumbers++;
      }
      
      // Check for icons
      const icons = card.querySelectorAll('.fa-, svg, [class*="icon"]');
      if (icons.length > 0) {
        results.hasIcons++;
      }
      
      // Extract titles
      const title = card.querySelector('.text-gray-600, .text-sm');
      if (title) {
        results.cardTitles.push(title.textContent.trim());
      }
    });
    
    console.log('Stats Cards Results:', results);
    return results;
  },

  // Test 5: Check subjects list
  testSubjectsList() {
    console.log('🧪 Testing Subjects List...');
    
    const subjectCards = document.querySelectorAll('[data-testid="subject-card"], .grid > div');
    const results = {
      subjectsFound: subjectCards.length,
      hasSearchBox: !!document.querySelector('input[placeholder*="البحث"], input[type="search"]'),
      hasSortDropdown: !!document.querySelector('select, .fa-sort'),
      hasRefreshButton: !!document.querySelector('button:contains("تحديث"), button[title*="تحديث"]')
    };
    
    console.log('Subjects List Results:', results);
    return results;
  },

  // Test 6: Check students list
  testStudentsList() {
    console.log('🧪 Testing Students List...');
    
    const studentCards = document.querySelectorAll('[data-testid="student-card"], .grid > div');
    const results = {
      studentsFound: studentCards.length,
      hasProfileImages: document.querySelectorAll('img[alt*=""], .rounded-full img').length,
      hasContactButtons: document.querySelectorAll('button[title*="رسالة"], .fa-envelope').length,
      hasViewButtons: document.querySelectorAll('button[title*="عرض"], .fa-eye').length
    };
    
    console.log('Students List Results:', results);
    return results;
  },

  // Test 7: Check form functionality
  testProfileForm() {
    console.log('🧪 Testing Profile Form...');
    
    // Try to trigger edit mode
    const editButton = document.querySelector('button:contains("تعديل"), button[title*="تعديل"]');
    if (editButton) {
      editButton.click();
      
      setTimeout(() => {
        const form = document.querySelector('form, .space-y-6');
        const results = {
          formExists: !!form,
          inputFields: document.querySelectorAll('input[type="text"], input[type="tel"], textarea').length,
          saveButton: !!document.querySelector('button:contains("حفظ"), button[type="submit"]'),
          cancelButton: !!document.querySelector('button:contains("إلغاء"), button[type="button"]'),
          validation: document.querySelectorAll('.text-red-600, .border-red-500').length
        };
        
        console.log('Profile Form Results:', results);
        return results;
      }, 1000);
    } else {
      console.log('❌ Edit button not found');
      return { formExists: false };
    }
  },

  // Test 8: Check responsive design
  testResponsiveDesign() {
    console.log('🧪 Testing Responsive Design...');
    
    const results = {
      mobileClasses: document.querySelectorAll('[class*="md:"], [class*="lg:"], [class*="sm:"]').length,
      flexboxUsage: document.querySelectorAll('[class*="flex"]').length,
      gridUsage: document.querySelectorAll('[class*="grid"]').length,
      hiddenOnMobile: document.querySelectorAll('.hidden.md\\:block, .hidden.lg\\:block').length
    };
    
    console.log('Responsive Design Results:', results);
    return results;
  },

  // Test 9: Check error handling
  testErrorHandling() {
    console.log('🧪 Testing Error Handling...');
    
    const results = {
      errorMessages: document.querySelectorAll('.text-red-500, .text-red-600, .bg-red-100').length,
      loadingStates: document.querySelectorAll('.animate-pulse, .animate-spin').length,
      emptyStates: document.querySelectorAll(':contains("لا توجد"), :contains("غير متاح")').length,
      retryButtons: document.querySelectorAll('button:contains("إعادة"), button:contains("تحديث")').length
    };
    
    console.log('Error Handling Results:', results);
    return results;
  },

  // Test 10: Check accessibility
  testAccessibility() {
    console.log('🧪 Testing Accessibility...');
    
    const results = {
      altTexts: document.querySelectorAll('img[alt]').length,
      ariaLabels: document.querySelectorAll('[aria-label]').length,
      focusableElements: document.querySelectorAll('button, input, select, textarea, a').length,
      headingStructure: {
        h1: document.querySelectorAll('h1').length,
        h2: document.querySelectorAll('h2').length,
        h3: document.querySelectorAll('h3').length
      }
    };
    
    console.log('Accessibility Results:', results);
    return results;
  },

  // Run all frontend tests
  async runAllTests() {
    console.log('🚀 Starting Frontend Components Test Suite...\n');
    
    const results = {};
    
    results.pageLoad = this.testPageLoad();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.tabsNavigation = this.testTabsNavigation();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.profileHeader = this.testProfileHeader();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.statsCards = this.testStatsCards();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.subjectsList = this.testSubjectsList();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.studentsList = this.testStudentsList();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.responsiveDesign = this.testResponsiveDesign();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.errorHandling = this.testErrorHandling();
    console.log('\n' + '='.repeat(50) + '\n');
    
    results.accessibility = this.testAccessibility();
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Test form (async)
    results.profileForm = this.testProfileForm();
    
    console.log('🏁 Frontend Test Suite Completed!');
    console.log('Results Summary:', results);
    
    return results;
  }
};

// Usage instructions
console.log(`
📋 Frontend Components Test Suite
=================================

To run tests:
1. Navigate to /instructor-profile
2. Make sure you're logged in as instructor
3. Open browser console
4. Run: testInstructorFrontend.runAllTests()

Individual tests:
- testInstructorFrontend.testPageLoad()
- testInstructorFrontend.testTabsNavigation()
- testInstructorFrontend.testProfileHeader()
- testInstructorFrontend.testStatsCards()
- testInstructorFrontend.testSubjectsList()
- testInstructorFrontend.testStudentsList()
- testInstructorFrontend.testProfileForm()
- testInstructorFrontend.testResponsiveDesign()
- testInstructorFrontend.testErrorHandling()
- testInstructorFrontend.testAccessibility()
`);

// Make available globally
if (typeof window !== 'undefined') {
  window.testInstructorFrontend = testInstructorFrontend;
}
