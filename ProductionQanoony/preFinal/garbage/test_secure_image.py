#!/usr/bin/env python3
"""
اختبار الوصول الآمن للصور في نظام تجديد الاشتراكات
"""

import os
import sys
import django
import requests
import json

# إعداد Django
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from subscriptions.models import Subscription
from authentication.models import CustomUser

def get_admin_token():
    """الحصول على token للأدمن"""
    login_url = 'http://localhost:8000/api/auth/login/'
    
    # بيانات الأدمن
    admin_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    try:
        response = requests.post(login_url, json=admin_data)
        if response.status_code == 200:
            data = response.json()
            return data.get('access')
        else:
            print(f"❌ فشل تسجيل دخول الأدمن: {response.status_code}")
            print(f"📋 الاستجابة: {response.text}")
            return None
    except Exception as e:
        print(f"❌ خطأ في تسجيل دخول الأدمن: {e}")
        return None

def get_user_token():
    """الحصول على token لمستخدم عادي"""
    login_url = 'http://localhost:8000/api/auth/login/'
    
    # بيانات المستخدم
    user_data = {
        'email': '<EMAIL>',
        'password': 'testpass123'
    }
    
    try:
        response = requests.post(login_url, json=user_data)
        if response.status_code == 200:
            data = response.json()
            return data.get('access')
        else:
            print(f"❌ فشل تسجيل دخول المستخدم: {response.status_code}")
            print(f"📋 الاستجابة: {response.text}")
            return None
    except Exception as e:
        print(f"❌ خطأ في تسجيل دخول المستخدم: {e}")
        return None

def test_image_access_with_header(subscription_id, token, user_type="مستخدم"):
    """اختبار الوصول للصورة باستخدام Authorization header"""
    print(f"\n🔐 اختبار الوصول للصورة (Header) - {user_type}:")
    
    image_url = f'http://localhost:8000/api/subscriptions/secure/image/{subscription_id}/'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get(image_url, headers=headers)
        
        if response.status_code == 200:
            print(f"   ✅ نجح الوصول للصورة")
            print(f"   📄 نوع المحتوى: {response.headers.get('content-type', 'غير محدد')}")
            print(f"   📏 حجم الملف: {len(response.content)} بايت")
            return True
        elif response.status_code == 401:
            print(f"   ❌ غير مصرح (401)")
            print(f"   📋 الاستجابة: {response.text}")
        elif response.status_code == 403:
            print(f"   ❌ ممنوع (403)")
            print(f"   📋 الاستجابة: {response.text}")
        elif response.status_code == 404:
            print(f"   ❌ غير موجود (404)")
            print(f"   📋 الاستجابة: {response.text}")
        else:
            print(f"   ❌ خطأ غير متوقع: {response.status_code}")
            print(f"   📋 الاستجابة: {response.text}")
        
        return False
        
    except Exception as e:
        print(f"   ❌ خطأ في الطلب: {e}")
        return False

def test_image_access_with_query(subscription_id, token, user_type="مستخدم"):
    """اختبار الوصول للصورة باستخدام query parameter"""
    print(f"\n🔗 اختبار الوصول للصورة (Query Parameter) - {user_type}:")
    
    image_url = f'http://localhost:8000/api/subscriptions/secure/image/{subscription_id}/?token={token}'
    
    try:
        response = requests.get(image_url)
        
        if response.status_code == 200:
            print(f"   ✅ نجح الوصول للصورة")
            print(f"   📄 نوع المحتوى: {response.headers.get('content-type', 'غير محدد')}")
            print(f"   📏 حجم الملف: {len(response.content)} بايت")
            return True
        elif response.status_code == 401:
            print(f"   ❌ غير مصرح (401)")
            print(f"   📋 الاستجابة: {response.text}")
        elif response.status_code == 403:
            print(f"   ❌ ممنوع (403)")
            print(f"   📋 الاستجابة: {response.text}")
        elif response.status_code == 404:
            print(f"   ❌ غير موجود (404)")
            print(f"   📋 الاستجابة: {response.text}")
        else:
            print(f"   ❌ خطأ غير متوقع: {response.status_code}")
            print(f"   📋 الاستجابة: {response.text}")
        
        return False
        
    except Exception as e:
        print(f"   ❌ خطأ في الطلب: {e}")
        return False

def test_image_access_without_auth(subscription_id):
    """اختبار الوصول للصورة بدون مصادقة"""
    print(f"\n🚫 اختبار الوصول للصورة بدون مصادقة:")
    
    image_url = f'http://localhost:8000/api/subscriptions/secure/image/{subscription_id}/'
    
    try:
        response = requests.get(image_url)
        
        if response.status_code == 401:
            print(f"   ✅ تم رفض الوصول بشكل صحيح (401)")
            return True
        else:
            print(f"   ❌ خطأ أمني: تم السماح بالوصول بدون مصادقة!")
            print(f"   📋 رمز الاستجابة: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في الطلب: {e}")
        return False

def get_subscription_with_image():
    """البحث عن اشتراك يحتوي على صورة"""
    try:
        subscription = Subscription.objects.filter(
            renewal_screenshot__isnull=False
        ).exclude(renewal_screenshot='').first()
        
        if subscription:
            print(f"📋 تم العثور على اشتراك مع صورة:")
            print(f"   🆔 ID: {subscription.id}")
            print(f"   👤 المستخدم: {subscription.user.email}")
            print(f"   🖼️ الصورة: {subscription.renewal_screenshot.name}")
            return subscription
        else:
            print("❌ لم يتم العثور على اشتراك يحتوي على صورة")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في البحث عن الاشتراك: {e}")
        return None

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبارات الوصول الآمن للصور")
    print("=" * 60)
    
    # البحث عن اشتراك مع صورة
    subscription = get_subscription_with_image()
    if not subscription:
        print("\n💡 لإنشاء بيانات تجريبية، قم بتشغيل:")
        print("   python test_renewal_image_upload.py")
        return
    
    subscription_id = subscription.id
    
    # الحصول على tokens
    print(f"\n🔑 الحصول على tokens للاختبار...")
    admin_token = get_admin_token()
    user_token = get_user_token()
    
    if not admin_token:
        print("❌ فشل في الحصول على token الأدمن")
        return
    
    if not user_token:
        print("❌ فشل في الحصول على token المستخدم")
        return
    
    print("✅ تم الحصول على tokens بنجاح")
    
    # اختبار الوصول بدون مصادقة
    test_image_access_without_auth(subscription_id)
    
    # اختبار الوصول بـ Authorization header
    test_image_access_with_header(subscription_id, admin_token, "أدمن")
    test_image_access_with_header(subscription_id, user_token, "مستخدم عادي")
    
    # اختبار الوصول بـ query parameter
    test_image_access_with_query(subscription_id, admin_token, "أدمن")
    test_image_access_with_query(subscription_id, user_token, "مستخدم عادي")
    
    # اختبار الوصول لاشتراك غير موجود
    print(f"\n🔍 اختبار الوصول لاشتراك غير موجود:")
    test_image_access_with_header(99999, admin_token, "أدمن")
    
    print("\n" + "=" * 60)
    print("✅ انتهت جميع الاختبارات!")
    
    print(f"\n💡 لاختبار الوصول من المتصفح:")
    print(f"   🔗 Header: http://localhost:8000/api/subscriptions/secure/image/{subscription_id}/")
    print(f"   🔗 Query: http://localhost:8000/api/subscriptions/secure/image/{subscription_id}/?token={admin_token[:20]}...")

if __name__ == '__main__':
    main()
