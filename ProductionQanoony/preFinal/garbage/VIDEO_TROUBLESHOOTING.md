# دليل استكشاف أخطاء الفيديو

## المشاكل الشائعة وحلولها

### 1. الفيديو لا يظهر (إطار أبيض)

#### الأسباب المحتملة:
- معرف الفيديو غير صحيح
- معرف الفيديو فارغ
- مشكلة في الاتصال بالإنترنت
- الفيديو محذوف من يوتيوب

#### الحلول:

**أولاً: فتح Developer Tools**
1. اضغط `F12` لفتح أدوات المطور
2. انتقل إلى تبويب `Console`
3. ابحث عن رسائل الخطأ

**ثانياً: اختبار الفيديو**
```javascript
// في console المتصفح، اكتب:
testCurrentVideo()
```

**ثالثاً: اختبار روابط مختلفة**
```javascript
// اختبار روابط مختلفة
testYouTubeUrls()
```

### 2. معرف الفيديو غير صحيح

#### التحقق من المعرف:
- يجب أن يكون 11 حرف
- يجب أن يحتوي على أحرف صحيحة فقط (a-z, A-Z, 0-9, _, -)
- مثال صحيح: `dQw4w9WgXcQ`

#### إصلاح المعرف:
1. اذهب إلى يوتيوب
2. ابحث عن الفيديو
3. انسخ المعرف من الرابط
4. تأكد من أنه 11 حرف

### 3. الفيديو محظور أو غير متاح

#### الأسباب:
- الفيديو محذوف
- الفيديو محظور في بلدك
- الفيديو خاص

#### الحلول:
1. تحقق من الرابط في يوتيوب مباشرة
2. جرب استخدام VPN
3. اتصل بصاحب الفيديو

## أدوات التصحيح المتاحة

### 1. Console Logs
النظام يطبع معلومات تصحيح في console:
```javascript
// معلومات الفيديو الحالي
console.log('YouTube Video Debug:', {
  videoId: '...',
  embedUrl: '...',
  isValid: true/false
});
```

### 2. دوال الاختبار
```javascript
// اختبار الفيديو الحالي
testCurrentVideo()

// اختبار روابط مختلفة
testYouTubeUrls()

// اختبار معرف محدد
testSpecificVideoId('dQw4w9WgXcQ')
```

### 3. مؤشرات بصرية
- **أخضر**: الفيديو متاح
- **أحمر**: الفيديو معطل
- **رمادي**: لا يوجد فيديو

## خطوات التصحيح

### الخطوة 1: فحص البيانات
```javascript
// في console
console.log('Selected Lecture:', selectedLecture)
console.log('Video ID:', selectedLecture?.youtube_video_id)
```

### الخطوة 2: اختبار المعرف
```javascript
// اختبار المعرف
testSpecificVideoId(selectedLecture.youtube_video_id)
```

### الخطوة 3: إنشاء iframe تجريبي
```javascript
// إنشاء iframe للاختبار
const test = createTestIframe('dQw4w9WgXcQ')
console.log('Test Iframe:', test.iframeHtml)
```

### الخطوة 4: اختبار في صفحة جديدة
1. انسخ رابط embed
2. افتح صفحة جديدة
3. الصق الرابط في المتصفح
4. تحقق من ظهور الفيديو

## أمثلة روابط صحيحة

### روابط كاملة:
```
https://www.youtube.com/watch?v=dQw4w9WgXcQ
https://youtu.be/dQw4w9WgXcQ
https://www.youtube.com/embed/dQw4w9WgXcQ
```

### معرفات صحيحة:
```
dQw4w9WgXcQ
jNQXAC9IVRw
9bZkp7q19f0
```

## أمثلة روابط خاطئة

### معرفات خاطئة:
```
dQw4w9WgXc  (10 أحرف فقط)
dQw4w9WgXcQQ (12 حرف)
dQw4w9WgXc@ (حرف غير مسموح)
```

### روابط خاطئة:
```
https://www.youtube.com/watch?v=short
https://youtu.be/invalid
https://vimeo.com/123456
```

## إصلاح المشاكل في قاعدة البيانات

### 1. فحص البيانات الحالية
```sql
-- فحص المحاضرات مع معرفات الفيديو
SELECT id, title_ar, youtube_video_id, LENGTH(youtube_video_id) as id_length
FROM courses_lecture 
WHERE youtube_video_id IS NOT NULL AND youtube_video_id != '';
```

### 2. تنظيف البيانات
```sql
-- إزالة المسافات الزائدة
UPDATE courses_lecture 
SET youtube_video_id = TRIM(youtube_video_id)
WHERE youtube_video_id IS NOT NULL;

-- إزالة المعرفات القصيرة جداً
UPDATE courses_lecture 
SET youtube_video_id = NULL
WHERE LENGTH(youtube_video_id) < 11;
```

### 3. التحقق من صحة المعرفات
```sql
-- فحص المعرفات التي لا تطابق النمط الصحيح
SELECT id, title_ar, youtube_video_id
FROM courses_lecture 
WHERE youtube_video_id IS NOT NULL 
AND youtube_video_id NOT REGEXP '^[a-zA-Z0-9_-]{11}$';
```

## الاتصال بالدعم

إذا لم تحل المشكلة:
1. التقط screenshot للخطأ
2. انسخ معلومات console
3. اذكر معرف الفيديو
4. اذكر المتصفح ونظام التشغيل

## ملاحظات مهمة

1. **الأمان**: لا تشارك معرفات الفيديو الخاصة
2. **الأداء**: الفيديوهات الكبيرة قد تحتاج وقت للتحميل
3. **التوافق**: تأكد من أن المتصفح محدث
4. **الإنترنت**: تأكد من اتصال إنترنت مستقر 