#!/usr/bin/env python3
"""
Test script for Subscription Renewal Image Upload
Tests the complete flow: user uploads image -> admin views it in dashboard
"""

import os
import sys
import django
import requests
import json
from io import BytesIO
from PIL import Image
import tempfile

# Add the project directory to Python path
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from subscriptions.models import Subscription, SubscriptionPlan
from authentication.models import CustomUser
from datetime import date, timedelta

User = get_user_model()

def create_test_image():
    """Create a test image for upload using PNG format"""
    print("🖼️ Creating test payment proof image...")

    # Create a simple colored image
    img = Image.new('RGB', (100, 100), color=(255, 0, 0))  # Red square

    # Save to a specific path as PNG (more reliable)
    temp_path = '/tmp/test_payment_proof.png'

    try:
        # Save as PNG which is simpler and more reliable
        img.save(temp_path, format='PNG')

        # Verify the file exists and is readable
        if os.path.exists(temp_path):
            # Test that PIL can read it back
            test_img = Image.open(temp_path)
            test_img.load()  # Force loading to verify it's valid
            test_img.close()

            file_size = os.path.getsize(temp_path)
            print(f"📏 Image file size: {file_size} bytes")
            print(f"✅ Test image created: {temp_path}")
            return temp_path
        else:
            raise Exception("File was not created")

    except Exception as e:
        print(f"❌ Failed to create test image: {e}")
        return None

def setup_test_data():
    """Setup test user and subscription"""
    print("🔧 Setting up test data...")
    
    # Create test user
    test_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Test',
            'last_name': 'User',
            'is_active': True,
            'email_verified': True
        }
    )
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Created test user: {test_user.email}")
    
    # Create admin user
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Admin',
            'last_name': 'User',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'email_verified': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"✅ Created admin user: {admin_user.email}")
    
    # Create subscription plan
    plan, created = SubscriptionPlan.objects.get_or_create(
        name='Test Plan',
        defaults={
            'duration_days': 30,
            'price': 199.00,
            'is_active': True
        }
    )
    if created:
        print(f"✅ Created test plan: {plan.name}")
    
    # Create expired subscription for renewal
    subscription, created = Subscription.objects.get_or_create(
        user=test_user,
        plan=plan,
        defaults={
            'start_date': date.today() - timedelta(days=35),
            'end_date': date.today() - timedelta(days=5),
            'status': 'expired',
            'renewal_status': 'approved'  # Not pending, so user can create new request
        }
    )

    # Make sure no pending renewals exist for this user
    Subscription.objects.filter(user=test_user, renewal_status='pending').update(renewal_status='approved')
    if created:
        print(f"✅ Created expired subscription for renewal")
    
    return test_user, admin_user, subscription, plan

def get_user_token(user, password):
    """Get authentication token for user"""
    print(f"🔑 Getting authentication token for {user.email}...")
    
    login_url = 'http://localhost:8000/api/auth/login/'
    login_data = {
        'email': user.email,
        'password': password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token = response.json().get('access')
            print(f"✅ Successfully obtained token for {user.email}")
            return token
        else:
            print(f"❌ Failed to get token: {response.status_code} - {response.text}")
            return None
    except requests.exceptions.ConnectionError:
        print("❌ Connection error: Make sure Django server is running on localhost:8000")
        return None

def test_image_upload(user_token, plan_id, image_path):
    """Test uploading renewal image"""
    print("📤 Testing image upload for renewal request...")

    upload_url = 'http://localhost:8000/api/subscriptions/renewal-requests/'
    headers = {'Authorization': f'Bearer {user_token}'}

    try:
        # Verify file exists and get info
        if not os.path.exists(image_path):
            print(f"❌ Image file not found: {image_path}")
            return False

        file_size = os.path.getsize(image_path)
        print(f"📤 Uploading image: {file_size} bytes")

        with open(image_path, 'rb') as img_file:
            files = {'renewal_screenshot': ('payment_proof.png', img_file, 'image/png')}
            data = {'plan_id': plan_id}

            response = requests.post(upload_url, headers=headers, files=files, data=data)

            if response.status_code == 200:
                print("✅ Image uploaded successfully!")
                print(f"📋 Response: {response.json()}")
                return True
            else:
                print(f"❌ Upload failed: {response.status_code}")
                print(f"📋 Response: {response.text}")
                return False

    except Exception as e:
        print(f"❌ Upload error: {e}")
        return False

def test_admin_view_subscriptions(admin_token):
    """Test admin viewing subscriptions with renewal requests"""
    print("👨‍💼 Testing admin view of subscriptions...")
    
    subscriptions_url = 'http://localhost:8000/api/subscriptions/admin/subscriptions/'
    headers = {'Authorization': f'Bearer {admin_token}'}
    
    try:
        response = requests.get(subscriptions_url, headers=headers)
        
        if response.status_code == 200:
            subscriptions = response.json()
            print("✅ Admin successfully retrieved subscriptions!")
            print(f"📊 Found {len(subscriptions)} subscriptions")
            
            # Find renewal requests
            renewal_requests = [sub for sub in subscriptions if sub.get('renewal_status') == 'pending']
            print(f"🔄 Found {len(renewal_requests)} pending renewal requests")
            
            for req in renewal_requests:
                print(f"📋 Renewal Request:")
                print(f"   User: {req.get('user_email')}")
                print(f"   Plan: {req.get('plan', {}).get('name')}")
                print(f"   Status: {req.get('renewal_status')}")
                print(f"   Screenshot: {'✅ Available' if req.get('renewal_screenshot') else '❌ Missing'}")
                
                if req.get('renewal_screenshot'):
                    print(f"   Screenshot URL: {req.get('renewal_screenshot')}")
            
            return renewal_requests
        else:
            print(f"❌ Failed to get subscriptions: {response.status_code}")
            print(f"📋 Response: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Admin view error: {e}")
        return []

def test_secure_image_access(admin_token, subscription_id):
    """Test secure image access"""
    print("🔒 Testing secure image access...")
    
    image_url = f'http://localhost:8000/api/subscriptions/secure/image/{subscription_id}/'
    headers = {'Authorization': f'Bearer {admin_token}'}
    
    try:
        response = requests.get(image_url, headers=headers)
        
        if response.status_code == 200:
            print("✅ Secure image access successful!")
            print(f"📋 Content-Type: {response.headers.get('Content-Type')}")
            print(f"📋 Content-Length: {len(response.content)} bytes")
            
            # Save the downloaded image for verification
            with open('/tmp/downloaded_renewal_image.jpg', 'wb') as f:
                f.write(response.content)
            print("💾 Image saved to /tmp/downloaded_renewal_image.jpg for verification")
            
            return True
        else:
            print(f"❌ Secure image access failed: {response.status_code}")
            print(f"📋 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Secure image access error: {e}")
        return False

def test_renewal_action(admin_token, subscription_id, action='approve'):
    """Test admin approval/rejection of renewal"""
    print(f"⚖️ Testing admin {action} action...")
    
    action_url = f'http://localhost:8000/api/subscriptions/renewal-action/{subscription_id}/'
    headers = {'Authorization': f'Bearer {admin_token}'}
    data = {'action': action}
    
    if action == 'deny':
        data['denial_reason'] = 'Test rejection reason'
    
    try:
        response = requests.post(action_url, headers=headers, json=data)
        
        if response.status_code == 200:
            print(f"✅ Admin {action} action successful!")
            print(f"📋 Response: {response.json()}")
            return True
        else:
            print(f"❌ Admin {action} action failed: {response.status_code}")
            print(f"📋 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Admin {action} action error: {e}")
        return False

def cleanup_test_files(image_path):
    """Clean up test files"""
    try:
        if image_path and os.path.exists(image_path):
            os.unlink(image_path)
            print(f"🧹 Cleaned up test image: {image_path}")

        if os.path.exists('/tmp/downloaded_renewal_image.jpg'):
            os.unlink('/tmp/downloaded_renewal_image.jpg')
            print("🧹 Cleaned up downloaded image")

        # Also clean up the specific test file
        if os.path.exists('/tmp/test_payment_proof.jpg'):
            os.unlink('/tmp/test_payment_proof.jpg')
            print("🧹 Cleaned up test payment proof")
    except Exception as e:
        print(f"⚠️ Cleanup error: {e}")

def main():
    """Main test function"""
    print("🚀 Starting Subscription Renewal Image Upload Test")
    print("=" * 60)
    
    # Setup test data
    test_user, admin_user, subscription, plan = setup_test_data()
    
    # Create test image
    image_path = create_test_image()

    if not image_path:
        print("❌ Failed to create test image, cannot continue")
        return

    try:
        # Get tokens
        user_token = get_user_token(test_user, 'testpass123')
        admin_token = get_user_token(admin_user, 'admin123')
        
        if not user_token or not admin_token:
            print("❌ Cannot proceed without authentication tokens")
            return
        
        print("\n" + "=" * 60)
        print("📤 STEP 1: User uploads renewal image")
        print("=" * 60)
        
        # Test image upload
        upload_success = test_image_upload(user_token, plan.id, image_path)
        
        if not upload_success:
            print("❌ Image upload failed, cannot continue test")
            return
        
        print("\n" + "=" * 60)
        print("👨‍💼 STEP 2: Admin views renewal requests")
        print("=" * 60)
        
        # Test admin view
        renewal_requests = test_admin_view_subscriptions(admin_token)
        
        print("\n" + "=" * 60)
        print("🔒 STEP 3: Admin accesses secure image")
        print("=" * 60)
        
        # Test secure image access
        image_access_success = test_secure_image_access(admin_token, subscription.id)
        
        print("\n" + "=" * 60)
        print("⚖️ STEP 4: Admin approves renewal")
        print("=" * 60)
        
        # Test admin action
        action_success = test_renewal_action(admin_token, subscription.id, 'approve')
        
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS")
        print("=" * 60)
        
        results = {
            "Image Upload": "✅ PASS" if upload_success else "❌ FAIL",
            "Admin View": "✅ PASS" if len(renewal_requests) > 0 else "❌ FAIL",
            "Secure Image Access": "✅ PASS" if image_access_success else "❌ FAIL",
            "Admin Action": "✅ PASS" if action_success else "❌ FAIL"
        }
        
        for test_name, result in results.items():
            print(f"{test_name:20} {result}")
        
        all_passed = all("✅ PASS" in result for result in results.values())
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 ALL TESTS PASSED! Renewal image system is working perfectly!")
        else:
            print("💥 SOME TESTS FAILED! Check the logs above for details.")
        print("=" * 60)
        
    finally:
        # Cleanup
        cleanup_test_files(image_path)

if __name__ == '__main__':
    main()
