# 📁 تحديث نظام رفع الملفات في المكتبة

## 🎯 الهدف
تغيير نظام رفع الملفات من URL إلى رفع ملف فعلي في تبويب المكتبة.

## 🔄 التغييرات المطبقة

### 1. Frontend Changes (`LibraryManagement.jsx`)

#### ✅ تحديث State Management
```javascript
// إضافة state للملف المحدد
const [selectedFile, setSelectedFile] = useState(null);

// إزالة file_url من documentForm
const [documentForm, setDocumentForm] = useState({
  title_ar: '',
  title_en: '',
  category_id: '',
  description_ar: '',
  file_type: 'pdf',
  author: '',
  publication_date: '',
  tags: '',
  is_featured: false
  // file_url: '' ← تم حذفها
});
```

#### ✅ إضافة وظيفة التعامل مع الملفات
```javascript
const handleFileSelect = (e) => {
  const file = e.target.files[0];
  // التحقق من نوع الملف
  // التحقق من حجم الملف (حد أقصى 10MB)
  // تحديد نوع الملف تلقائياً
};
```

#### ✅ تحديث وظيفة الإرسال
```javascript
const handleDocumentSubmit = async (e) => {
  // إنشاء FormData
  const formData = new FormData();
  
  // إضافة البيانات
  formData.append('title_ar', documentForm.title_ar);
  // ... باقي الحقول
  
  // إضافة الملف
  if (selectedFile) {
    formData.append('file_url', selectedFile);
  }
  
  // إرسال مع multipart headers
  const multipartHeaders = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'multipart/form-data'
  };
};
```

#### ✅ تحديث واجهة المستخدم
```jsx
<input
  type="file"
  onChange={handleFileSelect}
  accept=".pdf,.doc,.docx,.txt"
  className="w-full border border-gray-300 rounded-lg px-3 py-2"
  required={!editingDocument}
/>
```

### 2. Backend Changes

#### ✅ تحديث Serializer (`library/serializers.py`)
```python
class LegalDocumentSerializer(serializers.ModelSerializer):
    file_url = serializers.FileField(required=False)  # ← تغيير من CharField
    
    class Meta:
        read_only_fields = ['id', 'download_count', 'uploaded_by', 'created_at']
        # إزالة 'is_featured' من read_only_fields
```

#### ✅ تحديث ViewSet (`library/views.py`)
```python
from rest_framework.parsers import MultiPartParser, FormParser

class LegalDocumentViewSet(viewsets.ModelViewSet):
    parser_classes = [MultiPartParser, FormParser]  # ← إضافة دعم رفع الملفات
```

## 🔍 الميزات الجديدة

### ✅ التحقق من نوع الملف
- PDF: `application/pdf`
- Word: `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- نص: `text/plain`

### ✅ التحقق من حجم الملف
- الحد الأقصى: 10 ميجابايت
- رسالة خطأ واضحة عند تجاوز الحد

### ✅ تحديد نوع الملف تلقائياً
- PDF → `pdf`
- Word (.docx) → `docx`
- Word (.doc) → `doc`
- نص → `txt`

### ✅ معاينة الملف المحدد
- عرض اسم الملف
- عرض حجم الملف بالميجابايت
- حالة الملف الحالي عند التعديل

## 🧪 الاختبار

### ملف الاختبار: `test_library_upload.py`
- إنشاء ملف اختبار
- اختبار رفع وثيقة جديدة
- تنظيف ملفات الاختبار

### خطوات الاختبار اليدوي:
1. فتح تبويب المكتبة
2. النقر على "إضافة وثيقة جديدة"
3. ملء البيانات المطلوبة
4. اختيار ملف للرفع
5. حفظ الوثيقة
6. التحقق من رفع الملف بنجاح

## 🔧 متطلبات التشغيل

### Frontend:
- React مع دعم FormData
- axios للطلبات
- file input مع validation

### Backend:
- Django REST Framework
- MultiPartParser, FormParser
- FileField في النموذج
- مساحة تخزين للملفات

## 📝 ملاحظات مهمة

1. **الأمان**: يتم التحقق من نوع وحجم الملف
2. **التوافق**: يدعم PDF, Word, وملفات النص
3. **التعديل**: عند تعديل وثيقة، رفع ملف جديد اختياري
4. **الأداء**: حد أقصى 10MB لتجنب مشاكل الأداء
5. **UX**: رسائل واضحة للمستخدم عن حالة الرفع

## 🚀 النتيجة النهائية

✅ نظام رفع ملفات كامل وآمن
✅ واجهة مستخدم محسنة
✅ تحقق شامل من الملفات
✅ دعم أنواع ملفات متعددة
✅ رسائل خطأ واضحة
✅ تجربة مستخدم سلسة
