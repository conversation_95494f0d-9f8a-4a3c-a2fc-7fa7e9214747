#!/usr/bin/env python3
"""
Create sample student and instructor applications for testing
"""
import os
import sys
from pathlib import Path
from django.core.files.uploadedfile import SimpleUploadedFile

# Add project to path
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')

# Set environment variables before importing Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
os.environ['SECRET_KEY'] = 'django-insecure-temp-key-for-testing'
os.environ['DEBUG'] = 'True'

# Try to set database URL
db_path = Path('/home/<USER>/projects/PreQanoony/preFinal/db.sqlite3')
os.environ['DATABASE_URL'] = f'sqlite:///{db_path}'

import django
django.setup()

from authentication.models import CustomUser, StudentApplication, InstructorApplication
from subscriptions.models import SubscriptionPlan
from universities.models import University, AcademicYear

def create_sample_data():
    """Create sample applications for testing"""
    print("🔄 Creating sample applications...")
    
    # Create a sample subscription plan if none exists
    plan, created = SubscriptionPlan.objects.get_or_create(
        name="Basic Plan",
        defaults={
            'price': 100.00,
            'duration_months': 1,
            'description': 'Basic subscription plan'
        }
    )
    if created:
        print(f"✅ Created subscription plan: {plan.name}")
    
    # Create sample universities and academic years if none exist
    university, created = University.objects.get_or_create(
        name_en="Test University",
        defaults={
            'name_ar': 'جامعة تجريبية',
            'location': 'Test City'
        }
    )
    if created:
        print(f"✅ Created university: {university.name_en}")
    
    academic_year, created = AcademicYear.objects.get_or_create(
        year_name_en="First Year",
        defaults={
            'year_name_ar': 'السنة الأولى'
        }
    )
    if created:
        print(f"✅ Created academic year: {academic_year.year_name_en}")
    
    # Create sample users and student applications
    for i in range(3):
        email = f"student{i+1}@test.com"
        
        # Check if user already exists
        if CustomUser.objects.filter(email=email).exists():
            print(f"⚠️  User {email} already exists, skipping...")
            continue
            
        # Create user
        user = CustomUser.objects.create_user(
            email=email,
            first_name=f"Student{i+1}",
            last_name="Test",
            password="testpass123",
            is_active=True
        )
        print(f"✅ Created user: {user.email}")
        
        # Create a simple image file for testing
        image_content = b"fake image content for testing"
        image_file = SimpleUploadedFile(
            f"receipt_{i+1}.jpg",
            image_content,
            content_type="image/jpeg"
        )
        
        # Create student application
        app = StudentApplication.objects.create(
            user=user,
            selected_plan=plan,
            payment_screenshot=image_file
        )
        print(f"✅ Created student application: {app.id}")
    
    # Create sample instructor applications
    for i in range(2):
        email = f"instructor{i+1}@test.com"
        
        # Check if application already exists
        if InstructorApplication.objects.filter(email=email).exists():
            print(f"⚠️  Instructor application {email} already exists, skipping...")
            continue
            
        # Create instructor application
        app = InstructorApplication.objects.create(
            email=email,
            first_name=f"Instructor{i+1}",
            last_name="Test",
            phone_number=f"123456789{i}",
            bio=f"Test instructor {i+1} bio",
            specialty="Mathematics"
        )
        print(f"✅ Created instructor application: {app.id}")
    
    print("\n📊 Final counts:")
    print(f"   Student Applications: {StudentApplication.objects.count()}")
    print(f"   Instructor Applications: {InstructorApplication.objects.count()}")
    print(f"   Total Users: {CustomUser.objects.count()}")
    
    print("\n✅ Sample data created successfully!")
    print("🔄 Now refresh the admin dashboard to see the applications.")

if __name__ == "__main__":
    try:
        create_sample_data()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
