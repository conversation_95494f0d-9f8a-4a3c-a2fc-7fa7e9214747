#!/usr/bin/env python3
"""
Check student applications in the database
"""
import os
import django
import sys

# Setup Django
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# Set required environment variables
os.environ['SECRET_KEY'] = 'django-insecure-temp-key-for-testing'
os.environ['DEBUG'] = 'True'
os.environ['DATABASE_URL'] = 'sqlite:///db.sqlite3'

django.setup()

from authentication.models import CustomUser, StudentApplication, InstructorApplication
from students.models import StudentProfile

def check_applications():
    """Check student applications in the database"""
    print("=== Student Applications Check ===\n")
    
    # Count applications
    student_apps_count = StudentApplication.objects.count()
    instructor_apps_count = InstructorApplication.objects.count()
    student_profiles_count = StudentProfile.objects.count()
    instructors_count = CustomUser.objects.filter(is_instructor=True, is_active=True).count()
    
    print(f"📊 Database Counts:")
    print(f"   Student Applications: {student_apps_count}")
    print(f"   Instructor Applications: {instructor_apps_count}")
    print(f"   Student Profiles: {student_profiles_count}")
    print(f"   Active Instructors: {instructors_count}")
    print()
    
    # Show student applications
    if student_apps_count > 0:
        print("📋 Student Applications:")
        for app in StudentApplication.objects.all()[:5]:  # Show first 5
            status = "✅ Approved" if app.is_approved else ("❌ Rejected" if app.rejection_reason else "⏳ Pending")
            print(f"   ID: {app.id} | User: {app.user.email} | Status: {status} | Created: {app.created_at.strftime('%Y-%m-%d')}")
        if student_apps_count > 5:
            print(f"   ... and {student_apps_count - 5} more")
    else:
        print("📋 No Student Applications found")
    print()
    
    # Show instructor applications
    if instructor_apps_count > 0:
        print("👨‍🏫 Instructor Applications:")
        for app in InstructorApplication.objects.all()[:5]:  # Show first 5
            status = "✅ Approved" if app.is_approved else ("❌ Rejected" if app.rejection_reason else "⏳ Pending")
            email = app.user.email if app.user else app.email
            print(f"   ID: {app.id} | Email: {email} | Status: {status} | Created: {app.created_at.strftime('%Y-%m-%d')}")
        if instructor_apps_count > 5:
            print(f"   ... and {instructor_apps_count - 5} more")
    else:
        print("👨‍🏫 No Instructor Applications found")
    print()
    
    # Show student profiles
    if student_profiles_count > 0:
        print("🎓 Student Profiles:")
        for profile in StudentProfile.objects.all()[:5]:  # Show first 5
            university = profile.university.name_en if profile.university else "No University"
            year = profile.academic_year.year_name_en if profile.academic_year else "No Year"
            print(f"   ID: {profile.id} | Email: {profile.user.email} | University: {university} | Year: {year}")
        if student_profiles_count > 5:
            print(f"   ... and {student_profiles_count - 5} more")
    else:
        print("🎓 No Student Profiles found")
    print()
    
    # Show active instructors
    if instructors_count > 0:
        print("👨‍🏫 Active Instructors:")
        for instructor in CustomUser.objects.filter(is_instructor=True, is_active=True)[:5]:
            print(f"   ID: {instructor.id} | Email: {instructor.email} | Name: {instructor.first_name} {instructor.last_name}")
        if instructors_count > 5:
            print(f"   ... and {instructors_count - 5} more")
    else:
        print("👨‍🏫 No Active Instructors found")

if __name__ == "__main__":
    try:
        check_applications()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
