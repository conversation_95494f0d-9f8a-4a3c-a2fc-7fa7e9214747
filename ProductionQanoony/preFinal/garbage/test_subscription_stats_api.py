#!/usr/bin/env python3
"""
Test script for Subscription Stats API
Tests the revenue calculation logic after the fix
"""

import os
import sys
import django
import requests
import json
from decimal import Decimal

# Add the project directory to Python path
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from subscriptions.models import Subscription, SubscriptionPlan
from authentication.models import CustomUser

User = get_user_model()

def create_test_data():
    """Create test data for revenue calculation"""
    print("🔧 Creating test data...")
    
    # Create admin user if not exists
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Admin',
            'last_name': 'Test',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'email_verified': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"✅ Created admin user: {admin_user.email}")
    
    # Create test users
    users = []
    for i in range(3):
        user, created = User.objects.get_or_create(
            email=f'user{i+1}@test.com',
            defaults={
                'first_name': f'User{i+1}',
                'last_name': 'Test',
                'is_active': True,
                'email_verified': True
            }
        )
        users.append(user)
        if created:
            print(f"✅ Created user: {user.email}")
    
    # Create subscription plans
    plans_data = [
        {'name': 'Basic Plan', 'duration_days': 30, 'price': Decimal('100.00')},
        {'name': 'Premium Plan', 'duration_days': 90, 'price': Decimal('250.00')},
        {'name': 'Pro Plan', 'duration_days': 365, 'price': Decimal('800.00')}
    ]
    
    plans = []
    for plan_data in plans_data:
        plan, created = SubscriptionPlan.objects.get_or_create(
            name=plan_data['name'],
            defaults=plan_data
        )
        plans.append(plan)
        if created:
            print(f"✅ Created plan: {plan.name} - {plan.price} ج.م")
    
    # Create subscriptions with different statuses
    from datetime import date, timedelta
    
    subscriptions_data = [
        # Active subscriptions (should be counted in revenue)
        {'user': users[0], 'plan': plans[0], 'status': 'active'},  # 100 ج.م
        {'user': users[1], 'plan': plans[1], 'status': 'active'},  # 250 ج.م
        {'user': users[2], 'plan': plans[2], 'status': 'active'},  # 800 ج.م
        
        # Expired subscriptions (should NOT be counted)
        {'user': users[0], 'plan': plans[0], 'status': 'expired'},
        {'user': users[1], 'plan': plans[1], 'status': 'expired'},
        
        # Pending renewal (should NOT be counted in basic revenue)
        {'user': users[2], 'plan': plans[0], 'status': 'pending_renewal'},
    ]
    
    expected_revenue = Decimal('0.00')
    for sub_data in subscriptions_data:
        subscription, created = Subscription.objects.get_or_create(
            user=sub_data['user'],
            plan=sub_data['plan'],
            status=sub_data['status'],
            defaults={
                'start_date': date.today(),
                'end_date': date.today() + timedelta(days=sub_data['plan'].duration_days),
            }
        )
        if created:
            print(f"✅ Created subscription: {subscription.user.email} - {subscription.plan.name} - {subscription.status}")
            
        # Calculate expected revenue (only active subscriptions)
        if sub_data['status'] == 'active':
            expected_revenue += sub_data['plan'].price
    
    print(f"💰 Expected total revenue: {expected_revenue} ج.م")
    return admin_user, expected_revenue

def get_admin_token(admin_user):
    """Get authentication token for admin user"""
    print("🔑 Getting admin authentication token...")
    
    login_url = 'http://localhost:8000/api/auth/login/'
    login_data = {
        'email': admin_user.email,
        'password': 'admin123'
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token = response.json().get('access')
            print("✅ Successfully obtained admin token")
            return token
        else:
            print(f"❌ Failed to get token: {response.status_code} - {response.text}")
            return None
    except requests.exceptions.ConnectionError:
        print("❌ Connection error: Make sure Django server is running on localhost:8000")
        return None

def test_subscription_stats_api(token, expected_revenue):
    """Test the subscription stats API"""
    print("🧪 Testing Subscription Stats API...")
    
    stats_url = 'http://localhost:8000/api/subscriptions/admin/stats/'
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get(stats_url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API call successful!")
            print(f"📊 API Response:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # Test revenue calculation
            actual_revenue = Decimal(str(data['basic_stats']['total_revenue']))
            print(f"\n💰 Revenue Comparison:")
            print(f"   Expected: {expected_revenue} ج.م")
            print(f"   Actual:   {actual_revenue} ج.م")
            
            if actual_revenue == expected_revenue:
                print("✅ Revenue calculation is CORRECT!")
            else:
                print("❌ Revenue calculation is INCORRECT!")
                print(f"   Difference: {actual_revenue - expected_revenue} ج.م")
            
            # Test other stats
            basic_stats = data['basic_stats']
            print(f"\n📈 Other Statistics:")
            print(f"   Total Subscriptions: {basic_stats['total_subscriptions']}")
            print(f"   Active Subscriptions: {basic_stats['active_subscriptions']}")
            print(f"   Expired Subscriptions: {basic_stats['expired_subscriptions']}")
            print(f"   Pending Renewals: {basic_stats['pending_renewals']}")
            
            return True
            
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error: Make sure Django server is running on localhost:8000")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Subscription Stats API Test")
    print("=" * 50)
    
    # Create test data
    admin_user, expected_revenue = create_test_data()
    
    # Get authentication token
    token = get_admin_token(admin_user)
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    # Test the API
    success = test_subscription_stats_api(token, expected_revenue)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test completed successfully!")
    else:
        print("💥 Test failed!")

if __name__ == '__main__':
    main()
