#!/usr/bin/env python3
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from authentication.models import CustomUser
from django.contrib.auth.models import Group

def get_student_credentials():
    try:
        # Get Students group
        students_group = Group.objects.get(name='Students')
        
        # Get students
        students = CustomUser.objects.filter(groups=students_group)
        
        print("=== الطلاب المتاحين ===")
        print(f"عدد الطلاب: {students.count()}")
        print()
        
        for i, student in enumerate(students[:10], 1):
            print(f"{i}. Username: {student.username}")
            print(f"   Email: {student.email}")
            print(f"   Active: {student.is_active}")
            print(f"   Date joined: {student.date_joined}")
            print("-" * 40)
            
        # Get a test student
        if students.exists():
            test_student = students.first()
            print("\n=== بيانات طالب للتجربة ===")
            print(f"Username: {test_student.username}")
            print(f"Email: {test_student.email}")
            print("\nملاحظة: الباسورد الافتراضي عادة ما يكون 'password123' أو نفس الـ username")
            
    except Group.DoesNotExist:
        print("مجموعة الطلاب غير موجودة")
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    get_student_credentials()
