# 🔌 WebSocket Setup Guide

## 📋 **Overview:**
This guide explains how to use WebSocket connections with Django Channels in the React frontend.

---

## ⚙️ **Environment Configuration:**

### **1. Environment Variables (.env):**
```bash
# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8000

# WebSocket Configuration for Django Channels
REACT_APP_WS_BASE_URL=ws://localhost:8000

# For production with SSL:
# REACT_APP_WS_BASE_URL=wss://yourdomain.com
```

---

## 🛠️ **Available Utilities:**

### **1. WebSocket Utility (`src/utils/websocket.js`):**
- `createWebSocketURL()` - Create WebSocket URLs with authentication
- `createAuthenticatedWebSocket()` - Create authenticated WebSocket connections
- `sendWebSocketMessage()` - Send messages through WebSocket
- `WS_ENDPOINTS` - Predefined WebSocket endpoints
- `isWebSocketConnected()` - Check connection status
- `closeWebSocket()` - Close connections safely

### **2. React Hooks (`src/hooks/useWebSocket.js`):**
- `useWebSocket()` - Basic WebSocket hook
- `useChatWebSocket()` - Chat-specific WebSocket hook
- `useNotificationsWebSocket()` - Notifications WebSocket hook
- `useAIAssistantWebSocket()` - AI Assistant WebSocket hook

---

## 🚀 **Usage Examples:**

### **1. Basic WebSocket Connection:**
```javascript
import { useWebSocket } from '../hooks/useWebSocket';

const MyComponent = () => {
  const {
    isConnected,
    lastMessage,
    sendMessage,
    connectionState,
  } = useWebSocket('ws/my-endpoint/', {
    onMessage: (data) => {
      console.log('Received:', data);
    },
    onOpen: () => {
      console.log('Connected!');
    },
  });

  const handleSend = () => {
    sendMessage({ type: 'hello', data: 'world' });
  };

  return (
    <div>
      <p>Status: {connectionState}</p>
      <button onClick={handleSend} disabled={!isConnected}>
        Send Message
      </button>
    </div>
  );
};
```

### **2. Chat WebSocket:**
```javascript
import { useChatWebSocket } from '../hooks/useWebSocket';

const ChatComponent = ({ roomId }) => {
  const [messages, setMessages] = useState([]);
  
  const {
    isConnected,
    sendChatMessage,
    sendTyping,
  } = useChatWebSocket(roomId, {
    onMessage: (data) => {
      if (data.type === 'chat_message') {
        setMessages(prev => [...prev, data]);
      }
    },
  });

  const handleSendMessage = (message) => {
    sendChatMessage(message);
  };

  return (
    <div>
      {/* Chat UI */}
    </div>
  );
};
```

### **3. Notifications WebSocket:**
```javascript
import { useNotificationsWebSocket } from '../hooks/useWebSocket';

const NotificationsComponent = () => {
  const [notifications, setNotifications] = useState([]);
  
  const { isConnected } = useNotificationsWebSocket({
    onMessage: (data) => {
      if (data.type === 'notification') {
        setNotifications(prev => [...prev, data]);
      }
    },
  });

  return (
    <div>
      {notifications.map(notification => (
        <div key={notification.id}>
          {notification.message}
        </div>
      ))}
    </div>
  );
};
```

---

## 🔗 **Predefined Endpoints:**

```javascript
import { WS_ENDPOINTS } from '../utils/websocket';

// Available endpoints:
WS_ENDPOINTS.CHAT_ROOM(roomId)     // ws/chat/{roomId}/
WS_ENDPOINTS.PRIVATE_CHAT(userId)  // ws/private-chat/{userId}/
WS_ENDPOINTS.NOTIFICATIONS         // ws/notifications/
WS_ENDPOINTS.AI_ASSISTANT          // ws/ai-assistant/
WS_ENDPOINTS.COMMUNICATION         // ws/communication/
WS_ENDPOINTS.ADMIN_DASHBOARD       // ws/admin-dashboard/
```

---

## 🔐 **Authentication:**

WebSocket connections automatically include the JWT token from localStorage:
```javascript
// Token is automatically added as query parameter
// ws://localhost:8000/ws/chat/room1/?token=your_jwt_token
```

---

## 🔄 **Auto-Reconnection:**

The hooks support automatic reconnection:
```javascript
const { isConnected } = useWebSocket('ws/endpoint/', {
  reconnectAttempts: 3,      // Number of reconnection attempts
  reconnectInterval: 3000,   // Delay between attempts (ms)
});
```

---

## 📱 **Integration with Components:**

### **Chat Window Integration:**
```javascript
// In ChatWindow component
import { useChatWebSocket } from '../hooks/useWebSocket';

const ChatWindow = ({ room }) => {
  const {
    isConnected,
    sendChatMessage,
    lastMessage,
  } = useChatWebSocket(room.id, {
    onMessage: handleNewMessage,
  });

  // Rest of component logic...
};
```

### **Admin Dashboard Real-time Updates:**
```javascript
// In AdminDashboard component
import { useWebSocket } from '../hooks/useWebSocket';

const AdminDashboard = () => {
  const { lastMessage } = useWebSocket('ws/admin-dashboard/', {
    onMessage: (data) => {
      // Update dashboard stats in real-time
      if (data.type === 'stats_update') {
        updateStats(data.stats);
      }
    },
  });

  // Rest of component logic...
};
```

---

## 🧪 **Testing:**

Use the example component to test WebSocket connections:
```javascript
import WebSocketExample from '../examples/WebSocketExample';

// Add to your routes for testing
<Route path="/websocket-test" element={<WebSocketExample />} />
```

---

## 🔧 **Django Channels Backend Setup:**

Make sure your Django backend has:

1. **Channels installed and configured**
2. **WebSocket routing setup**
3. **Authentication middleware**
4. **Consumer classes for each endpoint**

Example Django consumer:
```python
# consumers.py
import json
from channels.generic.websocket import AsyncWebsocketConsumer

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_name = self.scope['url_route']['kwargs']['room_name']
        self.room_group_name = f'chat_{self.room_name}'
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        await self.accept()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        data = json.loads(text_data)
        # Handle incoming message
```

---

## 🎯 **Best Practices:**

1. **Always check connection status** before sending messages
2. **Handle reconnection gracefully** in your UI
3. **Clean up connections** when components unmount
4. **Use appropriate hooks** for different use cases
5. **Handle errors** and show user feedback
6. **Test WebSocket connections** thoroughly

---

## 🚀 **Ready to Use!**

The WebSocket setup is now complete and ready for integration with Django Channels backend!
