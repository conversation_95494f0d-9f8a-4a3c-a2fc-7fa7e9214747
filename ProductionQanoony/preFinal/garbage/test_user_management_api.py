#!/usr/bin/env python3
"""
Test script to check the user management APIs
"""
import os
import django
import sys

# Setup Django
sys.path.append('/home/<USER>/projects/PreQanoony/preFinal')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from authentication.models import CustomUser, StudentApplication, InstructorApplication
from students.models import StudentProfile
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

def get_admin_token():
    """Get admin token for API testing"""
    try:
        admin_user = CustomUser.objects.filter(is_staff=True).first()
        if not admin_user:
            print("No admin user found!")
            return None
        
        refresh = RefreshToken.for_user(admin_user)
        return str(refresh.access_token)
    except Exception as e:
        print(f"Error getting admin token: {e}")
        return None

def test_apis():
    """Test the user management APIs"""
    client = APIClient()
    
    # Get admin token
    token = get_admin_token()
    if not token:
        print("Could not get admin token")
        return
    
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
    
    print("=== Testing User Management APIs ===\n")
    
    # Test student applications
    print("1. Testing /api/auth/student-applications/")
    response = client.get('/api/auth/student-applications/')
    print(f"Status: {response.status_code}")
    print(f"Data type: {type(response.data)}")
    print(f"Data length: {len(response.data) if isinstance(response.data, list) else 'Not a list'}")
    if response.data:
        print(f"First item keys: {list(response.data[0].keys()) if isinstance(response.data, list) and response.data else 'No data'}")
    print(f"Response: {response.data[:2] if isinstance(response.data, list) else response.data}")
    print()
    
    # Test instructor applications
    print("2. Testing /api/auth/instructor-applications/")
    response = client.get('/api/auth/instructor-applications/')
    print(f"Status: {response.status_code}")
    print(f"Data type: {type(response.data)}")
    print(f"Data length: {len(response.data) if isinstance(response.data, list) else 'Not a list'}")
    if response.data:
        print(f"First item keys: {list(response.data[0].keys()) if isinstance(response.data, list) and response.data else 'No data'}")
    print(f"Response: {response.data[:2] if isinstance(response.data, list) else response.data}")
    print()
    
    # Test student profiles
    print("3. Testing /api/students/profiles/")
    response = client.get('/api/students/profiles/')
    print(f"Status: {response.status_code}")
    print(f"Data type: {type(response.data)}")
    print(f"Data length: {len(response.data) if isinstance(response.data, list) else 'Not a list'}")
    if response.data:
        print(f"First item keys: {list(response.data[0].keys()) if isinstance(response.data, list) and response.data else 'No data'}")
    print(f"Response: {response.data[:2] if isinstance(response.data, list) else response.data}")
    print()
    
    # Test instructors
    print("4. Testing /api/auth/instructors/")
    response = client.get('/api/auth/instructors/')
    print(f"Status: {response.status_code}")
    print(f"Data type: {type(response.data)}")
    print(f"Data length: {len(response.data) if isinstance(response.data, list) else 'Not a list'}")
    if response.data:
        print(f"First item keys: {list(response.data[0].keys()) if isinstance(response.data, list) and response.data else 'No data'}")
    print(f"Response: {response.data[:2] if isinstance(response.data, list) else response.data}")
    print()
    
    # Database counts
    print("=== Database Counts ===")
    print(f"Student Applications: {StudentApplication.objects.count()}")
    print(f"Instructor Applications: {InstructorApplication.objects.count()}")
    print(f"Student Profiles: {StudentProfile.objects.count()}")
    print(f"Instructors: {CustomUser.objects.filter(is_instructor=True, is_active=True).count()}")
    print(f"Total Users: {CustomUser.objects.count()}")

if __name__ == "__main__":
    test_apis()
