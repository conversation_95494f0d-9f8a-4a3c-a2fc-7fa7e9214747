# 🤖 Instructor Profile Development Prompts

## 📋 **نظرة عامة:**
مجموعة شاملة من الـ prompts لتطوير صفحة Instructor Profile من الصفر، مقسمة حسب المراحل والمكونات.

---

## 🔧 **Backend Development Prompts**

### **Prompt 1: إنشاء InstructorProfileView**
```
أريدك تنشئ InstructorProfileView في authentication/views.py بالمواصفات التالية:

1. **الوظيفة**: عرض وتعديل بيانات المدرس الحالي
2. **الـ endpoint**: /api/auth/instructors/profile/
3. **الـ methods**: GET, PATCH
4. **الـ permissions**: IsAuthenticated + التحقق من is_instructor=True

**المطلوب:**
- استخدم RetrieveUpdateAPIView
- تحقق من أن المستخدم مدرس في get_object()
- ارجع PermissionDenied إذا لم يكن مدرس
- استخدم InstructorProfileSerializer
- في update() ارجع response مع message نجاح

**البيانات المطلوب إرجاعها:**
- البيانات الأساسية من CustomUser
- التخصص والسيرة من InstructorApplication
- تواريخ الانضمام وآخر دخول

اكتب الكود كاملاً مع error handling.
```

### **Prompt 2: إنشاء InstructorProfileSerializer**
```
أريدك تنشئ InstructorProfileSerializer في authentication/serializers.py بالمواصفات التالية:

1. **الهدف**: serializer شامل لبيانات المدرس
2. **الـ model**: CustomUser
3. **الحقول المطلوبة**: id, email, first_name, last_name, phone_number, profile_image, specialty, bio, date_joined, last_login, is_active, email_verified

**المطلوب:**
- specialty و bio كـ SerializerMethodField من InstructorApplication
- read_only_fields للحقول غير القابلة للتعديل
- get_specialty() و get_bio() methods
- update() method مخصص لتحديث البيانات في الجدولين
- validation للحقول المطلوبة

**في update():**
- حدث البيانات الأساسية في CustomUser
- حدث specialty و bio في InstructorApplication
- أنشئ InstructorApplication إذا لم تكن موجودة

اكتب الكود كاملاً مع التعامل مع الأخطاء.
```

### **Prompt 3: إنشاء InstructorStatsView**
```
أريدك تنشئ InstructorStatsView في authentication/views.py لحساب إحصائيات المدرس:

**الإحصائيات المطلوبة:**
1. total_courses - من Course.objects.filter(instructor=user)
2. active_courses - الكورسات المنشورة
3. total_students - عدد الطلاب الفريدين في كورساته
4. total_sessions - من SessionBooking المكتملة
5. pending_sessions - الجلسات المعلقة
6. total_messages - رسائل المدرس
7. unread_messages - الرسائل غير المقروءة
8. this_month_sessions - جلسات هذا الشهر
9. average_rating - متوسط التقييم (إذا متوفر)

**المطلوب:**
- استخدم APIView
- تحقق من is_instructor
- اكتب helper methods لحساب كل إحصائية
- ارجع recent_activity (آخر 5 أنشطة)
- ارجع monthly_stats للـ charts

**recent_activity format:**
```json
{
  "type": "session_completed|new_message|course_published",
  "title": "العنوان",
  "description": "الوصف",
  "timestamp": "ISO datetime",
  "icon": "نوع الأيقونة",
  "color": "اللون"
}
```

اكتب الكود كاملاً مع جميع helper methods.
```

### **Prompt 4: إنشاء InstructorCoursesView**
```
أريدك تنشئ InstructorCoursesView في authentication/views.py لعرض كورسات المدرس:

**المطلوب:**
- استخدم ListAPIView
- filter الكورسات حسب instructor=request.user
- دعم query parameters: status, search, ordering
- استخدم InstructorCourseSerializer

**Query Parameters:**
- status: all|published|draft
- search: البحث في title_ar, title_en, description_ar
- ordering: created_at, -created_at, title_ar, -title_ar

**InstructorCourseSerializer يجب يحتوي على:**
- البيانات الأساسية للكورس
- subject_name من العلاقة
- semester_name من العلاقة
- students_count (عدد الطلاب المسجلين)
- lectures_count (عدد المحاضرات)
- quizzes_count (عدد الاختبارات)

اكتب الـ view والـ serializer كاملين مع الفلترة والبحث.
```

### **Prompt 5: إنشاء Image Upload Endpoint**
```
أريدك تنشئ endpoint لرفع الصورة الشخصية للمدرس:

**المواصفات:**
- الـ endpoint: /api/auth/instructors/profile/image/
- الـ method: POST
- الـ content_type: multipart/form-data
- الـ field: profile_image

**المطلوب:**
- استخدم APIView مع MultiPartParser
- تحقق من is_instructor
- validation للملف (نوع، حجم، أبعاد)
- أنواع مدعومة: JPG, PNG, GIF
- حد أقصى: 5MB
- أبعاد قصوى: 2000x2000
- احذف الصورة القديمة عند رفع جديدة
- ارجع URL الصورة الجديدة

**Validation:**
- تحقق من file type
- تحقق من file size
- تحقق من image dimensions باستخدام PIL
- رسائل خطأ واضحة بالعربية

اكتب الكود كاملاً مع جميع validations.
```

### **Prompt 6: إضافة URLs للـ APIs**
```
أريدك تضيف URLs الجديدة في authentication/urls.py:

**المطلوب إضافته:**
```python
path('instructors/profile/', InstructorProfileView.as_view(), name='instructor-profile'),
path('instructors/stats/', InstructorStatsView.as_view(), name='instructor-stats'),
path('instructors/courses/', InstructorCoursesView.as_view(), name='instructor-courses'),
path('instructors/profile/image/', InstructorProfileImageUploadView.as_view(), name='instructor-profile-image'),
```

**تأكد من:**
- إضافة imports للـ views الجديدة
- ترتيب URLs منطقي
- أسماء واضحة للـ name parameter

اكتب الكود كاملاً مع جميع imports.
```

### **Prompt 7: إنشاء Permission Classes**
```
أريدك تنشئ permission classes جديدة في authentication/permissions.py:

**1. IsInstructor:**
- يسمح للمدرسين المفعلين فقط
- تحقق من is_authenticated, is_instructor, is_active
- رسالة خطأ بالعربية

**2. IsInstructorOrAdmin:**
- يسمح للمدرسين أو الأدمن
- تحقق من is_instructor OR is_staff
- رسالة خطأ بالعربية

**3. IsOwnerOrAdmin:**
- للتحقق من ملكية الكائن
- has_object_permission method
- يسمح للمالك أو الأدمن

اكتب جميع الـ permission classes مع documentation.
```

---

## 🎨 **Frontend Development Prompts**

### **Prompt 8: إنشاء InstructorProfilePage**
```
أريدك تنشئ InstructorProfilePage.jsx في src/pages/ بالمواصفات التالية:

**الهيكل:**
- 4 تبويبات: الملف الشخصي، الإحصائيات، الكورسات، الطلاب
- Header مع صورة المدرس ومعلومات أساسية
- Navigation tabs
- Content area حسب التبويب النشط

**State Management:**
- activeTab (profile, stats, courses, students)
- instructor data
- stats data
- courses data
- loading states
- error handling
- editMode للملف الشخصي

**APIs Integration:**
- fetchInstructorData() - جلب البيانات الأساسية
- fetchStats() - جلب الإحصائيات
- fetchCourses() - جلب الكورسات
- handleProfileUpdate() - تحديث البيانات

**المطلوب:**
- استخدم Layout wrapper
- responsive design
- loading spinners
- error messages
- toast notifications للنجاح/الخطأ

اكتب الكود كاملاً مع جميع functions وstate management.
```

### **Prompt 9: إنشاء InstructorProfileHeader**
```
أريدك تنشئ InstructorProfileHeader.jsx في src/components/Instructor/:

**المطلوب:**
- عرض صورة المدرس مع زر تغيير الصورة
- عرض الاسم الكامل والتخصص
- عرض الإيميل ورقم الهاتف
- عرض تاريخ الانضمام
- زر "تعديل البيانات"
- عرض النبذة التعريفية إذا موجودة

**التصميم:**
- gradient background (yellow theme)
- responsive layout
- صورة دائرية مع border
- أيقونات للمعلومات
- hover effects

**Props:**
- instructor object
- onEditClick callback
- onImageClick callback (للمستقبل)

**Styling:**
- استخدم Tailwind CSS
- ألوان متسقة مع الموقع
- responsive breakpoints

اكتب الكود كاملاً مع جميع التفاصيل البصرية.
```

### **Prompt 10: إنشاء InstructorProfileForm**
```
أريدك تنشئ InstructorProfileForm.jsx في src/components/Instructor/:

**الحقول المطلوبة:**
- first_name (مطلوب)
- last_name (مطلوب)
- phone_number (اختياري)
- specialty (اختياري)
- bio (textarea، اختياري)

**المطلوب:**
- form validation
- loading state أثناء الحفظ
- error handling
- success feedback
- أزرار حفظ وإلغاء

**Validation:**
- first_name: مطلوب، أقل من 50 حرف
- last_name: مطلوب، أقل من 50 حرف
- phone_number: تنسيق صحيح إذا مدخل
- bio: أقل من 1000 حرف
- specialty: أقل من 100 حرف

**Props:**
- instructor object (البيانات الحالية)
- onSave callback
- onCancel callback

**Features:**
- auto-fill من البيانات الحالية
- real-time validation
- responsive design

اكتب الكود كاملاً مع validation وerror handling.
```

### **Prompt 11: إنشاء InstructorStatsCards**
```
أريدك تنشئ InstructorStatsCards.jsx في src/components/Instructor/:

**البطاقات المطلوبة:**
1. إجمالي الكورسات (total_courses)
2. إجمالي الطلاب (total_students)
3. الجلسات المكتملة (total_sessions)
4. متوسط التقييم (average_rating)

**كل بطاقة تحتوي على:**
- أيقونة ملونة
- العدد/القيمة بخط كبير
- العنوان
- لون مميز لكل نوع

**التصميم:**
- grid responsive (1 col mobile, 2 col tablet, 4 col desktop)
- shadow وhover effects
- ألوان مختلفة لكل بطاقة
- animations للأرقام (CountUp)

**إضافات:**
- Recent Activity section
- Monthly trends (إذا متوفرة)
- Quick actions buttons

**Props:**
- stats object من API

اكتب الكود كاملاً مع animations وresponsive design.
```

### **Prompt 12: إنشاء InstructorCoursesList**
```
أريدك تنشئ InstructorCoursesList.jsx في src/components/Instructor/:

**المطلوب:**
- عرض قائمة الكورسات في جدول أو cards
- search functionality
- filter بحالة النشر (الكل، منشور، مسودة)
- sorting (تاريخ الإنشاء، العنوان)
- pagination إذا لزم

**معلومات كل كورس:**
- العنوان (عربي وإنجليزي)
- المادة
- الفصل الدراسي
- عدد الطلاب
- عدد المحاضرات
- حالة النشر
- تاريخ الإنشاء

**Features:**
- loading state
- empty state
- error handling
- responsive design
- action buttons (عرض، تعديل)

**Props:**
- courses array
- loading boolean
- onRefresh callback

اكتب الكود كاملاً مع search وfiltering.
```

### **Prompt 13: إضافة Routing للصفحة**
```
أريدك تضيف routing للصفحة الجديدة في App.js:

**المطلوب:**
- إضافة import للـ InstructorProfilePage
- إضافة route جديد: /instructor-profile
- استخدام ProtectedRoute wrapper
- إضافة AnimatedPage wrapper

**الكود المطلوب:**
```jsx
<Route 
  path="instructor-profile" 
  element={
    <AnimatedPage>
      <ProtectedRoute>
        <InstructorProfilePage />
      </ProtectedRoute>
    </AnimatedPage>
  } 
/>
```

**تأكد من:**
- الـ route في المكان الصحيح
- imports صحيحة
- consistent مع باقي routes

اكتب التعديلات المطلوبة على App.js.
```

### **Prompt 14: تحديث Navigation**
```
أريدك تضيف رابط لصفحة Instructor Profile في الـ navigation:

**المطلوب:**
- إضافة رابط في Header للمدرسين
- إضافة رابط في InstructorDashboard
- شرطي display (للمدرسين فقط)

**في Header:**
- إضافة "الملف الشخصي" للمدرسين
- أيقونة مناسبة
- styling متسق

**في InstructorDashboard:**
- إضافة tab أو button للـ profile
- أو رابط في الـ header

**التحقق من الصلاحيات:**
- عرض الرابط للمدرسين فقط
- استخدام useAuth hook

اكتب التعديلات المطلوبة على الـ navigation components.
```

---

## 🧪 **Testing Prompts**

### **Prompt 15: اختبار Backend APIs**
```
أريدك تكتب اختبارات شاملة للـ APIs الجديدة:

**ملف الاختبار:** tests/test_instructor_profile.py

**الاختبارات المطلوبة:**
1. test_instructor_profile_get_success
2. test_instructor_profile_get_non_instructor_fails
3. test_instructor_profile_update_success
4. test_instructor_profile_update_validation_errors
5. test_instructor_stats_success
6. test_instructor_courses_list
7. test_image_upload_success
8. test_image_upload_invalid_file

**كل اختبار يجب:**
- إنشاء test data مناسبة
- authentication setup
- assertions شاملة
- cleanup بعد الاختبار

**Test Data:**
- إنشاء instructor user
- إنشاء courses مرتبطة
- إنشاء sessions وmessages
- إنشاء instructor application

اكتب جميع الاختبارات مع setup وteardown.
```

### **Prompt 16: اختبار Frontend Components**
```
أريدك تكتب اختبارات للـ React components:

**الاختبارات المطلوبة:**
1. InstructorProfilePage rendering
2. InstructorProfileHeader display
3. InstructorProfileForm validation
4. InstructorStatsCards data display
5. Navigation integration

**كل اختبار يجب:**
- mock APIs
- mock user context
- test user interactions
- test error states
- test loading states

**Testing Tools:**
- React Testing Library
- Jest
- Mock Service Worker (للـ APIs)

**Test Cases:**
- successful data loading
- error handling
- form submission
- navigation between tabs
- responsive behavior

اكتب اختبارات شاملة لجميع المكونات.
```

---

---

## 🎨 **UI/UX Enhancement Prompts**

### **Prompt 17: إنشاء Loading States**
```
أريدك تنشئ loading states متقدمة لجميع مكونات Instructor Profile:

**المطلوب:**
1. **ProfileSkeleton.jsx** - skeleton للملف الشخصي
2. **StatsSkeleton.jsx** - skeleton للإحصائيات
3. **CoursesSkeleton.jsx** - skeleton لقائمة الكورسات
4. **LoadingSpinner.jsx** - spinner مخصص

**ProfileSkeleton:**
- skeleton للصورة الشخصية
- skeleton للاسم والتخصص
- skeleton للمعلومات الأساسية
- animated shimmer effect

**StatsSkeleton:**
- 4 بطاقات skeleton
- skeleton للأرقام والعناوين
- placeholder للأيقونات

**Features:**
- smooth animations
- realistic proportions
- consistent styling
- responsive design

اكتب جميع الـ skeleton components مع animations.
```

### **Prompt 18: إنشاء Error Handling Components**
```
أريدك تنشئ error handling components متقدمة:

**المطلوب:**
1. **ErrorBoundary.jsx** - React error boundary
2. **ErrorMessage.jsx** - عرض رسائل الخطأ
3. **RetryButton.jsx** - زر إعادة المحاولة
4. **NetworkError.jsx** - خطأ الشبكة

**ErrorBoundary:**
- catch JavaScript errors
- fallback UI جميل
- error reporting (optional)
- reset functionality

**ErrorMessage:**
- أنواع مختلفة من الأخطاء
- أيقونات مناسبة
- رسائل واضحة بالعربية
- action buttons

**Features:**
- user-friendly messages
- consistent styling
- accessibility support
- responsive design

اكتب جميع error handling components.
```

### **Prompt 19: إنشاء Image Upload Component**
```
أريدك تنشئ ImageUpload.jsx component متقدم:

**المطلوب:**
- drag & drop functionality
- image preview قبل الرفع
- progress bar أثناء الرفع
- crop functionality (اختياري)
- validation visual feedback

**Features:**
- دعم multiple file types
- file size validation
- image dimensions validation
- error messages واضحة
- success feedback

**UI Elements:**
- drop zone مع border dashed
- preview area
- upload button
- progress indicator
- error/success messages

**Props:**
- onUpload callback
- maxSize limit
- acceptedTypes array
- currentImage URL

اكتب الكود كاملاً مع drag & drop وvalidation.
```

### **Prompt 20: إنشاء Charts Components**
```
أريدك تنشئ charts components للإحصائيات:

**المطلوب:**
1. **StatsChart.jsx** - line chart للإحصائيات الشهرية
2. **CoursesChart.jsx** - bar chart للكورسات
3. **StudentsChart.jsx** - pie chart لتوزيع الطلاب
4. **ActivityChart.jsx** - area chart للنشاط

**استخدم:**
- Recharts library
- responsive charts
- custom colors (yellow theme)
- tooltips بالعربية
- legends واضحة

**StatsChart:**
- خط للجلسات الشهرية
- خط للطلاب الجدد
- grid lines
- hover effects

**Features:**
- loading states
- empty data handling
- responsive design
- accessibility

اكتب جميع chart components مع Recharts.
```

---

## 🔧 **Advanced Features Prompts**

### **Prompt 21: إنشاء Search & Filter System**
```
أريدك تنشئ نظام بحث وفلترة متقدم:

**المطلوب:**
1. **SearchBar.jsx** - شريط بحث مع autocomplete
2. **FilterPanel.jsx** - panel للفلاتر
3. **SortDropdown.jsx** - dropdown للترتيب
4. **useSearch.js** - custom hook للبحث

**SearchBar:**
- real-time search
- search suggestions
- clear button
- keyboard shortcuts (Ctrl+K)

**FilterPanel:**
- multiple filter types
- date range picker
- status filters
- category filters
- reset filters button

**useSearch Hook:**
- debounced search
- search history
- filter state management
- URL sync (optional)

اكتب نظام البحث والفلترة كاملاً.
```

### **Prompt 22: إنشاء Notification System**
```
أريدك تنشئ نظام إشعارات متقدم:

**المطلوب:**
1. **NotificationCenter.jsx** - مركز الإشعارات
2. **NotificationItem.jsx** - عنصر إشعار واحد
3. **useNotifications.js** - hook لإدارة الإشعارات
4. **NotificationTypes.js** - أنواع الإشعارات

**NotificationCenter:**
- dropdown مع قائمة الإشعارات
- mark as read functionality
- delete notifications
- real-time updates

**NotificationItem:**
- أيقونة حسب النوع
- timestamp relative
- read/unread status
- action buttons

**Types:**
- new_message
- session_booked
- course_published
- student_enrolled

اكتب نظام الإشعارات كاملاً مع real-time updates.
```

### **Prompt 23: إنشاء Export Functionality**
```
أريدك تنشئ وظائف التصدير:

**المطلوب:**
1. **ExportButton.jsx** - زر التصدير مع options
2. **exportUtils.js** - utility functions للتصدير
3. **PDFExport.js** - تصدير PDF
4. **CSVExport.js** - تصدير CSV

**ExportButton:**
- dropdown مع خيارات التصدير
- PDF, CSV, Excel options
- loading state أثناء التصدير
- success/error feedback

**exportUtils:**
- formatDataForExport()
- generateFileName()
- downloadFile()
- validateData()

**PDFExport:**
- استخدم jsPDF أو react-pdf
- تصدير إحصائيات المدرس
- تصدير قائمة الكورسات
- تصدير قائمة الطلاب

اكتب جميع export functionalities.
```

### **Prompt 24: إنشاء Settings Panel**
```
أريدك تنشئ لوحة إعدادات للمدرس:

**المطلوب:**
1. **InstructorSettings.jsx** - الصفحة الرئيسية
2. **ProfileSettings.jsx** - إعدادات الملف الشخصي
3. **NotificationSettings.jsx** - إعدادات الإشعارات
4. **PrivacySettings.jsx** - إعدادات الخصوصية

**ProfileSettings:**
- تغيير كلمة المرور
- إعدادات الظهور
- معلومات الاتصال
- تفضيلات اللغة

**NotificationSettings:**
- إشعارات الإيميل
- إشعارات الموقع
- إشعارات الرسائل
- تكرار الإشعارات

**PrivacySettings:**
- إظهار/إخفاء المعلومات
- من يمكنه التواصل
- إعدادات البحث

اكتب لوحة الإعدادات كاملة مع جميع الخيارات.
```

---

## 📱 **Mobile & Responsive Prompts**

### **Prompt 25: تحسين Mobile Experience**
```
أريدك تحسن تجربة الموبايل لصفحة Instructor Profile:

**المطلوب:**
1. **MobileHeader.jsx** - header مخصص للموبايل
2. **MobileTabs.jsx** - tabs responsive للموبايل
3. **MobileStats.jsx** - إحصائيات محسنة للموبايل
4. **SwipeGestures.js** - دعم swipe gestures

**MobileHeader:**
- compact design
- hamburger menu
- profile image أصغر
- essential info only

**MobileTabs:**
- horizontal scroll tabs
- swipe between tabs
- active tab indicator
- smooth animations

**Responsive Breakpoints:**
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

اكتب التحسينات المطلوبة للموبايل.
```

### **Prompt 26: إنشاء Progressive Web App Features**
```
أريدك تضيف PWA features لصفحة المدرس:

**المطلوب:**
1. **ServiceWorker.js** - caching للصفحات والبيانات
2. **OfflineIndicator.jsx** - مؤشر حالة الاتصال
3. **InstallPrompt.jsx** - prompt لتثبيت التطبيق
4. **PushNotifications.js** - إشعارات push

**ServiceWorker:**
- cache الصفحات المهمة
- cache الصور والـ assets
- offline fallback pages
- background sync

**OfflineIndicator:**
- عرض حالة الاتصال
- retry button عند انقطاع الاتصال
- queue actions للتنفيذ عند العودة

**Features:**
- install app prompt
- offline functionality
- background sync
- push notifications

اكتب PWA features كاملة.
```

---

## 🔒 **Security & Performance Prompts**

### **Prompt 27: تحسين الأمان**
```
أريدك تحسن الأمان في صفحة Instructor Profile:

**المطلوب:**
1. **Input Validation** - validation شامل للمدخلات
2. **XSS Protection** - حماية من XSS attacks
3. **CSRF Protection** - حماية من CSRF
4. **File Upload Security** - أمان رفع الملفات

**Input Validation:**
- sanitize جميع المدخلات
- validate file types بدقة
- check file signatures
- limit file sizes

**XSS Protection:**
- escape HTML في العرض
- validate URLs
- sanitize rich text
- CSP headers

**File Upload:**
- virus scanning (إذا ممكن)
- file type validation
- rename uploaded files
- secure storage path

اكتب تحسينات الأمان كاملة.
```

### **Prompt 28: تحسين الأداء**
```
أريدك تحسن أداء صفحة Instructor Profile:

**المطلوب:**
1. **Lazy Loading** - تحميل المكونات عند الحاجة
2. **Memoization** - React.memo للمكونات
3. **Virtual Scrolling** - للقوائم الطويلة
4. **Image Optimization** - تحسين الصور

**Lazy Loading:**
- lazy load tabs content
- lazy load images
- code splitting للمكونات
- dynamic imports

**Memoization:**
- React.memo للمكونات الثقيلة
- useMemo للحسابات المعقدة
- useCallback للـ functions
- optimize re-renders

**Performance Monitoring:**
- measure component render times
- track API response times
- monitor memory usage
- lighthouse scores

اكتب تحسينات الأداء كاملة.
```

---

## 🧪 **Testing & Quality Assurance Prompts**

### **Prompt 29: اختبارات E2E**
```
أريدك تكتب اختبارات End-to-End لصفحة Instructor Profile:

**المطلوب:**
- استخدم Cypress أو Playwright
- اختبار User Journey كامل
- اختبار جميع الوظائف
- اختبار Responsive design

**Test Scenarios:**
1. تسجيل دخول مدرس وعرض الصفحة
2. تعديل البيانات الشخصية
3. رفع صورة شخصية
4. التنقل بين التبويبات
5. عرض الإحصائيات
6. البحث في الكورسات
7. تسجيل خروج

**كل اختبار يجب:**
- setup test data
- login as instructor
- perform actions
- verify results
- cleanup

اكتب جميع E2E tests.
```

### **Prompt 30: Performance Testing**
```
أريدك تكتب اختبارات الأداء:

**المطلوب:**
1. **Load Testing** - اختبار الحمولة
2. **Stress Testing** - اختبار الضغط
3. **Performance Benchmarks** - معايير الأداء
4. **Memory Leak Detection** - كشف تسريب الذاكرة

**Load Testing:**
- محاكاة 100+ مستخدم متزامن
- اختبار APIs تحت الحمولة
- قياس response times
- monitor server resources

**Performance Benchmarks:**
- page load time < 2 seconds
- API response time < 500ms
- image load time < 1 second
- smooth 60fps animations

**Tools:**
- Artillery.js للـ load testing
- Lighthouse للـ performance
- Chrome DevTools للـ profiling

اكتب اختبارات الأداء كاملة.
```

---

## 📚 **Documentation Prompts**

### **Prompt 31: كتابة Documentation**
```
أريدك تكتب documentation شامل لصفحة Instructor Profile:

**المطلوب:**
1. **API Documentation** - توثيق جميع APIs
2. **Component Documentation** - توثيق React components
3. **User Guide** - دليل المستخدم
4. **Developer Guide** - دليل المطور

**API Documentation:**
- endpoint descriptions
- request/response examples
- error codes
- authentication requirements

**Component Documentation:**
- props descriptions
- usage examples
- styling guidelines
- accessibility notes

**User Guide:**
- step-by-step instructions
- screenshots
- troubleshooting
- FAQ section

اكتب documentation شامل بالعربية والإنجليزية.
```

### **Prompt 32: إنشاء Deployment Guide**
```
أريدك تكتب دليل deployment لصفحة Instructor Profile:

**المطلوب:**
1. **Environment Setup** - إعداد البيئة
2. **Database Migrations** - migrations للجداول الجديدة
3. **Static Files** - إعداد الملفات الثابتة
4. **Production Checklist** - checklist للإنتاج

**Environment Setup:**
- Python dependencies
- Node.js dependencies
- environment variables
- database configuration

**Database Migrations:**
- backup existing data
- run new migrations
- verify data integrity
- rollback plan

**Production Checklist:**
- security settings
- performance optimization
- monitoring setup
- backup procedures

اكتب دليل deployment كامل.
```

---

**📅 تاريخ الإنشاء:** 2025-07-13
**👨‍💻 المطور:** Augment Agent
**🎯 الحالة:** 32 Prompt شامل جاهز للاستخدام
**📊 التغطية:** Backend + Frontend + Testing + Security + Performance + Documentation
**⚡ الاستخدام:** نسخ كل prompt واستخدامه مع AI assistant للتطوير
