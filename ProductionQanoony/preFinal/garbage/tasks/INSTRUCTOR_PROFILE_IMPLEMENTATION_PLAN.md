# 🚀 خطة تنفيذ صفحة Instructor Profile

## 📋 **ملخص المهمة:**
تطوير صفحة Instructor Profile شاملة تتضمن عرض وتعديل البيانات الشخصية، الإحصائيات، الكورسات، والطلاب.

---

## 🎯 **الأهداف الرئيسية:**

### **✅ الهدف الأساسي:**
إنشاء صفحة profile مخصصة للمدرسين تتيح لهم:
1. عرض وتعديل بياناتهم الشخصية
2. مشاهدة إحصائياتهم التدريسية
3. إدارة كورساتهم
4. متابعة طلابهم

### **✅ الأهداف الثانوية:**
1. تحسين تجربة المدرسين في النظام
2. توفير dashboard مصغر للمدرسين
3. تسهيل إدارة البيانات الشخصية
4. عرض الإحصائيات بشكل جذاب

---

## 📊 **تحليل الوضع الحالي:**

### **✅ المتوفر:**
- **InstructorDashboard.jsx** - لوحة تحكم أساسية (جلسات + رسائل)
- **ProfileView API** - عام لجميع المستخدمين
- **InstructorDetailListView** - للأدمن فقط
- **CustomUser Model** - يحتوي على البيانات الأساسية
- **InstructorApplication Model** - يحتوي على التخصص والسيرة

### **❌ المفقود:**
- صفحة profile مخصصة للمدرسين
- APIs للإحصائيات والكورسات
- واجهة تعديل البيانات المتخصصة
- عرض الطلاب المرتبطين

---

## 🛠️ **خطة التنفيذ التفصيلية:**

### **المرحلة 1: Backend Development (يوم 1-2)**

#### **الخطوة 1.1: إنشاء APIs جديدة**
```python
# في authentication/views.py

class InstructorProfileView(generics.RetrieveUpdateAPIView):
    """عرض وتعديل profile المدرس الحالي"""
    serializer_class = InstructorProfileSerializer
    permission_classes = [IsAuthenticated]
    
    def get_object(self):
        user = self.request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً للوصول إلى هذه الصفحة')
        return user

class InstructorStatsView(views.APIView):
    """إحصائيات المدرس الشاملة"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        user = request.user
        if not user.is_instructor:
            raise PermissionDenied('يجب أن تكون مدرساً')
        
        # حساب الإحصائيات من جميع الجداول المرتبطة
        stats = self.calculate_instructor_stats(user)
        return Response(stats)

class InstructorCoursesView(generics.ListAPIView):
    """الكورسات التي يدرسها المدرس"""
    serializer_class = InstructorCourseSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        if not user.is_instructor:
            return Course.objects.none()
        return Course.objects.filter(instructor=user)
```

#### **الخطوة 1.2: إنشاء Serializers**
```python
# في authentication/serializers.py

class InstructorProfileSerializer(serializers.ModelSerializer):
    """Serializer شامل لـ profile المدرس"""
    specialty = serializers.SerializerMethodField()
    bio = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomUser
        fields = [
            'id', 'email', 'first_name', 'last_name', 'phone_number',
            'profile_image', 'specialty', 'bio', 'date_joined', 'last_login',
            'is_active', 'email_verified'
        ]
        read_only_fields = ['email', 'date_joined', 'last_login', 'is_active', 'email_verified']
    
    def get_specialty(self, obj):
        try:
            return obj.instructor_application.specialty
        except:
            return ''
    
    def get_bio(self, obj):
        try:
            return obj.instructor_application.bio
        except:
            return ''
    
    def update(self, instance, validated_data):
        # تحديث البيانات في CustomUser
        for attr, value in validated_data.items():
            if attr not in ['specialty', 'bio']:
                setattr(instance, attr, value)
        instance.save()
        
        # تحديث التخصص والسيرة في InstructorApplication
        specialty = self.context['request'].data.get('specialty')
        bio = self.context['request'].data.get('bio')
        
        if specialty is not None or bio is not None:
            app, created = InstructorApplication.objects.get_or_create(user=instance)
            if specialty is not None:
                app.specialty = specialty
            if bio is not None:
                app.bio = bio
            app.save()
        
        return instance

class InstructorCourseSerializer(serializers.ModelSerializer):
    """Serializer للكورسات مع معلومات إضافية"""
    students_count = serializers.SerializerMethodField()
    lectures_count = serializers.SerializerMethodField()
    subject_name = serializers.CharField(source='subject.title_ar', read_only=True)
    
    class Meta:
        model = Course
        fields = [
            'id', 'title_ar', 'title_en', 'description_ar', 'subject_name',
            'students_count', 'lectures_count', 'is_published', 'created_at'
        ]
    
    def get_students_count(self, obj):
        # حساب عدد الطلاب المسجلين في الكورس
        return 0  # سيتم تطويرها لاحقاً
    
    def get_lectures_count(self, obj):
        return obj.lectures.count()
```

#### **الخطوة 1.3: إضافة URLs**
```python
# في authentication/urls.py
urlpatterns = [
    # ... existing URLs
    path('instructors/profile/', InstructorProfileView.as_view(), name='instructor-profile'),
    path('instructors/stats/', InstructorStatsView.as_view(), name='instructor-stats'),
    path('instructors/courses/', InstructorCoursesView.as_view(), name='instructor-courses'),
]
```

#### **الخطوة 1.4: إنشاء Permission Class**
```python
# في authentication/permissions.py
class IsInstructor(permissions.BasePermission):
    """يسمح بالوصول للمدرسين فقط"""
    message = 'يجب أن تكون مدرساً للوصول إلى هذا المورد'
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_instructor
        )
```

### **المرحلة 2: Frontend Development (يوم 2-3)**

#### **الخطوة 2.1: إنشاء الصفحة الرئيسية**
```jsx
// src/pages/InstructorProfilePage.jsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import Layout from '../components/layout/Layout';
import InstructorProfileHeader from '../components/Instructor/InstructorProfileHeader';
import InstructorProfileForm from '../components/Instructor/InstructorProfileForm';
import InstructorStatsCards from '../components/Instructor/InstructorStatsCards';
import InstructorCoursesList from '../components/Instructor/InstructorCoursesList';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import ErrorMessage from '../components/UI/ErrorMessage';
import { toast } from '../components/UI/Toast';

const InstructorProfilePage = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [instructor, setInstructor] = useState(null);
  const [stats, setStats] = useState(null);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);

  // Tabs configuration
  const tabs = [
    { key: 'profile', label: 'الملف الشخصي', icon: 'FaUser' },
    { key: 'stats', label: 'الإحصائيات', icon: 'FaChartBar' },
    { key: 'courses', label: 'الكورسات', icon: 'FaBook' },
    { key: 'students', label: 'الطلاب', icon: 'FaUsers' }
  ];

  useEffect(() => {
    fetchInstructorData();
  }, []);

  const fetchInstructorData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };
      
      // جلب بيانات المدرس
      const profileResponse = await fetch('/api/auth/instructors/profile/', { headers });
      if (!profileResponse.ok) throw new Error('فشل في جلب بيانات المدرس');
      const profileData = await profileResponse.json();
      setInstructor(profileData);
      
      // جلب الإحصائيات
      const statsResponse = await fetch('/api/auth/instructors/stats/', { headers });
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }
      
      // جلب الكورسات
      const coursesResponse = await fetch('/api/auth/instructors/courses/', { headers });
      if (coursesResponse.ok) {
        const coursesData = await coursesResponse.json();
        setCourses(Array.isArray(coursesData) ? coursesData : coursesData.results || []);
      }
      
    } catch (err) {
      console.error('Error fetching instructor data:', err);
      setError('حدث خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = async (formData) => {
    try {
      const token = localStorage.getItem('access');
      const response = await fetch('/api/auth/instructors/profile/', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) throw new Error('فشل في تحديث البيانات');
      
      const updatedData = await response.json();
      setInstructor(updatedData.data || updatedData);
      setEditMode(false);
      toast.success('تم تحديث البيانات بنجاح');
      
    } catch (err) {
      console.error('Error updating profile:', err);
      toast.error('حدث خطأ في تحديث البيانات');
    }
  };

  if (loading) return <Layout><LoadingSpinner /></Layout>;
  if (error) return <Layout><ErrorMessage message={error} /></Layout>;
  if (!instructor) return <Layout><ErrorMessage message="لم يتم العثور على بيانات المدرس" /></Layout>;

  return (
    <Layout>
      <div className="max-w-6xl mx-auto p-6 space-y-6">
        {/* Profile Header */}
        <InstructorProfileHeader 
          instructor={instructor}
          onEditClick={() => setEditMode(true)}
        />
        
        {/* Tabs Navigation */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8">
            {tabs.map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.key
                    ? 'border-yellow-500 text-yellow-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
        
        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'profile' && (
            editMode ? (
              <InstructorProfileForm
                instructor={instructor}
                onSave={handleProfileUpdate}
                onCancel={() => setEditMode(false)}
              />
            ) : (
              <InstructorProfileView instructor={instructor} />
            )
          )}
          
          {activeTab === 'stats' && stats && (
            <InstructorStatsCards stats={stats} />
          )}
          
          {activeTab === 'courses' && (
            <InstructorCoursesList courses={courses} />
          )}
          
          {activeTab === 'students' && (
            <div className="text-center py-12 text-gray-500">
              قريباً - إدارة الطلاب
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default InstructorProfilePage;
```

#### **الخطوة 2.2: إنشاء المكونات الفرعية**

**أ. InstructorProfileHeader.jsx**
```jsx
// src/components/Instructor/InstructorProfileHeader.jsx
import React from 'react';
import { FaCamera, FaEdit, FaEnvelope, FaPhone, FaCalendar } from 'react-icons/fa';

const InstructorProfileHeader = ({ instructor, onEditClick }) => {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-lg p-6 text-white shadow-lg">
      <div className="flex flex-col md:flex-row items-center gap-6">
        {/* Profile Image */}
        <div className="relative">
          <img 
            src={instructor.profile_image || '/default-instructor-avatar.png'}
            alt={`${instructor.first_name} ${instructor.last_name}`}
            className="w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-white object-cover"
          />
          <button className="absolute bottom-0 right-0 bg-white text-yellow-500 rounded-full p-2 shadow-lg hover:bg-yellow-50 transition-colors">
            <FaCamera size={16} />
          </button>
        </div>
        
        {/* Profile Info */}
        <div className="flex-1 text-center md:text-right">
          <h1 className="text-3xl md:text-4xl font-bold mb-2">
            {instructor.first_name} {instructor.last_name}
          </h1>
          <p className="text-yellow-100 text-lg md:text-xl mb-3">
            {instructor.specialty || 'مدرس قانون'}
          </p>
          
          <div className="flex flex-col md:flex-row gap-4 text-yellow-200">
            <div className="flex items-center gap-2">
              <FaEnvelope size={16} />
              <span>{instructor.email}</span>
            </div>
            {instructor.phone_number && (
              <div className="flex items-center gap-2">
                <FaPhone size={16} />
                <span>{instructor.phone_number}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <FaCalendar size={16} />
              <span>انضم في {formatDate(instructor.date_joined)}</span>
            </div>
          </div>
        </div>
        
        {/* Edit Button */}
        <button 
          onClick={onEditClick}
          className="bg-white text-yellow-500 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-50 transition-colors flex items-center gap-2 shadow-lg"
        >
          <FaEdit size={16} />
          تعديل البيانات
        </button>
      </div>
      
      {/* Bio */}
      {instructor.bio && (
        <div className="mt-6 pt-6 border-t border-yellow-300">
          <p className="text-yellow-100 leading-relaxed">
            {instructor.bio}
          </p>
        </div>
      )}
    </div>
  );
};

export default InstructorProfileHeader;
```

---

**📅 تاريخ الإنشاء:** 2025-07-13  
**👨‍💻 المطور:** Augment Agent  
**🎯 الحالة:** خطة تفصيلية جاهزة للتنفيذ  
**⏱️ الوقت المتوقع:** 4 أيام عمل
