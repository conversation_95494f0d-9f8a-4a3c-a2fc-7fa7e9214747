# 🔗 Instructor Profile - Endpoints والـ Permissions

## 📋 **ملخص:**
تفاصيل شاملة لجميع الـ APIs والـ permissions المطلوبة لصفحة Instructor Profile.

---

## 🛠️ **الـ Endpoints المطلوبة:**

### **1. Instructor Profile Management**

#### **GET /api/auth/instructors/profile/**
```json
{
  "description": "جلب بيانات المدرس الحالي",
  "method": "GET",
  "permissions": ["IsAuthenticated", "IsInstructor"],
  "response": {
    "id": "uuid",
    "email": "<EMAIL>",
    "first_name": "أحمد",
    "last_name": "محمد",
    "phone_number": "+************",
    "profile_image": "http://localhost:8000/media/profile_images/instructor.jpg",
    "specialty": "القانون المدني",
    "bio": "مدرس قانون مدني بخبرة 10 سنوات في التدريس الجامعي...",
    "date_joined": "2024-01-15T10:30:00Z",
    "last_login": "2025-07-13T14:20:00Z",
    "is_active": true,
    "email_verified": true
  },
  "status_codes": {
    "200": "نجح الطلب",
    "401": "غير مصرح - يجب تسجيل الدخول",
    "403": "ممنوع - يجب أن تكون مدرساً"
  }
}
```

#### **PATCH /api/auth/instructors/profile/**
```json
{
  "description": "تحديث بيانات المدرس الحالي",
  "method": "PATCH",
  "permissions": ["IsAuthenticated", "IsInstructor"],
  "request_body": {
    "first_name": "أحمد",
    "last_name": "محمد الجديد",
    "phone_number": "+************",
    "bio": "نبذة محدثة عن المدرس...",
    "specialty": "القانون المدني والتجاري"
  },
  "response": {
    "message": "تم تحديث البيانات بنجاح",
    "data": {
      "id": "uuid",
      "first_name": "أحمد",
      "last_name": "محمد الجديد",
      "phone_number": "+************",
      "bio": "نبذة محدثة عن المدرس...",
      "specialty": "القانون المدني والتجاري"
    }
  },
  "validation": {
    "first_name": "مطلوب، أقل من 50 حرف",
    "last_name": "مطلوب، أقل من 50 حرف",
    "phone_number": "اختياري، تنسيق صحيح",
    "bio": "اختياري، أقل من 1000 حرف",
    "specialty": "اختياري، أقل من 100 حرف"
  },
  "status_codes": {
    "200": "تم التحديث بنجاح",
    "400": "بيانات غير صحيحة",
    "401": "غير مصرح",
    "403": "ممنوع"
  }
}
```

### **2. Instructor Statistics**

#### **GET /api/auth/instructors/stats/**
```json
{
  "description": "جلب إحصائيات المدرس الشاملة",
  "method": "GET",
  "permissions": ["IsAuthenticated", "IsInstructor"],
  "response": {
    "total_courses": 5,
    "active_courses": 3,
    "total_students": 120,
    "total_lectures": 45,
    "total_sessions": 32,
    "completed_sessions": 28,
    "pending_sessions": 4,
    "total_messages": 230,
    "unread_messages": 12,
    "this_month_sessions": 12,
    "this_month_students": 25,
    "average_rating": 4.7,
    "total_ratings": 89,
    "recent_activity": [
      {
        "id": 1,
        "type": "session_completed",
        "title": "جلسة مكتملة",
        "description": "جلسة قانون مدني مع الطالب أحمد محمد",
        "timestamp": "2025-07-13T10:00:00Z",
        "icon": "session",
        "color": "green"
      },
      {
        "id": 2,
        "type": "new_message",
        "title": "رسالة جديدة",
        "description": "رسالة من الطالب فاطمة أحمد",
        "timestamp": "2025-07-13T09:30:00Z",
        "icon": "message",
        "color": "blue"
      }
    ],
    "monthly_stats": {
      "sessions_trend": [8, 12, 15, 18, 12, 10],
      "students_trend": [20, 25, 30, 35, 25, 22],
      "months": ["فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو"]
    }
  },
  "status_codes": {
    "200": "نجح الطلب",
    "401": "غير مصرح",
    "403": "ممنوع"
  }
}
```

### **3. Instructor Courses**

#### **GET /api/auth/instructors/courses/**
```json
{
  "description": "جلب قائمة الكورسات التي يدرسها المدرس",
  "method": "GET",
  "permissions": ["IsAuthenticated", "IsInstructor"],
  "query_params": {
    "status": "all|published|draft",
    "search": "نص البحث",
    "ordering": "created_at|-created_at|title_ar"
  },
  "response": {
    "count": 5,
    "next": null,
    "previous": null,
    "results": [
      {
        "id": 1,
        "title_ar": "القانون المدني الأساسي",
        "title_en": "Basic Civil Law",
        "description_ar": "مقدمة شاملة في القانون المدني...",
        "subject": {
          "id": 1,
          "title_ar": "القانون المدني",
          "title_en": "Civil Law"
        },
        "semester": {
          "id": 1,
          "title_ar": "الفصل الأول",
          "academic_year": {
            "id": 1,
            "year_number": 1,
            "title_ar": "السنة الأولى"
          }
        },
        "students_count": 45,
        "lectures_count": 12,
        "quizzes_count": 5,
        "is_published": true,
        "created_at": "2024-02-01T00:00:00Z",
        "updated_at": "2024-06-15T10:30:00Z"
      }
    ]
  },
  "status_codes": {
    "200": "نجح الطلب",
    "401": "غير مصرح",
    "403": "ممنوع"
  }
}
```

### **4. Instructor Students**

#### **GET /api/auth/instructors/students/**
```json
{
  "description": "جلب قائمة الطلاب المرتبطين بالمدرس",
  "method": "GET",
  "permissions": ["IsAuthenticated", "IsInstructor"],
  "query_params": {
    "course_id": "معرف الكورس للفلترة",
    "search": "نص البحث",
    "status": "active|inactive"
  },
  "response": {
    "count": 120,
    "next": "http://localhost:8000/api/auth/instructors/students/?page=2",
    "previous": null,
    "results": [
      {
        "id": "uuid",
        "full_name": "أحمد محمد علي",
        "email": "<EMAIL>",
        "student_id": "S2024001",
        "university": {
          "id": 1,
          "name_ar": "جامعة الملك سعود"
        },
        "academic_year": {
          "id": 1,
          "title_ar": "السنة الأولى"
        },
        "enrolled_courses": [
          {
            "course_id": 1,
            "course_title": "القانون المدني الأساسي",
            "progress": 75,
            "last_accessed": "2025-07-13T08:30:00Z"
          }
        ],
        "total_sessions": 8,
        "completed_sessions": 6,
        "average_score": 85.5,
        "last_activity": "2025-07-13T08:30:00Z",
        "is_active": true
      }
    ]
  },
  "status_codes": {
    "200": "نجح الطلب",
    "401": "غير مصرح",
    "403": "ممنوع"
  }
}
```

### **5. Profile Image Upload**

#### **POST /api/auth/instructors/profile/image/**
```json
{
  "description": "رفع صورة شخصية جديدة للمدرس",
  "method": "POST",
  "permissions": ["IsAuthenticated", "IsInstructor"],
  "content_type": "multipart/form-data",
  "request_body": {
    "profile_image": "ملف الصورة (JPG, PNG, GIF)"
  },
  "validation": {
    "file_size": "أقصى حجم 5MB",
    "file_types": ["image/jpeg", "image/png", "image/gif"],
    "dimensions": "أقصى أبعاد 2000x2000 بكسل"
  },
  "response": {
    "message": "تم رفع الصورة بنجاح",
    "profile_image": "http://localhost:8000/media/profile_images/instructor_uuid.jpg"
  },
  "status_codes": {
    "200": "تم الرفع بنجاح",
    "400": "ملف غير صحيح",
    "401": "غير مصرح",
    "403": "ممنوع",
    "413": "حجم الملف كبير جداً"
  }
}
```

---

## 🔐 **الـ Permissions التفصيلية:**

### **1. IsInstructor Permission**
```python
class IsInstructor(permissions.BasePermission):
    """
    يسمح بالوصول للمدرسين المفعلين فقط
    """
    message = 'يجب أن تكون مدرساً مفعلاً للوصول إلى هذا المورد'
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_instructor and
            request.user.is_active
        )
```

### **2. IsInstructorOrAdmin Permission**
```python
class IsInstructorOrAdmin(permissions.BasePermission):
    """
    يسمح بالوصول للمدرسين أو الأدمن
    """
    message = 'يجب أن تكون مدرساً أو أدمن للوصول إلى هذا المورد'
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            (request.user.is_instructor or request.user.is_staff) and
            request.user.is_active
        )
```

### **3. IsOwnerOrAdmin Permission**
```python
class IsOwnerOrAdmin(permissions.BasePermission):
    """
    يسمح للمالك أو الأدمن فقط
    """
    def has_object_permission(self, request, view, obj):
        return (
            request.user == obj or 
            request.user.is_staff
        )
```

---

## 🧪 **اختبار الـ Endpoints:**

### **1. اختبار Authentication:**
```bash
# اختبار بدون token
curl -X GET "http://localhost:8000/api/auth/instructors/profile/"
# Expected: 401 Unauthorized

# اختبار مع token طالب
curl -X GET "http://localhost:8000/api/auth/instructors/profile/" \
  -H "Authorization: Bearer STUDENT_TOKEN"
# Expected: 403 Forbidden

# اختبار مع token مدرس
curl -X GET "http://localhost:8000/api/auth/instructors/profile/" \
  -H "Authorization: Bearer INSTRUCTOR_TOKEN"
# Expected: 200 OK
```

### **2. اختبار Profile Update:**
```bash
curl -X PATCH "http://localhost:8000/api/auth/instructors/profile/" \
  -H "Authorization: Bearer INSTRUCTOR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "أحمد",
    "last_name": "محمد الجديد",
    "bio": "نبذة محدثة",
    "specialty": "القانون المدني والتجاري"
  }'
```

### **3. اختبار Stats API:**
```bash
curl -X GET "http://localhost:8000/api/auth/instructors/stats/" \
  -H "Authorization: Bearer INSTRUCTOR_TOKEN"
```

### **4. اختبار Image Upload:**
```bash
curl -X POST "http://localhost:8000/api/auth/instructors/profile/image/" \
  -H "Authorization: Bearer INSTRUCTOR_TOKEN" \
  -F "profile_image=@/path/to/image.jpg"
```

---

## 📊 **Error Handling:**

### **Standard Error Response Format:**
```json
{
  "error": "نوع الخطأ",
  "detail": "وصف تفصيلي للخطأ",
  "code": "error_code",
  "field_errors": {
    "field_name": ["رسالة الخطأ"]
  }
}
```

### **Common Error Codes:**
- **`unauthorized`** - غير مصرح (401)
- **`forbidden`** - ممنوع (403)
- **`not_found`** - غير موجود (404)
- **`validation_error`** - خطأ في البيانات (400)
- **`file_too_large`** - حجم الملف كبير (413)
- **`invalid_file_type`** - نوع ملف غير مدعوم (400)

---

**📅 تاريخ الإنشاء:** 2025-07-13  
**👨‍💻 المطور:** Augment Agent  
**🎯 الحالة:** مواصفات تقنية جاهزة  
**📋 المرجع:** INSTRUCTOR_PROFILE_ANALYSIS.md
