"""
Middleware لتحسين الطلبات وتقليل التكرار
"""
import time
import hashlib
from django.core.cache import cache
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
import logging

logger = logging.getLogger(__name__)


class RequestDeduplicationMiddleware(MiddlewareMixin):
    """
    Middleware لمنع الطلبات المكررة في فترة زمنية قصيرة
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        # تطبيق فقط على GET requests
        if request.method != 'GET':
            return None
        
        # تجاهل الطلبات الإدارية
        if request.path.startswith('/admin/'):
            return None
        
        # إنشاء مفتاح فريد للطلب
        request_key = self._generate_request_key(request)
        
        # التحقق من وجود الطلب في الكاش
        cached_response = cache.get(request_key)
        if cached_response:
            logger.info(f"Duplicate request blocked: {request.path}")
            return JsonResponse({
                'detail': 'Duplicate request detected',
                'cached': True
            }, status=429)
        
        # حفظ الطلب في الكاش لمدة 2 ثانية
        cache.set(request_key, True, timeout=2)
        
        return None
    
    def _generate_request_key(self, request):
        """
        إنشاء مفتاح فريد للطلب بناءً على المستخدم والمسار والمعاملات
        """
        user_id = getattr(request.user, 'id', 'anonymous')
        path = request.path
        query_params = request.GET.urlencode()
        
        # إنشاء hash للمفتاح
        key_string = f"{user_id}:{path}:{query_params}"
        return f"req_dedup:{hashlib.md5(key_string.encode()).hexdigest()}"


class APIResponseCacheMiddleware(MiddlewareMixin):
    """
    Middleware لتخزين استجابات API مؤقتاً
    """
    
    CACHEABLE_PATHS = [
        '/api/subscriptions/plans/',
        '/api/universities/',
        '/api/courses/subjects/',
    ]
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        # تطبيق فقط على GET requests للمسارات المحددة
        if request.method != 'GET':
            return None
        
        if not any(request.path.startswith(path) for path in self.CACHEABLE_PATHS):
            return None
        
        # إنشاء مفتاح الكاش
        cache_key = self._generate_cache_key(request)
        
        # البحث في الكاش
        cached_response = cache.get(cache_key)
        if cached_response:
            logger.info(f"Serving cached response for: {request.path}")
            return JsonResponse(cached_response)
        
        return None
    
    def process_response(self, request, response):
        # حفظ الاستجابة في الكاش إذا كانت ناجحة
        if (request.method == 'GET' and 
            response.status_code == 200 and
            any(request.path.startswith(path) for path in self.CACHEABLE_PATHS)):
            
            cache_key = self._generate_cache_key(request)
            
            try:
                # تحويل الاستجابة إلى JSON وحفظها
                if hasattr(response, 'data'):
                    cache.set(cache_key, response.data, timeout=300)  # 5 دقائق
                    logger.info(f"Cached response for: {request.path}")
            except Exception as e:
                logger.warning(f"Failed to cache response: {e}")
        
        return response
    
    def _generate_cache_key(self, request):
        """
        إنشاء مفتاح الكاش
        """
        user_id = getattr(request.user, 'id', 'anonymous')
        path = request.path
        query_params = request.GET.urlencode()
        
        key_string = f"api_cache:{user_id}:{path}:{query_params}"
        return hashlib.md5(key_string.encode()).hexdigest()


class RequestLoggingMiddleware(MiddlewareMixin):
    """
    Middleware لتسجيل الطلبات المشبوهة أو المتكررة
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        # تسجيل الطلبات المتكررة
        user_id = getattr(request.user, 'id', 'anonymous')
        request_count_key = f"req_count:{user_id}:{int(time.time() // 60)}"  # per minute
        
        current_count = cache.get(request_count_key, 0)
        cache.set(request_count_key, current_count + 1, timeout=60)
        
        # تحذير إذا كان عدد الطلبات مرتفع
        if current_count > 100:  # أكثر من 100 طلب في الدقيقة
            logger.warning(f"High request rate from user {user_id}: {current_count} requests/minute")
        
        return None
    
    def process_response(self, request, response):
        # تسجيل الأخطاء
        if response.status_code >= 400:
            logger.warning(f"Error response {response.status_code} for {request.path}")
        
        return response


class PerformanceMonitoringMiddleware(MiddlewareMixin):
    """
    Middleware لمراقبة أداء الطلبات
    """
    
    def process_request(self, request):
        request.start_time = time.time()
        return None
    
    def process_response(self, request, response):
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # تسجيل الطلبات البطيئة
            if duration > 2.0:  # أكثر من ثانيتين
                logger.warning(f"Slow request: {request.path} took {duration:.2f}s")
            
            # إضافة header للمدة
            response['X-Response-Time'] = f"{duration:.3f}s"
        
        return response
