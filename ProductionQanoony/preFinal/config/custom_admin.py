from django.contrib.admin import AdminSite
from django.contrib import admin

class SuperuserOnlyAdminSite(AdminSite):
    def has_permission(self, request):
        return request.user.is_active and request.user.is_superuser

# Instantiate custom admin site
custom_admin_site = SuperuserOnlyAdminSite(name='superadmin')

# Automatically discover admin modules of installed apps
admin.autodiscover()

# Mirror all model registrations from the default admin site to the custom site
for model, model_admin_obj in admin.site._registry.items():
    # Re-use the same ModelAdmin class
    custom_admin_site.register(model, model_admin_obj.__class__) 