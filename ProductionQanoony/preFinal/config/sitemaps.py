from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from django.utils import timezone
from datetime import datetime
from courses.models import Subject, Lecture
from library.models import LibraryItem
from careers.models import JobPosting
from universities.models import University


class StaticViewSitemap(Sitemap):
    """Sitemap for static pages"""
    priority = 0.8
    changefreq = 'weekly'
    protocol = 'https'

    def items(self):
        return [
            'home',
            'about', 
            'register',
            'login',
            'join-instructor',
            'jobs',
        ]

    def location(self, item):
        if item == 'home':
            return '/'
        return f'/{item}'

    def lastmod(self, obj):
        return timezone.now()


class SubjectSitemap(Sitemap):
    """Sitemap for subjects/courses"""
    changefreq = 'weekly'
    priority = 0.7
    protocol = 'https'

    def items(self):
        return Subject.objects.filter(is_active=True)

    def location(self, obj):
        return f'/courses/subject/{obj.id}'

    def lastmod(self, obj):
        # Get the latest lecture update time for this subject
        latest_lecture = obj.lectures.order_by('-created_at').first()
        if latest_lecture:
            return latest_lecture.created_at
        return timezone.now()


class LectureSitemap(Sitemap):
    """Sitemap for lectures"""
    changefreq = 'monthly'
    priority = 0.6
    protocol = 'https'

    def items(self):
        return Lecture.objects.filter(is_published=True)

    def location(self, obj):
        return f'/courses/lecture/{obj.id}'

    def lastmod(self, obj):
        return obj.created_at


class LibrarySitemap(Sitemap):
    """Sitemap for library items"""
    changefreq = 'monthly'
    priority = 0.5
    protocol = 'https'

    def items(self):
        return LibraryItem.objects.filter(is_active=True)

    def location(self, obj):
        return f'/library/item/{obj.id}'

    def lastmod(self, obj):
        return obj.created_at


class JobsSitemap(Sitemap):
    """Sitemap for job postings"""
    changefreq = 'daily'
    priority = 0.6
    protocol = 'https'

    def items(self):
        return JobPosting.objects.filter(
            is_active=True,
            application_deadline__gte=timezone.now().date()
        )

    def location(self, obj):
        return f'/jobs/{obj.id}'

    def lastmod(self, obj):
        return obj.updated_at if hasattr(obj, 'updated_at') else obj.created_at


class UniversitySitemap(Sitemap):
    """Sitemap for universities"""
    changefreq = 'monthly'
    priority = 0.4
    protocol = 'https'

    def items(self):
        return University.objects.filter(is_active=True)

    def location(self, obj):
        return f'/universities/{obj.id}'

    def lastmod(self, obj):
        return timezone.now()


# Sitemap index
sitemaps = {
    'static': StaticViewSitemap,
    'subjects': SubjectSitemap,
    'lectures': LectureSitemap,
    'library': LibrarySitemap,
    'jobs': JobsSitemap,
    'universities': UniversitySitemap,
}
