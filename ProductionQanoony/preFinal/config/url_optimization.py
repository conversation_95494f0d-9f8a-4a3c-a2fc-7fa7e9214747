"""
URL optimization and redirect management for better SEO
"""
from django.http import HttpResponsePermanentRedirect, HttpResponseRedirect
from django.urls import reverse
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
import re


class SEORedirectMiddleware(MiddlewareMixin):
    """
    Middleware to handle SEO-friendly redirects and URL optimization
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        # Define redirect rules
        self.redirect_rules = {
            # Old URLs to new SEO-friendly URLs
            '/courses/': '/library/',  # Redirect old courses to library
            '/materials/': '/library/',  # Redirect materials to library
            '/career/': '/jobs/',  # Redirect career to jobs
            '/careers/': '/jobs/',  # Redirect careers to jobs
            '/about-us/': '/about/',  # Redirect about-us to about
            '/contact-us/': '/about/',  # Redirect contact to about
            '/sign-up/': '/register/',  # Redirect sign-up to register
            '/signin/': '/login/',  # Redirect signin to login
            '/log-in/': '/login/',  # Redirect log-in to login
        }
        
        # URL patterns that should be redirected
        self.pattern_redirects = [
            (r'^/course/(\d+)/$', r'/courses/subject/\1/'),
            (r'^/material/(\d+)/$', r'/library/item/\1/'),
            (r'^/job/(\d+)/$', r'/jobs/\1/'),
        ]
        
        super().__init__(get_response)

    def process_request(self, request):
        """
        Process incoming requests for SEO redirects
        """
        path = request.path_info.lower()
        
        # Handle direct URL redirects
        if path in self.redirect_rules:
            new_url = self.redirect_rules[path]
            return HttpResponsePermanentRedirect(new_url)
        
        # Handle pattern-based redirects
        for pattern, replacement in self.pattern_redirects:
            match = re.match(pattern, path)
            if match:
                new_url = re.sub(pattern, replacement, path)
                return HttpResponsePermanentRedirect(new_url)
        
        # Force HTTPS in production
        if not settings.DEBUG and not request.is_secure():
            return HttpResponsePermanentRedirect(
                f"https://{request.get_host()}{request.get_full_path()}"
            )
        
        # Remove trailing slashes for consistency (except for root and API endpoints)
        if path != '/' and path.endswith('/') and not path.startswith('/api/'):
            new_path = path.rstrip('/')
            if request.GET:
                new_path += f"?{request.GET.urlencode()}"
            return HttpResponsePermanentRedirect(new_path)
        
        # Redirect www to non-www
        if request.get_host().startswith('www.'):
            new_host = request.get_host()[4:]  # Remove 'www.'
            new_url = f"{request.scheme}://{new_host}{request.get_full_path()}"
            return HttpResponsePermanentRedirect(new_url)
        
        return None


class CanonicalURLMiddleware(MiddlewareMixin):
    """
    Middleware to ensure canonical URLs for better SEO
    """
    
    def process_response(self, request, response):
        """
        Add canonical URL headers
        """
        if response.status_code == 200 and 'text/html' in response.get('Content-Type', ''):
            # Get the canonical URL
            canonical_url = self.get_canonical_url(request)
            
            # Add canonical link header
            response['Link'] = f'<{canonical_url}>; rel="canonical"'
        
        return response
    
    def get_canonical_url(self, request):
        """
        Generate canonical URL for the current request
        """
        # Use HTTPS in production
        scheme = 'https' if not settings.DEBUG else request.scheme
        
        # Remove www from host
        host = request.get_host()
        if host.startswith('www.'):
            host = host[4:]
        
        # Clean path
        path = request.path_info
        if path != '/' and path.endswith('/'):
            path = path.rstrip('/')
        
        return f"{scheme}://{host}{path}"


def generate_seo_friendly_slug(title, max_length=50):
    """
    Generate SEO-friendly slugs from titles
    """
    import unicodedata
    import re
    
    # Convert to lowercase
    slug = title.lower()
    
    # Replace Arabic characters with transliteration
    arabic_to_latin = {
        'ا': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h',
        'خ': 'kh', 'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z', 'س': 's',
        'ش': 'sh', 'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a',
        'غ': 'gh', 'ف': 'f', 'ق': 'q', 'ك': 'k', 'ل': 'l', 'م': 'm',
        'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y', 'ى': 'a', 'ة': 'h',
        'ء': 'a', 'آ': 'a', 'أ': 'a', 'إ': 'i', 'ؤ': 'u', 'ئ': 'i'
    }
    
    for arabic, latin in arabic_to_latin.items():
        slug = slug.replace(arabic, latin)
    
    # Remove non-alphanumeric characters
    slug = re.sub(r'[^a-z0-9\s-]', '', slug)
    
    # Replace spaces with hyphens
    slug = re.sub(r'\s+', '-', slug)
    
    # Remove multiple consecutive hyphens
    slug = re.sub(r'-+', '-', slug)
    
    # Remove leading/trailing hyphens
    slug = slug.strip('-')
    
    # Limit length
    if len(slug) > max_length:
        slug = slug[:max_length].rstrip('-')
    
    return slug or 'untitled'


def get_breadcrumb_schema(request, breadcrumbs):
    """
    Generate breadcrumb schema markup for SEO
    """
    schema = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": []
    }
    
    for i, (name, url) in enumerate(breadcrumbs, 1):
        item = {
            "@type": "ListItem",
            "position": i,
            "name": name,
            "item": request.build_absolute_uri(url) if url else None
        }
        schema["itemListElement"].append(item)
    
    return schema


def optimize_url_structure():
    """
    Guidelines for SEO-friendly URL structure
    """
    return {
        'guidelines': [
            'Use hyphens (-) instead of underscores (_)',
            'Keep URLs short and descriptive',
            'Use lowercase letters only',
            'Include target keywords naturally',
            'Avoid unnecessary parameters',
            'Use logical hierarchy',
            'Make URLs readable by humans'
        ],
        'examples': {
            'good': [
                '/courses/constitutional-law',
                '/library/civil-law-books',
                '/jobs/legal-advisor-cairo',
                '/about/our-mission'
            ],
            'bad': [
                '/courses/course_id=123&type=law',
                '/library/item.php?id=456',
                '/jobs/Job%20Listing%20Page',
                '/about/About_Us_Page'
            ]
        }
    }


class URLOptimizationUtils:
    """
    Utility class for URL optimization
    """
    
    @staticmethod
    def clean_url_parameters(url):
        """
        Remove unnecessary URL parameters for cleaner URLs
        """
        import urllib.parse
        
        parsed = urllib.parse.urlparse(url)
        
        # Keep only essential parameters
        essential_params = ['page', 'category', 'search', 'filter']
        
        if parsed.query:
            params = urllib.parse.parse_qs(parsed.query)
            cleaned_params = {
                k: v for k, v in params.items() 
                if k in essential_params
            }
            
            new_query = urllib.parse.urlencode(cleaned_params, doseq=True)
            return urllib.parse.urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))
        
        return url
    
    @staticmethod
    def generate_pagination_urls(base_url, current_page, total_pages):
        """
        Generate SEO-friendly pagination URLs
        """
        urls = {}
        
        if current_page > 1:
            urls['prev'] = f"{base_url}?page={current_page - 1}"
            urls['first'] = base_url
        
        if current_page < total_pages:
            urls['next'] = f"{base_url}?page={current_page + 1}"
            urls['last'] = f"{base_url}?page={total_pages}"
        
        return urls
    
    @staticmethod
    def validate_url_seo(url):
        """
        Validate URL for SEO best practices
        """
        issues = []
        
        if len(url) > 100:
            issues.append("URL too long (>100 characters)")
        
        if '_' in url:
            issues.append("Contains underscores (use hyphens instead)")
        
        if url != url.lower():
            issues.append("Contains uppercase letters")
        
        if '%' in url:
            issues.append("Contains encoded characters")
        
        if url.count('/') > 5:
            issues.append("Too many directory levels")
        
        return {
            'is_valid': len(issues) == 0,
            'issues': issues,
            'score': max(0, 100 - len(issues) * 20)
        }
