"""
URL configuration for config project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include, re_path
from django.contrib.sitemaps.views import sitemap
from config.custom_admin import custom_admin_site
from config.sitemaps import sitemaps
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from django.http import HttpResponse

def react_app_view(request):
    """
    Simple view for React Router paths
    Returns a basic response to avoid 404 errors
    """
    return HttpResponse("React App Route - handled by frontend", status=200)

urlpatterns = [
    path('admin/', custom_admin_site.urls),

    # SEO URLs
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps}, name='django.contrib.sitemaps.views.sitemap'),

    # Test WebSocket pages
    path('test_websocket.html', TemplateView.as_view(template_name='test_websocket.html'), name='test_websocket'),
    path('websocket_test.html', TemplateView.as_view(template_name='websocket_test.html'), name='websocket_test'),

    # API URLs
    path('api/auth/', include('authentication.urls')),
    path('api/universities/', include('universities.urls')),
    path('api/students/', include('students.urls')),
    path('api/courses/', include('courses.urls')),
    path('api/library/', include('library.urls')),
    path('api/careers/', include('careers.urls')),
    path('api/communication/', include('communication.urls')),
    path('api/scheduling/', include('scheduling.urls')),
    path('api/notifications/', include('notifications.urls')),
    path('api/email-verification/', include('email_verification.urls')),
    path('api/subscriptions/', include(('subscriptions.urls',
         'subscriptions'), namespace='subscriptions')),
    path('api/ai-assistant/', include('ai_assistant.urls')),

    # React Router paths - to avoid 404 errors
    path('login/', react_app_view, name='react_login'),
    path('register/', react_app_view, name='react_register'),
    path('about/', react_app_view, name='react_about'),
    path('jobs/', react_app_view, name='react_jobs'),
    path('library/', react_app_view, name='react_library'),
    path('student-profile/', react_app_view, name='react_student_profile'),
    path('instructor-dashboard/', react_app_view, name='react_instructor_dashboard'),
    path('admin-user/', react_app_view, name='react_admin_user'),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
