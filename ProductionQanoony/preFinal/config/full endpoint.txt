"""
Full API Endpoints Documentation
===============================

This file contains all API endpoints organized by permissions and functionality.
All endpoints are prefixed with /api/ and require JWT authentication unless specified otherwise.

Last Updated: 2025-01-27
"""

# =============================================================================
# AUTHENTICATION ENDPOINTS (Public & Authenticated)
# =============================================================================

AUTHENTICATION_ENDPOINTS = {
    "public": {
        "register": {
            "url": "/api/auth/register/",
            "method": "POST",
            "description": "Register a new user and send email verification token",
            "permissions": "AllowAny",
            "request_body": {
                "email": "string (required)",
                "first_name": "string (required)",
                "last_name": "string (required)",
                "phone_number": "string (required)",
                "password": "string (required)",
                "profile_image": "file (optional)"
            }
        },
        "verify_email": {
            "url": "/api/auth/verify-email/",
            "method": "POST",
            "description": "Verify user email using token",
            "permissions": "AllowAny (only for unverified users)",
            "request_body": {
                "token": "string (required)"
            }
        },
        "login": {
            "url": "/api/auth/login/",
            "method": "POST",
            "description": "Login with email and password (JWT)",
            "permissions": "AllowAny",
            "request_body": {
                "email": "string (required)",
                "password": "string (required)"
            }
        },
        "password_reset_request": {
            "url": "/api/auth/password-reset/request/",
            "method": "POST",
            "description": "Request password reset (sends token to email)",
            "permissions": "AllowAny",
            "request_body": {
                "email": "string (required)"
            }
        },
        "password_reset_confirm": {
            "url": "/api/auth/password-reset/confirm/",
            "method": "POST",
            "description": "Reset password using token",
            "permissions": "AllowAny",
            "request_body": {
                "token": "string (required)",
                "new_password": "string (required)"
            }
        }
    },
    "authenticated": {
        "logout": {
            "url": "/api/auth/logout/",
            "method": "POST",
            "description": "Logout user (JWT/session-based)",
            "permissions": "IsAuthenticated",
            "request_body": "None"
        },
        "profile": {
            "url": "/api/auth/profile/",
            "method": "GET, PATCH",
            "description": "View or update user profile",
            "permissions": "IsAuthenticated",
            "request_body": {
                "first_name": "string (optional)",
                "last_name": "string (optional)",
                "phone_number": "string (optional)",
                "profile_image": "file (optional)"
            }
        },
        "student_application": {
            "url": "/api/auth/student-application/",
            "method": "GET, POST",
            "description": "Submit or view student application (requires payment screenshot)",
            "permissions": "IsAuthenticated",
            "request_body": {
                "payment_screenshot": "file (required for POST)"
            }
        }
    },
    "admin_only": {
        "student_application_admin_action": {
            "url": "/api/auth/student-application/{pk}/{action}/",
            "method": "POST",
            "description": "Admin approve/reject student application",
            "permissions": "IsAdminUser",
            "actions": ["approve", "reject"],
            "request_body": {
                "rejection_reason": "string (required for reject action)"
            }
        },
        "student_applications_list": {
            "url": "/api/auth/student-applications/",
            "method": "GET",
            "description": "List all student applications for admin review",
            "permissions": "IsAdminUser"
        }
    }
}

# =============================================================================
# UNIVERSITIES ENDPOINTS (Public & Admin)
# =============================================================================

UNIVERSITIES_ENDPOINTS = {
    "public": {
        "universities_active": {
            "url": "/api/universities/universities/active/",
            "method": "GET",
            "description": "List all active universities (for registration and public use)",
            "permissions": "AllowAny"
        },
        "universities_detail": {
            "url": "/api/universities/universities/{id}/",
            "method": "GET",
            "description": "Retrieve details of a university",
            "permissions": "AllowAny"
        },
        "academic_years_list": {
            "url": "/api/universities/academic-years/",
            "method": "GET",
            "description": "List all active academic years (independent of universities)",
            "permissions": "AllowAny"
        },
        "academic_years_detail": {
            "url": "/api/universities/academic-years/{id}/",
            "method": "GET",
            "description": "Retrieve details of an academic year",
            "permissions": "AllowAny"
        }
    },
    "admin_only": {
        "universities_crud": {
            "url": "/api/universities/universities/",
            "method": "GET, POST, PATCH, DELETE",
            "description": "Admin can create, update, or delete universities",
            "permissions": "IsAdminUser",
            "request_body": {
                "name_ar": "string (required)",
                "name_en": "string (required)",
                "code": "string (required)",
                "city": "string (required)",
                "is_active": "boolean (optional)"
            }
        },
        "academic_years_crud": {
            "url": "/api/universities/academic-years/",
            "method": "GET, POST, PATCH, DELETE",
            "description": "Admin can create, update, or delete academic years",
            "permissions": "IsAdminUser",
            "request_body": {
                "university": "integer (required)",
                "year_number": "integer (required)",
                "year_name_ar": "string (required)",
                "year_name_en": "string (required)",
                "is_active": "boolean (optional)"
            }
        }
    }
}

# =============================================================================
# STUDENTS ENDPOINTS (Student & Admin)
# =============================================================================

STUDENTS_ENDPOINTS = {
    "student_only": {
        "student_dashboard": {
            "url": "/api/students/progress/dashboard/",
            "method": "GET",
            "description": "Get an overview of the student's profile and progress",
            "permissions": "IsAuthenticated (student only)"
        },
        "student_profile_me": {
            "url": "/api/students/profiles/me/",
            "method": "GET",
            "description": "Get the authenticated student's own profile",
            "permissions": "IsAuthenticated (student only)"
        },
        "student_profile_update": {
            "url": "/api/students/profiles/{id}/",
            "method": "GET, PATCH",
            "description": "View or update the authenticated student's profile",
            "permissions": "IsAuthenticated (student can only access own profile)",
            "request_body": {
                "student_id": "string (optional)",
                "university": "integer (optional)",
                "academic_year": "integer (optional)"
            }
        },
        "student_progress_update": {
            "url": "/api/students/progress/{id}/",
            "method": "GET, PATCH",
            "description": "View or update the student's progress for a course",
            "permissions": "IsAuthenticated (student can only access own progress)",
            "request_body": {
                "completed_lectures": "integer (optional)",
                "total_lectures": "integer (optional)",
                "quiz_scores": "json (optional)"
            }
        }
    },
    "admin_only": {
        "student_profiles_list": {
            "url": "/api/students/profiles/",
            "method": "GET",
            "description": "Admin can list all student profiles",
            "permissions": "IsAdminUser"
        },
        "student_progress_list": {
            "url": "/api/students/progress/",
            "method": "GET",
            "description": "Admin can list all student progress records",
            "permissions": "IsAdminUser"
        }
    }
}

# =============================================================================
# COURSES ENDPOINTS (Student, Instructor & Admin)
# =============================================================================

COURSES_ENDPOINTS = {
    "public": {
        "semesters_list": {
            "url": "/api/courses/semesters/",
            "method": "GET",
            "description": "List all semesters",
            "permissions": "IsEmailVerified"
        },
        "subjects_list": {
            "url": "/api/courses/subjects/?semester={semester_id}",
            "method": "GET",
            "description": "List all subjects for a semester",
            "permissions": "IsEmailVerified"
        },
        "lectures_list": {
            "url": "/api/courses/lectures/?subject={subject_id}",
            "method": "GET",
            "description": "List all lectures for a subject",
            "permissions": "IsEmailVerified"
        },
        "lecture_pdf_summary": {
            "url": "/api/courses/lectures/{id}/pdf-summary/",
            "method": "GET",
            "description": "Get the PDF summary for a lecture (if available)",
            "permissions": "IsEmailVerified"
        },
        "quizzes_list": {
            "url": "/api/courses/quizzes/",
            "method": "GET",
            "description": "List all quizzes",
            "permissions": "IsEmailVerified"
        },
        "questions_list": {
            "url": "/api/courses/questions/",
            "method": "GET",
            "description": "List all questions",
            "permissions": "IsEmailVerified"
        },
        "answers_list": {
            "url": "/api/courses/answers/",
            "method": "GET",
            "description": "List all answers",
            "permissions": "IsEmailVerified"
        }
    },
    "instructor_admin": {
        "semesters_create": {
            "url": "/api/courses/semesters/",
            "method": "POST",
            "description": "Create a new semester",
            "permissions": "IsEmailVerified & IsInstructorOrAdmin",
            "request_body": {
                "title_ar": "string (required)",
                "title_en": "string (required)",
                "description_ar": "string (required)",
                "academic_year": "integer (required)",
                "order": "integer (required)",
                "is_published": "boolean (optional)"
            }
        },
        "subjects_create": {
            "url": "/api/courses/subjects/",
            "method": "POST",
            "description": "Create a new subject",
            "permissions": "IsEmailVerified & IsInstructorOrAdmin",
            "request_body": {
                "name_ar": "string (required)",
                "name_en": "string (required)",
                "code": "string (required)",
                "description_ar": "string (required)",
                "semester": "integer (required)",
                "academic_year": "integer (required)",
                "is_active": "boolean (optional)"
            }
        },
        "lectures_create": {
            "url": "/api/courses/lectures/",
            "method": "POST",
            "description": "Create a new lecture",
            "permissions": "IsEmailVerified & IsInstructorOrAdmin",
            "request_body": {
                "subject_id": "integer (required)",
                "title_ar": "string (required)",
                "youtube_video_id": "string (required)",
                "duration_minutes": "integer (required)",
                "order": "integer (required)",
                "is_published": "boolean (optional)"
            }
        },
        "quizzes_create": {
            "url": "/api/courses/quizzes/",
            "method": "POST",
            "description": "Create a new quiz",
            "permissions": "IsEmailVerified & IsInstructorOrAdmin",
            "request_body": {
                "lecture_id": "integer (required)",
                "title_ar": "string (required)",
                "instructions_ar": "string (required)",
                "time_limit_minutes": "integer (required)",
                "passing_score": "integer (required)",
                "is_active": "boolean (optional)"
            }
        },
        "questions_create": {
            "url": "/api/courses/questions/",
            "method": "POST",
            "description": "Create a new question",
            "permissions": "IsEmailVerified & IsInstructorOrAdmin"
        },
        "answers_create": {
            "url": "/api/courses/answers/",
            "method": "POST",
            "description": "Create a new answer",
            "permissions": "IsEmailVerified & IsInstructorOrAdmin"
        },
        "certificates_create": {
            "url": "/api/courses/certificates/",
            "method": "POST",
            "description": "Create a new certificate",
            "permissions": "IsEmailVerified & IsInstructorOrAdmin",
            "request_body": {
                "student_id": "integer (required)",
                "semester_id": "integer (required)"
            }
        }
    },
    "student_only": {
        "quiz_attempts_create": {
            "url": "/api/courses/quiz-attempts/",
            "method": "POST",
            "description": "Create a new quiz attempt (student only, for self)",
            "permissions": "IsEmailVerified & IsStudent",
            "request_body": {
                "student_id": "integer (required)",
                "quiz_id": "integer (required)",
                "answers": "json (required)"
            }
        },
        "quiz_attempts_list": {
            "url": "/api/courses/quiz-attempts/",
            "method": "GET",
            "description": "List all quiz attempts (student sees own attempts)",
            "permissions": "IsEmailVerified & IsStudent"
        },
        "certificates_list": {
            "url": "/api/courses/certificates/",
            "method": "GET",
            "description": "List all certificates (student sees own)",
            "permissions": "IsEmailVerified & IsStudent"
        },
        "certificate_download": {
            "url": "/api/courses/certificates/{id}/download/",
            "method": "GET",
            "description": "Download a dynamically generated PDF certificate",
            "permissions": "IsEmailVerified & IsStudent"
        }
    },
    "admin_only": {
        "quiz_attempts_list_all": {
            "url": "/api/courses/quiz-attempts/",
            "method": "GET",
            "description": "List all quiz attempts (admin sees all)",
            "permissions": "IsAdminUser"
        },
        "certificates_list_all": {
            "url": "/api/courses/certificates/",
            "method": "GET",
            "description": "List all certificates (admin sees all)",
            "permissions": "IsAdminUser"
        }
    }
}

# =============================================================================
# SCHEDULING ENDPOINTS (Student, Instructor & Admin)
# =============================================================================

SCHEDULING_ENDPOINTS = {
    "authenticated": {
        "sessions_list": {
            "url": "/api/scheduling/sessions/",
            "method": "GET",
            "description": "List all live sessions",
            "permissions": "IsAuthenticated",
            "features": ["search", "ordering", "filtering"]
        },
        "sessions_detail": {
            "url": "/api/scheduling/sessions/{id}/",
            "method": "GET",
            "description": "Retrieve a specific live session",
            "permissions": "IsAuthenticated"
        },
        "calendar_list": {
            "url": "/api/scheduling/calendar/",
            "method": "GET",
            "description": "List calendar events for the authenticated user",
            "permissions": "IsAuthenticated"
        },
        "calendar_upcoming": {
            "url": "/api/scheduling/calendar/upcoming/",
            "method": "GET",
            "description": "List upcoming calendar events",
            "permissions": "IsAuthenticated"
        }
    },
    "student_only": {
        "bookings_create": {
            "url": "/api/scheduling/bookings/",
            "method": "POST",
            "description": "Book a session (requires payment screenshot)",
            "permissions": "IsStudent",
            "request_body": {
                "session": "integer (required)",
                "payment_screenshot": "file (required)"
            }
        },
        "bookings_list": {
            "url": "/api/scheduling/bookings/",
            "method": "GET",
            "description": "List student's own bookings",
            "permissions": "IsStudent"
        },
        "bookings_feedback": {
            "url": "/api/scheduling/bookings/{id}/feedback/",
            "method": "POST",
            "description": "Submit feedback for a session",
            "permissions": "IsStudent",
            "request_body": {
                "feedback_rating": "integer (1-5, optional)",
                "feedback_comment": "string (optional)"
            }
        }
    },
    "instructor_admin": {
        "sessions_create": {
            "url": "/api/scheduling/sessions/",
            "method": "POST",
            "description": "Create a new live session",
            "permissions": "IsInstructorOrAdmin",
            "request_body": {
                "instructor": "integer (required)",
                "title_ar": "string (required)",
                "description_ar": "string (required)",
                "scheduled_date": "datetime (required)",
                "duration_minutes": "integer (required)",
                "max_participants": "integer (required)",
                "meeting_link": "url (required)",
                "semester": "integer (optional)"
            }
        },
        "sessions_update": {
            "url": "/api/scheduling/sessions/{id}/",
            "method": "PATCH, DELETE",
            "description": "Update or delete a live session",
            "permissions": "IsInstructorOrAdmin"
        },
        "sessions_cancel": {
            "url": "/api/scheduling/sessions/{id}/cancel/",
            "method": "POST",
            "description": "Cancel a live session",
            "permissions": "IsInstructorOrAdmin"
        },
        "bookings_list_instructor": {
            "url": "/api/scheduling/bookings/",
            "method": "GET",
            "description": "List bookings for instructor's sessions",
            "permissions": "IsInstructorOrAdmin"
        }
    },
    "admin_only": {
        "bookings_approve": {
            "url": "/api/scheduling/bookings/{id}/approve/",
            "method": "POST",
            "description": "Approve a session booking",
            "permissions": "IsAdminUser"
        },
        "bookings_reject": {
            "url": "/api/scheduling/bookings/{id}/reject/",
            "method": "POST",
            "description": "Reject a session booking",
            "permissions": "IsAdminUser",
            "request_body": {
                "rejection_reason": "string (optional)"
            }
        }
    }
}

# =============================================================================
# LIBRARY ENDPOINTS (Public, Student & Admin)
# =============================================================================

LIBRARY_ENDPOINTS = {
    "public": {
        "categories_list": {
            "url": "/api/library/categories/",
            "method": "GET",
            "description": "List all categories",
            "permissions": "Public"
        },
        "documents_list": {
            "url": "/api/library/documents/",
            "method": "GET",
            "description": "List all documents",
            "permissions": "Public"
        },
        "documents_featured": {
            "url": "/api/library/documents/featured/",
            "method": "GET",
            "description": "List all featured documents",
            "permissions": "Public"
        },
        "documents_search": {
            "url": "/api/library/documents/search/?q={term}",
            "method": "GET",
            "description": "Search documents by title, author, or tags",
            "permissions": "Public"
        },
        "documents_detail": {
            "url": "/api/library/documents/{id}/",
            "method": "GET",
            "description": "Retrieve a specific document",
            "permissions": "Public"
        }
    },
    "student_only": {
        "documents_download": {
            "url": "/api/library/documents/{id}/download/",
            "method": "GET",
            "description": "Download a document (access tracked)",
            "permissions": "IsEmailVerified & IsStudent"
        }
    },
    "instructor_admin": {
        "documents_create": {
            "url": "/api/library/documents/",
            "method": "POST",
            "description": "Upload a new document",
            "permissions": "Instructor/Admin only",
            "request_body": {
                "title_ar": "string (required)",
                "title_en": "string (required)",
                "category_id": "integer (required)",
                "description_ar": "string (required)",
                "file_url": "url (required)",
                "file_type": "string (required)",
                "author": "string (required)",
                "publication_date": "date (required)",
                "tags": "string (optional)",
                "is_featured": "boolean (optional)"
            }
        }
    },
    "admin_only": {
        "categories_create": {
            "url": "/api/library/categories/",
            "method": "POST",
            "description": "Create a new category",
            "permissions": "Admin only",
            "request_body": {
                "name_ar": "string (required)",
                "name_en": "string (required)",
                "order": "integer (required)",
                "is_active": "boolean (optional)"
            }
        },
        "access_list": {
            "url": "/api/library/access/",
            "method": "GET",
            "description": "List all document access records",
            "permissions": "Admin only"
        }
    }
}

# =============================================================================
# NOTIFICATIONS ENDPOINTS (Authenticated & Admin)
# =============================================================================

NOTIFICATIONS_ENDPOINTS = {
    "authenticated": {
        "notifications_list": {
            "url": "/api/notifications/",
            "method": "GET",
            "description": "List notifications for the authenticated user",
            "permissions": "IsAuthenticated"
        },
        "notifications_mark_read": {
            "url": "/api/notifications/{id}/mark_as_read/",
            "method": "POST",
            "description": "Mark a notification as read",
            "permissions": "IsAuthenticated"
        },
        "notifications_history": {
            "url": "/api/notifications/history/",
            "method": "GET",
            "description": "List all notifications for the authenticated user",
            "permissions": "IsAuthenticated"
        }
    },
    "admin_only": {
        "templates_list": {
            "url": "/api/notifications/templates/",
            "method": "GET",
            "description": "List all notification templates",
            "permissions": "IsAdminUser"
        },
        "templates_create": {
            "url": "/api/notifications/templates/",
            "method": "POST",
            "description": "Create a new notification template",
            "permissions": "IsAdminUser",
            "request_body": {
                "name": "string (required)",
                "subject_ar": "string (required)",
                "content_ar": "string (required)",
                "notification_type": "string (required)",
                "is_active": "boolean (optional)"
            }
        },
        "templates_update": {
            "url": "/api/notifications/templates/{id}/",
            "method": "PATCH, DELETE",
            "description": "Update or delete a notification template",
            "permissions": "IsAdminUser"
        },
        "notifications_send": {
            "url": "/api/notifications/send/",
            "method": "POST",
            "description": "Send a notification to one or more users",
            "permissions": "IsAdminUser",
            "request_body": {
                "recipients": "array (required)",
                "template_id": "integer (required)"
            }
        }
    }
}

# =============================================================================
# CAREERS ENDPOINTS (Public, Student, Employer & Admin)
# =============================================================================

CAREERS_ENDPOINTS = {
    "public": {
        "jobs_list": {
            "url": "/api/careers/jobs/",
            "method": "GET",
            "description": "List all active job postings",
            "permissions": "Public",
            "features": ["search", "ordering"]
        },
        "jobs_detail": {
            "url": "/api/careers/jobs/{id}/",
            "method": "GET",
            "description": "Retrieve a job posting by ID",
            "permissions": "Public"
        },
        "categories_list": {
            "url": "/api/careers/categories/",
            "method": "GET",
            "description": "List all active job categories",
            "permissions": "Public"
        },
        "categories_detail": {
            "url": "/api/careers/categories/{id}/",
            "method": "GET",
            "description": "Retrieve a job category by ID",
            "permissions": "Public"
        }
    },
    "student_only": {
        "applications_create": {
            "url": "/api/careers/applications/",
            "method": "POST",
            "description": "Apply for a job (upload resume and cover letter)",
            "permissions": "Authenticated student",
            "request_body": {
                "job_id": "integer (required)",
                "cover_letter": "string (required)",
                "resume": "file (required)"
            }
        },
        "applications_list": {
            "url": "/api/careers/applications/",
            "method": "GET",
            "description": "List student's own job applications",
            "permissions": "Authenticated student"
        },
        "applications_detail": {
            "url": "/api/careers/applications/{id}/",
            "method": "GET",
            "description": "Retrieve a job application by ID",
            "permissions": "Student (own)"
        }
    },
    "employer_admin": {
        "jobs_create": {
            "url": "/api/careers/jobs/",
            "method": "POST",
            "description": "Create a new job posting",
            "permissions": "Employer/Admin only",
            "request_body": {
                "title_ar": "string (required)",
                "company_name": "string (required)",
                "category_id": "integer (required)",
                "job_type": "string (required)",
                "location": "string (required)",
                "description_ar": "string (required)",
                "requirements_ar": "string (required)",
                "salary_range": "string (required)",
                "application_deadline": "date (required)",
                "contact_email": "email (required)",
                "contact_phone": "string (required)",
                "is_active": "boolean (optional)"
            }
        },
        "jobs_update": {
            "url": "/api/careers/jobs/{id}/",
            "method": "PUT, PATCH, DELETE",
            "description": "Update or delete a job posting",
            "permissions": "Employer/Admin only (owner or admin)"
        },
        "jobs_my_jobs": {
            "url": "/api/careers/jobs/my-jobs/",
            "method": "GET",
            "description": "List jobs posted by the current employer/admin",
            "permissions": "Employer/Admin only"
        },
        "applications_list_employer": {
            "url": "/api/careers/applications/",
            "method": "GET",
            "description": "List job applications for employer's jobs",
            "permissions": "Employer (for their jobs)"
        },
        "applications_detail_employer": {
            "url": "/api/careers/applications/{id}/",
            "method": "GET",
            "description": "Retrieve a job application by ID",
            "permissions": "Employer (for their jobs)"
        }
    },
    "admin_only": {
        "categories_create": {
            "url": "/api/careers/categories/",
            "method": "POST",
            "description": "Create a new job category",
            "permissions": "Admin only",
            "request_body": {
                "name_en": "string (required)",
                "name_ar": "string (required)",
                "description_ar": "string (required)",
                "is_active": "boolean (optional)"
            }
        },
        "categories_update": {
            "url": "/api/careers/categories/{id}/",
            "method": "PUT, PATCH, DELETE",
            "description": "Update or delete a job category",
            "permissions": "Admin only"
        },
        "applications_list_all": {
            "url": "/api/careers/applications/",
            "method": "GET",
            "description": "List all job applications",
            "permissions": "Admin only"
        },
        "applications_update": {
            "url": "/api/careers/applications/{id}/",
            "method": "PUT, PATCH, DELETE",
            "description": "Update or delete a job application",
            "permissions": "Admin only"
        }
    }
}

# =============================================================================
# COMMUNICATION ENDPOINTS (Authenticated & Admin)
# =============================================================================

COMMUNICATION_ENDPOINTS = {
    "authenticated": {
        "chatrooms_list": {
            "url": "/api/communication/chatrooms/",
            "method": "GET",
            "description": "List chat rooms",
            "permissions": "Authenticated users"
        },
        "chatrooms_create": {
            "url": "/api/communication/chatrooms/",
            "method": "POST",
            "description": "Create a new chat room",
            "permissions": "Authenticated users"
        },
        "chatrooms_join": {
            "url": "/api/communication/chatrooms/{id}/join/",
            "method": "POST",
            "description": "Join a chat room",
            "permissions": "Authenticated users"
        },
        "messages_list": {
            "url": "/api/communication/messages/",
            "method": "GET",
            "description": "List messages",
            "permissions": "Authenticated users"
        },
        "messages_create": {
            "url": "/api/communication/messages/",
            "method": "POST",
            "description": "Send a message",
            "permissions": "Authenticated users"
        },
        "forum_categories_list": {
            "url": "/api/communication/forum-categories/",
            "method": "GET",
            "description": "List forum categories",
            "permissions": "Authenticated users"
        },
        "forum_topics_list": {
            "url": "/api/communication/forum-topics/",
            "method": "GET",
            "description": "List forum topics",
            "permissions": "Authenticated users"
        },
        "forum_topics_create": {
            "url": "/api/communication/forum-topics/",
            "method": "POST",
            "description": "Create a forum topic",
            "permissions": "Authenticated users"
        },
        "forum_posts_list": {
            "url": "/api/communication/forum-posts/",
            "method": "GET",
            "description": "List forum posts",
            "permissions": "Authenticated users"
        },
        "forum_posts_create": {
            "url": "/api/communication/forum-posts/",
            "method": "POST",
            "description": "Create a forum post",
            "permissions": "Authenticated users"
        }
    },
    "moderator_admin": {
        "forum_topics_pin": {
            "url": "/api/communication/forum-topics/{id}/pin/",
            "method": "POST",
            "description": "Pin a forum topic",
            "permissions": "Moderators/Admins"
        },
        "forum_topics_lock": {
            "url": "/api/communication/forum-topics/{id}/lock/",
            "method": "POST",
            "description": "Lock a forum topic",
            "permissions": "Moderators/Admins"
        },
        "forum_posts_approve": {
            "url": "/api/communication/forum-posts/{id}/approve/",
            "method": "POST",
            "description": "Approve a forum post",
            "permissions": "Moderators/Admins"
        }
    }
}

# =============================================================================
# AI ASSISTANT ENDPOINTS (Subscribed & Verified Users)
# =============================================================================

AI_ASSISTANT_ENDPOINTS = {
    "subscribed_verified": {
        "conversations_list": {
            "url": "/api/ai-assistant/conversations/",
            "method": "GET",
            "description": "List all conversations for the authenticated user",
            "permissions": "IsSubscribedAndVerified"
        },
        "conversations_create": {
            "url": "/api/ai-assistant/conversations/",
            "method": "POST",
            "description": "Create a new conversation",
            "permissions": "IsSubscribedAndVerified",
            "request_body": {
                "student": "integer (required)",
                "title_ar": "string (required)",
                "context_type": "string (required)",
                "course": "integer (optional)"
            }
        },
        "conversations_history": {
            "url": "/api/ai-assistant/conversations/{id}/history/",
            "method": "GET",
            "description": "Get all messages in a conversation",
            "permissions": "IsSubscribedAndVerified"
        },
        "messages_send": {
            "url": "/api/ai-assistant/messages/send/",
            "method": "POST",
            "description": "Send a message to the AI assistant and receive a reply",
            "permissions": "IsSubscribedAndVerified",
            "request_body": {
                "conversation": "integer (required)",
                "content_ar": "string (required)"
            }
        },
        "messages_rate": {
            "url": "/api/ai-assistant/messages/{id}/rate/",
            "method": "POST",
            "description": "Rate an AI response message (1-5 stars, optional comment)",
            "permissions": "IsSubscribedAndVerified",
            "request_body": {
                "rating": "integer (1-5, required)",
                "comment": "string (optional)"
            }
        }
    },
    "admin_only": {
        "prompt_templates_list": {
            "url": "/api/ai-assistant/prompt-templates/",
            "method": "GET",
            "description": "List prompt templates",
            "permissions": "IsAdminUser"
        },
        "prompt_templates_create": {
            "url": "/api/ai-assistant/prompt-templates/",
            "method": "POST",
            "description": "Create a prompt template",
            "permissions": "IsAdminUser"
        },
        "usage_limits_list": {
            "url": "/api/ai-assistant/usage-limits/",
            "method": "GET",
            "description": "List usage limits for users",
            "permissions": "IsAdminUser"
        },
        "usage_limits_create": {
            "url": "/api/ai-assistant/usage-limits/",
            "method": "POST",
            "description": "Create usage limits for users",
            "permissions": "IsAdminUser"
        }
    }
}

# =============================================================================
# SUBSCRIPTIONS ENDPOINTS (Admin Only)
# =============================================================================

SUBSCRIPTIONS_ENDPOINTS = {
    "admin_only": {
        "admin_renewal_action": {
            "url": "/api/subscriptions/admin/renewal-action/{pk}/",
            "method": "POST",
            "description": "Admin approve/deny subscription renewal",
            "permissions": "Admin only",
            "features": [
                "Sends notification on approval/denial",
                "Updates subscription status",
                "Creates in-app notifications"
            ]
        }
    }
}

# =============================================================================
# EMAIL VERIFICATION ENDPOINTS (Public)
# =============================================================================

EMAIL_VERIFICATION_ENDPOINTS = {
    "public": {
        "request_confirmation": {
            "url": "/api/email-verification/request/",
            "method": "POST",
            "description": "Request email confirmation",
            "permissions": "Public",
            "request_body": {
                "email": "string (required)"
            }
        },
        "confirm_email": {
            "url": "/api/email-verification/confirm/",
            "method": "POST",
            "description": "Confirm email via token",
            "permissions": "Public",
            "request_body": {
                "token": "string (required)"
            }
        },
        "resend_confirmation": {
            "url": "/api/email-verification/resend/",
            "method": "POST",
            "description": "Resend confirmation email",
            "permissions": "Public",
            "request_body": {
                "email": "string (required)"
            }
        }
    }
}

# =============================================================================
# PERMISSION LEVELS SUMMARY
# =============================================================================

PERMISSION_LEVELS = {
    "public": "No authentication required",
    "authenticated": "JWT authentication required",
    "student_only": "JWT authentication + student role required",
    "instructor_admin": "JWT authentication + instructor or admin role required",
    "employer_admin": "JWT authentication + employer or admin role required",
    "moderator_admin": "JWT authentication + moderator or admin role required",
    "subscribed_verified": "JWT authentication + email verified + active subscription required",
    "admin_only": "JWT authentication + admin role required"
}

# =============================================================================
# WEBHOOKS & BACKGROUND TASKS
# =============================================================================

BACKGROUND_TASKS = {
    "deactivate_expired_subscriptions": {
        "type": "Celery periodic task",
        "frequency": "Daily",
        "description": "Deactivates expired subscriptions and sends notifications",
        "actions": [
            "Sets is_active=False for expired subscriptions",
            "Sets user.is_active=False for expired users",
            "Creates in-app notification for expired users"
        ]
    },
    "reset_ai_usage_limits": {
        "type": "Celery periodic task",
        "frequency": "Daily/Monthly",
        "description": "Resets AI usage limits for users",
        "actions": [
            "Resets daily_used to 0",
            "Resets monthly_used to 0 on first day of month"
        ]
    }
}

# =============================================================================
# WEBSOCKET ENDPOINTS
# =============================================================================

WEBSOCKET_ENDPOINTS = {
    "real_time_chat": {
        "url": "ws://<host>/ws/chat/<room_id>/",
        "auth": "JWT or session (user must be authenticated)",
        "description": "Real-time chat functionality",
        "features": [
            "User joins chat room group on connect",
            "Send/receive messages in real-time",
            "File and image message support",
            "Notifications for new messages"
        ]
    }
}

# =============================================================================
# API VERSIONING & DEPRECATION
# =============================================================================

API_INFO = {
    "version": "1.0",
    "base_url": "/api/",
    "authentication": "JWT (JSON Web Tokens)",
    "content_type": "application/json",
    "language": "Arabic (user-facing messages), English (API documentation)",
    "rate_limiting": "Not implemented",
    "cors": "Enabled for frontend integration"
}