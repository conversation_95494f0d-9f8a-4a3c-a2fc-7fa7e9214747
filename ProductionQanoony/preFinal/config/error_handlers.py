from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.core.exceptions import ValidationError, PermissionDenied
from django.db import IntegrityError
from django.http import Http404
import logging

logger = logging.getLogger(__name__)

def custom_exception_handler(exc, context):
    """
    معالج مخصص للأخطاء يرجع رسائل واضحة باللغة العربية
    """
    # استدعاء معالج الأخطاء الافتراضي أولاً
    response = exception_handler(exc, context)
    
    # إذا لم يتم التعامل مع الخطأ بواسطة DRF، نتعامل معه بأنفسنا
    if response is None:
        custom_response_data = handle_unhandled_exceptions(exc, context)
        if custom_response_data:
            return Response(custom_response_data['data'], status=custom_response_data['status'])
    
    # تخصيص رسائل الأخطاء الموجودة
    if response is not None:
        custom_response_data = get_custom_error_message(exc, response.status_code)
        if custom_response_data:
            response.data = custom_response_data
    
    return response

def handle_unhandled_exceptions(exc, context):
    """
    معالجة الأخطاء التي لم يتم التعامل معها بواسطة DRF
    """
    if isinstance(exc, ValidationError):
        return {
            'data': {
                'error': 'خطأ في التحقق من البيانات',
                'detail': 'البيانات المرسلة غير صحيحة. يرجى التحقق من المدخلات.',
                'errors': exc.message_dict if hasattr(exc, 'message_dict') else str(exc)
            },
            'status': status.HTTP_400_BAD_REQUEST
        }
    
    elif isinstance(exc, PermissionDenied):
        return {
            'data': {
                'error': 'غير مصرح لك',
                'detail': 'ليس لديك الصلاحية للوصول إلى هذا المورد.',
                'code': 'permission_denied'
            },
            'status': status.HTTP_403_FORBIDDEN
        }
    
    elif isinstance(exc, Http404):
        return {
            'data': {
                'error': 'المورد غير موجود',
                'detail': 'المورد المطلوب غير موجود أو تم حذفه.',
                'code': 'not_found'
            },
            'status': status.HTTP_404_NOT_FOUND
        }
    
    elif isinstance(exc, IntegrityError):
        return {
            'data': {
                'error': 'خطأ في قاعدة البيانات',
                'detail': 'حدث تضارب في البيانات. قد تكون البيانات مكررة أو غير صحيحة.',
                'code': 'integrity_error'
            },
            'status': status.HTTP_400_BAD_REQUEST
        }
    
    # خطأ عام غير متوقع
    logger.error(f"Unhandled exception: {type(exc).__name__}: {str(exc)}")
    return {
        'data': {
            'error': 'خطأ داخلي في الخادم',
            'detail': 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً.',
            'code': 'internal_server_error'
        },
        'status': status.HTTP_500_INTERNAL_SERVER_ERROR
    }

def get_custom_error_message(exc, status_code):
    """
    إرجاع رسائل خطأ مخصصة حسب نوع الخطأ ورمز الحالة
    """
    error_messages = {
        400: {
            'error': 'طلب غير صحيح',
            'detail': 'البيانات المرسلة غير صحيحة أو ناقصة.',
            'code': 'bad_request'
        },
        401: {
            'error': 'غير مصرح',
            'detail': 'يجب تسجيل الدخول للوصول إلى هذا المورد.',
            'code': 'unauthorized'
        },
        403: {
            'error': 'ممنوع',
            'detail': 'ليس لديك الصلاحية للوصول إلى هذا المورد.',
            'code': 'forbidden'
        },
        404: {
            'error': 'غير موجود',
            'detail': 'المورد المطلوب غير موجود.',
            'code': 'not_found'
        },
        405: {
            'error': 'طريقة غير مسموحة',
            'detail': 'طريقة HTTP المستخدمة غير مسموحة لهذا المورد.',
            'code': 'method_not_allowed'
        },
        429: {
            'error': 'طلبات كثيرة',
            'detail': 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً.',
            'code': 'too_many_requests'
        },
        500: {
            'error': 'خطأ داخلي في الخادم',
            'detail': 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً.',
            'code': 'internal_server_error'
        }
    }
    
    return error_messages.get(status_code)

# رسائل خطأ مخصصة للتحقق من الصحة
VALIDATION_ERROR_MESSAGES = {
    'required': 'هذا الحقل مطلوب.',
    'blank': 'هذا الحقل لا يمكن أن يكون فارغاً.',
    'null': 'هذا الحقل لا يمكن أن يكون فارغاً.',
    'invalid': 'قيمة غير صحيحة.',
    'max_length': 'تأكد من أن هذا الحقل لا يحتوي على أكثر من {max_length} حرف.',
    'min_length': 'تأكد من أن هذا الحقل يحتوي على الأقل {min_length} حرف.',
    'unique': 'هذه القيمة موجودة بالفعل.',
    'invalid_choice': 'اختيار غير صحيح.',
    'invalid_email': 'عنوان بريد إلكتروني غير صحيح.',
    'invalid_url': 'رابط غير صحيح.',
    'invalid_date': 'تاريخ غير صحيح.',
    'invalid_datetime': 'تاريخ ووقت غير صحيح.',
    'invalid_time': 'وقت غير صحيح.',
    'invalid_number': 'رقم غير صحيح.',
    'max_value': 'تأكد من أن هذه القيمة أقل من أو تساوي {max_value}.',
    'min_value': 'تأكد من أن هذه القيمة أكبر من أو تساوي {min_value}.',
}

def get_field_error_message(field_name, error_code, error_params=None):
    """
    إرجاع رسالة خطأ مخصصة لحقل معين
    """
    message = VALIDATION_ERROR_MESSAGES.get(error_code, 'قيمة غير صحيحة.')

    if error_params:
        try:
            message = message.format(**error_params)
        except (KeyError, ValueError):
            pass

    return f"{field_name}: {message}"

# Decorator لمعالجة الأخطاء في الـ views
def handle_api_errors(view_func):
    """
    Decorator لمعالجة الأخطاء في API views
    """
    def wrapper(*args, **kwargs):
        try:
            return view_func(*args, **kwargs)
        except ValidationError as e:
            return Response({
                'error': 'خطأ في التحقق من البيانات',
                'detail': 'البيانات المرسلة غير صحيحة. يرجى التحقق من المدخلات.',
                'errors': e.message_dict if hasattr(e, 'message_dict') else str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except PermissionDenied as e:
            return Response({
                'error': 'غير مصرح لك',
                'detail': 'ليس لديك الصلاحية للوصول إلى هذا المورد.',
                'code': 'permission_denied'
            }, status=status.HTTP_403_FORBIDDEN)
        except Http404 as e:
            return Response({
                'error': 'المورد غير موجود',
                'detail': 'المورد المطلوب غير موجود أو تم حذفه.',
                'code': 'not_found'
            }, status=status.HTTP_404_NOT_FOUND)
        except IntegrityError as e:
            return Response({
                'error': 'خطأ في قاعدة البيانات',
                'detail': 'حدث تضارب في البيانات. قد تكون البيانات مكررة أو غير صحيحة.',
                'code': 'integrity_error'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Unexpected error in {view_func.__name__}: {type(e).__name__}: {str(e)}")
            return Response({
                'error': 'خطأ داخلي في الخادم',
                'detail': 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً.',
                'code': 'internal_server_error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    return wrapper

# دالة مساعدة لإنشاء رسائل خطأ موحدة
def create_error_response(error_type, detail=None, code=None, status_code=status.HTTP_400_BAD_REQUEST):
    """
    إنشاء استجابة خطأ موحدة
    """
    error_data = {
        'error': error_type,
        'detail': detail or 'حدث خطأ غير متوقع.',
    }

    if code:
        error_data['code'] = code

    return Response(error_data, status=status_code)

# رسائل خطأ شائعة
class ErrorMessages:
    # أخطاء المصادقة
    AUTHENTICATION_REQUIRED = 'يجب تسجيل الدخول للوصول إلى هذا المورد.'
    INVALID_CREDENTIALS = 'بيانات الدخول غير صحيحة.'
    TOKEN_EXPIRED = 'انتهت صلاحية رمز المصادقة. يرجى تسجيل الدخول مرة أخرى.'
    EMAIL_NOT_VERIFIED = 'يرجى تأكيد البريد الإلكتروني أولاً.'

    # أخطاء الصلاحيات
    PERMISSION_DENIED = 'ليس لديك الصلاحية للوصول إلى هذا المورد.'
    ADMIN_REQUIRED = 'هذا المورد متاح للمديرين فقط.'
    INSTRUCTOR_REQUIRED = 'هذا المورد متاح للمدرسين فقط.'
    STUDENT_REQUIRED = 'هذا المورد متاح للطلاب فقط.'
    SUBSCRIPTION_REQUIRED = 'يجب أن يكون لديك اشتراك نشط للوصول إلى هذا المورد.'

    # أخطاء البيانات
    INVALID_DATA = 'البيانات المرسلة غير صحيحة.'
    REQUIRED_FIELD = 'هذا الحقل مطلوب.'
    DUPLICATE_ENTRY = 'هذه البيانات موجودة بالفعل.'
    INVALID_FORMAT = 'تنسيق البيانات غير صحيح.'

    # أخطاء الموارد
    NOT_FOUND = 'المورد المطلوب غير موجود.'
    ALREADY_EXISTS = 'هذا المورد موجود بالفعل.'
    CANNOT_DELETE = 'لا يمكن حذف هذا المورد.'

    # أخطاء النظام
    INTERNAL_ERROR = 'حدث خطأ داخلي في النظام. يرجى المحاولة لاحقاً.'
    SERVICE_UNAVAILABLE = 'الخدمة غير متاحة حالياً. يرجى المحاولة لاحقاً.'
    RATE_LIMIT_EXCEEDED = 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً.'
