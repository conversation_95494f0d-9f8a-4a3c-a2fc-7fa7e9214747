from django.shortcuts import render
from rest_framework import viewsets, permissions, status, views
from rest_framework.response import Response
from .models import StudentProfile, StudentProgress
from .serializers import StudentProfileSerializer, StudentProgressSerializer
from django.shortcuts import get_object_or_404
from rest_framework.decorators import action

# Create your views here.

class IsStudentOrAdmin(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and (getattr(request.user, 'is_student', False) or request.user.is_staff)
    def has_object_permission(self, request, view, obj):
        if request.user.is_staff:
            return True
        # Support both StudentProfile and StudentProgress
        if hasattr(obj, 'user'):
            return obj.user == request.user
        if hasattr(obj, 'student') and hasattr(obj.student, 'user'):
            return obj.student.user == request.user
        return False

class StudentProfileViewSet(viewsets.ModelViewSet):
    queryset = StudentProfile.objects.all()
    serializer_class = StudentProfileSerializer
    permission_classes = [IsStudentOrAdmin]

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return StudentProfile.objects.all()
        return StudentProfile.objects.filter(user=user)

    @action(detail=False, methods=['get', 'patch'], permission_classes=[permissions.IsAuthenticated])
    def me(self, request):
        # للأدمن والمدرسين: إرجاع معلومات أساسية بدلاً من student profile
        if getattr(request.user, 'is_staff', False) or getattr(request.user, 'is_instructor', False):
            if request.method == 'PATCH':
                # الأدمن والمدرسين لا يحتاجون لتحديث academic year
                return Response({
                    'detail': 'الأدمن والمدرسين لا يحتاجون لتحديث السنة الدراسية.'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'id': f'admin-{request.user.id}',  # معرف وهمي للأدمن
                'user': request.user.id,
                'user_email': request.user.email,
                'user_type': 'admin' if request.user.is_staff else 'instructor',
                'first_name': request.user.first_name,
                'last_name': request.user.last_name,
                'is_admin_access': True
            })

        # التحقق من أن المستخدم طالب
        if not getattr(request.user, 'is_student', False):
            return Response({
                'detail': 'هذا الـ endpoint مخصص للطلاب والأدمن والمدرسين فقط.'
            }, status=status.HTTP_403_FORBIDDEN)

        profile = get_object_or_404(StudentProfile, user=request.user)

        if request.method == 'PATCH':
            serializer = self.get_serializer(profile, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # GET request
        serializer = self.get_serializer(profile)
        return Response(serializer.data)

class StudentProgressViewSet(viewsets.ModelViewSet):
    queryset = StudentProgress.objects.all()
    serializer_class = StudentProgressSerializer
    permission_classes = [IsStudentOrAdmin]

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return StudentProgress.objects.all()

        queryset = StudentProgress.objects.filter(student__user=user)

        # Filter by student's academic year
        if hasattr(user, 'student_profile') and user.student_profile.academic_year:
            queryset = queryset.filter(semester__academic_year=user.student_profile.academic_year)

        return queryset

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def dashboard(self, request):
        # التحقق من أن المستخدم طالب وليس أدمن أو مدرس
        if not getattr(request.user, 'is_student', False):
            return Response({
                'detail': 'لوحة التحكم مخصصة للطلاب فقط. الأدمن والمدرسين لديهم لوحات تحكم منفصلة.'
            }, status=status.HTTP_403_FORBIDDEN)

        profile = get_object_or_404(StudentProfile, user=request.user)
        progress = StudentProgress.objects.filter(student=profile)

        # Filter by student's academic year
        if profile.academic_year:
            progress = progress.filter(semester__academic_year=profile.academic_year)

        serializer = self.get_serializer(progress, many=True)
        return Response({
            'profile': StudentProfileSerializer(profile).data,
            'progress': serializer.data,
        })
