from django.db import models
from django.conf import settings
from universities.models import University, AcademicYear
# from courses.models import Course  # Placeholder, to be replaced when Course is implemented

class StudentProfile(models.Model):
    id = models.AutoField(primary_key=True)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='student_profile')
    university = models.ForeignKey(University, on_delete=models.SET_NULL, null=True, related_name='students')
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.SET_NULL, null=True, related_name='students')
    student_id = models.CharField(max_length=50, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.email} - {self.student_id}"

class StudentProgress(models.Model):
    id = models.AutoField(primary_key=True)
    student = models.Foreign<PERSON>ey(StudentProfile, on_delete=models.CASCADE, related_name='progress')
    semester = models.ForeignKey('courses.Semester', on_delete=models.CASCADE, related_name='student_progress', null=True, blank=True)
    completed_lectures = models.IntegerField(default=0)
    total_lectures = models.IntegerField(default=0)
    quiz_scores = models.JSONField(default=dict)
    last_accessed = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.student} - Semester {self.semester}"
