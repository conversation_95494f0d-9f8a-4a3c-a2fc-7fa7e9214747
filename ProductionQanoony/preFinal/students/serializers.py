from rest_framework import serializers
from .models import Student<PERSON>rofile, StudentProgress
from universities.models import University, AcademicYear

class StudentProfileSerializer(serializers.ModelSerializer):
    user_email = serializers.EmailField(source='user.email', read_only=True)
    university_name = serializers.CharField(source='university.name_en', read_only=True)
    academic_year_name = serializers.CharField(source='academic_year.year_name_en', read_only=True)

    class Meta:
        model = StudentProfile
        fields = [
            'id', 'user', 'user_email', 'university', 'university_name',
            'academic_year', 'academic_year_name', 'student_id', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'user_email', 'university_name', 'academic_year_name']

class StudentProgressSerializer(serializers.ModelSerializer):
    student_id = serializers.CharField(source='student.student_id', read_only=True)

    class Meta:
        model = StudentProgress
        fields = [
            'id', 'student', 'student_id', 'semester', 'completed_lectures',
            'total_lectures', 'quiz_scores', 'last_accessed'
        ]
        read_only_fields = ['id', 'student_id', 'last_accessed']

    def validate(self, data):
        completed = data.get('completed_lectures', self.instance.completed_lectures if self.instance else 0)
        total = data.get('total_lectures', self.instance.total_lectures if self.instance else 0)
        if completed < 0 or total < 0:
            raise serializers.ValidationError('عدد المحاضرات لا يمكن أن يكون سالبًا')
        return data 