from django.contrib import admin
from .models import StudentProfile, StudentProgress

@admin.register(StudentProfile)
class StudentProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'student_id', 'university', 'academic_year', 'created_at')
    list_filter = ('university', 'academic_year')
    search_fields = ('student_id', 'user__email', 'university__name_en', 'academic_year__year_name_en')
    ordering = ('-created_at',)

@admin.register(StudentProgress)
class StudentProgressAdmin(admin.ModelAdmin):
    list_display = ('student', 'semester', 'completed_lectures', 'total_lectures', 'last_accessed')
    list_filter = ('student', 'semester')
    search_fields = ('student__student_id',)
    ordering = ('-last_accessed',)
