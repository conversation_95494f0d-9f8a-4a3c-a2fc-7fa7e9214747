# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('universities', '0001_initial'),
        ('courses', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StudentProfile',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('student_id', models.CharField(max_length=50, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('academic_year', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='students', to='universities.academicyear')),
                ('university', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='students', to='universities.university')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='student_profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='StudentProgress',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('completed_lectures', models.IntegerField(default=0)),
                ('total_lectures', models.IntegerField(default=0)),
                ('quiz_scores', models.JSONField(default=dict)),
                ('last_accessed', models.DateTimeField(auto_now=True)),
                ('semester', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='student_progress', to='courses.semester')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress', to='students.studentprofile')),
            ],
        ),
    ]
