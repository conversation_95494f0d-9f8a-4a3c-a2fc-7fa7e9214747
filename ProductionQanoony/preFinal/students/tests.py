from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from universities.models import University, AcademicYear
from .models import StudentProfile, StudentProgress

User = get_user_model()

class StudentProfileProgressTests(APITestCase):
    def setUp(self):
        self.admin = User.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='adminpass')
        self.student_user = User.objects.create_user(email='<EMAIL>', first_name='Student', last_name='User', password='studentpass', is_student=True)
        self.university = University.objects.create(name_ar='جامعة القاهرة', name_en='Cairo University', code='CU', city='Cairo')
        self.academic_year = AcademicYear.objects.create(university=self.university, year_number=1, year_name_ar='الفرقة الأولى', year_name_en='First Year')
        self.profile = StudentProfile.objects.create(user=self.student_user, university=self.university, academic_year=self.academic_year, student_id='S123')
        self.progress = StudentProgress.objects.create(student=self.profile, semester=None, completed_lectures=5, total_lectures=10, quiz_scores={"quiz1": 80})
        self.client = APIClient()

    def test_profile_view_update(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('studentprofile-detail', args=[self.profile.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['student_id'], 'S123')
        # Update
        response = self.client.patch(url, {'student_id': 'S124'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.profile.refresh_from_db()
        self.assertEqual(self.profile.student_id, 'S124')

    def test_dashboard(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('studentprogress-dashboard')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('profile', response.data)
        self.assertIn('progress', response.data)

    def test_progress_tracking(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('studentprogress-detail', args=[self.progress.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Update progress
        response = self.client.patch(url, {'completed_lectures': 7})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.progress.refresh_from_db()
        self.assertEqual(self.progress.completed_lectures, 7)

    def test_admin_can_view_all_profiles(self):
        self.client.force_authenticate(user=self.admin)
        url = reverse('studentprofile-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)

    def test_admin_can_view_all_progress(self):
        self.client.force_authenticate(user=self.admin)
        url = reverse('studentprogress-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)

    def test_profile_view_unauthenticated(self):
        url = reverse('studentprofile-detail', args=[self.profile.id])
        self.client.logout()
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_profile_update_forbidden(self):
        # Another student cannot update this profile
        other_user = User.objects.create_user(email='<EMAIL>', first_name='Other', last_name='Student', password='otherpass', is_student=True)
        self.client.force_authenticate(user=other_user)
        url = reverse('studentprofile-detail', args=[self.profile.id])
        response = self.client.patch(url, {'student_id': 'HACKED'})
        self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND])

    def test_profile_update_invalid(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('studentprofile-detail', args=[self.profile.id])
        response = self.client.patch(url, {'student_id': ''})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_profile_not_found(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('studentprofile-detail', args=[9999])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_progress_view_unauthenticated(self):
        url = reverse('studentprogress-detail', args=[self.progress.id])
        self.client.logout()
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_progress_update_forbidden(self):
        other_user = User.objects.create_user(email='<EMAIL>', first_name='Other', last_name='Student', password='otherpass', is_student=True)
        self.client.force_authenticate(user=other_user)
        url = reverse('studentprogress-detail', args=[self.progress.id])
        response = self.client.patch(url, {'completed_lectures': 99})
        self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND])

    def test_progress_update_invalid(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('studentprogress-detail', args=[self.progress.id])
        response = self.client.patch(url, {'completed_lectures': -1})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_progress_not_found(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('studentprogress-detail', args=[9999])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
