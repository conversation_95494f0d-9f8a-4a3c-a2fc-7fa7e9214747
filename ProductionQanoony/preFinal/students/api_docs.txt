# Students API Documentation

## 1. Student Dashboard
- **Endpoint:** /api/students/progress/dashboard/
- **Method:** GET
- **Description:** Get an overview of the student's profile and progress.
- **Permissions:** IsAuthenticated (student only)
- **Request Example:**
GET /api/students/progress/dashboard/
- **Response Example:**
200 OK
{
  "profile": {
    "id": 1,
    "user": 2,
    "user_email": "<EMAIL>",
    "university": 1,
    "university_name": "Cairo University",
    "academic_year": 1,
    "academic_year_name": "First Year",
    "student_id": "S123",
    "created_at": "2024-06-01T12:00:00Z",
    "updated_at": "2024-06-01T12:00:00Z"
  },
  "progress": [
    {
      "id": 1,
      "student": 1,
      "student_id": "S123",
      "course": 1,
      "completed_lectures": 5,
      "total_lectures": 10,
      "quiz_scores": {"quiz1": 80},
      "last_accessed": "2024-06-01T12:00:00Z"
    }
  ]
}

## 2. Student Profile (View/Update)
- **Endpoint:** /api/students/profiles/{id}/
- **Method:** GET, PATCH
- **Description:** View or update the authenticated student's profile.
- **Permissions:** IsAuthenticated (student can only access own profile, admin can access all)
- **Request Example (PATCH):**
{
  "student_id": "S124"
}
- **Response Example (GET):**
200 OK
{
  "id": 1,
  "user": 2,
  "user_email": "<EMAIL>",
  "university": 1,
  "university_name": "Cairo University",
  "academic_year": 1,
  "academic_year_name": "First Year",
  "student_id": "S124",
  "created_at": "2024-06-01T12:00:00Z",
  "updated_at": "2024-06-01T12:00:00Z"
}

## 3. Student Profile (Me)
- **Endpoint:** /api/students/profiles/me/
- **Method:** GET
- **Description:** Get the authenticated student's own profile.
- **Permissions:** IsAuthenticated (student only)
- **Request Example:**
GET /api/students/profiles/me/
- **Response Example:**
200 OK
{
  ... (same as above)
}

## 4. Student Progress (View/Update)
- **Endpoint:** /api/students/progress/{id}/
- **Method:** GET, PATCH
- **Description:** View or update the student's progress for a course.
- **Permissions:** IsAuthenticated (student can only access own progress, admin can access all)
- **Request Example (PATCH):**
{
  "completed_lectures": 7
}
- **Response Example (GET):**
200 OK
{
  "id": 1,
  "student": 1,
  "student_id": "S123",
  "course": 1,
  "completed_lectures": 7,
  "total_lectures": 10,
  "quiz_scores": {"quiz1": 80},
  "last_accessed": "2024-06-01T12:00:00Z"
}

## 5. Admin: List All Student Profiles
- **Endpoint:** /api/students/profiles/
- **Method:** GET
- **Description:** Admin can list all student profiles.
- **Permissions:** IsAdminUser
- **Request Example:**
GET /api/students/profiles/
- **Response Example:**
200 OK
[
  { ... }
]

## 6. Admin: List All Student Progress
- **Endpoint:** /api/students/progress/
- **Method:** GET
- **Description:** Admin can list all student progress records.
- **Permissions:** IsAdminUser
- **Request Example:**
GET /api/students/progress/
- **Response Example:**
200 OK
[
  { ... }
] 