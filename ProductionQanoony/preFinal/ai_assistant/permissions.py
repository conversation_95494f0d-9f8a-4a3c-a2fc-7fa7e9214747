import logging
from rest_framework.permissions import BasePermission
from subscriptions.models import Subscription
from django.utils import timezone

logger = logging.getLogger(__name__)

class IsSubscribedAndVerified(BasePermission):
    message = 'يجب أن تكون مشتركًا وموثق البريد الإلكتروني للوصول إلى مساعد الذكاء الاصطناعي.'

    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            logger.warning("AI Assistant Permission denied: User is not authenticated.")
            self.message = 'يجب تسجيل الدخول أولاً.'
            return False

        logger.info(f"AI Assistant Check for user '{user.email}' (ID: {user.id})")

        # السماح للأدمن والمدرسين بالوصول مباشرة بدون التحقق من الاشتراك
        if getattr(user, 'is_staff', False) or getattr(user, 'is_instructor', False):
            logger.info(f"Admin/Instructor {user.email} granted direct access to AI assistant")
            return True

        # Check email verification للطلاب فقط
        email_verified = getattr(user, 'email_verified', False)
        logger.info(f"Email verification status: {email_verified}")

        if not email_verified:
            self.message = 'يرجى توثيق بريدك الإلكتروني أولاً.'
            logger.warning(f"AI Assistant Permission denied for '{user.email}': Email not verified.")
            return False

        # Check active subscription
        now = timezone.now().date()
        logger.info(f"Checking subscriptions for date: {now}")
        
        # Get all subscriptions for the user
        all_subscriptions = Subscription.objects.filter(user=user)
        logger.info(f"Total subscriptions found: {all_subscriptions.count()}")
        
        for sub in all_subscriptions:
            logger.info(f"Subscription ID {sub.id}: status='{sub.status}', is_active={sub.is_active}, end_date={sub.end_date}, plan={sub.plan}")
        
        active_subscriptions = Subscription.objects.filter(
            user=user,
            status='active',
            end_date__gte=now
        )
        has_active = active_subscriptions.exists()

        logger.info(f"Active subscriptions count: {active_subscriptions.count()}")
        logger.info(f"Access granted: {has_active}")

        if not has_active:
            self.message = 'يجب أن يكون لديك اشتراك نشط للوصول إلى هذه الخدمة.'
            logger.warning(f"AI Assistant Permission denied for '{user.email}': No active subscription found.")
            return False

        logger.info(f"AI Assistant access GRANTED for user '{user.email}'")
        return True

class IsStaffOrActiveSubscriptionOrDenied(BasePermission):
    message = 'يجب أن تكون موظفًا (staff) أو مدرسًا أو لديك اشتراك نشط للوصول.'

    def has_permission(self, request, view):
        user = request.user
        if not user or not user.is_authenticated:
            return False
        
        # إذا كان المستخدم staff، امنحه الوصول مباشرة
        if getattr(user, 'is_staff', False):
            return True
            
        # إذا كان المستخدم مدرس، امنحه الوصول
        if getattr(user, 'is_instructor', False):
            return True
            
        # للآخرين، تحقق من الاشتراك النشط
        now = timezone.now().date()
        has_active = Subscription.objects.filter(
            user=user,
            status='active',
            is_active=True,
            end_date__gte=now
        ).exists()
        if has_active:
            return True
        self.message = 'يجب أن تكون موظفًا (staff) أو مدرسًا أو لديك اشتراك نشط للوصول.'
        return False 