AI Assistant API Endpoints Documentation
======================================

Access Requirements:
--------------------
- Any authenticated, email-verified user with an active subscription (student, instructor, admin, etc.) can access the AI assistant endpoints.
- No StudentProfile is required.

1. List/Create Conversations
---------------------------
URL: /api/ai-assistant/conversations/
Method: GET, POST
Description: List all conversations for the authenticated user or create a new conversation.
Permissions: IsSubscribedAndVerified (Authenticated, email-verified, active subscription)
Sample Request (POST):
{
  "student": 1,
  "title_ar": "محادثة قانونية",
  "context_type": "legal_question",
  "course": 1
}
Sample Response (201):
{
  "id": 1,
  "student": 1,
  "title_ar": "محادثة قانونية",
  "context_type": "legal_question",
  "course": 1,
  ...
}

2. Conversation History
----------------------
URL: /api/ai-assistant/conversations/{id}/history/
Method: GET
Description: Get all messages in a conversation.
Permissions: IsSubscribedAndVerified (Authenticated, email-verified, active subscription)
Sample Response (200):
[
  {"id": 1, "message_type": "user", "content_ar": "...", ...},
  {"id": 2, "message_type": "assistant", "content_ar": "...", ...}
]

3. Send Message to AI
---------------------
URL: /api/ai-assistant/messages/send/
Method: POST
Description: Send a message to the AI assistant and receive a reply.
Permissions: IsSubscribedAndVerified (Authenticated, email-verified, active subscription)
Sample Request:
{
  "conversation": 1,
  "content_ar": "ما هو القانون؟"
}
Sample Response (200):
{
  "user_message": {"id": 1, "message_type": "user", "content_ar": "ما هو القانون؟", ...},
  "assistant_message": {"id": 2, "message_type": "assistant", "content_ar": "القانون هو...", ...}
}

4. Rate AI Message
------------------
URL: /api/ai-assistant/messages/{id}/rate/
Method: POST
Description: Rate an AI response message (1-5 stars, optional comment).
Permissions: IsSubscribedAndVerified (Authenticated, email-verified, active subscription)
Sample Request:
{
  "rating": 4,
  "comment": "مفيد جداً"
}
Sample Response (200):
{
  "status": "تم التقييم",
  "feedback": {"user": 1, "message": 2, "rating": 4, "comment": "مفيد جداً", ...}
}

5. List/Create Prompt Templates (Admin)
---------------------------------------
URL: /api/ai-assistant/prompt-templates/
Method: GET, POST
Description: List or create prompt templates (admin only).
Permissions: IsAdminUser

6. List/Create Usage Limits (Admin)
-----------------------------------
URL: /api/ai-assistant/usage-limits/
Method: GET, POST
Description: List or create usage limits for users (admin only).
Permissions: IsAdminUser

Notes:
------
- All endpoints require JWT authentication.
- All user-facing messages are in Arabic.
- Any authenticated, email-verified user with an active subscription can access AI endpoints. 