from django.contrib import admin
from .models import AIConversation, AIMessage, AIPromptTemplate, AIUsageLimit, AIFeedback

@admin.register(AIConversation)
class AIConversationAdmin(admin.ModelAdmin):
    list_display = ('title_ar', 'student', 'context_type', 'semester', 'is_active', 'created_at', 'updated_at')
    search_fields = ('title_ar', 'student__user__email')
    list_filter = ('context_type', 'is_active', 'created_at', 'updated_at')
    ordering = ('-created_at',)

@admin.register(AIMessage)
class AIMessageAdmin(admin.ModelAdmin):
    list_display = ('conversation', 'message_type', 'timestamp', 'tokens_used', 'response_time_seconds', 'is_helpful')
    search_fields = ('conversation__title_ar', 'content_ar')
    list_filter = ('message_type', 'is_helpful', 'timestamp')
    ordering = ('-timestamp',)

@admin.register(AIPromptTemplate)
class AIPromptTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'is_active', 'created_at')
    search_fields = ('name', 'template_text')
    list_filter = ('category', 'is_active', 'created_at')
    ordering = ('-created_at',)

@admin.register(AIUsageLimit)
class AIUsageLimitAdmin(admin.ModelAdmin):
    list_display = ('student', 'daily_limit', 'monthly_limit', 'daily_used', 'monthly_used', 'last_reset_date')
    search_fields = ('student__user__email',)
    list_filter = ('last_reset_date',)
    ordering = ('-last_reset_date',)

@admin.register(AIFeedback)
class AIFeedbackAdmin(admin.ModelAdmin):
    list_display = ('user', 'message', 'rating', 'comment', 'created_at')
    search_fields = ('user__email', 'comment')
    list_filter = ('rating', 'created_at')
    ordering = ('-created_at',)
