from rest_framework import serializers
from .models import AIConversation, AIMessage, AIPromptTemplate, AIUsageLimit, AIFeedback
from django.utils import timezone

class AIConversationSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIConversation
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class AIMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIMessage
        fields = '__all__'
        read_only_fields = ('timestamp',)

class AIPromptTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIPromptTemplate
        fields = '__all__'
        read_only_fields = ('created_at',)

    def validate_category(self, value):
        allowed = [choice[0] for choice in AIPromptTemplate.CATEGORY_CHOICES]
        if value not in allowed:
            raise serializers.ValidationError('فئة القالب غير صالحة')
        return value

class AIUsageLimitSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIUsageLimit
        fields = '__all__'

    def validate(self, data):
        if data.get('daily_limit', 0) < 0 or data.get('monthly_limit', 0) < 0:
            raise serializers.ValidationError('الحدود يجب أن تكون أرقاماً موجبة')
        if data.get('daily_used', 0) < 0 or data.get('monthly_used', 0) < 0:
            raise serializers.ValidationError('الاستخدام يجب أن يكون رقماً موجباً')
        return data 

class AIFeedbackSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIFeedback
        fields = '__all__'
        read_only_fields = ('created_at',)

    def validate_rating(self, value):
        if not (1 <= value <= 5):
            raise serializers.ValidationError('التقييم يجب أن يكون بين 1 و 5')
        return value 