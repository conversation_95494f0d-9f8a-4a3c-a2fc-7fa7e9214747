import logging
from django.shortcuts import render
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Count, Avg, Sum, Q, F
from django.utils import timezone
from datetime import timed<PERSON>ta
from django.db import models
from .models import AIConversation, AIMessage, AIPromptTemplate, AIUsageLimit, AIFeedback
from .serializers import (
    AIConversationSerializer, AIMessageSerializer, AIPromptTemplateSerializer, AIUsageLimitSerializer, AIFeedbackSerializer
)
from students.models import StudentProfile
from django.utils import timezone
from django.conf import settings
from .services import send_to_openrouter, OpenRouterAPIError
from .permissions import IsSubscribedAndVerified
from rest_framework.permissions import IsAuthenticated
from authentication.models import CustomUser

logger = logging.getLogger(__name__)

# Create your views here.

class IsStudent(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'student_profile')

class IsAdminOrReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated
        return request.user.is_staff

class AIConversationViewSet(viewsets.ModelViewSet):
    serializer_class = AIConversationSerializer
    permission_classes = [IsSubscribedAndVerified]

    def get_queryset(self):
        user = self.request.user

        # السماح للأدمن بمشاهدة كل المحادثات
        if getattr(user, 'is_staff', False):
            return AIConversation.objects.all()

        # السماح للمدرسين بمشاهدة كل المحادثات (أو يمكن تخصيصها حسب الحاجة)
        if getattr(user, 'is_instructor', False):
            return AIConversation.objects.all()

        # للطلاب فقط محادثاتهم
        if not getattr(user, 'is_student', False):
            return AIConversation.objects.none()

        # Auto-create StudentProfile if it doesn't exist for a student user
        student, created = StudentProfile.objects.get_or_create(
            user=user,
            defaults={'university_id': 1, 'academic_year_id': 1, 'student_id': f'AUTO-{user.id}'}
        )
        return AIConversation.objects.filter(student=student)

    @action(detail=False, methods=['post'], permission_classes=[IsSubscribedAndVerified])
    def create_admin_conversation(self, request):
        """إنشاء محادثة جديدة للأدمن والمدرسين بدون student profile"""
        user = request.user

        # فقط للأدمن والمدرسين
        if not (getattr(user, 'is_staff', False) or getattr(user, 'is_instructor', False)):
            return Response({'detail': 'هذا الـ endpoint مخصص للأدمن والمدرسين فقط'}, status=status.HTTP_403_FORBIDDEN)

        # إنشاء student profile وهمي للأدمن/المدرس إذا لم يكن موجود
        # للأدمن والمدرسين، نستخدم أول جامعة وسنة دراسية متاحة أو null
        from universities.models import University, AcademicYear

        first_university = University.objects.first()
        first_academic_year = AcademicYear.objects.first()

        student, created = StudentProfile.objects.get_or_create(
            user=user,
            defaults={
                'university': first_university,
                'academic_year': first_academic_year,
                'student_id': f'ADMIN-{user.id}' if user.is_staff else f'INSTRUCTOR-{user.id}'
            }
        )

        # إنشاء محادثة جديدة
        conversation = AIConversation.objects.create(
            student=student,
            title_ar=request.data.get('title_ar', 'محادثة إدارية'),
            context_type=request.data.get('context_type', 'general')
        )

        serializer = self.get_serializer(conversation)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['get'], permission_classes=[IsSubscribedAndVerified])
    def history(self, request, pk=None):
        conversation = self.get_object()
        messages = conversation.messages.all().order_by('timestamp')
        serializer = AIMessageSerializer(messages, many=True)
        return Response(serializer.data)

class AIMessageViewSet(viewsets.ModelViewSet):
    serializer_class = AIMessageSerializer
    permission_classes = [IsSubscribedAndVerified]

    def get_queryset(self):
        user = self.request.user

        # السماح للأدمن بمشاهدة كل الرسائل
        if getattr(user, 'is_staff', False):
            return AIMessage.objects.all()

        # السماح للمدرسين بمشاهدة كل الرسائل
        if getattr(user, 'is_instructor', False):
            return AIMessage.objects.all()

        # للطلاب فقط رسائلهم
        if not getattr(user, 'is_student', False):
            return AIMessage.objects.none()

        student, created = StudentProfile.objects.get_or_create(
            user=user,
            defaults={'university_id': 1, 'academic_year_id': 1, 'student_id': f'AUTO-{user.id}'}
        )
        return AIMessage.objects.filter(conversation__student=student)

    @action(detail=True, methods=['post'], permission_classes=[IsSubscribedAndVerified])
    def rate(self, request, pk=None):
        message = self.get_object()
        serializer = AIFeedbackSerializer(data={
            'user': request.user.id,
            'message': message.id,
            'rating': request.data.get('rating'),
            'comment': request.data.get('comment', '')
        })
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({'status': 'تم التقييم', 'feedback': serializer.data})

    @action(detail=False, methods=['post'], permission_classes=[IsSubscribedAndVerified])
    def send(self, request):
        """Send a message to the AI assistant (OpenRouter integration)."""
        conversation_id = request.data.get('conversation')
        content_ar = request.data.get('content_ar')
        if not conversation_id or not content_ar:
            return Response({'detail': 'يجب تحديد المحادثة والمحتوى'}, status=status.HTTP_400_BAD_REQUEST)
        # التحقق من وجود المحادثة
        try:
            # للأدمن والمدرسين: يمكنهم الوصول لأي محادثة
            if getattr(request.user, 'is_staff', False) or getattr(request.user, 'is_instructor', False):
                conversation = AIConversation.objects.get(id=conversation_id)
            else:
                # للطلاب: فقط محادثاتهم
                conversation = AIConversation.objects.get(id=conversation_id, student__user=request.user)
        except AIConversation.DoesNotExist:
            return Response({'detail': 'المحادثة غير موجودة'}, status=status.HTTP_404_NOT_FOUND)

        # Usage limit enforcement - فقط للطلاب، الأدمن والمدرسين بدون حدود
        usage = None
        if getattr(request.user, 'is_student', False):
            try:
                usage = AIUsageLimit.objects.get(student=conversation.student)
            except AIUsageLimit.DoesNotExist:
                return Response({'detail': 'لا يوجد حد استخدام معرف لهذا الطالب.'}, status=status.HTTP_403_FORBIDDEN)
            today = timezone.now().date()
            if usage.last_reset_date != today:
                usage.daily_used = 0
                usage.last_reset_date = today
                usage.save()
            if usage.daily_used >= usage.daily_limit:
                return Response({'detail': 'لقد تجاوزت الحد اليومي لاستخدام مساعد الذكاء الاصطناعي.'}, status=status.HTTP_403_FORBIDDEN)
            if usage.monthly_used >= usage.monthly_limit:
                return Response({'detail': 'لقد تجاوزت الحد الشهري لاستخدام مساعد الذكاء الاصطناعي.'}, status=status.HTTP_403_FORBIDDEN)
        # Save user message
        user_msg = AIMessage.objects.create(
            conversation=conversation,
            message_type='user',
            content_ar=content_ar,
            tokens_used=len(content_ar.split()),
            response_time_seconds=0.01
        )
        # Call OpenRouter API
        try:
            api_key = getattr(settings, 'OPENROUTER_API_KEY', None)
            referer = getattr(settings, 'OPENROUTER_REFERER', None)
            result = send_to_openrouter(
                prompt=content_ar,
                model="mistralai/mistral-small-3.2-24b-instruct:free",
                api_key=api_key,
                referer=referer,
                conversation=None  # Optionally pass conversation context
            )

            # Check if there's an error in the response
            if 'error' in result:
                return Response({'detail': result['error']}, status=status.HTTP_400_BAD_REQUEST)

            ai_response = result['reply']
            usage_data = result.get('usage', {})
            tokens_used = usage_data.get('total_tokens', 0)
            response_time = usage_data.get('response_time', 0.01)
        except OpenRouterAPIError as e:
            logger.error(f'OpenRouter API Error: {str(e)}')
            return Response({'detail': f'خطأ في الاتصال بمساعد الذكاء الاصطناعي: {str(e)}'}, status=status.HTTP_502_BAD_GATEWAY)
        except Exception as e:
            import traceback
            logger.error(f'Unexpected error in AI send: {str(e)}')
            logger.error(f'Traceback: {traceback.format_exc()}')
            return Response({'detail': f'حدث خطأ غير متوقع: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        # Save assistant message
        ai_msg = AIMessage.objects.create(
            conversation=conversation,
            message_type='assistant',
            content_ar=ai_response,
            tokens_used=tokens_used,
            response_time_seconds=response_time
        )
        # Increment usage counters - فقط للطلاب
        if usage:  # usage موجود فقط للطلاب
            usage.daily_used += 1
            usage.monthly_used += 1
            usage.save()
        return Response({
            'user_message': AIMessageSerializer(user_msg).data,
            'assistant_message': AIMessageSerializer(ai_msg).data
        })

class AIPromptTemplateViewSet(viewsets.ModelViewSet):
    queryset = AIPromptTemplate.objects.all()
    serializer_class = AIPromptTemplateSerializer
    permission_classes = [permissions.IsAdminUser]
    filterset_fields = ['category', 'is_active']
    search_fields = ['name', 'template_text']

class AIUsageLimitViewSet(viewsets.ModelViewSet):
    queryset = AIUsageLimit.objects.all()
    serializer_class = AIUsageLimitSerializer
    permission_classes = [permissions.IsAdminUser]
    filterset_fields = ['student']
    search_fields = ['student__user__email']


class AIStatsView(APIView):
    """
    Admin view for AI assistant statistics
    """
    permission_classes = [permissions.IsAdminUser]

    def get(self, request):
        """Get AI assistant statistics for admin dashboard"""
        try:
            # Basic counts
            total_conversations = AIConversation.objects.count()
            active_conversations = AIConversation.objects.filter(is_active=True).count()
            total_messages = AIMessage.objects.count()
            user_messages = AIMessage.objects.filter(message_type='user').count()
            assistant_messages = AIMessage.objects.filter(message_type='assistant').count()

            # Token usage
            total_tokens_used = AIMessage.objects.aggregate(
                total=Sum('tokens_used')
            )['total'] or 0

            # Response time statistics
            avg_response_time = AIMessage.objects.filter(
                message_type='assistant'
            ).aggregate(
                avg=Avg('response_time_seconds')
            )['avg'] or 0

            # User satisfaction (helpful messages)
            helpful_messages = AIMessage.objects.filter(is_helpful=True).count()
            total_rated_messages = AIMessage.objects.filter(is_helpful__isnull=False).count()
            satisfaction_rating = (helpful_messages / total_rated_messages * 5) if total_rated_messages > 0 else 0

            # Recent activity (last 7 days)
            week_ago = timezone.now() - timedelta(days=7)
            recent_conversations = AIConversation.objects.filter(created_at__gte=week_ago).count()
            recent_messages = AIMessage.objects.filter(timestamp__gte=week_ago).count()

            # Daily and monthly usage
            today = timezone.now().date()
            month_start = today.replace(day=1)
            daily_usage = AIMessage.objects.filter(timestamp__date=today).count()
            monthly_usage = AIMessage.objects.filter(timestamp__date__gte=month_start).count()

            # Context types distribution
            context_types = AIConversation.objects.values('context_type').annotate(
                count=Count('id')
            ).order_by('-count')

            # Calculate percentages for context types
            total_for_percentage = sum(item['count'] for item in context_types)
            for item in context_types:
                item['percentage'] = round((item['count'] / total_for_percentage * 100), 1) if total_for_percentage > 0 else 0

            # Most active users (by conversation count)
            active_users = AIConversation.objects.values(
                'student__user__email'
            ).annotate(
                conversation_count=Count('id')
            ).order_by('-conversation_count')[:5]

            # Usage limits statistics
            usage_limits = AIUsageLimit.objects.all()
            users_near_daily_limit = usage_limits.filter(
                daily_used__gte=F('daily_limit') * 0.8
            ).count()
            users_near_monthly_limit = usage_limits.filter(
                monthly_used__gte=F('monthly_limit') * 0.8
            ).count()

            return Response({
                'basic_stats': {
                    'total_conversations': total_conversations,
                    'active_conversations': active_conversations,
                    'total_messages': total_messages,
                    'user_messages': user_messages,
                    'assistant_messages': assistant_messages,
                    'total_tokens_used': total_tokens_used,
                    'average_response_time': round(avg_response_time, 2),
                    'user_satisfaction_rating': round(satisfaction_rating, 1),
                    'daily_usage': daily_usage,
                    'monthly_usage': monthly_usage,
                },
                'recent_activity': {
                    'recent_conversations': recent_conversations,
                    'recent_messages': recent_messages,
                },
                'distributions': {
                    'context_types': list(context_types),
                },
                'top_users': list(active_users),
                'usage_alerts': {
                    'users_near_daily_limit': users_near_daily_limit,
                    'users_near_monthly_limit': users_near_monthly_limit,
                }
            })

        except Exception as e:
            return Response(
                {'error': 'Failed to fetch AI stats', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
