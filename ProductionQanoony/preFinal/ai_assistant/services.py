import requests
import logging
import re

OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"

class OpenRouterAPIError(Exception):
    pass

legal_keywords = [
    "قانون", "تشريع", "نظام", "مادة", "نص قانوني", "دستور", "لوائح", "قواعد",
    "محكمة", "قاضي", "نيابة", "دفاع", "محامي", "هيئة قضائية", "مجلس دولة", "محكمة تمييز",
    "عقد", "اتفاقية", "شرط", "التزام", "إبرام", "فسخ", "بند", "تنفيذ عقد",
    "جريمة", "جنحة", "عقوبة", "ضبطية", "تحقيق", "تهمة", "حبس", "غرامة",
    "مدني", "جزائي", "تجاري", "دستوري", "إداري", "عمل", "أحوال شخصية", "ضريبي",
    "حق", "واجب", "ملكية", "تعويض", "مسؤولية", "دعوى", "إثبات", "حماية",
    "وزارة العدل", "النيابة العامة", "السجل التجاري", "هيئة التحكيم", "جهة مختصة", "سلطة قضائية"
]

def is_legal_prompt(prompt):
    """
    Check if the prompt contains legal keywords using regex pattern matching.
    Ignores case and Arabic definite articles.
    """
    prompt_processed = re.sub(r'\bال', '', prompt.lower())
    legal_keywords_processed = [re.sub(r'\bال', '', keyword.lower()) for keyword in legal_keywords]
    pattern = r'\b(?:' + '|'.join(map(re.escape, legal_keywords_processed)) + r')\b'
    return bool(re.search(pattern, prompt_processed))

def send_to_openrouter(prompt, model="mistralai/mistral-small-3.2-24b-instruct:free", api_key=None, referer=None, conversation=None):
    """
    Send a prompt to OpenRouter and return the assistant's reply and token usage.
    """
    # Check if prompt is legal
    if not is_legal_prompt(prompt):
        return {"error": "لا يمكن الرد على الأسئلة غير القانونية. هذه الخدمة مخصصة للأسئلة القانونية فقط."}
    
    if not api_key:
        raise ValueError("OpenRouter API key is required.")
    if not referer:
        raise ValueError("HTTP Referer is required.")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "HTTP-Referer": referer,
        "Content-Type": "application/json",
    }
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": prompt}
        ]
    }
    if conversation:
        # Optionally add conversation context for continuity
        data["conversation_id"] = conversation

    # Print payload and headers (hide API key for security)
    print("OpenRouter payload:", data)
    print("OpenRouter headers:", {**headers, "Authorization": "Bearer ***"})

    try:
        response = requests.post(OPENROUTER_API_URL, json=data, headers=headers, timeout=30)
        response.raise_for_status()
        result = response.json()
        # Parse the response to extract the assistant's reply and token usage
        reply = result["choices"][0]["message"]["content"]
        usage = result.get("usage", {})
        return {
            "reply": reply,
            "usage": usage,
            "raw": result
        }
    except requests.RequestException as e:
        error_msg = f"فشل في الاتصال بالخادم: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}
    except (KeyError, IndexError) as e:
        error_msg = f"استجابة غير متوقعة من الخادم: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg} 