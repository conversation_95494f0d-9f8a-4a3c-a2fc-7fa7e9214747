from django.db import models
from students.models import StudentProfile
from courses.models import Semester
from django.conf import settings

# Create your models here.

class AIConversation(models.Model):
    CONTEXT_TYPE_CHOICES = [
        ('legal_question', 'سؤال قانوني عام'),
        ('civil_law', 'القانون المدني'),
        ('criminal_law', 'القانون الجنائي'),
        ('commercial_law', 'القانون التجاري'),
        ('constitutional_law', 'القانون الدستوري'),
        ('course_help', 'مساعدة في المادة الدراسية'),
        ('general', 'استفسار عام'),
    ]
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE, related_name='ai_conversations', verbose_name='الطالب')
    title_ar = models.CharField(max_length=255, verbose_name='عنوان المحادثة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    is_active = models.BooleanField(default=True, verbose_name='نشطة')
    context_type = models.CharField(max_length=30, choices=CONTEXT_TYPE_CHOICES, verbose_name='نوع السياق')
    semester = models.ForeignKey(Semester, on_delete=models.SET_NULL, null=True, blank=True, related_name='ai_conversations', verbose_name='الفصل الدراسي')

    def __str__(self):
        return f"{self.title_ar} ({self.student.user.email})"

class AIMessage(models.Model):
    MESSAGE_TYPE_CHOICES = [
        ('user', 'User'),
        ('assistant', 'Assistant'),
    ]
    conversation = models.ForeignKey(AIConversation, on_delete=models.CASCADE, related_name='messages', verbose_name='المحادثة')
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPE_CHOICES, verbose_name='نوع الرسالة')
    content_ar = models.TextField(verbose_name='المحتوى (عربي)')
    tokens_used = models.IntegerField(verbose_name='عدد الرموز المستخدمة')
    response_time_seconds = models.DecimalField(max_digits=6, decimal_places=2, verbose_name='زمن الاستجابة (ثواني)')
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name='الطابع الزمني')
    is_helpful = models.BooleanField(null=True, blank=True, verbose_name='مفيد؟')

    def __str__(self):
        return f"{self.get_message_type_display()} - {self.timestamp}"

class AIPromptTemplate(models.Model):
    CATEGORY_CHOICES = [
        ('legal_summary', 'Legal Summary'),
        ('law_explanation', 'Law Explanation'),
        ('quiz_help', 'Quiz Help'),
    ]
    name = models.CharField(max_length=100, verbose_name='اسم القالب')
    template_text = models.TextField(verbose_name='نص القالب')
    category = models.CharField(max_length=30, choices=CATEGORY_CHOICES, verbose_name='الفئة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def __str__(self):
        return self.name

class AIUsageLimit(models.Model):
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE, related_name='ai_usage_limits', verbose_name='الطالب')
    daily_limit = models.IntegerField(verbose_name='الحد اليومي')
    monthly_limit = models.IntegerField(verbose_name='الحد الشهري')
    daily_used = models.IntegerField(default=0, verbose_name='المستخدم يومياً')
    monthly_used = models.IntegerField(default=0, verbose_name='المستخدم شهرياً')
    last_reset_date = models.DateField(verbose_name='تاريخ آخر إعادة تعيين')

    def __str__(self):
        return f"{self.student.user.email} - Limits"

class AIFeedback(models.Model):
    RATING_CHOICES = [
        (1, '1'),
        (2, '2'),
        (3, '3'),
        (4, '4'),
        (5, '5'),
    ]
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='ai_feedbacks', verbose_name='المستخدم')
    message = models.ForeignKey(AIMessage, on_delete=models.CASCADE, related_name='feedbacks', verbose_name='رسالة الذكاء الاصطناعي')
    rating = models.PositiveSmallIntegerField(choices=RATING_CHOICES, verbose_name='التقييم')
    comment = models.TextField(blank=True, null=True, verbose_name='تعليق (اختياري)')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    def __str__(self):
        return f"{self.user.email} - {self.rating} stars on message {self.message.id}"
