from celery import shared_task
from django.utils import timezone
from .models import AIUsageLimit
import logging

logger = logging.getLogger(__name__)

@shared_task
def reset_daily_usage():
    today = timezone.now().date()
    updated = AIUsageLimit.objects.all().update(daily_used=0)
    logger.info(f"[AI Assistant] Daily usage reset for {updated} users on {today}")

@shared_task
def reset_monthly_usage():
    today = timezone.now().date()
    updated = AIUsageLimit.objects.all().update(monthly_used=0, last_reset_date=today)
    logger.info(f"[AI Assistant] Monthly usage reset for {updated} users on {today}") 