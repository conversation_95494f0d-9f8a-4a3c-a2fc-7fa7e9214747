# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('students', '0001_initial'),
        ('ai_assistant', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='aiusagelimit',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_usage_limits', to='students.studentprofile', verbose_name='الطالب'),
        ),
        migrations.AddField(
            model_name='aimessage',
            name='conversation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='ai_assistant.aiconversation', verbose_name='المحادثة'),
        ),
        migrations.AddField(
            model_name='aifeedback',
            name='message',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedbacks', to='ai_assistant.aimessage', verbose_name='رسالة الذكاء الاصطناعي'),
        ),
        migrations.AddField(
            model_name='aifeedback',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_feedbacks', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم'),
        ),
        migrations.AddField(
            model_name='aiconversation',
            name='semester',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ai_conversations', to='courses.semester', verbose_name='الفصل الدراسي'),
        ),
        migrations.AddField(
            model_name='aiconversation',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_conversations', to='students.studentprofile', verbose_name='الطالب'),
        ),
    ]
