# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AIConversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_ar', models.CharField(max_length=255, verbose_name='عنوان المحادثة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('context_type', models.CharField(choices=[('legal_question', 'سؤال قانوني عام'), ('civil_law', 'القانون المدني'), ('criminal_law', 'القانون الجنائي'), ('commercial_law', 'القانون التجاري'), ('constitutional_law', 'القانون الدستوري'), ('course_help', 'مساعدة في المادة الدراسية'), ('general', 'استفسار عام')], max_length=30, verbose_name='نوع السياق')),
            ],
        ),
        migrations.CreateModel(
            name='AIFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveSmallIntegerField(choices=[(1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5')], verbose_name='التقييم')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='تعليق (اختياري)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
        ),
        migrations.CreateModel(
            name='AIMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('user', 'User'), ('assistant', 'Assistant')], max_length=20, verbose_name='نوع الرسالة')),
                ('content_ar', models.TextField(verbose_name='المحتوى (عربي)')),
                ('tokens_used', models.IntegerField(verbose_name='عدد الرموز المستخدمة')),
                ('response_time_seconds', models.DecimalField(decimal_places=2, max_digits=6, verbose_name='زمن الاستجابة (ثواني)')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='الطابع الزمني')),
                ('is_helpful', models.BooleanField(blank=True, null=True, verbose_name='مفيد؟')),
            ],
        ),
        migrations.CreateModel(
            name='AIPromptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('template_text', models.TextField(verbose_name='نص القالب')),
                ('category', models.CharField(choices=[('legal_summary', 'Legal Summary'), ('law_explanation', 'Law Explanation'), ('quiz_help', 'Quiz Help')], max_length=30, verbose_name='الفئة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
        ),
        migrations.CreateModel(
            name='AIUsageLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('daily_limit', models.IntegerField(verbose_name='الحد اليومي')),
                ('monthly_limit', models.IntegerField(verbose_name='الحد الشهري')),
                ('daily_used', models.IntegerField(default=0, verbose_name='المستخدم يومياً')),
                ('monthly_used', models.IntegerField(default=0, verbose_name='المستخدم شهرياً')),
                ('last_reset_date', models.DateField(verbose_name='تاريخ آخر إعادة تعيين')),
            ],
        ),
    ]
