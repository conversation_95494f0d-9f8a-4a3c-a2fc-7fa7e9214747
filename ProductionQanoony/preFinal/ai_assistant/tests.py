from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from authentication.models import CustomUser
from students.models import StudentProfile
from courses.models import Subject, semester
from .models import AIConversation, AIMessage, AIPromptTemplate, AIUsageLimit, AIFeedback
from django.test.utils import override_settings
from django.utils import timezone
from universities.models import University, AcademicYear
from subscriptions.models import Subscription, SubscriptionPlan
from datetime import timedelta
from unittest.mock import patch

# Create your tests here.

@override_settings(DEFAULT_FILE_STORAGE='django.core.files.storage.FileSystemStorage')
class AIAssistantTests(APITestCase):
    def setUp(self):
        self.university = University.objects.create(name_ar='جامعة القاهرة', name_en='Cairo University', code='CU', city='Cairo')
        self.academic_year = AcademicYear.objects.create(university=self.university, year_number=1, year_name_ar='الأولى', year_name_en='First Year')
        self.subject = Subject.objects.create(name_ar='القانون المدني', name_en='Civil Law', code='CIVIL1', description_ar='...', academic_year=self.academic_year)
        self.user = CustomUser.objects.create_user(email='<EMAIL>', first_name='Stu', last_name='Dent', password='UserPass1234', is_student=True)
        self.user.email_verified = True
        self.user.save()
        self.student = StudentProfile.objects.create(user=self.user, university=self.university, academic_year=self.academic_year, student_id='AI-1')
        self.course = Course.objects.create(subject=self.subject, title_ar='قانون', title_en='Law', description_ar='...', instructor=self.user, order=1)
        self.admin = CustomUser.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='AdminPass1234')
        self.prompt = AIPromptTemplate.objects.create(name='Legal Summary', template_text='تلخيص قانوني', category='legal_summary', is_active=True)
        self.usage = AIUsageLimit.objects.create(student=self.student, daily_limit=10, monthly_limit=100, daily_used=0, monthly_used=0, last_reset_date=timezone.now().date())
        # Create a valid subscription for the user
        self.plan = SubscriptionPlan.objects.create(name='Standard', duration_days=30, price=100.0)
        today = timezone.now().date()
        self.subscription = Subscription.objects.create(
            user=self.user,
            plan=self.plan,
            start_date=today,
            end_date=today + timedelta(days=30),
            status='active',
            is_active=True
        )
        self.semester = None

    def test_conversation_creation(self):
        self.client.force_authenticate(user=self.user)
        url = reverse('ai-conversation-list')
        data = {
            'student': self.student.id,
            'title_ar': 'محادثة قانونية',
            'context_type': 'legal_question',
            'semester': self.semester
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(AIConversation.objects.filter(title_ar='محادثة قانونية').exists())

    @patch('ai_assistant.views.send_to_openrouter')
    def test_message_exchange(self, mock_openrouter):
        mock_openrouter.return_value = {'reply': 'القانون هو...', 'usage': {'total_tokens': 10, 'response_time': 0.5}}
        self.client.force_authenticate(user=self.user)
        conversation = AIConversation.objects.create(student=self.student, title_ar='محادثة', context_type='general', semester=self.semester)
        url = reverse('ai-message-send')
        data = {'conversation': conversation.id, 'content_ar': 'ما هو القانون؟'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('assistant_message', response.data)
        self.assertTrue(AIMessage.objects.filter(conversation=conversation, message_type='assistant').exists())

    def test_prompt_template_management(self):
        self.client.force_authenticate(user=self.admin)
        url = reverse('ai-prompt-template-list')
        data = {'name': 'شرح قانون', 'template_text': 'اشرح القانون', 'category': 'law_explanation', 'is_active': True}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(AIPromptTemplate.objects.filter(name='شرح قانون').exists())

    def test_usage_limit_enforcement(self):
        self.client.force_authenticate(user=self.admin)
        url = reverse('ai-usage-limit-list')
        data = {'student': self.student.id, 'daily_limit': 5, 'monthly_limit': 50, 'daily_used': 2, 'monthly_used': 10, 'last_reset_date': timezone.now().date()}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(AIUsageLimit.objects.filter(student=self.student, daily_limit=5).exists())

    def test_rate_ai_message(self):
        self.client.force_authenticate(user=self.user)
        conversation = AIConversation.objects.create(student=self.student, title_ar='محادثة', context_type='general', semester=self.semester)
        msg = AIMessage.objects.create(conversation=conversation, message_type='assistant', content_ar='رد', tokens_used=5, response_time_seconds=0.5)
        url = reverse('ai-message-rate', args=[msg.id])
        response = self.client.post(url, {'rating': 4, 'comment': 'مفيد جداً'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        feedback = AIFeedback.objects.filter(message=msg, user=self.user).first()
        self.assertIsNotNone(feedback)
        self.assertEqual(feedback.rating, 4)
        self.assertEqual(feedback.comment, 'مفيد جداً')

    def test_celery_reset_daily_usage(self):
        from .tasks import reset_daily_usage
        self.usage.daily_used = 5
        self.usage.save()
        reset_daily_usage()
        self.usage.refresh_from_db()
        self.assertEqual(self.usage.daily_used, 0)

    def test_celery_reset_monthly_usage(self):
        from .tasks import reset_monthly_usage
        self.usage.monthly_used = 50
        self.usage.save()
        reset_monthly_usage()
        self.usage.refresh_from_db()
        self.assertEqual(self.usage.monthly_used, 0)

    @patch('ai_assistant.views.send_to_openrouter')
    def test_message_exchange_between_user_and_agent(self, mock_openrouter):
        mock_openrouter.return_value = {'reply': 'القانون هو...', 'usage': {'total_tokens': 10, 'response_time': 0.5}}
        self.client.force_authenticate(user=self.user)
        conversation = AIConversation.objects.create(student=self.student, title_ar='محادثة', context_type='general', semester=self.semester)
        url = reverse('ai-message-send')
        data = {'conversation': conversation.id, 'content_ar': 'ما هو القانون؟'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        user_msg = AIMessage.objects.filter(conversation=conversation, message_type='user').first()
        ai_msg = AIMessage.objects.filter(conversation=conversation, message_type='assistant').first()
        self.assertIsNotNone(user_msg)
        self.assertIsNotNone(ai_msg)
        self.assertEqual(user_msg.content_ar, 'ما هو القانون؟')
        self.assertEqual(ai_msg.content_ar, 'القانون هو...')

    def test_permission_denied_for_unauthenticated(self):
        url = reverse('ai-conversation-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_permission_denied_for_unverified(self):
        self.user.email_verified = False
        self.user.save()
        self.client.force_authenticate(user=self.user)
        url = reverse('ai-conversation-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_permission_denied_for_unsubscribed(self):
        # Remove all subscriptions for this user
        Subscription.objects.filter(user=self.user).delete()
        self.user.email_verified = True
        self.user.save()
        self.client.force_authenticate(user=self.user)
        url = reverse('ai-conversation-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_invalid_input_missing_fields(self):
        self.client.force_authenticate(user=self.user)
        url = reverse('ai-message-send')
        response = self.client.post(url, {})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_invalid_conversation_id(self):
        self.client.force_authenticate(user=self.user)
        url = reverse('ai-message-send')
        data = {'conversation': 9999, 'content_ar': 'سؤال'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_invalid_rating(self):
        self.client.force_authenticate(user=self.user)
        conversation = AIConversation.objects.create(student=self.student, title_ar='محادثة', context_type='general', semester=self.semester)
        msg = AIMessage.objects.create(conversation=conversation, message_type='assistant', content_ar='رد', tokens_used=5, response_time_seconds=0.5)
        url = reverse('ai-message-rate', args=[msg.id])
        response = self.client.post(url, {'rating': 10, 'comment': 'خارج النطاق'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_usage_limit_enforced(self):
        self.usage.daily_used = self.usage.daily_limit
        self.usage.save()
        self.client.force_authenticate(user=self.user)
        conversation = AIConversation.objects.create(student=self.student, title_ar='محادثة', context_type='general', semester=self.semester)
        url = reverse('ai-message-send')
        data = {'conversation': conversation.id, 'content_ar': 'سؤال'}
        from unittest.mock import patch
        with patch('ai_assistant.views.send_to_openrouter') as mock_openrouter:
            mock_openrouter.return_value = {'reply': '...', 'usage': {'total_tokens': 10, 'response_time': 0.5}}
            response = self.client.post(url, data)
        self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_200_OK])

    def test_openrouter_api_failure(self):
        self.client.force_authenticate(user=self.user)
        conversation = AIConversation.objects.create(student=self.student, title_ar='محادثة', context_type='general', semester=self.semester)
        url = reverse('ai-message-send')
        data = {'conversation': conversation.id, 'content_ar': 'سؤال'}
        from unittest.mock import patch
        with patch('ai_assistant.views.send_to_openrouter', side_effect=Exception('API failure')):
            response = self.client.post(url, data)
        self.assertIn(response.status_code, [status.HTTP_502_BAD_GATEWAY, status.HTTP_500_INTERNAL_SERVER_ERROR])
