from django.urls import path
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    AIConversationViewSet,
    AIMessageViewSet,
    AIPromptTemplateViewSet,
    AIUsageLimitViewSet,
    AIStatsView
)

router = DefaultRouter()
router.register(r'conversations', AIConversationViewSet, basename='ai-conversation')
router.register(r'messages', AIMessageViewSet, basename='ai-message')
router.register(r'prompt-templates', AIPromptTemplateViewSet, basename='ai-prompt-template')
router.register(r'usage-limits', AIUsageLimitViewSet, basename='ai-usage-limit')

urlpatterns = router.urls + [
    path('admin/stats/', AIStatsView.as_view(), name='ai-stats'),
]