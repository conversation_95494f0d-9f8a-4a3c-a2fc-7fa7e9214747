from django.db import models
from authentication.models import CustomUser
from students.models import StudentProfile

# Create your models here.

class LiveSession(models.Model):
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('ongoing', 'Ongoing'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    instructor = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='live_sessions')
    title_ar = models.CharField(max_length=255)
    description_ar = models.TextField()
    scheduled_date = models.DateTimeField()
    duration_minutes = models.IntegerField()
    max_participants = models.IntegerField()
    meeting_link = models.URLField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    created_at = models.DateTimeField(auto_now_add=True)
    semester = models.ForeignKey('courses.Semester', on_delete=models.CASCADE, related_name='live_sessions', null=True, blank=True)

    def __str__(self):
        return f"{self.title_ar} ({self.scheduled_date})"

class SessionBooking(models.Model):
    ATTENDANCE_CHOICES = [
        ('registered', 'تم التسجيل'),
        ('attended', 'حضر'),
        ('absent', 'غائب'),
    ]
    session = models.ForeignKey(LiveSession, on_delete=models.CASCADE, related_name='bookings')
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE, related_name='session_bookings')
    booked_at = models.DateTimeField(auto_now_add=True)
    attendance_status = models.CharField(max_length=20, choices=ATTENDANCE_CHOICES, default='registered')
    feedback_rating = models.IntegerField(null=True, blank=True)
    feedback_comment = models.TextField(blank=True)
    payment_screenshot = models.ImageField(
        upload_to='session_payments/',
        null=True,
        blank=True,
        verbose_name='صورة إيصال الدفع'
    )
    is_approved = models.BooleanField(
        default=False,
        verbose_name='تمت الموافقة'
    )
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='تاريخ الموافقة'
    )
    approved_by = models.ForeignKey(
        'authentication.CustomUser',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='approved_session_bookings',
        verbose_name='تمت الموافقة بواسطة'
    )
    rejection_reason = models.TextField(
        blank=True,
        verbose_name='سبب الرفض'
    )

    def __str__(self):
        return f"{self.student.user.email} - {self.session.title_ar}"

    class Meta:
        unique_together = ('session', 'student')

class Calendar(models.Model):
    EVENT_TYPE_CHOICES = [
        ('live_class', 'حصة مباشرة'),
        ('quiz', 'اختبار'),
        ('subscription_expiry', 'انتهاء الاشتراك'),
    ]
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='calendar_events')
    title_ar = models.CharField(max_length=255)
    description_ar = models.TextField()
    event_date = models.DateTimeField()
    event_type = models.CharField(max_length=30, choices=EVENT_TYPE_CHOICES)
    is_reminder_sent = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.title_ar} ({self.event_date})"
