from django.shortcuts import render
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import LiveSession, SessionBooking, Calendar
from .serializers import LiveSessionSerializer, SessionBookingSerializer, CalendarSerializer
from authentication.models import CustomUser
from students.models import StudentProfile
from rest_framework.permissions import IsAdminUser
from django.utils import timezone
from rest_framework import serializers

# Create your views here.

class IsInstructorOrAdmin(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and (getattr(request.user, 'is_staff', False) or getattr(request.user, 'is_instructor', False))

class IsStudent(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and getattr(request.user, 'is_student', False)

class LiveSessionViewSet(viewsets.ModelViewSet):
    queryset = LiveSession.objects.all()
    serializer_class = LiveSessionSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title_ar', 'description_ar', 'semester__title_ar', 'semester__title_en']
    ordering_fields = ['scheduled_date', 'created_at', 'status']

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsInstructorOrAdmin()]
        return [permissions.IsAuthenticated()]

    def get_queryset(self):
        user = self.request.user
        queryset = LiveSession.objects.all()

        # For students, only show sessions for their academic year
        if user.is_authenticated and hasattr(user, 'student_profile'):
            if not (user.is_staff or getattr(user, 'is_instructor', False)):
                if user.student_profile.academic_year:
                    queryset = queryset.filter(semester__academic_year=user.student_profile.academic_year)

        # Staff and instructors can see all sessions
        return queryset.order_by('scheduled_date')

    @action(detail=True, methods=['post'], permission_classes=[IsInstructorOrAdmin])
    def cancel(self, request, pk=None):
        session = self.get_object()
        session.status = 'cancelled'
        session.save()
        return Response({'status': 'cancelled'})

class SessionBookingViewSet(viewsets.ModelViewSet):
    queryset = SessionBooking.objects.all()
    serializer_class = SessionBookingSerializer
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['booked_at', 'attendance_status']

    def get_permissions(self):
        if self.action in ['create', 'destroy', 'update', 'partial_update']:
            return [IsStudent()]
        if self.action in ['approve', 'reject']:
            return [IsAdminUser()]
        return [permissions.IsAuthenticated()]

    def get_queryset(self):
        user = self.request.user
        if getattr(user, 'is_student', False):
            return SessionBooking.objects.filter(student__user=user)
        elif getattr(user, 'is_instructor', False) or user.is_staff:
            return SessionBooking.objects.filter(session__instructor=user)
        return SessionBooking.objects.none()

    def perform_create(self, serializer):
        # Ensure payment screenshot is provided
        if not self.request.FILES.get('payment_screenshot'):
            raise serializers.ValidationError({'payment_screenshot': 'يجب رفع صورة إيصال الدفع'})
        serializer.save()

    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def approve(self, request, pk=None):
        booking = self.get_object()
        if booking.is_approved:
            return Response({'detail': 'تمت الموافقة بالفعل'}, status=status.HTTP_400_BAD_REQUEST)
        # Delete screenshot after approval
        if booking.payment_screenshot:
            booking.payment_screenshot.delete(save=False)
            booking.payment_screenshot = None
        booking.is_approved = True
        booking.approved_at = timezone.now()
        booking.approved_by = request.user
        booking.rejection_reason = ''
        booking.save()
        return Response({'status': 'تمت الموافقة على الحجز'})

    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def reject(self, request, pk=None):
        booking = self.get_object()
        if booking.is_approved:
            return Response({'detail': 'لا يمكن رفض حجز تمت الموافقة عليه'}, status=status.HTTP_400_BAD_REQUEST)
        reason = request.data.get('rejection_reason', '')
        # Delete screenshot after rejection
        if booking.payment_screenshot:
            booking.payment_screenshot.delete(save=False)
            booking.payment_screenshot = None
        booking.is_approved = False
        booking.approved_at = None
        booking.approved_by = request.user
        booking.rejection_reason = reason
        booking.save()
        return Response({'status': 'تم رفض الحجز', 'reason': reason})

    @action(detail=True, methods=['post'], permission_classes=[IsStudent])
    def feedback(self, request, pk=None):
        booking = self.get_object()
        rating = request.data.get('feedback_rating')
        comment = request.data.get('feedback_comment', '')
        if rating is not None:
            booking.feedback_rating = rating
        booking.feedback_comment = comment
        booking.save()
        return Response({'status': 'feedback submitted'})

class CalendarViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Calendar.objects.all()
    serializer_class = CalendarSerializer
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['event_date', 'created_at']

    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated:
            return Calendar.objects.filter(user=user)
        return Calendar.objects.none()

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def upcoming(self, request):
        now = timezone.now()
        events = Calendar.objects.filter(user=request.user, event_date__gte=now).order_by('event_date')
        serializer = self.get_serializer(events, many=True)
        return Response(serializer.data)
