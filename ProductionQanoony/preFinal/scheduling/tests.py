from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from authentication.models import CustomUser
from students.models import StudentProfile
from courses.models import Subject
from universities.models import University, AcademicYear
from .models import LiveSession, SessionBooking, Calendar
from datetime import timedelta
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile
from io import BytesIO
from PIL import Image
from django.test.utils import override_settings

@override_settings(DEFAULT_FILE_STORAGE='django.core.files.storage.FileSystemStorage')
class SchedulingTests(APITestCase):
    def setUp(self):
        self.university = University.objects.create(name_ar='جامعة', name_en='University', code='UNI', city='Cairo')
        self.academic_year = AcademicYear.objects.create(university=self.university, year_number=1, year_name_ar='سنة أولى', year_name_en='First Year')
        self.instructor = CustomUser.objects.create_user(email='<EMAIL>', password='pass', first_name='Inst', last_name='User', is_instructor=True, email_verified=True)
        self.student_user = CustomUser.objects.create_user(email='<EMAIL>', password='pass', first_name='Stu', last_name='Dent', is_student=True, email_verified=True)
        self.student = StudentProfile.objects.create(user=self.student_user, university=self.university, academic_year=self.academic_year, student_id='SCHED-1')
        self.subject = Subject.objects.create(name_ar='مادة', name_en='Subject', code='SUBJ1', description_ar='وصف', academic_year=self.academic_year)
        self.course = Course.objects.create(subject=self.subject, title_ar='قانون', title_en='Law', description_ar='...', instructor=self.instructor, order=1)
        self.client = APIClient()
        self.semester = None

    def test_livesession_creation_by_instructor(self):
        self.client.force_authenticate(user=self.instructor)
        url = reverse('livesession-list')
        data = {
            'instructor': self.instructor.id,
            'course': self.course.id,
            'title_ar': 'جلسة مباشرة',
            'description_ar': 'شرح مباشر',
            'scheduled_date': (timezone.now() + timedelta(days=1)).isoformat(),
            'duration_minutes': 60,
            'max_participants': 10,
            'meeting_link': 'https://meet.example.com/abc',
            'status': 'scheduled'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(LiveSession.objects.count(), 1)

    def test_livesession_creation_by_student_forbidden(self):
        self.client.force_authenticate(user=self.student_user)
        url = reverse('livesession-list')
        data = {
            'instructor': self.instructor.id,
            'course': self.course.id,
            'title_ar': 'جلسة مباشرة',
            'description_ar': 'شرح مباشر',
            'scheduled_date': (timezone.now() + timedelta(days=1)).isoformat(),
            'duration_minutes': 60,
            'max_participants': 10,
            'meeting_link': 'https://meet.example.com/abc',
            'status': 'scheduled'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_session_booking_by_student(self):
        session = LiveSession.objects.create(instructor=self.instructor, semester=self.semester, title_ar='جلسة', description_ar='...', scheduled_date=timezone.now() + timedelta(days=1), duration_minutes=60, max_participants=2, meeting_link='https://meet.example.com/abc', status='scheduled')
        self.client.force_authenticate(user=self.student_user)
        url = reverse('sessionbooking-list')
        image_io = BytesIO()
        image = Image.new('RGB', (1, 1), color='white')
        image.save(image_io, format='PNG')
        image_io.seek(0)
        dummy_image = SimpleUploadedFile("receipt.png", image_io.read(), content_type="image/png")
        data = {'session': session.id, 'student': self.student.id, 'payment_screenshot': dummy_image}
        response = self.client.post(url, data, format='multipart')
        print('RESPONSE DATA:', response.data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(SessionBooking.objects.count(), 1)

    def test_overbooking_not_allowed(self):
        session = LiveSession.objects.create(instructor=self.instructor, semester=self.semester, title_ar='جلسة', description_ar='...', scheduled_date=timezone.now() + timedelta(days=1), duration_minutes=60, max_participants=1, meeting_link='https://meet.example.com/abc', status='scheduled')
        self.client.force_authenticate(user=self.student_user)
        url = reverse('sessionbooking-list')
        data = {'session': session.id, 'student': self.student.id}
        self.client.post(url, data, format='json')
        # Try to book again (should fail)
        student2_user = CustomUser.objects.create_user(email='<EMAIL>', password='pass', first_name='Stu2', last_name='Dent', is_student=True, email_verified=True)
        student2 = StudentProfile.objects.create(user=student2_user, university=self.university, academic_year=self.academic_year, student_id='SCHED-2-unique')
        self.client.force_authenticate(user=student2_user)
        data2 = {'session': session.id, 'student': student2.id}
        response = self.client.post(url, data2, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_double_booking_by_same_student_not_allowed(self):
        session = LiveSession.objects.create(instructor=self.instructor, semester=self.semester, title_ar='جلسة', description_ar='...', scheduled_date=timezone.now() + timedelta(days=1), duration_minutes=60, max_participants=2, meeting_link='https://meet.example.com/abc', status='scheduled')
        self.client.force_authenticate(user=self.student_user)
        url = reverse('sessionbooking-list')
        from io import BytesIO
        from PIL import Image
        from django.core.files.uploadedfile import SimpleUploadedFile
        image_io = BytesIO()
        image = Image.new('RGB', (1, 1), color='white')
        image.save(image_io, format='PNG')
        image_io.seek(0)
        dummy_image = SimpleUploadedFile("receipt.png", image_io.read(), content_type="image/png")
        data = {'session': session.id, 'student': self.student.id, 'payment_screenshot': dummy_image}
        response1 = self.client.post(url, data, format='multipart')
        self.assertEqual(response1.status_code, status.HTTP_201_CREATED)
        # Try to book again for the same student (should fail)
        image_io2 = BytesIO()
        image2 = Image.new('RGB', (1, 1), color='white')
        image2.save(image_io2, format='PNG')
        image_io2.seek(0)
        dummy_image2 = SimpleUploadedFile("receipt2.png", image_io2.read(), content_type="image/png")
        data2 = {'session': session.id, 'student': self.student.id, 'payment_screenshot': dummy_image2}
        response2 = self.client.post(url, data2, format='multipart')
        self.assertEqual(response2.status_code, status.HTTP_400_BAD_REQUEST)

    def test_booking_feedback(self):
        session = LiveSession.objects.create(instructor=self.instructor, semester=self.semester, title_ar='جلسة', description_ar='...', scheduled_date=timezone.now() + timedelta(days=1), duration_minutes=60, max_participants=2, meeting_link='https://meet.example.com/abc', status='scheduled')
        booking = SessionBooking.objects.create(session=session, student=self.student)
        self.client.force_authenticate(user=self.student_user)
        url = reverse('sessionbooking-feedback', args=[booking.id])
        data = {'feedback_rating': 5, 'feedback_comment': 'ممتاز'}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        booking.refresh_from_db()
        self.assertEqual(booking.feedback_rating, 5)
        self.assertEqual(booking.feedback_comment, 'ممتاز')

    def test_calendar_event_view(self):
        Calendar.objects.create(user=self.student_user, title_ar='حدث', description_ar='...', event_date=timezone.now() + timedelta(days=2), event_type='live_class')
        self.client.force_authenticate(user=self.student_user)
        url = reverse('calendar-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_upcoming_calendar_events(self):
        Calendar.objects.create(user=self.student_user, title_ar='حدث', description_ar='...', event_date=timezone.now() + timedelta(days=2), event_type='live_class')
        Calendar.objects.create(user=self.student_user, title_ar='اختبار', description_ar='...', event_date=timezone.now() - timedelta(days=1), event_type='quiz')
        self.client.force_authenticate(user=self.student_user)
        url = reverse('calendar-upcoming')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_permissions_enforcement(self):
        session = LiveSession.objects.create(instructor=self.instructor, semester=self.semester, title_ar='جلسة', description_ar='...', scheduled_date=timezone.now() + timedelta(days=1), duration_minutes=60, max_participants=2, meeting_link='https://meet.example.com/abc', status='scheduled')
        booking = SessionBooking.objects.create(session=session, student=self.student)
        self.client.logout()
        url = reverse('sessionbooking-feedback', args=[booking.id])
        data = {'feedback_rating': 4, 'feedback_comment': 'جيد'}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
