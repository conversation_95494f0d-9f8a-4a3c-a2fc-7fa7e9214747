from django.contrib import admin
from .models import LiveSession, SessionBooking, Calendar

@admin.register(LiveSession)
class LiveSessionAdmin(admin.ModelAdmin):
    list_display = ('title_ar', 'instructor', 'semester', 'scheduled_date', 'status', 'max_participants')
    search_fields = ('title_ar', 'instructor__email', 'semester__title_en')
    list_filter = ('status', 'scheduled_date', 'instructor', 'semester')
    ordering = ('-scheduled_date',)

@admin.register(SessionBooking)
class SessionBookingAdmin(admin.ModelAdmin):
    list_display = ('session', 'student', 'booked_at', 'attendance_status', 'feedback_rating', 'is_approved', 'approved_by', 'payment_screenshot_thumb')
    search_fields = ('session__title_ar', 'student__user__email')
    list_filter = ('attendance_status', 'booked_at', 'session', 'is_approved')
    ordering = ('-booked_at',)

    def payment_screenshot_thumb(self, obj):
        if obj.payment_screenshot:
            return f'<img src="{obj.payment_screenshot.url}" width="60" height="60" />'
        return '—'
    payment_screenshot_thumb.short_description = 'إيصال الدفع'
    payment_screenshot_thumb.allow_tags = True

@admin.register(Calendar)
class CalendarAdmin(admin.ModelAdmin):
    list_display = ('title_ar', 'user', 'event_date', 'event_type', 'is_reminder_sent')
    search_fields = ('title_ar', 'user__email')
    list_filter = ('event_type', 'is_reminder_sent', 'event_date')
    ordering = ('-event_date',)
