# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('students', '0001_initial'),
        ('courses', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LiveSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_ar', models.CharField(max_length=255)),
                ('description_ar', models.TextField()),
                ('scheduled_date', models.DateTimeField()),
                ('duration_minutes', models.IntegerField()),
                ('max_participants', models.IntegerField()),
                ('meeting_link', models.URLField()),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('ongoing', 'Ongoing'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='scheduled', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('instructor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='live_sessions', to=settings.AUTH_USER_MODEL)),
                ('semester', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='live_sessions', to='courses.semester')),
            ],
        ),
        migrations.CreateModel(
            name='Calendar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_ar', models.CharField(max_length=255)),
                ('description_ar', models.TextField()),
                ('event_date', models.DateTimeField()),
                ('event_type', models.CharField(choices=[('live_class', 'حصة مباشرة'), ('quiz', 'اختبار'), ('subscription_expiry', 'انتهاء الاشتراك')], max_length=30)),
                ('is_reminder_sent', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_events', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SessionBooking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('booked_at', models.DateTimeField(auto_now_add=True)),
                ('attendance_status', models.CharField(choices=[('registered', 'تم التسجيل'), ('attended', 'حضر'), ('absent', 'غائب')], default='registered', max_length=20)),
                ('feedback_rating', models.IntegerField(blank=True, null=True)),
                ('feedback_comment', models.TextField(blank=True)),
                ('payment_screenshot', models.ImageField(blank=True, null=True, upload_to='session_payments/', verbose_name='صورة إيصال الدفع')),
                ('is_approved', models.BooleanField(default=False, verbose_name='تمت الموافقة')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('rejection_reason', models.TextField(blank=True, verbose_name='سبب الرفض')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_session_bookings', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='scheduling.livesession')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='session_bookings', to='students.studentprofile')),
            ],
            options={
                'unique_together': {('session', 'student')},
            },
        ),
    ]
