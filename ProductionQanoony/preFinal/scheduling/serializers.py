from rest_framework import serializers
from .models import LiveSession, SessionBooking, Calendar
from students.models import StudentProfile
from authentication.models import CustomUser
from courses.models import Semester

class LiveSessionSerializer(serializers.ModelSerializer):
    instructor = serializers.PrimaryKeyRelatedField(queryset=CustomUser.objects.filter(is_staff=True) | CustomUser.objects.filter(is_instructor=True))
    semester = serializers.PrimaryKeyRelatedField(queryset=Semester.objects.all(), required=False, allow_null=True)

    class Meta:
        model = LiveSession
        fields = '__all__'

    def validate(self, data):
        if data.get('max_participants', 1) < 1:
            raise serializers.ValidationError({'max_participants': 'يجب أن يكون عدد المشاركين أكبر من 0'})
        return data

class SessionBookingSerializer(serializers.ModelSerializer):
    student = serializers.PrimaryKeyRelatedField(queryset=StudentProfile.objects.all())
    session = serializers.PrimaryKeyRelatedField(queryset=LiveSession.objects.all())
    payment_screenshot = serializers.ImageField(required=True, write_only=True)
    is_approved = serializers.BooleanField(read_only=True)
    approved_at = serializers.DateTimeField(read_only=True)
    approved_by = serializers.PrimaryKeyRelatedField(read_only=True)
    rejection_reason = serializers.CharField(read_only=True)

    class Meta:
        model = SessionBooking
        fields = '__all__'

    def validate(self, data):
        session = data['session']
        student = data['student']
        if session.bookings.count() >= session.max_participants:
            raise serializers.ValidationError('تم الوصول للحد الأقصى من المشاركين في هذه الجلسة')
        if SessionBooking.objects.filter(session=session, student=student).exists():
            raise serializers.ValidationError('لقد قمت بالفعل بحجز هذه الجلسة')
        return data

    def validate_feedback_rating(self, value):
        if value is not None and (value < 1 or value > 5):
            raise serializers.ValidationError('التقييم يجب أن يكون بين 1 و 5')
        return value

class CalendarSerializer(serializers.ModelSerializer):
    user = serializers.PrimaryKeyRelatedField(queryset=CustomUser.objects.all())

    class Meta:
        model = Calendar
        fields = '__all__' 