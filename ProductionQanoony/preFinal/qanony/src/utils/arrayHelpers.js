import { useState, useEffect } from 'react';

/**
 * Array Helper Functions
 * حل مشكلة APIs التي ترجع objects بدلاً من arrays
 */

/**
 * تحويل استجابة API إلى array آمن
 * @param {any} data - البيانات المُرجعة من API
 * @param {string} fallbackKey - المفتاح البديل للبحث عن البيانات (افتراضي: 'results')
 * @returns {Array} - array آمن للاستخدام
 */
export const ensureArray = (data, fallbackKey = 'results') => {
  // إذا كانت البيانات array بالفعل
  if (Array.isArray(data)) {
    return data;
  }
  
  // إذا كانت البيانات null أو undefined
  if (!data) {
    return [];
  }
  
  // إذا كانت البيانات object
  if (typeof data === 'object') {
    // تحقق من المفاتيح الشائعة
    if (Array.isArray(data[fallbackKey])) {
      return data[fallbackKey];
    }
    
    if (Array.isArray(data.data)) {
      return data.data;
    }
    
    if (Array.isArray(data.items)) {
      return data.items;
    }
    
    if (Array.isArray(data.list)) {
      return data.list;
    }
    
    // إذا كان object يحتوي على خصائص تشبه array items
    const keys = Object.keys(data);
    const arrayKey = keys.find(key => Array.isArray(data[key]));
    if (arrayKey) {
      return data[arrayKey];
    }
  }
  
  // إذا فشل كل شيء، أرجع array فارغ
  console.warn('ensureArray: Could not convert data to array, returning empty array:', data);
  return [];
};

/**
 * معالجة استجابة API مع تسجيل مفصل
 * @param {any} response - استجابة API
 * @param {string} context - السياق للتسجيل
 * @param {string} fallbackKey - المفتاح البديل
 * @returns {Array} - array آمن
 */
export const processApiResponse = (response, context = 'API', fallbackKey = 'results') => {
  console.log(`${context} response:`, {
    type: typeof response,
    isArray: Array.isArray(response),
    keys: typeof response === 'object' ? Object.keys(response) : null,
    length: Array.isArray(response) ? response.length : null
  });
  
  const result = ensureArray(response, fallbackKey);
  
  console.log(`${context} processed:`, {
    resultType: typeof result,
    isArray: Array.isArray(result),
    length: result.length
  });
  
  return result;
};

/**
 * معالجة أخطاء API مع إرجاع array فارغ
 * @param {Error} error - الخطأ
 * @param {string} context - السياق
 * @param {Function} setError - دالة تعيين الخطأ
 * @returns {Array} - array فارغ
 */
export const handleApiError = (error, context = 'API', setError = null) => {
  console.error(`${context} error:`, error);
  
  const errorMessage = error.response?.data?.message || 
                      error.response?.data?.detail || 
                      error.message || 
                      `حدث خطأ في ${context}`;
  
  if (setError) {
    setError(errorMessage);
  }
  
  return [];
};

/**
 * دالة مساعدة لمعالجة multiple API calls
 * @param {Array} apiCalls - مصفوفة من API calls
 * @param {Object} setters - كائن يحتوي على setters للبيانات
 * @param {Function} setLoading - دالة تعيين حالة التحميل
 * @param {Function} setError - دالة تعيين الخطأ
 */
export const handleMultipleApiCalls = async (apiCalls, setters, setLoading, setError) => {
  setLoading(true);
  setError(null);
  
  try {
    const results = await Promise.allSettled(apiCalls.map(call => call.promise));
    
    results.forEach((result, index) => {
      const { key, context, fallbackKey } = apiCalls[index];
      const setter = setters[key];
      
      if (result.status === 'fulfilled') {
        const processedData = processApiResponse(result.value.data, context, fallbackKey);
        setter(processedData);
      } else {
        console.error(`${context} failed:`, result.reason);
        setter([]);
      }
    });
    
  } catch (error) {
    console.error('Multiple API calls failed:', error);
    setError('حدث خطأ في تحميل البيانات');
    
    // تعيين arrays فارغة لجميع setters
    Object.values(setters).forEach(setter => setter([]));
  } finally {
    setLoading(false);
  }
};

/**
 * Hook مخصص لمعالجة API data
 * @param {Function} apiCall - دالة API call
 * @param {string} context - السياق
 * @param {string} fallbackKey - المفتاح البديل
 * @returns {Object} - حالة البيانات
 */
export const useApiData = (apiCall, context = 'API', fallbackKey = 'results') => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiCall();
      const processedData = processApiResponse(response.data, context, fallbackKey);
      setData(processedData);
    } catch (err) {
      const errorData = handleApiError(err, context, setError);
      setData(errorData);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return { data, loading, error, refetch: fetchData };
};

/**
 * دالة للتحقق من صحة البيانات قبل استخدام .map()
 * @param {any} data - البيانات للتحقق منها
 * @param {string} context - السياق للتسجيل
 * @returns {boolean} - هل البيانات آمنة للاستخدام
 */
export const isArraySafe = (data, context = '') => {
  const safe = Array.isArray(data);
  if (!safe && context) {
    console.warn(`${context}: Data is not an array:`, typeof data, data);
  }
  return safe;
};

/**
 * دالة wrapper آمنة لـ .map()
 * @param {any} data - البيانات
 * @param {Function} mapFunction - دالة map
 * @param {string} context - السياق
 * @returns {Array} - النتيجة أو array فارغ
 */
export const safeMap = (data, mapFunction, context = '') => {
  if (!isArraySafe(data, context)) {
    return [];
  }
  
  try {
    return data.map(mapFunction);
  } catch (error) {
    console.error(`${context}: Error in map function:`, error);
    return [];
  }
};

export default {
  ensureArray,
  processApiResponse,
  handleApiError,
  handleMultipleApiCalls,
  useApiData,
  isArraySafe,
  safeMap
};
