/**
 * دالة لاستخراج معرف اليوتيوب من الرابط الكامل
 * @param {string} url - رابط اليوتيوب أو معرف الفيديو
 * @returns {string|null} معرف الفيديو أو null إذا لم يتم العثور عليه
 */
export const extractYouTubeId = (url) => {
  if (!url) return null;
  
  // إذا كان معرف فقط (بدون رابط)
  if (url.length <= 20 && !url.includes('youtube.com') && !url.includes('youtu.be')) {
    return url;
  }
  
  // روابط youtube.com
  const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(youtubeRegex);
  
  if (match) {
    return match[1];
  }
  
  return null;
};

/**
 * دالة للتحقق من صحة معرف اليوتيوب
 * @param {string} videoId - معرف الفيديو
 * @returns {boolean} true إذا كان المعرف صحيح
 */
export const isValidYouTubeId = (videoId) => {
  console.log('isValidYouTubeId called with:', videoId);
  
  if (!videoId) {
    console.log('No videoId provided');
    return false;
  }
  
  // تنظيف المعرف من المسافات الزائدة
  const cleanVideoId = videoId.trim();
  
  // التحقق من أن المعرف 11 حرف
  if (cleanVideoId.length !== 11) {
    console.log('VideoId length is not 11:', cleanVideoId.length);
    return false;
  }
  
  // التحقق من أن المعرف يحتوي على أحرف صحيحة فقط
  const validRegex = /^[a-zA-Z0-9_-]{11}$/;
  const isValid = validRegex.test(cleanVideoId);
  
  console.log('VideoId validation result:', isValid);
  return isValid;
};

/**
 * دالة لإنشاء رابط embed اليوتيوب
 * @param {string} videoId - معرف الفيديو
 * @returns {string|null} رابط embed أو null إذا كان المعرف غير صحيح
 */
export const getYouTubeEmbedUrl = (videoId) => {
  console.log('getYouTubeEmbedUrl called with:', videoId);
  
  if (!videoId) {
    console.log('No videoId provided');
    return null;
  }
  
  // تنظيف المعرف من المسافات الزائدة
  const cleanVideoId = videoId.trim();
  
  if (!isValidYouTubeId(cleanVideoId)) {
    console.log('Invalid YouTube ID:', cleanVideoId);
    return null;
  }
  
  const embedUrl = `https://www.youtube.com/embed/${cleanVideoId}`;
  console.log('Generated embed URL:', embedUrl);
  return embedUrl;
};

/**
 * دالة لإنشاء رابط مشاهدة اليوتيوب
 * @param {string} videoId - معرف الفيديو
 * @returns {string} رابط المشاهدة
 */
export const getYouTubeWatchUrl = (videoId) => {
  if (!isValidYouTubeId(videoId)) return null;
  return `https://www.youtube.com/watch?v=${videoId}`;
};

/**
 * دالة لإنشاء رابط thumbnail اليوتيوب
 * @param {string} videoId - معرف الفيديو
 * @param {string} quality - جودة الصورة (default, hq, mq, sd, maxres)
 * @returns {string} رابط الصورة المصغرة
 */
export const getYouTubeThumbnailUrl = (videoId, quality = 'default') => {
  if (!isValidYouTubeId(videoId)) return null;
  return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;
};

/**
 * دالة لمعالجة رابط اليوتيوب المدخل من المستخدم
 * @param {string} input - الرابط أو المعرف المدخل
 * @returns {object} { videoId, isValid, error }
 */
export const processYouTubeInput = (input) => {
  if (!input) {
    return {
      videoId: null,
      isValid: false,
      error: 'معرف فيديو يوتيوب مطلوب'
    };
  }
  
  // استخراج المعرف من الرابط
  const extractedId = extractYouTubeId(input);
  
  if (!extractedId) {
    return {
      videoId: null,
      isValid: false,
      error: 'رابط يوتيوب غير صحيح'
    };
  }
  
  // التحقق من صحة المعرف
  if (!isValidYouTubeId(extractedId)) {
    return {
      videoId: extractedId,
      isValid: false,
      error: 'معرف فيديو يوتيوب غير صحيح (يجب أن يكون 11 حرف)'
    };
  }
  
  return {
    videoId: extractedId,
    isValid: true,
    error: null
  };
}; 