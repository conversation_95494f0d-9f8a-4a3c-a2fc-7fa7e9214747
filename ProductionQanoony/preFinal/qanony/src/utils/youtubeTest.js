import { processYouTubeInput, getYouTubeEmbedUrl, isValidYouTubeId } from './youtubeUtils';

/**
 * دالة لاختبار روابط اليوتيوب المختلفة
 */
export const testYouTubeUrls = () => {
  const testCases = [
    // روابط صحيحة
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://youtu.be/dQw4w9WgXcQ',
    'https://www.youtube.com/embed/dQw4w9WgXcQ',
    'dQw4w9WgXcQ',
    
    // روابط خاطئة
    'https://www.youtube.com/watch?v=invalid',
    'https://youtu.be/short',
    'invalid-id',
    '',
    null,
    undefined
  ];

  console.log('=== YouTube URL Testing ===');
  
  testCases.forEach((testCase, index) => {
    console.log(`\nTest Case ${index + 1}:`, testCase);
    
    try {
      const result = processYouTubeInput(testCase);
      console.log('Process Result:', result);
      
      if (result.videoId) {
        const embedUrl = getYouTubeEmbedUrl(result.videoId);
        console.log('Embed URL:', embedUrl);
        console.log('Is Valid:', result.isValid);
      }
    } catch (error) {
      console.error('Error processing:', error);
    }
  });
  
  console.log('\n=== End Testing ===');
};

/**
 * دالة لاختبار معرف يوتيوب محدد
 */
export const testSpecificVideoId = (videoId) => {
  console.log('=== Testing Specific Video ID ===');
  console.log('Input:', videoId);
  
  const result = processYouTubeInput(videoId);
  console.log('Process Result:', result);
  
  if (result.videoId) {
    const embedUrl = getYouTubeEmbedUrl(result.videoId);
    console.log('Embed URL:', embedUrl);
    
    // إنشاء رابط iframe للاختبار
    const iframeHtml = `<iframe width="560" height="315" src="${embedUrl}" frameborder="0" allowfullscreen></iframe>`;
    console.log('Iframe HTML:', iframeHtml);
  }
  
  return result;
};

/**
 * دالة لإنشاء رابط iframe تجريبي
 */
export const createTestIframe = (videoId) => {
  const embedUrl = getYouTubeEmbedUrl(videoId);
  if (!embedUrl) {
    console.error('Invalid video ID:', videoId);
    return null;
  }
  
  return {
    videoId,
    embedUrl,
    iframeHtml: `<iframe width="560" height="315" src="${embedUrl}" frameborder="0" allowfullscreen></iframe>`,
    watchUrl: `https://www.youtube.com/watch?v=${videoId}`
  };
};

// تصدير الدوال للاستخدام في console
if (typeof window !== 'undefined') {
  window.testYouTubeUrls = testYouTubeUrls;
  window.testSpecificVideoId = testSpecificVideoId;
  window.createTestIframe = createTestIframe;
} 