/**
 * Keyword optimization utilities for better SEO content
 */

// Primary keywords for the legal education platform
export const PRIMARY_KEYWORDS = {
  main: [
    'منصة قانوني التعليمية',
    'تعليم قانوني',
    'كلية الحقوق',
    'طلاب القانون',
    'محاضرات قانونية',
    'التعليم الإلكتروني القانوني'
  ],
  secondary: [
    'دراسة القانون',
    'مكتبة قانونية',
    'اختبارات قانونية',
    'وظائف قانونية',
    'محامين',
    'استشارات قانونية',
    'القانون المصري',
    'التشريعات',
    'المحاماة',
    'العدالة'
  ],
  longTail: [
    'أفضل منصة تعليمية لطلاب كلية الحقوق',
    'محاضرات قانونية تفاعلية أونلاين',
    'مكتبة قانونية شاملة للطلاب',
    'فرص عمل للمحامين الجدد',
    'دورات تدريبية في القانون المصري',
    'اختبارات تقييم للطلاب القانونيين',
    'منصة تعليم القانون عن بعد',
    'تطوير مهارات المحاماة والاستشارات'
  ]
};

// Content optimization templates
export const CONTENT_TEMPLATES = {
  heroSection: {
    title: 'منصة قانوني التعليمية - {keyword}',
    description: 'اكتشف أفضل {keyword} في مصر. انضم لأكثر من 5000 طالب واحصل على تعليم قانوني متميز مع {feature}.',
    cta: 'ابدأ رحلتك في {field} اليوم'
  },
  
  aboutSection: {
    title: 'عن {platform} - رؤيتنا في {field}',
    description: 'نحن {platform} الرائدة في {field}، نقدم {services} لطلاب وخريجي كلية الحقوق.',
    mission: 'مهمتنا تطوير {field} في مصر من خلال {methods}'
  },
  
  servicesSection: {
    title: '{service} - خدماتنا المتميزة',
    description: 'استفد من {service} المتطورة التي نقدمها في {platform}',
    benefits: 'احصل على {benefits} من خلال {service} المتخصصة'
  }
};

// Keyword density optimization
export const KEYWORD_DENSITY = {
  primary: 2.5, // 2-3% for primary keywords
  secondary: 1.5, // 1-2% for secondary keywords
  longTail: 0.5 // 0.5-1% for long-tail keywords
};

/**
 * Calculate keyword density in text
 */
export const calculateKeywordDensity = (text, keyword) => {
  const words = text.toLowerCase().split(/\s+/);
  const keywordWords = keyword.toLowerCase().split(/\s+/);
  const totalWords = words.length;
  
  let keywordCount = 0;
  
  // Count exact matches
  for (let i = 0; i <= words.length - keywordWords.length; i++) {
    const phrase = words.slice(i, i + keywordWords.length).join(' ');
    if (phrase === keyword.toLowerCase()) {
      keywordCount++;
    }
  }
  
  return {
    count: keywordCount,
    density: (keywordCount / totalWords) * 100,
    totalWords,
    isOptimal: (keywordCount / totalWords) * 100 <= KEYWORD_DENSITY.primary
  };
};

/**
 * Optimize content for target keywords
 */
export const optimizeContent = (content, targetKeywords) => {
  let optimizedContent = content;
  const suggestions = [];
  
  targetKeywords.forEach(keyword => {
    const density = calculateKeywordDensity(content, keyword);
    
    if (density.density < 1) {
      suggestions.push({
        keyword,
        action: 'increase',
        current: density.density,
        target: KEYWORD_DENSITY.primary,
        message: `زيادة استخدام "${keyword}" في المحتوى`
      });
    } else if (density.density > 4) {
      suggestions.push({
        keyword,
        action: 'decrease',
        current: density.density,
        target: KEYWORD_DENSITY.primary,
        message: `تقليل استخدام "${keyword}" لتجنب الحشو`
      });
    }
  });
  
  return {
    optimizedContent,
    suggestions,
    analysis: targetKeywords.map(keyword => ({
      keyword,
      ...calculateKeywordDensity(content, keyword)
    }))
  };
};

/**
 * Generate SEO-optimized content variations
 */
export const generateContentVariations = (template, variables) => {
  const variations = [];
  
  // Generate different combinations
  Object.keys(variables).forEach(key => {
    if (Array.isArray(variables[key])) {
      variables[key].forEach(value => {
        let variation = template;
        variation = variation.replace(new RegExp(`{${key}}`, 'g'), value);
        variations.push(variation);
      });
    }
  });
  
  return variations;
};

/**
 * Semantic keyword analysis
 */
export const getSemanticKeywords = (primaryKeyword) => {
  const semanticMap = {
    'تعليم قانوني': [
      'دراسة القانون', 'تعلم القانون', 'التعليم القانوني',
      'الدراسات القانونية', 'التدريب القانوني'
    ],
    'كلية الحقوق': [
      'طلاب الحقوق', 'خريجي الحقوق', 'دراسة الحقوق',
      'تخصص القانون', 'الكلية القانونية'
    ],
    'محاضرات قانونية': [
      'دروس القانون', 'شرح القانون', 'تعليم القانون',
      'المحاضرات التعليمية', 'الدورات القانونية'
    ],
    'مكتبة قانونية': [
      'كتب القانون', 'المراجع القانونية', 'الوثائق القانونية',
      'المصادر القانونية', 'الأدبيات القانونية'
    ]
  };
  
  return semanticMap[primaryKeyword] || [];
};

/**
 * Content structure optimization for SEO
 */
export const optimizeContentStructure = (content) => {
  const structure = {
    h1: [], // Main title
    h2: [], // Section headers
    h3: [], // Subsection headers
    paragraphs: [],
    lists: [],
    images: []
  };
  
  // Extract structure elements (simplified)
  const lines = content.split('\n');
  
  lines.forEach(line => {
    if (line.startsWith('# ')) {
      structure.h1.push(line.substring(2));
    } else if (line.startsWith('## ')) {
      structure.h2.push(line.substring(3));
    } else if (line.startsWith('### ')) {
      structure.h3.push(line.substring(4));
    } else if (line.trim() && !line.startsWith('#')) {
      structure.paragraphs.push(line);
    }
  });
  
  return {
    structure,
    recommendations: [
      structure.h1.length === 0 ? 'إضافة عنوان رئيسي (H1)' : null,
      structure.h2.length < 2 ? 'إضافة المزيد من العناوين الفرعية (H2)' : null,
      structure.paragraphs.length < 3 ? 'إضافة المزيد من المحتوى' : null
    ].filter(Boolean)
  };
};

/**
 * Local SEO keywords for Egyptian legal education
 */
export const LOCAL_SEO_KEYWORDS = {
  cities: [
    'القاهرة', 'الإسكندرية', 'الجيزة', 'شبرا الخيمة',
    'بورسعيد', 'السويس', 'الأقصر', 'أسوان', 'المنصورة',
    'طنطا', 'الزقازيق', 'إسماعيلية', 'دمياط', 'كفر الشيخ'
  ],
  universities: [
    'جامعة القاهرة', 'جامعة عين شمس', 'جامعة الإسكندرية',
    'جامعة أسيوط', 'جامعة المنصورة', 'جامعة طنطا',
    'جامعة الزقازيق', 'جامعة حلوان', 'جامعة بنها'
  ],
  legalFields: [
    'القانون المدني', 'القانون الجنائي', 'القانون التجاري',
    'القانون الإداري', 'القانون الدستوري', 'قانون العمل',
    'القانون الدولي', 'قانون الأسرة', 'القانون البحري'
  ]
};

/**
 * Generate location-based content
 */
export const generateLocalContent = (baseContent, location) => {
  const localizedContent = baseContent
    .replace(/في مصر/g, `في ${location}`)
    .replace(/المصري/g, `في ${location}`)
    .replace(/بمصر/g, `ب${location}`);
  
  return localizedContent;
};

/**
 * Content readability optimization
 */
export const optimizeReadability = (text) => {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim());
  const words = text.split(/\s+/);
  
  const avgWordsPerSentence = words.length / sentences.length;
  const avgCharsPerWord = words.reduce((sum, word) => sum + word.length, 0) / words.length;
  
  const readabilityScore = 100 - (avgWordsPerSentence * 1.5) - (avgCharsPerWord * 2);
  
  return {
    score: Math.max(0, Math.min(100, readabilityScore)),
    avgWordsPerSentence,
    avgCharsPerWord,
    recommendations: [
      avgWordsPerSentence > 20 ? 'تقسيم الجمل الطويلة' : null,
      avgCharsPerWord > 6 ? 'استخدام كلمات أبسط' : null,
      readabilityScore < 50 ? 'تحسين وضوح المحتوى' : null
    ].filter(Boolean)
  };
};
