/**
 * مركز معالجة الأخطاء للـ Frontend
 * يوفر دوال موحدة لمعالجة وعرض الأخطاء للمستخدم
 */

// رسائل الأخطاء الافتراضية
export const DEFAULT_ERROR_MESSAGES = {
  // أخطاء الشبكة
  NETWORK_ERROR: 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.',
  TIMEOUT_ERROR: 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.',
  SERVER_ERROR: 'خطأ في الخادم. يرجى المحاولة لاحقاً.',
  
  // أخطاء المصادقة
  UNAUTHORIZED: 'يجب تسجيل الدخول للوصول إلى هذا المورد.',
  FORBIDDEN: 'ليس لديك الصلاحية للوصول إلى هذا المورد.',
  TOKEN_EXPIRED: 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.',
  
  // أخطاء البيانات
  VALIDATION_ERROR: 'البيانات المدخلة غير صحيحة. يرجى التحقق من المعلومات.',
  NOT_FOUND: 'المورد المطلوب غير موجود.',
  DUPLICATE_ERROR: 'هذه البيانات موجودة بالفعل.',
  
  // أخطاء عامة
  UNKNOWN_ERROR: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
  SUBSCRIPTION_REQUIRED: 'يجب أن يكون لديك اشتراك نشط للوصول إلى هذه الخدمة.',
};

// أكواد الأخطاء وما يقابلها من رسائل
export const ERROR_CODE_MESSAGES = {
  400: 'طلب غير صحيح',
  401: 'غير مصرح',
  403: 'ممنوع',
  404: 'غير موجود',
  405: 'طريقة غير مسموحة',
  408: 'انتهت مهلة الطلب',
  409: 'تضارب في البيانات',
  422: 'بيانات غير قابلة للمعالجة',
  429: 'طلبات كثيرة',
  500: 'خطأ داخلي في الخادم',
  502: 'خطأ في البوابة',
  503: 'الخدمة غير متاحة',
  504: 'انتهت مهلة البوابة',
};

/**
 * استخراج رسالة الخطأ من استجابة الـ API
 * @param {Object} error - كائن الخطأ من axios
 * @returns {Object} - كائن يحتوي على معلومات الخطأ
 */
export const extractErrorMessage = (error) => {
  // إذا لم يكن هناك استجابة (مشكلة في الشبكة)
  if (!error.response) {
    if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
      return {
        message: DEFAULT_ERROR_MESSAGES.NETWORK_ERROR,
        type: 'network',
        code: 'NETWORK_ERROR'
      };
    }
    if (error.code === 'ECONNABORTED') {
      return {
        message: DEFAULT_ERROR_MESSAGES.TIMEOUT_ERROR,
        type: 'timeout',
        code: 'TIMEOUT_ERROR'
      };
    }
    return {
      message: DEFAULT_ERROR_MESSAGES.UNKNOWN_ERROR,
      type: 'unknown',
      code: 'UNKNOWN_ERROR'
    };
  }

  const { status, data } = error.response;
  
  // محاولة استخراج الرسالة من البيانات المرجعة
  let message = DEFAULT_ERROR_MESSAGES.UNKNOWN_ERROR;
  let details = null;
  let code = null;

  if (data) {
    // إذا كانت البيانات نص بسيط
    if (typeof data === 'string') {
      message = data;
    }
    // إذا كانت البيانات كائن
    else if (typeof data === 'object') {
      // ترتيب الأولوية في البحث عن الرسالة
      message = data.detail || 
                data.error || 
                data.message || 
                data.non_field_errors?.[0] ||
                ERROR_CODE_MESSAGES[status] ||
                DEFAULT_ERROR_MESSAGES.UNKNOWN_ERROR;
      
      details = data.errors || data.details;
      code = data.code;
    }
  } else {
    message = ERROR_CODE_MESSAGES[status] || DEFAULT_ERROR_MESSAGES.UNKNOWN_ERROR;
  }

  // تحديد نوع الخطأ حسب رمز الحالة
  let type = 'unknown';
  if (status >= 400 && status < 500) {
    type = 'client';
  } else if (status >= 500) {
    type = 'server';
  }

  return {
    message,
    details,
    type,
    code: code || status.toString(),
    status
  };
};

/**
 * معالجة الأخطاء وإرجاع رسالة مناسبة للمستخدم
 * @param {Object} error - كائن الخطأ
 * @param {string} context - سياق الخطأ (اختياري)
 * @returns {Object} - معلومات الخطأ المعالجة
 */
export const handleApiError = (error, context = '') => {
  const errorInfo = extractErrorMessage(error);
  
  // إضافة السياق إذا كان متوفراً
  if (context) {
    errorInfo.context = context;
    errorInfo.fullMessage = `${context}: ${errorInfo.message}`;
  } else {
    errorInfo.fullMessage = errorInfo.message;
  }

  // تسجيل الخطأ في الكونسول للمطورين
  console.error('API Error:', {
    context,
    error: errorInfo,
    originalError: error
  });

  return errorInfo;
};

/**
 * معالجة أخطاء النماذج وإرجاع رسائل مناسبة للحقول
 * @param {Object} error - كائن الخطأ
 * @returns {Object} - كائن يحتوي على أخطاء الحقول
 */
export const handleFormErrors = (error) => {
  const errorInfo = extractErrorMessage(error);
  const fieldErrors = {};

  if (errorInfo.details && typeof errorInfo.details === 'object') {
    // معالجة أخطاء الحقول
    Object.keys(errorInfo.details).forEach(field => {
      const fieldError = errorInfo.details[field];
      if (Array.isArray(fieldError)) {
        fieldErrors[field] = fieldError[0]; // أخذ أول رسالة خطأ
      } else {
        fieldErrors[field] = fieldError;
      }
    });
  }

  return {
    generalError: errorInfo.message,
    fieldErrors,
    hasFieldErrors: Object.keys(fieldErrors).length > 0
  };
};

/**
 * إنشاء رسالة خطأ مخصصة للعمليات المختلفة
 * @param {string} operation - نوع العملية
 * @param {Object} error - كائن الخطأ
 * @returns {string} - رسالة الخطأ المخصصة
 */
export const getOperationErrorMessage = (operation, error) => {
  const errorInfo = extractErrorMessage(error);
  
  const operationMessages = {
    'login': 'فشل في تسجيل الدخول',
    'register': 'فشل في إنشاء الحساب',
    'fetch': 'فشل في جلب البيانات',
    'create': 'فشل في إنشاء العنصر',
    'update': 'فشل في تحديث العنصر',
    'delete': 'فشل في حذف العنصر',
    'upload': 'فشل في رفع الملف',
    'download': 'فشل في تحميل الملف',
    'submit': 'فشل في إرسال البيانات',
    'verify': 'فشل في التحقق',
    'reset': 'فشل في إعادة التعيين'
  };

  const operationMessage = operationMessages[operation] || 'فشل في العملية';
  return `${operationMessage}: ${errorInfo.message}`;
};

/**
 * فحص ما إذا كان الخطأ يتطلب إعادة تسجيل الدخول
 * @param {Object} error - كائن الخطأ
 * @returns {boolean} - true إذا كان يتطلب إعادة تسجيل الدخول
 */
export const requiresReauth = (error) => {
  if (!error.response) return false;
  
  const { status, data } = error.response;
  
  // رمز 401 يعني عدم المصادقة
  if (status === 401) return true;
  
  // فحص رسائل انتهاء صلاحية التوكن
  if (data && typeof data === 'object') {
    const message = (data.detail || data.error || '').toLowerCase();
    return message.includes('token') && 
           (message.includes('expired') || message.includes('invalid'));
  }
  
  return false;
};

/**
 * إنشاء كائن خطأ موحد للعرض في الـ UI
 * @param {Object} error - كائن الخطأ
 * @param {string} defaultMessage - رسالة افتراضية
 * @returns {Object} - كائن الخطأ للعرض
 */
export const createDisplayError = (error, defaultMessage = DEFAULT_ERROR_MESSAGES.UNKNOWN_ERROR) => {
  const errorInfo = handleApiError(error);
  
  return {
    message: errorInfo.message || defaultMessage,
    type: errorInfo.type || 'error',
    code: errorInfo.code,
    timestamp: new Date().toISOString(),
    canRetry: errorInfo.type === 'network' || errorInfo.type === 'timeout' || errorInfo.status >= 500
  };
};

// تصدير دالة مساعدة للاستخدام السريع
export const getErrorMessage = (error, context = '') => {
  return handleApiError(error, context).fullMessage;
};
