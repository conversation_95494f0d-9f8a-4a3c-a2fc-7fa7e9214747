// WebSocket utility for Django Channels integration

const WS_BASE_URL = process.env.REACT_APP_WS_BASE_URL || 'ws://localhost:8000';

/**
 * Create WebSocket connection URL
 * @param {string} endpoint - WebSocket endpoint (e.g., 'ws/chat/room_name/')
 * @param {string} token - Authentication token (optional)
 * @returns {string} Complete WebSocket URL
 */
export const createWebSocketURL = (endpoint, token = null) => {
  const baseUrl = WS_BASE_URL.replace(/\/$/, ''); // Remove trailing slash
  const cleanEndpoint = endpoint.replace(/^\//, ''); // Remove leading slash
  
  let url = `${baseUrl}/${cleanEndpoint}`;
  
  // Add token as query parameter if provided
  if (token) {
    const separator = url.includes('?') ? '&' : '?';
    url += `${separator}token=${token}`;
  }
  
  return url;
};

/**
 * Create WebSocket connection with authentication
 * @param {string} endpoint - WebSocket endpoint
 * @param {Object} options - Connection options
 * @returns {WebSocket} WebSocket instance
 */
export const createAuthenticatedWebSocket = (endpoint, options = {}) => {
  const token = localStorage.getItem('access');
  const url = createWebSocketURL(endpoint, token);
  
  const ws = new WebSocket(url);
  
  // Default event handlers
  ws.onopen = (event) => {
    console.log('WebSocket connected:', endpoint);
    if (options.onOpen) options.onOpen(event);
  };
  
  ws.onclose = (event) => {
    console.log('WebSocket disconnected:', endpoint, event.code, event.reason);
    if (options.onClose) options.onClose(event);
  };
  
  ws.onerror = (error) => {
    console.error('WebSocket error:', endpoint, error);
    if (options.onError) options.onError(error);
  };
  
  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      if (options.onMessage) options.onMessage(data);
    } catch (e) {
      console.error('Failed to parse WebSocket message:', event.data);
      if (options.onMessage) options.onMessage(event.data);
    }
  };
  
  return ws;
};

/**
 * Send message through WebSocket
 * @param {WebSocket} ws - WebSocket instance
 * @param {Object} message - Message object to send
 */
export const sendWebSocketMessage = (ws, message) => {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(message));
  } else {
    console.warn('WebSocket is not open. ReadyState:', ws.readyState);
  }
};

/**
 * Common WebSocket endpoints
 */
export const WS_ENDPOINTS = {
  // Chat endpoints
  CHAT_ROOM: (roomId) => `ws/chat/${roomId}/`,
  PRIVATE_CHAT: (userId) => `ws/private-chat/${userId}/`,
  
  // Notification endpoints
  NOTIFICATIONS: 'ws/notifications/',
  
  // AI Assistant endpoints
  AI_ASSISTANT: 'ws/ai-assistant/',
  
  // Communication endpoints
  COMMUNICATION: 'ws/communication/',
  
  // Admin dashboard real-time updates
  ADMIN_DASHBOARD: 'ws/admin-dashboard/',
};

/**
 * WebSocket connection states
 */
export const WS_STATES = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
};

/**
 * Check if WebSocket is connected
 * @param {WebSocket} ws - WebSocket instance
 * @returns {boolean} Connection status
 */
export const isWebSocketConnected = (ws) => {
  return ws && ws.readyState === WebSocket.OPEN;
};

/**
 * Close WebSocket connection safely
 * @param {WebSocket} ws - WebSocket instance
 */
export const closeWebSocket = (ws) => {
  if (ws && ws.readyState !== WebSocket.CLOSED) {
    ws.close();
  }
};

export default {
  createWebSocketURL,
  createAuthenticatedWebSocket,
  sendWebSocketMessage,
  WS_ENDPOINTS,
  WS_STATES,
  isWebSocketConnected,
  closeWebSocket,
};
