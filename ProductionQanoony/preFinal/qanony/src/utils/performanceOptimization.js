// Performance optimization utilities for better SEO and user experience

/**
 * Lazy load images with intersection observer
 */
export const lazyLoadImages = () => {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => imageObserver.observe(img));
  }
};

/**
 * Preload critical resources
 */
export const preloadCriticalResources = () => {
  // Preload critical CSS
  const criticalCSS = [
    '/static/css/main.css',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap'
  ];

  criticalCSS.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = href;
    document.head.appendChild(link);
  });

  // Preload critical JavaScript
  const criticalJS = [
    '/static/js/main.js'
  ];

  criticalJS.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'script';
    link.href = src;
    document.head.appendChild(link);
  });
};

/**
 * Optimize images for better performance
 */
export const optimizeImages = () => {
  const images = document.querySelectorAll('img');
  
  images.forEach(img => {
    // Add loading="lazy" for non-critical images
    if (!img.hasAttribute('loading') && !img.closest('.hero, .above-fold')) {
      img.setAttribute('loading', 'lazy');
    }

    // Add proper alt text if missing
    if (!img.alt && img.dataset.alt) {
      img.alt = img.dataset.alt;
    }

    // Add width and height to prevent layout shift
    if (!img.width && img.dataset.width) {
      img.width = img.dataset.width;
    }
    if (!img.height && img.dataset.height) {
      img.height = img.dataset.height;
    }
  });
};

/**
 * Minimize layout shift by setting dimensions
 */
export const preventLayoutShift = () => {
  // Add aspect ratio containers for dynamic content
  const dynamicContainers = document.querySelectorAll('.dynamic-content');
  
  dynamicContainers.forEach(container => {
    if (!container.style.minHeight) {
      container.style.minHeight = '200px'; // Prevent layout shift
    }
  });
};

/**
 * Optimize third-party scripts loading
 */
export const optimizeThirdPartyScripts = () => {
  // Defer non-critical third-party scripts
  const scripts = document.querySelectorAll('script[src*="analytics"], script[src*="gtag"], script[src*="facebook"]');
  
  scripts.forEach(script => {
    if (!script.hasAttribute('defer') && !script.hasAttribute('async')) {
      script.setAttribute('defer', '');
    }
  });
};

/**
 * Service Worker registration for caching
 */
export const registerServiceWorker = () => {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          console.log('SW registered: ', registration);
        })
        .catch(registrationError => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
};

/**
 * Prefetch important pages
 */
export const prefetchImportantPages = () => {
  const importantPages = [
    '/about',
    '/register',
    '/login',
    '/library',
    '/jobs'
  ];

  // Prefetch on hover or after initial load
  setTimeout(() => {
    importantPages.forEach(page => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = page;
      document.head.appendChild(link);
    });
  }, 2000); // Wait 2 seconds after initial load
};

/**
 * Optimize font loading
 */
export const optimizeFontLoading = () => {
  // Use font-display: swap for better performance
  const style = document.createElement('style');
  style.textContent = `
    @font-face {
      font-family: 'Cairo';
      font-display: swap;
    }
    @font-face {
      font-family: 'Tajawal';
      font-display: swap;
    }
  `;
  document.head.appendChild(style);
};

/**
 * Initialize all performance optimizations
 */
export const initPerformanceOptimizations = () => {
  // Run immediately
  preloadCriticalResources();
  optimizeFontLoading();
  
  // Run after DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    lazyLoadImages();
    optimizeImages();
    preventLayoutShift();
    optimizeThirdPartyScripts();
    prefetchImportantPages();
  });

  // Register service worker
  registerServiceWorker();
};

/**
 * Monitor Core Web Vitals
 */
export const monitorCoreWebVitals = () => {
  // Monitor LCP (Largest Contentful Paint)
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP:', entry.startTime);
        }
        if (entry.entryType === 'first-input') {
          console.log('FID:', entry.processingStart - entry.startTime);
        }
      }
    });

    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input'] });
  }

  // Monitor CLS (Cumulative Layout Shift)
  let clsValue = 0;
  let clsEntries = [];

  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (!entry.hadRecentInput) {
        clsValue += entry.value;
        clsEntries.push(entry);
      }
    }
    console.log('CLS:', clsValue);
  });

  observer.observe({ entryTypes: ['layout-shift'] });
};
