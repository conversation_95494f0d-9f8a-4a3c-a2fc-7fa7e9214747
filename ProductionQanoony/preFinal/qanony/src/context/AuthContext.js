import React, { createContext, useState, useEffect } from 'react';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';

export const AuthContext = createContext();

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(() => {
    const stored = localStorage.getItem('user');
    return stored ? JSON.parse(stored) : null;
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load tokens from storage
  const [tokens, setTokens] = useState(() => {
    const access = localStorage.getItem('access');
    const refresh = localStorage.getItem('refresh');
    if (access && refresh) {
      // Set axios header immediately
      axios.defaults.headers.common['Authorization'] = `Bearer ${access}`;
      return { access, refresh };
    }
    return null;
  });

  // Set axios header whenever tokens change
  useEffect(() => {
    if (tokens?.access) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${tokens.access}`;
    } else {
      delete axios.defaults.headers.common['Authorization'];
    }
  }, [tokens]);

  useEffect(() => {
    // Global interceptor: إذا وصلنا إلى 401 بسبب توكن غير صالح نمسح التوكنات ونُعيد توجيه المستخدم
    const interceptor = axios.interceptors.response.use(
      res => res,
      async (err) => {
        if (err.response && err.response.status === 401) {
          const originalRequest = err.config;
          const refresh = localStorage.getItem('refresh');
          const errorDetail = err.response.data?.detail || '';
          const isTokenError = errorDetail.includes('token') || 
                              errorDetail.includes('credentials were not provided') ||
                              errorDetail.includes('Given token not valid');
          
          if (refresh && !originalRequest._retry && isTokenError) {
            originalRequest._retry = true;
            try {
              const { data } = await axios.post(`${API_BASE_URL}/api/auth/token/refresh/`, { refresh }, { headers: { Authorization: '' } });
              const { access } = data;
              localStorage.setItem('access', access);
              setTokens({ access, refresh });
              axios.defaults.headers.common['Authorization'] = `Bearer ${access}`;
              originalRequest.headers['Authorization'] = `Bearer ${access}`;
              return axios(originalRequest);
            } catch (e) {
              // فشل التجديد - لا تمسح التوكنات فوراً، دع المستخدم يحاول مرة أخرى
              console.warn('Token refresh failed, but keeping tokens for retry');
              return Promise.reject(err);
            }
          }
          // فقط امسح التوكنات إذا كان refresh token غير موجود أو فشل التجديد مرتين
          if (!refresh || originalRequest._retry) {
            localStorage.removeItem('access');
            localStorage.removeItem('refresh');
            localStorage.removeItem('user');
            setTokens(null);
            setUser(null);
            delete axios.defaults.headers.common['Authorization'];
            window.location.href = '/login'; // تحويل المستخدم لتسجيل الدخول
          }
        }
        return Promise.reject(err);
      }
    );
    // إلغاء التسجيل عند تفكيك المكوّن
    return () => axios.interceptors.response.eject(interceptor);
  }, []);

  // Always load user from localStorage on mount
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    // If tokens exist, refresh profile from backend
    const access = localStorage.getItem('access');
    const refresh = localStorage.getItem('refresh');
    if (access && refresh) {
      setTokens({ access, refresh });
      axios.defaults.headers.common['Authorization'] = `Bearer ${access}`;
      fetchProfile()
        .then((data) => {
          setUser(data);
          localStorage.setItem('user', JSON.stringify(data));
        })
        .catch(() => {
          // ignore errors here
        });
    }
  }, []);

  const register = async (data) => {
    setLoading(true);
    setError(null);

    // prepare payload - use FormData if file or non-JSON fields exist
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, value);
      }
    });

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };

    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/auth/register/`,
        formData,
        config
      );
      setUser(response.data);
      return { success: true, data: response.data };
    } catch (err) {
      const message = err.response?.data || err.message;
      setError(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  const login = async ({ email, password }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/auth/login/`, {
        email,
        password,
      }, {
        // Ensure this request doesn't use a potentially old token
        headers: { 'Authorization': '' }
      });
      
      const { access, refresh } = response.data;
      
      // *** FIX: Manually set the Authorization header for subsequent requests ***
      axios.defaults.headers.common['Authorization'] = `Bearer ${access}`;

      setTokens({ access, refresh });
      localStorage.setItem('access', access);
      localStorage.setItem('refresh', refresh);

      // Now, try to fetch the profile data with the correct token
      try {
        const profile = await fetchProfile();
        setUser(profile);
        localStorage.setItem('user', JSON.stringify(profile));
        return { success: true, user: profile };
      } catch (e) {
        // Fallback to decoded JWT
        try {
          const decoded = jwtDecode(access);
          setUser(decoded);
          localStorage.setItem('user', JSON.stringify(decoded));
          return { success: true, user: decoded };
        } catch {
          setUser({ email });
          return { success: true, user: { email } };
        }
      }
    } catch (err) {
      const message = err.response?.data || err.message;
      setError(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await axios.post(`${API_BASE_URL}/api/auth/logout/`);
    } catch (e) {
      // ignore errors (token may be invalid)
    }
    setTokens(null);
    setUser(null);
    localStorage.removeItem('access');
    localStorage.removeItem('refresh');
    localStorage.removeItem('user');
  };

  const fetchProfile = async () => {
    try {
      const res = await axios.get(`${API_BASE_URL}/api/auth/profile/`);
      return res.data;
    } catch (e) {
      throw e;
    }
  };

  const updateProfile = async (data) => {
    try {
      const res = await axios.patch(`${API_BASE_URL}/api/auth/profile/`, data);
      return res.data;
    } catch (e) {
      throw e;
    }
  };

  const verifyEmail = async (token) => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/auth/verify-email/`, { token });
      // if success maybe update user state ???
      return { success: true, data: response.data };
    } catch (err) {
      const message = err.response?.data || err.message;
      setError(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    loading,
    error,
    isAuthenticated: (() => {
      const authenticated = !!user && !!tokens?.access;
      return authenticated;
    })(),
    register,
    login,
    logout,
    fetchProfile,
    updateProfile,
    verifyEmail,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}; 