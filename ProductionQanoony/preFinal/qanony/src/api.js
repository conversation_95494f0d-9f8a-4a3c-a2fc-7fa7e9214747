import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://127.0.0.1:8000';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000, // 10 ثوان timeout
});

apiClient.interceptors.request.use(
  (config) => {
    // List of public endpoints that don't require authentication
    const publicEndpoints = [
      '/api/subscriptions/plans/',
      '/api/auth/register/',
      '/api/auth/login/',
      '/api/auth/verify-email/',
      '/api/auth/password-reset/request/',
      '/api/auth/password-reset/confirm/',
      '/api/email-verification/request/',
      '/api/email-verification/confirm/',
      '/api/email-verification/resend/',
    ];
    
    // Check if this is a public endpoint
    const isPublicEndpoint = publicEndpoints.some(endpoint => 
      config.url && config.url.includes(endpoint)
    );
    
    if (isPublicEndpoint) {
      return config;
    }
    
    // Use the global axios Authorization header instead of reading from localStorage
    const globalAuthHeader = axios.defaults.headers.common['Authorization'];
    if (globalAuthHeader) {
      config.headers.Authorization = globalAuthHeader;
    } else {
      // Fallback to localStorage if global header is not set
      const token = localStorage.getItem('access');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor لمعالجة الأخطاء
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // تسجيل الأخطاء للمطورين
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });

    // إضافة معلومات إضافية للخطأ
    if (error.response) {
      error.isApiError = true;
      error.statusCode = error.response.status;
    } else if (error.request) {
      error.isNetworkError = true;
    }

    return Promise.reject(error);
  }
);

export default apiClient;