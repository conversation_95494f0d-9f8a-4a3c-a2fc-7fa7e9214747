import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';

const JoinInstructorPage = () => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');
  const { register, handleSubmit, reset, formState: { errors } } = useForm();

  useEffect(() => {
    // Fetch current application status
    axios.get('/api/auth/instructor-application/')
      .then(res => {
        setStatus(res.data);
        setLoading(false);
      })
      .catch(() => {
        setStatus(null);
        setLoading(false);
      });
  }, [submitted]);

  const onSubmit = async (data) => {
    setError('');
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (key === 'cv' && value && value.length > 0) {
        formData.append('cv', value[0]);
      } else if (value) {
        formData.append(key, value);
      }
    });
    setLoading(true);
    try {
      await axios.post('/api/auth/instructor-application/', formData);
      setSubmitted(true);
      reset();
    } catch (e) {
      setError(e.response?.data?.detail || 'حدث خطأ، حاول مرة أخرى');
    }
    setLoading(false);
  };

  return (
    <div className="join-instructor-container" style={{maxWidth: 500, margin: '40px auto', background: '#fff', borderRadius: 12, boxShadow: '0 4px 10px rgba(0,0,0,0.08)', padding: 32, direction: 'rtl'}}>
      <h1 style={{textAlign: 'center', color: 'var(--primary)', fontWeight: 700}}>انضم إلينا كمعلم</h1>
      <p style={{textAlign: 'center', marginBottom: 16}}>
        منصة قانوني ترحب بانضمامك لفريق المعلمين!<br/>
        إذا كنت صاحب خبرة أو شغف في تدريس القانون، يمكنك التقديم الآن ليتم مراجعة طلبك من الإدارة.
      </p>
      <ul style={{marginBottom: 24, color: '#555', fontSize: 15}}>
        <li>✔️ شارك خبرتك مع آلاف الطلاب.</li>
        <li>✔️ احصل على مزايا حصرية للمعلمين.</li>
        <li>✔️ دعم فني وإداري متواصل.</li>
      </ul>
      {loading ? <div style={{textAlign: 'center'}}>جاري التحميل...</div> : (
        status && status.is_approved !== undefined ? (
          <div style={{textAlign: 'center', color: status.is_approved ? 'green' : '#d9534f', fontWeight: 600}}>
            {status.is_approved
              ? 'تمت الموافقة على طلبك! أنت الآن معلم في المنصة.'
              : status.rejection_reason
                ? `تم رفض الطلب: ${status.rejection_reason}`
                : 'طلبك قيد المراجعة من الإدارة.'}
          </div>
        ) : (
          <form className="join-instructor-form" onSubmit={handleSubmit(onSubmit)} style={{display: 'flex', flexDirection: 'column', gap: 14}}>
            <label>الاسم الأول</label>
            <input type="text" {...register('first_name', { required: true })} />
            {errors.first_name && <span className="error">مطلوب</span>}

            <label>الاسم الأخير</label>
            <input type="text" {...register('last_name', { required: true })} />
            {errors.last_name && <span className="error">مطلوب</span>}

            <label>البريد الإلكتروني</label>
            <input type="email" {...register('email', { required: true })} />

            <label>رقم الهاتف</label>
            <input type="text" {...register('phone_number', { required: true })} />
            {errors.phone_number && <span className="error">مطلوب</span>}

            <label>التخصص</label>
            <input type="text" {...register('specialty', { required: false })} placeholder="مثال: القانون المدني" />

            <label>نبذة تعريفية</label>
            <textarea {...register('bio', { required: true })} rows={3} />
            {errors.bio && <span className="error">مطلوب</span>}

            <label>السيرة الذاتية (إجباري)</label>
            <input type="file" {...register('cv', { required: true })} accept=".pdf,.doc,.docx,.jpg,.png" />
            {errors.cv && <span className="error">مطلوب</span>}

            {error && <div className="error" style={{color: 'red', textAlign: 'center'}}>{error}</div>}
            <button type="submit" className="btn-primary" disabled={loading} style={{marginTop: 10}}>
              {loading ? 'جاري الإرسال...' : 'إرسال الطلب'}
            </button>
          </form>
        )
      )}
    </div>
  );
};

export default JoinInstructorPage; 