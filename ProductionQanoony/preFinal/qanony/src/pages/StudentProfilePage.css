/* Main layout container for the student profile page */
.student-profile-layout {
  direction: rtl; /* Right-to-left layout */
  min-height: 80vh; /* Ensure it takes up most of the viewport height */
  padding: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Profile Header Styles */
.profile-header {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.2);
}

@media (max-width: 768px) {
  .student-profile-layout {
    padding: 0.5rem;
  }

  .profile-header {
    padding: 1rem !important;
  }

  .profile-header .grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }
}

/* Sidebar styling */
.profile-sidebar {
  background-color: transparent;
  padding: 0;
}

@media (max-width: 1024px) {
  .profile-sidebar {
    width: 100% !important;
    margin-bottom: 1rem;
  }
}

/* Course Cards Enhancement */
.course-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e5e7eb;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Progress Bar Animation */
.progress-bar {
  transition: width 0.5s ease-in-out;
}

/* Search Input Enhancement */
.search-input {
  transition: all 0.2s ease-in-out;
}

.search-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile Responsive */
@media (max-width: 640px) {
  .course-card {
    padding: 1rem !important;
  }

  .profile-header h1 {
    font-size: 1.5rem !important;
  }

  .profile-header .grid {
    grid-template-columns: 1fr !important;
    text-align: center;
  }
}

/* Main content area styling */
.profile-content {
  flex-grow: 1; /* Take up the remaining space */
  padding: 1rem;
}

/* Ensure tab content headers have consistent styling */
.profile-content h2 {
  font-size: 1.75rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 0.5rem;
}

/* General card styling used across tabs */
.course-card, .summary-card, .quiz-card, .chatroom-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.course-card:hover, .summary-card:hover, .quiz-card:hover, .chatroom-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

/* Generic button styles for cards */
.watch-button,
.download-button,
.start-quiz-button,
.open-chat-button,
.submit-quiz-button {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 6px;
  background-color: #facc15;
  color: #78350f;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
}

.watch-button:hover,
.download-button:hover,
.start-quiz-button:hover,
.open-chat-button:hover,
.submit-quiz-button:hover {
  background-color: #fde047;
  color: #a16207;
}

.back-button {
  display: inline-block;
  margin-bottom: 16px;
  padding: 8px 16px;
  border-radius: 6px;
  background-color: #6c757d;
  color: #fff;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.back-button:hover {
  background-color: #5a6268;
}

/* Custom scrollbar for sidebar */
.sidebar nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar nav::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.sidebar nav::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.sidebar nav::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .student-profile-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
  }

  .sidebar nav {
    flex-direction: row;
    gap: 8px;
    width: 100%;
  }

  .nav-button {
    flex: 1;
    white-space: nowrap;
    text-align: center;
  }
}

/* --- Lecture Detail View --- */
.lecture-detail-container {
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.07);
}

/* Video Container Styles */
.video-container {
  position: relative;
  width: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.video-container iframe {
  display: block;
  width: 100%;
  height: 400px;
  border: none;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 2rem;
}

.video-placeholder p {
  margin: 0.5rem 0;
  text-align: center;
}

.lecture-info {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #facc15;
}

.lecture-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #333;
}

/* Watch Button Styles */
.watch-button-large {
  display: inline-block;
  background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.watch-button-large:hover {
  background: linear-gradient(135deg, #cc0000 0%, #990000 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  color: white;
  text-decoration: none;
}

.watch-button-small {
  display: inline-block;
  background: #007bff;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.875rem;
  transition: background-color 0.3s ease;
}

.watch-button-small:hover {
  background: #0056b3;
  color: white;
  text-decoration: none;
}

/* Lecture Card Styles */
.lecture-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.lecture-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

/* Status Badge Styles */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.available {
  background-color: #d1fae5;
  color: #065f46;
}

.status-badge.unavailable {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Debug Styles */
.debug-info {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 8px;
  margin: 8px 0;
  font-family: monospace;
  font-size: 0.875rem;
  color: #374151;
}

/* Responsive Video */
@media (max-width: 768px) {
  .video-container iframe {
    height: 250px;
  }
  
  .video-placeholder {
    min-height: 200px;
    padding: 1rem;
  }
  
  .watch-button-large {
    padding: 10px 20px;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .video-container iframe {
    height: 200px;
  }
  
  .video-placeholder {
    min-height: 150px;
    padding: 0.75rem;
  }
}

/* --- Quiz Button Styling --- */
.start-quiz-button {
  display: inline-block;
  width: 100%;
  background-color: #facc15;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  text-decoration: none;
  font-size: 1rem;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
}

.start-quiz-button:hover {
  background-color: #eab308;
  transform: translateY(-2px);
}

/* --- Quiz Taking Interface --- */
.quiz-taking-container, .quiz-results-container {
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.07);
}

.question-block {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.question-text {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.answers-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.answer-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.answer-label:hover {
  background-color: #f9f9f9;
}

.answer-label input[type="radio"] {
  accent-color: #facc15;
}

.quiz-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.submit-quiz-button {
  background-color: #28a745;
  color: white;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
}

.submit-quiz-button:hover {
  background-color: #218838;
}

/* --- Quiz Results View --- */
.score-summary {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 2rem;
}

.score-summary p {
  font-size: 1.2rem;
}

.score-summary strong {
  font-size: 2rem;
  color: #facc15;
}

.results-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-item {
  padding: 1rem;
  border-radius: 6px;
  border-left-width: 5px;
}

.result-item.correct {
  background-color: #D1FAE5; /* Light Green */
  border-left-color: #10B981; /* Green */
}

.result-item.incorrect {
  background-color: #FEE2E2; /* Light Red */
  border-left-color: #EF4444; /* Red */
}

/* Download PDF Button Styles */
.download-pdf-button {
  display: inline-block;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.download-pdf-button:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  color: white;
  text-decoration: none;
} 