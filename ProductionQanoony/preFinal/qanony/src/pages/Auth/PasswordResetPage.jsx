import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import axios from 'axios';
import './PasswordResetPage.css';

const PasswordResetPage = () => {
  const [step, setStep] = useState(1);
  const [message, setMessage] = useState('');

  const { register: registerEmail, handleSubmit: handleSubmitEmail, formState: { errors: errorsEmail } } = useForm();
  const { register: registerConfirm, handleSubmit: handleSubmitConfirm, formState: { errors: errorsConfirm } } = useForm();

  const requestReset = async ({ email }) => {
    try {
      await axios.post('/api/auth/password-reset/request/', { email });
      setMessage('تم إرسال رمز التحقق إلى بريدك الإلكتروني.');
      setStep(2);
    } catch (e) {
      setMessage('تعذر إرسال البريد، تأكد من صحة الإيميل.');
    }
  };

  const confirmReset = async (data) => {
    try {
      await axios.post('/api/auth/password-reset/confirm/', data);
      setMessage('تم تغيير كلمة المرور بنجاح، يمكنك تسجيل الدخول الآن.');
      setStep(3);
    } catch (e) {
      setMessage('حدث خطأ، تأكد من الرمز وكلمة المرور الجديدة.');
    }
  };

  return (
    <div className="password-reset-container">
      <h1>استعادة كلمة المرور</h1>
      {message && <p className="msg">{message}</p>}

      {step === 1 && (
        <form onSubmit={handleSubmitEmail(requestReset)} className="reset-form">
          <label>البريد الإلكتروني</label>
          <input type="email" {...registerEmail('email', { required: true })} />
          {errorsEmail.email && <span className="error">مطلوب</span>}
          <button type="submit" className="btn-primary">إرسال رمز التحقق</button>
        </form>
      )}

      {step === 2 && (
        <form onSubmit={handleSubmitConfirm(confirmReset)} className="reset-form">
          <label>رمز التحقق</label>
          <input {...registerConfirm('token', { required: true })} />
          {errorsConfirm.token && <span className="error">مطلوب</span>}
          <label>كلمة مرور جديدة</label>
          <input type="password" {...registerConfirm('new_password', { required: true })} />
          {errorsConfirm.new_password && <span className="error">مطلوب</span>}
          <button type="submit" className="btn-primary">تأكيد</button>
        </form>
      )}
    </div>
  );
};

export default PasswordResetPage; 