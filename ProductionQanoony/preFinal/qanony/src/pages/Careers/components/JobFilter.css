.job-filter {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    height: fit-content;
}

.filter-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #343a40;
    margin: 0 0 24px;
    text-align: right;
    border-bottom: 1px solid #f1f3f5;
    padding-bottom: 16px;
}

.filter-group {
    margin-bottom: 24px;
}

.filter-group h4 {
    font-size: 1.1rem;
    font-weight: 500;
    color: #495057;
    margin: 0 0 12px;
    text-align: right;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.95rem;
    color: #495057;
    text-align: right;
    width: 100%;
}

.filter-select,
.filter-input {
    width: 100%;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #ced4da;
    font-size: 1rem;
    color: #495057;
    transition: border-color 0.2s;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #facc15;
    box-shadow: 0 0 0 2px rgba(250, 206, 25, 0.2);
} 