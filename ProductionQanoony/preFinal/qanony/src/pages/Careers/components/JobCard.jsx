import React from 'react';
import { Link } from 'react-router-dom';
import './JobCard.css'; // We will create this file later

const JobCard = ({ job }) => {
    // Fallback for missing job data
    if (!job) {
        return <div className="job-card-placeholder">لا توجد بيانات للوظيفة</div>;
    }
    
    const { id, title_ar, company_name, location, job_type, application_deadline } = job;
    
    return (
        <div className="job-card">
            <div className="card-header">
                <h3 className="job-title">{title_ar || 'عنوان الوظيفة غير متوفر'}</h3>
                <p className="company-name">{company_name || 'اسم الشركة غير متوفر'}</p>
            </div>
            <div className="card-body">
                <p className="job-info"><strong>الموقع:</strong> {location || 'غير محدد'}</p>
                <p className="job-info"><strong>نوع الوظيفة:</strong> {job_type || 'غير محدد'}</p>
                <p className="job-info"><strong>آخر موعد للتقديم:</strong> {application_deadline ? new Date(application_deadline).toLocaleDateString('ar-EG') : 'غير محدد'}</p>
            </div>
            <div className="card-footer">
                <Link to={`/jobs/${id}`} className="details-button">عرض التفاصيل</Link>
            </div>
        </div>
    );
};

export default JobCard;
