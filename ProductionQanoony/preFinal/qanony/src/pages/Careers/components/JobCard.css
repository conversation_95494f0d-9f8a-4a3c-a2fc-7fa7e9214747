.job-card {
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 24px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.job-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.card-header {
  margin-bottom: 16px;
  border-bottom: 1px solid #f1f3f5;
  padding-bottom: 16px;
}

.job-title {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #343a40;
}

.company-name {
  margin: 4px 0 0;
  font-size: 1rem;
  color: #868e96;
}

.card-body .job-info {
  margin: 0 0 10px;
  font-size: 0.95rem;
  color: #495057;
}

.card-body .job-info strong {
  color: #343a40;
}

.card-footer {
  margin-top: auto;
  padding-top: 16px;
  text-align: left;
}

.details-button {
  background-color: #facc15;
  color: #fff;
  padding: 10px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s ease;
  display: inline-block;
}

.details-button:hover {
  background-color: #eab308;
} 