import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './JobFilter.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const JobFilter = ({ onFilterChange }) => {
    const [categories, setCategories] = useState([]);
    const [selectedCategories, setSelectedCategories] = useState([]);
    const [jobType, setJobType] = useState('');
    const [location, setLocation] = useState('');

    // Fetch categories from API
    useEffect(() => {
        const fetchCategories = async () => {
            try {
                const { data } = await axios.get(`${API_BASE_URL}/api/careers/categories/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
                setCategories(Array.isArray(data) ? data : []);
            } catch (error) {
                console.error("Failed to fetch job categories:", error);
            }
        };
        fetchCategories();
    }, []);

    // Handle filter changes and notify parent component
    useEffect(() => {
        const filters = {
            categories: selectedCategories.join(','),
            job_type: jobType,
            location: location,
        };
        onFilterChange(filters);
    }, [selectedCategories, jobType, location, onFilterChange]);

    const handleCategoryChange = (e) => {
        const { value, checked } = e.target;
        setSelectedCategories(prev => 
            checked ? [...prev, value] : prev.filter(cat => cat !== value)
        );
    };

    const jobTypes = ['دوام كامل', 'دوام جزئي', 'تدريب', 'عن بعد'];

    return (
        <aside className="job-filter">
            <h3 className="filter-title">تصفية الوظائف</h3>

            {/* Category Filter */}
            <div className="filter-group">
                <h4>فئة الوظيفة</h4>
                <div className="checkbox-group">
                    {categories.map(cat => (
                        <label key={cat.id} className="checkbox-label">
                            <input 
                                type="checkbox" 
                                value={cat.id} 
                                onChange={handleCategoryChange}
                            />
                            {cat.name_ar}
                        </label>
                    ))}
                </div>
            </div>

            {/* Job Type Filter */}
            <div className="filter-group">
                <h4>نوع الوظيفة</h4>
                <select value={jobType} onChange={(e) => setJobType(e.target.value)} className="filter-select">
                    <option value="">الكل</option>
                    {jobTypes.map(type => (
                        <option key={type} value={type}>{type}</option>
                    ))}
                </select>
            </div>

            {/* Location Filter */}
            <div className="filter-group">
                <h4>الموقع</h4>
                <input 
                    type="text" 
                    placeholder="مثال: الرياض"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="filter-input"
                />
            </div>
        </aside>
    );
};

export default JobFilter;
