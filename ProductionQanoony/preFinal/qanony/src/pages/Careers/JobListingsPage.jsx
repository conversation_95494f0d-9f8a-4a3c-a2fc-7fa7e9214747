import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import JobCard from './components/JobCard';
import JobFilter from './components/JobFilter';
import SEOHead from '../../components/SEO/SEOHead';
import './JobListingsPage.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const JobListingsPage = () => {
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    categories: '',
    job_type: '',
    location: '',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Debounce search term
  const useDebounce = (value, delay) => {
    const [debouncedValue, setDebouncedValue] = useState(value);
    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);
      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);
    return debouncedValue;
  };

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const fetchJobs = useCallback(async (isNewQuery = false) => {
    if (isNewQuery) {
      setPage(1); // Reset page for new filter/search
      setJobs([]); // Clear existing jobs
    }
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: isNewQuery ? 1 : page,
      });

      if (debouncedSearchTerm) params.append('search', debouncedSearchTerm);
      if (filters.categories) params.append('category', filters.categories);
      if (filters.job_type) params.append('job_type', filters.job_type);
      if (filters.location) params.append('location', filters.location);
      
      const { data } = await axios.get(`${API_BASE_URL}/api/careers/jobs/`, { params, headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      
      setJobs(prevJobs => isNewQuery ? (data.results || []) : [...prevJobs, ...(data.results || [])]);
      setHasMore(data.next !== null);
    } catch (err) {
      setError('حدث خطأ أثناء جلب الوظائف. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  }, [page, debouncedSearchTerm, filters]);

  // Effect for initial load and when filters/search change
  useEffect(() => {
    fetchJobs(true);
  }, [debouncedSearchTerm, filters]);

  // Effect for pagination
  useEffect(() => {
    if (page > 1) {
      fetchJobs(false);
    }
  }, [page]);


  const handleFilterChange = useCallback((newFilters) => {
    setFilters(newFilters);
  }, []);

  return (
    <>
      <SEOHead
        title="فرص العمل والتوظيف - بوابة الوظائف القانونية | منصة قانوني"
        description="اكتشف أحدث فرص العمل في المجال القانوني. وظائف محامين، مستشارين قانونيين، وفرص تدريب في أفضل المكاتب والشركات القانونية في مصر."
        keywords="وظائف قانونية، فرص عمل محامين، توظيف قانوني، وظائف مستشار قانوني، فرص تدريب قانوني، مكاتب المحاماة، شركات قانونية، بوابة التوظيف، وظائف كلية الحقوق، مهن قانونية"
        url="https://qanony.com/jobs"
        type="website"
      />
      <div className="job-listings-page">
      <header className="page-header">
        <h1>ابحث عن فرصتك الوظيفية التالية</h1>
        <div className="search-bar-wrapper">
          <input
            type="text"
            placeholder="ابحث بالمنصب، الشركة، أو الكلمات المفتاحية..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
      </header>

      <div className="page-content">
        <JobFilter onFilterChange={handleFilterChange} />

        <main className="listings-container">
          {loading && jobs.length === 0 ? (
            <div className="status-message">جاري تحميل الوظائف...</div>
          ) : error ? (
            <div className="status-message error">{error}</div>
          ) : jobs.length === 0 ? (
            <div className="status-message">لا توجد وظائف تطابق معايير البحث الحالية.</div>
          ) : (
            <>
              <div className="jobs-grid">
                {jobs.map(job => (
                  <JobCard key={job.id} job={job} />
                ))}
              </div>
              {hasMore && !loading && (
                <div className="load-more-container">
                  <button onClick={() => setPage(p => p + 1)} className="load-more-button">
                    تحميل المزيد
                  </button>
                </div>
              )}
            </>
          )}
           {loading && jobs.length > 0 && <div className="status-message">جاري تحميل المزيد...</div>}
        </main>
      </div>
    </div>
    </>
  );
};

export default JobListingsPage;
