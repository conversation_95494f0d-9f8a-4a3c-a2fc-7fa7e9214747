/* Styles for JobListingsPage */

.job-listings-page {
  padding: 32px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #212529;
  margin: 0 0 16px;
}

.search-bar-wrapper {
  max-width: 700px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 14px 20px;
  border-radius: 50px;
  border: 1px solid #dee2e6;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #facc15;
  box-shadow: 0 0 0 3px rgba(250, 204, 21, 0.25);
}

.page-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 32px;
  align-items: flex-start;
}

.listings-container {
  min-height: 500px;
}

.jobs-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.status-message {
  text-align: center;
  padding: 40px;
  font-size: 1.1rem;
  color: #6c757d;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.status-message.error {
  color: #dc3545;
}

.load-more-container {
  text-align: center;
  margin-top: 32px;
}

.load-more-button {
  background-color: #343a40;
  color: #fff;
  padding: 12px 28px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 50px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.load-more-button:hover {
  background-color: #495057;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .page-content {
    grid-template-columns: 1fr;
  }

  .job-filter {
    margin-bottom: 32px;
  }
}

@media (max-width: 576px) {
  .job-listings-page {
    padding: 24px;
  }
  .page-header h1 {
    font-size: 2rem;
  }
}

.job-filter select:focus,
.job-filter input:focus {
  outline: none;
  border-color: #facc15;
}
