import React, { useState, useEffect, useContext } from 'react';
import { useParams, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import TopicListItem from './components/TopicListItem';
import './Forum.css';

const TopicListPage = () => {
    const { categoryId } = useParams();
    const [topics, setTopics] = useState([]);
    const [category, setCategory] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const { authTokens } = useContext(AuthContext);
    // Basic pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    useEffect(() => {
        const fetchTopics = async () => {
            setLoading(true);
            setError(null);
            try {
                // Fetch category details to display name
                const categoryResponse = await axios.get(`/api/communication/forum-categories/${categoryId}/`, {
                     headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authTokens?.access || localStorage.getItem('access')}`
                    }
                });
                setCategory(categoryResponse.data);

                // Fetch topics for the category
                const topicsResponse = await axios.get(`/api/communication/forum-topics/?category=${categoryId}&page=${currentPage}`, {
                     headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authTokens?.access || localStorage.getItem('access')}`
                    }
                });
                const topicsData = Array.isArray(topicsResponse.data.results) ? topicsResponse.data.results : [];
                setTopics(topicsData);
                setTotalPages(Math.ceil(topicsResponse.data.count / 10)); // Assuming 10 items per page
            } catch (err) {
                setError('Failed to fetch topics. Please check the category and try again.');
                setTopics([]); // تعيين array فارغ في حالة الخطأ
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        if (authTokens && categoryId) {
            fetchTopics();
        }
    }, [authTokens, categoryId, currentPage]);

    if (loading) {
        return <div className="loading">Loading...</div>;
    }

    if (error) {
        return <div className="error">{error}</div>;
    }

    return (
        <div className="topic-list-page">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                <h2>مواضيع التصنيف</h2>
                <Link to={`/forum/create-topic?category=${categoryId}`} className="btn-primary">
                    إنشاء موضوع جديد في هذا التصنيف
                </Link>
            </div>

            <div className="topic-list">
                {!Array.isArray(topics) ? (
                    <p className="error">خطأ في تحميل المواضيع</p>
                ) : topics.length > 0 ? (
                    topics.map(topic => (
                        <TopicListItem key={topic.id} topic={topic} />
                    ))
                ) : (
                    <p>No topics found in this category.</p>
                )}
            </div>

            {/* Basic Pagination Controls */}
            <div className="pagination">
                <button onClick={() => setCurrentPage(p => Math.max(1, p - 1))} disabled={currentPage === 1}>
                    Previous
                </button>
                <span>Page {currentPage} of {totalPages}</span>
                <button onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))} disabled={currentPage === totalPages}>
                    Next
                </button>
            </div>
        </div>
    );
};

export default TopicListPage; 