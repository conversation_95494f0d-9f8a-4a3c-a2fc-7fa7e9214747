import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';

const TopicDetailsPage = () => {
  const { id } = useParams();
  const { authTokens } = useContext(AuthContext);
  const [topic, setTopic] = useState(null);
  const [replies, setReplies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [replyContent, setReplyContent] = useState('');
  const [replyLoading, setReplyLoading] = useState(false);
  const [replyError, setReplyError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const topicRes = await axios.get(`/api/communication/forum-topics/${id}/`, {
          headers: {
            'Authorization': `Bearer ${authTokens?.access || localStorage.getItem('access')}`
          }
        });
        setTopic(topicRes.data);
        const repliesRes = await axios.get(`/api/communication/forum-posts/?topic=${id}`, {
          headers: {
            'Authorization': `Bearer ${authTokens?.access || localStorage.getItem('access')}`
          }
        });
        const repliesData = repliesRes.data.results || repliesRes.data;
        setReplies(Array.isArray(repliesData) ? repliesData : []);
      } catch (err) {
        setError('تعذر تحميل بيانات الموضوع أو الردود.');
        setReplies([]); // تعيين array فارغ في حالة الخطأ
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [id, authTokens]);

  const handleReplySubmit = async (e) => {
    e.preventDefault();
    setReplyLoading(true);
    setReplyError(null);
    try {
      await axios.post('/api/communication/forum-posts/', {
        topic: id,
        content: replyContent
      }, {
        headers: {
          'Authorization': `Bearer ${authTokens?.access || localStorage.getItem('access')}`,
          'Content-Type': 'application/json'
        }
      });
      setReplyContent('');
      // Reload replies
      const repliesRes = await axios.get(`/api/communication/forum-posts/?topic=${id}`, {
        headers: {
          'Authorization': `Bearer ${authTokens?.access || localStorage.getItem('access')}`
        }
      });
      setReplies(repliesRes.data.results || repliesRes.data);
    } catch (err) {
      setReplyError('فشل إرسال الرد. حاول مرة أخرى.');
    } finally {
      setReplyLoading(false);
    }
  };

  if (loading) return <div className="loading">جاري التحميل...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!topic) return null;

  return (
    <div style={{
      minHeight: '100vh',
      background: '#faf9f6',
      padding: '0',
    }}>
      <div style={{
        maxWidth: 800,
        margin: '40px auto',
        background: 'rgba(255,255,255,0.98)',
        borderRadius: 20,
        boxShadow: '0 8px 32px rgba(80,80,80,0.10)',
        padding: '32px 24px 40px 24px',
        border: '1.5px solid #f3e9c7',
        position: 'relative',
      }}>
        <h1 style={{ color: '#facc15', fontWeight: 900, fontSize: 28, marginBottom: 8 }}>{topic.title}</h1>
        <div style={{ color: '#666', fontSize: 16, marginBottom: 24 }}>
          بواسطة {topic.author?.username || topic.author_name || 'مستخدم'} • {new Date(topic.created_at).toLocaleDateString('ar-EG')}
        </div>
        <div style={{ fontSize: 18, lineHeight: 1.8, color: '#333', marginBottom: 32 }}>{topic.content}</div>
        <div style={{height: 1, background: '#f3e9c7', margin: '24px 0 32px 0', borderRadius: 2}} />
        <h2 style={{ color: '#facc15', fontWeight: 800, fontSize: 20, marginBottom: 18 }}>الردود</h2>
        <div>
          {!Array.isArray(replies) ? (
            <div style={{ color: 'red', textAlign: 'center', padding: '20px' }}>
              خطأ في تحميل الردود
            </div>
          ) : replies.length > 0 ? (
            replies.map(reply => (
              <div key={reply.id} style={{
                background: '#f7f7fa',
                borderRadius: 12,
                border: '1px solid #f3e9c7',
                padding: '18px 16px',
                marginBottom: 16,
                color: '#333',
              }}>
                <div style={{ fontWeight: 700, color: '#facc15', marginBottom: 6 }}>{reply.author?.username || reply.author_name || 'مستخدم'}</div>
                <div style={{ fontSize: 16, marginBottom: 4 }}>{reply.content}</div>
                <div style={{ fontSize: 13, color: '#888' }}>{new Date(reply.created_at).toLocaleString()}</div>
              </div>
            ))
          ) : (
            <div style={{ color: '#facc15', fontSize: 18, textAlign: 'center', margin: '32px 0' }}>لا توجد ردود بعد. كن أول من يرد!</div>
          )}
        </div>
        <form onSubmit={handleReplySubmit} style={{ marginTop: 32 }}>
          <textarea
            value={replyContent}
            onChange={e => setReplyContent(e.target.value)}
            required
            rows={4}
            placeholder="اكتب ردك هنا..."
            style={{ width: '100%', borderRadius: 10, border: '1.5px solid #facc15', padding: 12, fontSize: 16, marginBottom: 12, background: '#fff', color: '#333', resize: 'vertical' }}
          />
          {replyError && <div className="error" style={{ color: 'red', marginBottom: 10 }}>{replyError}</div>}
          <button type="submit" disabled={replyLoading || !replyContent.trim()} style={{
            background: '#facc15',
            color: '#fff',
            fontWeight: 700,
            fontSize: 17,
            border: 'none',
            borderRadius: 10,
            padding: '10px 32px',
            cursor: replyLoading ? 'not-allowed' : 'pointer',
            opacity: replyLoading ? 0.7 : 1,
            transition: 'opacity 0.2s',
          }}>
            {replyLoading ? 'جاري الإرسال...' : 'إرسال الرد'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default TopicDetailsPage; 