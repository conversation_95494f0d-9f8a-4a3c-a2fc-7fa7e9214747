import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { useNavigate, useLocation } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';

const CreateTopicPage = () => {
  const [categories, setCategories] = useState([]);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [category, setCategory] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { authTokens } = useContext(AuthContext);
  const navigate = useNavigate();
  const location = useLocation();

  // Preselect category if passed in query
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const cat = params.get('category');
    if (cat) setCategory(cat);
  }, [location.search]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const res = await axios.get('/api/communication/forum-categories/', {
          headers: {
            'Authorization': `Bearer ${authTokens?.access || localStorage.getItem('access')}`
          }
        });
        const categoriesData = Array.isArray(res.data) ? res.data : res.data.results || [];
        setCategories(categoriesData);
      } catch (err) {
        setCategories([]);
      }
    };
    fetchCategories();
  }, [authTokens]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    try {
      await axios.post('/api/communication/forum-topics/', {
        title,
        content,
        category
      }, {
        headers: {
          'Authorization': `Bearer ${authTokens?.access || localStorage.getItem('access')}`,
          'Content-Type': 'application/json'
        }
      });
      navigate(`/forum`);
    } catch (err) {
      setError('فشل إنشاء الموضوع. تأكد من صحة البيانات وحاول مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="create-topic-page" style={{maxWidth: 600, margin: '40px auto', background: '#fff', borderRadius: 12, padding: 24}}>
      <h1 style={{marginBottom: 24}}>إنشاء موضوع جديد</h1>
      <form onSubmit={handleSubmit}>
        <div className="input-group" style={{marginBottom: 16}}>
          <label>العنوان</label>
          <input type="text" value={title} onChange={e => setTitle(e.target.value)} required className="input" />
        </div>
        <div className="input-group" style={{marginBottom: 16}}>
          <label>التصنيف</label>
          <select value={category} onChange={e => setCategory(e.target.value)} required className="input">
            <option value="" disabled>اختر التصنيف</option>
            {Array.isArray(categories) ? categories.map(cat => (
              <option key={cat.id} value={cat.id}>{cat.name}</option>
            )) : (
              <option disabled>خطأ في تحميل التصنيفات</option>
            )}
          </select>
        </div>
        <div className="input-group" style={{marginBottom: 16}}>
          <label>المحتوى</label>
          <textarea value={content} onChange={e => setContent(e.target.value)} required rows={6} className="input" />
        </div>
        {error && <div className="error" style={{color: 'red', marginBottom: 12}}>{error}</div>}
        <button type="submit" className="btn-primary" disabled={loading} style={{width: '100%'}}>
          {loading ? 'جاري الإرسال...' : 'إنشاء الموضوع'}
        </button>
      </form>
    </div>
  );
};

export default CreateTopicPage; 