import React from 'react';
import { Link } from 'react-router-dom';
import '../Forum.css';
// import { FaThumbtack, FaLock } from 'react-icons/fa'; // Example icons

const TopicListItem = ({ topic }) => {
    return (
        // <PERSON> will eventually go to /forum/topic/{topic.id}
        <Link to={`/forum/topic/${topic.id}`} className="topic-list-item-link">
            <div className="topic-list-item">
                <div className="topic-info">
                    <h4 className="topic-title">{topic.title}</h4>
                    <p className="topic-meta">
                        by {topic.author.username} on {new Date(topic.created_at).toLocaleDateString()}
                    </p>
                </div>
                <div className="topic-stats">
                    <span>Replies: {topic.post_count}</span>
                </div>
                <div className="topic-status-icons">
                    {topic.is_pinned && <span title="Pinned">📌</span> /* <FaThumbtack /> */}
                    {topic.is_locked && <span title="Locked">🔒</span> /* <FaLock /> */}
                </div>
            </div>
        </Link>
    );
};

export default TopicListItem; 