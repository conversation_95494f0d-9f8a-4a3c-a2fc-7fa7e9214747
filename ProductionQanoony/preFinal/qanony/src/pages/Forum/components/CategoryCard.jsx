import React from 'react';
import { Link } from 'react-router-dom';
import '../Forum.css';

const CategoryCard = ({ category }) => {
    return (
        <Link to={`/forum/category/${category.id}`} className="category-card-link">
            <div className="category-card">
                <h3 className="category-card-name">{category.name}</h3>
                <p className="category-card-description">{category.description}</p>
                {/* Optional: Add more details if available from API */}
                {/* <div className="category-card-stats">
                    <span>Topics: {category.topic_count}</span>
                    <span>Last Activity: {category.last_activity}</span>
                </div> */}
            </div>
        </Link>
    );
};

export default CategoryCard; 