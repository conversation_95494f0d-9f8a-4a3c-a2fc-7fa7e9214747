import React, { useState, useEffect, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import apiClient from '../../api';
import { AuthContext } from '../../context/AuthContext';
import CategoryCard from './components/CategoryCard';
import TopicListItem from './components/TopicListItem';
import './Forum.css';

const ForumHomePage = () => {
    const [categories, setCategories] = useState([]);
    const [latestTopics, setLatestTopics] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const { authTokens } = useContext(AuthContext);
    const navigate = useNavigate();
    const [subjects, setSubjects] = useState([]);
    const [selectedSubject, setSelectedSubject] = useState('');
    const [subjectsLoading, setSubjectsLoading] = useState(true);
    const [subjectsError, setSubjectsError] = useState(null);

    useEffect(() => {
        const fetchSubjects = async () => {
            setSubjectsLoading(true);
            setSubjectsError(null);
            try {
                const res = await apiClient.get('/api/courses/subjects/');
                console.log('Subjects API response:', res.data);

                // تأكد من أن البيانات array
                if (Array.isArray(res.data)) {
                    setSubjects(res.data);
                } else if (res.data && Array.isArray(res.data.results)) {
                    setSubjects(res.data.results);
                } else if (res.data && typeof res.data === 'object') {
                    // إذا كان object، حول إلى array
                    setSubjects([]);
                    console.warn('Subjects API returned object instead of array:', res.data);
                } else {
                    setSubjects([]);
                }
            } catch (error) {
                console.error('Error fetching subjects:', error);
                if (error.response?.status === 403) {
                    setSubjectsError('يجب توثيق البريد الإلكتروني أو وجود اشتراك نشط للوصول إلى المواد');
                } else {
                    setSubjectsError('تعذر تحميل المواد');
                }
                setSubjects([]); // تأكد من أن subjects هو array حتى في حالة الخطأ
            } finally {
                setSubjectsLoading(false);
            }
        };
        fetchSubjects();
    }, []);

    useEffect(() => {
        const fetchCategories = async () => {
            try {
                const url = selectedSubject
                    ? `/api/communication/forum-categories/?subject=${selectedSubject}`
                    : '/api/communication/forum-categories/';
                const categoryResponse = await apiClient.get(url);
                console.log('Categories API response:', categoryResponse.data);

                // تأكد من أن البيانات array
                if (Array.isArray(categoryResponse.data)) {
                    setCategories(categoryResponse.data);
                } else if (categoryResponse.data && Array.isArray(categoryResponse.data.results)) {
                    setCategories(categoryResponse.data.results);
                } else {
                    setCategories([]);
                    console.warn('Categories API returned unexpected format:', categoryResponse.data);
                }
            } catch (error) {
                console.error('Error fetching categories:', error);
                setCategories([]);
            }
        };
        fetchCategories();
    }, [selectedSubject]);

    useEffect(() => {
        const fetchTopics = async () => {
            try {
                const topicResponse = await apiClient.get('/api/communication/forum-topics/?ordering=-created_at');
                console.log('Topics API response:', topicResponse.data);

                // تأكد من أن البيانات array
                if (Array.isArray(topicResponse.data)) {
                    setLatestTopics(topicResponse.data);
                } else if (topicResponse.data && Array.isArray(topicResponse.data.results)) {
                    setLatestTopics(topicResponse.data.results);
                } else {
                    setLatestTopics([]);
                    console.warn('Topics API returned unexpected format:', topicResponse.data);
                }
            } catch (error) {
                console.error('Error fetching topics:', error);
                setLatestTopics([]);
            } finally {
                setLoading(false);
            }
        };
        fetchTopics();
    }, []);

    // Debug info
    console.log('ForumHomePage render:', {
        subjects: subjects,
        subjectsType: typeof subjects,
        subjectsIsArray: Array.isArray(subjects),
        subjectsLength: subjects?.length,
        categories: categories?.length,
        latestTopics: latestTopics?.length,
        loading,
        error,
        subjectsLoading,
        subjectsError
    });

    if (loading) {
        return <div className="loading">جاري التحميل...</div>;
    }

    if (error) {
        return <div className="error">{error}</div>;
    }

    return (
        <div style={{
            minHeight: '100vh',
            background: '#faf9f6',
            padding: '0',
        }}>
            <div style={{
                maxWidth: 900,
                margin: '40px auto',
                background: 'rgba(255,255,255,0.98)',
                borderRadius: 24,
                boxShadow: '0 8px 32px rgba(80,80,80,0.10)',
                padding: '32px 28px 40px 28px',
                border: '1.5px solid #f3e9c7',
                position: 'relative',
            }}>
                <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: 32,
                }}>
                    <h1 style={{
                        fontWeight: 900,
                        fontSize: 34,
                        color: '#facc15',
                        letterSpacing: '-1px',
                        margin: 0,
                    }}>
                        ساحة الأفكار والمشاركة
                    </h1>
                    <Link to="/forum/create-topic" className="btn-primary" style={{
                        fontSize: 20,
                        padding: '12px 36px',
                        borderRadius: 12,
                        background: '#facc15',
                        color: '#fff',
                        fontWeight: 700,
                        boxShadow: '0 2px 8px rgba(250, 204, 21, 0.10)',
                        border: 'none',
                        transition: 'background 0.2s',
                        textDecoration: 'none',
                    }}>
                        + موضوع جديد
                    </Link>
                </div>

                <section className="forum-categories">
                    <h2 style={{
                        color: '#facc15',
                        fontWeight: 800,
                        fontSize: 22,
                        marginBottom: 18,
                        marginTop: 0,
                        letterSpacing: '-0.5px',
                    }}>التصنيفات</h2>
                    <div style={{ marginBottom: 24 }}>
                        {subjectsLoading ? (
                            <div style={{ color: '#facc15', fontSize: 16 }}>جاري تحميل المواد...</div>
                        ) : subjectsError ? (
                            <div style={{ color: 'red', fontSize: 16 }}>{subjectsError}</div>
                        ) : (
                            <select
                                value={selectedSubject}
                                onChange={e => setSelectedSubject(e.target.value)}
                                style={{
                                    padding: '8px 16px',
                                    borderRadius: 8,
                                    border: '1.5px solid #facc15',
                                    fontSize: 16,
                                    color: '#333',
                                    background: '#fff',
                                    minWidth: 200,
                                    marginBottom: 0
                                }}
                            >
                                <option value="">كل المواد</option>
                                {Array.isArray(subjects) && subjects.map(subj => (
                                    <option key={subj.id} value={subj.id}>{subj.name_ar || subj.name_en}</option>
                                ))}
                            </select>
                        )}
                    </div>
                    <div className="category-grid">
                        {!Array.isArray(categories) ? (
                            <div style={{ color: 'red', textAlign: 'center', padding: '20px' }}>
                                خطأ في تحميل الفئات
                            </div>
                        ) : categories.length > 0 ? (
                            categories.map(category => (
                                <CategoryCard key={category.id} category={category} />
                            ))
                        ) : (
                            <div style={{
                                width: '100%',
                                minHeight: 180,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                background: '#f7f7fa',
                                borderRadius: 16,
                                border: '1.5px dashed #facc15',
                                margin: '32px 0',
                                color: '#facc15',
                                fontSize: 22,
                                boxShadow: '0 2px 8px rgba(250, 204, 21, 0.04)',
                            }}>
                                <span style={{fontSize: 54, marginBottom: 12}}>📂</span>
                                <div>لا توجد تصنيفات متاحة بعد</div>
                                <div style={{fontSize: 16, marginTop: 8}}>سيتم إضافة تصنيفات المنتدى قريبًا!</div>
                            </div>
                        )}
                    </div>
                </section>

                <div style={{height: 1, background: '#f3e9c7', margin: '40px 0 32px 0', borderRadius: 2}} />

                <section className="latest-topics">
                    <h2 style={{
                        color: '#facc15',
                        fontWeight: 800,
                        fontSize: 22,
                        marginBottom: 18,
                        marginTop: 0,
                        letterSpacing: '-0.5px',
                    }}>أحدث المواضيع</h2>
                    <div className="topic-list">
                        {!Array.isArray(latestTopics) ? (
                            <div style={{ color: 'red', textAlign: 'center', padding: '20px' }}>
                                خطأ في تحميل المواضيع
                            </div>
                        ) : latestTopics.length > 0 ? (
                            latestTopics.map(topic => (
                                <TopicListItem key={topic.id} topic={topic} />
                            ))
                        ) : (
                            <div style={{
                                width: '100%',
                                minHeight: 180,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                background: '#f7f7fa',
                                borderRadius: 16,
                                border: '1.5px dashed #facc15',
                                margin: '32px 0',
                                color: '#facc15',
                                fontSize: 22,
                                boxShadow: '0 2px 8px rgba(250, 204, 21, 0.04)',
                            }}>
                                <span style={{fontSize: 54, marginBottom: 12}}>💬</span>
                                <div>لا توجد مواضيع منشورة بعد</div>
                                <div style={{fontSize: 16, marginTop: 8}}>كن أول من يشارك فكرة أو سؤال في المنتدى!</div>
                                <Link to="/forum/create-topic" className="btn-primary" style={{marginTop: 18, fontSize: 18, padding: '10px 32px', borderRadius: 10, background: '#facc15', color: '#fff', fontWeight: 700, border: 'none', textDecoration: 'none'}}>إنشاء أول موضوع</Link>
                            </div>
                        )}
                    </div>
                </section>
            </div>
        </div>
    );
};

export default ForumHomePage; 