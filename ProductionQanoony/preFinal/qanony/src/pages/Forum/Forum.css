/* Forum.css */ 

/* General Forum Layout */
.forum-home-page, .topic-list-page {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.forum-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid #eee;
    padding-bottom: 1rem;
}

.forum-header h1 {
    font-size: 2rem;
    color: #333;
}

/* Generic Button Style */
.btn.btn-primary {
    background-color: #007bff;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.2s;
}

.btn.btn-primary:hover {
    background-color: #0056b3;
}

/* Loading and Error states */
.loading, .error {
    text-align: center;
    padding: 3rem;
    font-size: 1.2rem;
    color: #555;
}

.error {
    color: #d9534f;
}

/* Category Grid */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.category-card-link {
    text-decoration: none;
    color: inherit;
}

.category-card {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1.5rem;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
    cursor: pointer;
}

.category-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.category-card-name {
    font-size: 1.4rem;
    color: #0056b3;
    margin-bottom: 0.5rem;
}

.category-card-description {
    font-size: 1rem;
    color: #666;
}

/* Topic List */
.latest-topics, .topic-list-page .topic-list {
    margin-top: 3rem;
}

.latest-topics h2 {
    margin-bottom: 1.5rem;
}

.topic-list-item-link {
    text-decoration: none;
    color: inherit;
}

.topic-list-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 1rem;
    transition: background-color 0.2s;
}

.topic-list-item:hover {
    background-color: #f1f1f1;
}

.topic-list-item .topic-info {
    flex-grow: 1;
}

.topic-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.topic-meta {
    font-size: 0.85rem;
    color: #777;
}

.topic-stats {
    margin-left: 1rem;
    min-width: 80px;
    text-align: right;
    color: #555;
}

.topic-status-icons {
    margin-left: 1rem;
    font-size: 1.2rem;
}
.topic-status-icons span {
    margin-left: 0.5rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

.pagination button {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    margin: 0 0.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.pagination button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.pagination span {
    font-weight: bold;
} 