import React, { useEffect, useState } from 'react';
import Layout from '../components/layout/Layout';
import apiClient from '../api';
import ChatWindow from '../components/ChatWindow/ChatWindow';

const tabStyle = {
  display: 'inline-block',
  padding: '10px 28px',
  borderRadius: '8px 8px 0 0',
  background: '#FFF8E1',
  border: 'none',
  fontWeight: 700,
  fontSize: 18,
  cursor: 'pointer',
  marginLeft: 8,
  marginRight: 8,
  color: '#facc15',
  transition: 'background 0.2s, color 0.2s',
};
const tabActiveStyle = {
  ...tabStyle,
  background: '#fff',
  borderBottom: '2px solid #facc15',
  color: '#facc15',
};

const cardBaseStyle = {
  background: '#FFF8E1',
  borderRadius: 14,
  boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
  padding: '26px 32px',
  marginBottom: 24,
  display: 'flex',
  flexDirection: 'column',
  gap: 10,
  borderLeft: '6px solid #facc15',
  color: '#222',
  position: 'relative',
  transition: 'background 0.2s, color 0.2s',
  cursor: 'pointer',
};
const cardHoverStyle = {
  background: '#eab308',
  color: '#fff',
};

const labelStyle = {
  color: '#facc15',
  fontWeight: 700,
  fontSize: 15,
  marginLeft: 6,
  transition: 'color 0.2s',
};

const valueStyle = {
  color: '#222',
  fontWeight: 700,
  fontSize: 18,
  transition: 'color 0.2s',
};
const valueHoverStyle = {
  color: '#fff',
};

const statusColors = {
  pending: '#FEF3C7',
  approved: '#D1FAE5',
  rejected: '#FEE2E2',
  completed: '#DBEAFE',
  cancelled: '#F3F4F6',
};

const statusTextColors = {
  pending: '#92400E',
  approved: '#065F46',
  rejected: '#991B1B',
  completed: '#1E40AF',
  cancelled: '#1F2937',
};

const InstructorDashboard = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('sessions');
  const [hoveredCard, setHoveredCard] = useState(null);
  const [chatRooms, setChatRooms] = useState([]);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [roomsLoading, setRoomsLoading] = useState(false);
  const [roomsError, setRoomsError] = useState(null);

  useEffect(() => {
    const fetchSessions = async () => {
      setLoading(true);
      setError(null);
      try {
        const { data } = await apiClient.get('/api/courses/private-tutor/');
        setSessions(Array.isArray(data) ? data : []);
      } catch (err) {
        setError('حدث خطأ أثناء تحميل الجلسات');
      } finally {
        setLoading(false);
      }
    };
    fetchSessions();
  }, []);

  useEffect(() => {
    if (activeTab !== 'messages') return;
    setRoomsLoading(true);
    setRoomsError(null);
    apiClient.get('/api/communication/chatrooms/')
      .then(res => {
        setChatRooms(Array.isArray(res.data) ? res.data : []);
        setSelectedRoom(res.data && res.data.length > 0 ? res.data[0] : null);
      })
      .catch(() => setRoomsError('تعذر تحميل غرف الدردشة'))
      .finally(() => setRoomsLoading(false));
  }, [activeTab]);

  return (
    <Layout>
      <div style={{ maxWidth: 900, margin: '0 auto', marginTop: 40 }}>
        <div style={{ display: 'flex', borderBottom: '2px solid #eee', marginBottom: 24 }}>
          <button
            style={activeTab === 'sessions' ? tabActiveStyle : tabStyle}
            onClick={() => setActiveTab('sessions')}
          >
            الجلسات
          </button>
          <button
            style={activeTab === 'messages' ? tabActiveStyle : tabStyle}
            onClick={() => setActiveTab('messages')}
          >
            الرسائل
          </button>
        </div>
        {activeTab === 'sessions' && (
          <>
            <div style={{ marginBottom: 24, textAlign: 'center', fontSize: 18, color: '#facc15', fontWeight: 700 }}>
              عدد الجلسات: <span style={{ color: '#222', background: '#FFD70022', borderRadius: 8, padding: '2px 12px', marginRight: 6 }}>{sessions.length}</span>
            </div>
            {loading ? (
              <div style={{ textAlign: 'center', color: '#666', padding: 30 }}>جاري التحميل...</div>
            ) : error ? (
              <div style={{ textAlign: 'center', color: 'red', padding: 30 }}>{error}</div>
            ) : sessions.length === 0 ? (
              <div style={{ textAlign: 'center', color: '#888' }}>لا توجد جلسات حتى الآن</div>
            ) : (
              sessions.map(session => {
                const isHovered = hoveredCard === session.id;
                return (
                  <div
                    key={session.id}
                    style={{ ...cardBaseStyle, ...(isHovered ? cardHoverStyle : {}) }}
                    onMouseEnter={() => setHoveredCard(session.id)}
                    onMouseLeave={() => setHoveredCard(null)}
                  >
                    <div style={{ ...labelStyle, fontSize: 17, marginBottom: 8, color: isHovered ? '#fff' : '#facc15' }}>المادة</div>
                    <div style={{ ...valueStyle, ...(isHovered ? valueHoverStyle : {}) }}>{session.subject_name}</div>
                    <div style={{ marginTop: 10 }}>
                      <span style={{ ...labelStyle, color: isHovered ? '#fff' : '#facc15' }}>الطالب:</span>
                      <span style={{ ...valueStyle, ...(isHovered ? valueHoverStyle : {}) }}>{session.student_name}</span>
                    </div>
                    <div>
                      <span style={{ ...labelStyle, color: isHovered ? '#fff' : '#facc15' }}>الموعد:</span>
                      <span style={{ ...valueStyle, ...(isHovered ? valueHoverStyle : {}) }}>{new Date(session.scheduled_at).toLocaleString('ar-EG')}</span>
                    </div>
                    <div>
                      <span style={{ ...labelStyle, color: isHovered ? '#fff' : '#facc15' }}>المدة:</span>
                      <span style={{ ...valueStyle, ...(isHovered ? valueHoverStyle : {}) }}>{session.duration_minutes} دقيقة</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginTop: 8 }}>
                      <span style={{
                        background: statusColors[session.status] || '#eee',
                        color: statusTextColors[session.status] || '#333',
                        borderRadius: 6,
                        padding: '4px 14px',
                        fontWeight: 700,
                        fontSize: 15,
                        letterSpacing: 1,
                        transition: 'color 0.2s',
                      }}>
                        {session.status_display || session.status}
                      </span>
                      {session.meeting_link && session.status === 'approved' && (
                        <a href={session.meeting_link} target="_blank" rel="noopener noreferrer" style={{ color: isHovered ? '#fff' : '#facc15', fontWeight: 700, marginRight: 12, textDecoration: 'underline', transition: 'color 0.2s' }}>
                          رابط الجلسة
                        </a>
                      )}
                    </div>
                    {session.instructor_notes && (
                      <div style={{ marginTop: 10, color: isHovered ? '#fff' : '#facc15', fontSize: 15 }}>
                        <b>ملاحظات المدرس:</b> <span style={{ color: isHovered ? '#fff' : '#222' }}>{session.instructor_notes}</span>
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </>
        )}
        {activeTab === 'messages' && (
          <div style={{ background: '#fff', borderRadius: 12, boxShadow: '0 2px 8px rgba(0,0,0,0.06)', padding: 0, minHeight: 500 }}>
            {roomsLoading ? (
              <div style={{ textAlign: 'center', color: '#888', padding: 40 }}>جاري تحميل غرف الدردشة...</div>
            ) : roomsError ? (
              <div style={{ textAlign: 'center', color: 'red', padding: 40 }}>{roomsError}</div>
            ) : chatRooms.length === 0 ? (
              <div style={{ textAlign: 'center', color: '#888', padding: 40 }}>لا توجد غرف دردشة متاحة</div>
            ) : (
              <>
                <div style={{ padding: 24, borderBottom: '1px solid #eee', background: '#FFF8E1', borderRadius: '12px 12px 0 0' }}>
                  <label htmlFor="room-select" style={{ color: '#facc15', fontWeight: 700, marginLeft: 10 }}>اختر غرفة الدردشة:</label>
                  <select
                    id="room-select"
                    value={selectedRoom ? selectedRoom.id : ''}
                    onChange={e => setSelectedRoom(chatRooms.find(r => r.id === Number(e.target.value)))}
                    style={{ padding: '8px 16px', borderRadius: 8, border: '1px solid #facc15', fontWeight: 600, color: '#222', background: '#fff', minWidth: 180 }}
                  >
                    {chatRooms.map(room => (
                      <option key={room.id} value={room.id}>{room.name}</option>
                    ))}
                  </select>
                </div>
                {selectedRoom && (
                  <ChatWindow room={selectedRoom} />
                )}
              </>
            )}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default InstructorDashboard; 