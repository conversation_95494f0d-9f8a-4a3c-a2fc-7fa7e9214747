import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  AcademicCapIcon,
  BookOpenIcon,
  UserGroupIcon,
  GlobeAltIcon,
  SparklesIcon,
  HeartIcon,
  ShieldCheckIcon,
  RocketLaunchIcon,
  StarIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';
import Logo from '../components/shared/Logo';
import SEOHead from '../components/SEO/SEOHead';

const AboutPage = () => {
  const stats = [
    { number: "500+", label: "محاضرة تفاعلية", icon: AcademicCapIcon },
    { number: "50+", label: "أستاذ متخصص", icon: BookOpenIcon },
    { number: "20+", label: "مادة قانونية", icon: GlobeAltIcon },
    { number: "200+", label: "ساعة تعليمية", icon: UserGroupIcon },
  ];

  const values = [
    {
      icon: HeartIcon,
      title: "التميز الأكاديمي",
      description: "نؤمن بأن التميز الأكاديمي هو الأساس الذي تُبنى عليه الأمم، ونعمل على تقديم محتوى تعليمي عالي الجودة يواكب أحدث التطورات في مجال القانون."
    },
    {
      icon: ShieldCheckIcon,
      title: "الموثوقية والجودة",
      description: "نلتزم بأعلى معايير الجودة والموثوقية في جميع خدماتنا، ونسعى لتقديم تجربة تعليمية متميزة تلبي احتياجات طلاب القانون."
    },
    {
      icon: RocketLaunchIcon,
      title: "الابتكار والتطوير",
      description: "نؤمن بقوة الابتكار والتطوير المستمر، ونعمل على تطوير منصتنا باستمرار لتقديم أفضل تجربة تعليمية ممكنة."
    },
    {
      icon: StarIcon,
      title: "الريادة والتميز",
      description: "نسعى لأن نكون الوجهة الأولى لطلاب القانون في الوطن العربي، ونعمل على تحقيق الريادة في مجال التعليم القانوني الرقمي."
    }
  ];

  // تم حذف فريق العمل بناءً على طلب المستخدم

  return (
    <>
      <SEOHead
        title="عن منصة قانوني التعليمية - رؤيتنا ورسالتنا في تطوير التعليم القانوني"
        description="تعرف على منصة قانوني التعليمية، رؤيتنا في تطوير التعليم القانوني في مصر، قيمنا ومبادئنا، وقصة نجاحنا في خدمة طلاب كلية الحقوق."
        keywords="عن قانوني، منصة تعليمية، تعليم قانوني، رؤية ورسالة، تطوير التعليم، كلية الحقوق، التعليم الإلكتروني، قصة نجاح، تعليم متميز"
        url="https://qanony.com/about"
        type="website"
      />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-yellow-300 to-yellow-400">
        <div className="absolute inset-0 bg-black bg-opacity-10"></div>
        <div className="relative container mx-auto px-4 py-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center text-white"
          >
            <div className="flex justify-center mb-8">
              <Logo className="w-20 h-20" color="text-white" />
            </div>
            <h1 className="text-5xl md:text-7xl font-bold font-cairo mb-6">
              منصة قانوني
            </h1>
            <p className="text-xl md:text-2xl font-tajawal mb-8 max-w-3xl mx-auto leading-relaxed">
              "القانون هو التعبير الأسمى عن إرادة الشعب، وهو الضمان الوحيد لحماية الحقوق والحريات.
              إن دراسة القانون ليست مجرد حفظ للنصوص، بل فهم لروح العدالة وتطبيق لمبادئ الإنصاف"
            </p>
            <div className="inline-block px-6 py-3 bg-white bg-opacity-20 backdrop-blur-sm rounded-full border border-white">
              <span className="text-white font-semibold">- الدكتور عبد الرزاق السنهوري، عميد القانون المدني العربي</span>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Mission & Vision */}
      <div className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-dark font-cairo mb-6">رسالتنا ورؤيتنا</h2>
            <div className="grid md:grid-cols-2 gap-12 max-w-4xl mx-auto">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl">
                <LightBulbIcon className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-dark font-cairo mb-4">رسالتنا</h3>
                <p className="text-gray-700 font-tajawal leading-relaxed">
                  "نؤمن بأن التعليم هو أساس تقدم الأمم، ونسعى لتقديم تجربة تعليمية متميزة 
                  تجمع بين الأصالة والحداثة، لبناء جيل من المحامين والقضاة المتميزين 
                  الذين يحملون رسالة العدالة والحرية في الوطن العربي"
                </p>
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl">
                <SparklesIcon className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-dark font-cairo mb-4">رؤيتنا</h3>
                <p className="text-gray-700 font-tajawal leading-relaxed">
                  "نسعى لأن نكون الوجهة الأولى لطلاب القانون في الوطن العربي، 
                  والمنصة الرائدة في مجال التعليم القانوني الرقمي، 
                  ونعمل على تطوير منصتنا باستمرار لتقديم أفضل تجربة تعليمية ممكنة"
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-24 bg-gradient-to-r from-yellow-300 to-yellow-400">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-white font-cairo mb-6">إنجازاتنا بالأرقام</h2>
            <p className="text-xl text-white/90 font-tajawal mb-12">
              "الأرقام لا تكذب، وهي خير شاهد على نجاح مسيرتنا في خدمة طلاب القانون"
            </p>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="flex justify-center mb-4">
                  <stat.icon className="w-12 h-12 text-white/80" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-white/80 font-tajawal">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>

      {/* Values Section */}
      <div className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-dark font-cairo mb-6">قيمنا ومبادئنا</h2>
            <p className="text-xl text-gray-600 font-tajawal max-w-3xl mx-auto">
              "القيم هي الأساس الذي تُبنى عليه الأمم، ونحن نؤمن بقيم راسخة تقود مسيرتنا"
            </p>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gradient-to-br from-gray-50 to-gray-100 p-8 rounded-2xl text-center hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2"
              >
                <value.icon className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-dark font-cairo mb-4">{value.title}</h3>
                <p className="text-gray-600 font-tajawal leading-relaxed">{value.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>

      {/* Team Section - تم حذفه بناءً على طلب المستخدم */}

      {/* Call to Action */}
      <div className="py-24 bg-gradient-to-r from-yellow-300 to-yellow-400">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold text-white font-cairo mb-6">
              انضم إلينا اليوم
            </h2>
            <p className="text-xl text-white/90 font-tajawal mb-8 max-w-2xl mx-auto">
              "المستقبل ينتظر من يخطو نحوه بثقة وإصرار، 
              وفرصتك لتصبح محامياً متميزاً تبدأ من هنا"
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/login"
                className="inline-block bg-white text-yellow-900 font-semibold py-4 px-8 rounded-lg text-lg hover:bg-gray-100 transition-colors duration-300 transform hover:scale-105"
              >
                ابدأ رحلتك القانونية
              </Link>
              <Link
                to="/login"
                className="inline-block border-2 border-white text-white font-semibold py-4 px-8 rounded-lg text-lg hover:bg-white hover:text-yellow-900 transition-colors duration-300 transform hover:scale-105"
              >
                تعرف على المواد الدراسية
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
    </>
  );
};

export default AboutPage; 