.document-card {
  background: #fff;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  transition: transform 0.2s ease;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.document-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.document-icon {
  font-size: 2rem;
  color: #666;
}

.document-info {
  flex: 1;
}

.document-title {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.document-subtitle {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #666;
}

.document-metadata {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #777;
  margin: 0.5rem 0;
}

.document-category {
  margin-top: 0.5rem;
}

.category-tag {
  background: #f0f0f0;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.85rem;
  color: #555;
}

.document-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.download-button, .share-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.download-button {
  background: #007bff;
  color: white;
}

.download-button:hover:not(:disabled) {
  background: #0056b3;
}

.download-button.loading {
  background: #ccc;
  cursor: not-allowed;
}

.download-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.share-button {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
}

.share-button:hover {
  background: #e9ecef;
}

@media (max-width: 768px) {
  .document-card {
    flex-direction: column;
  }
  
  .document-actions {
    width: 100%;
    justify-content: stretch;
  }
  
  .download-button, .share-button {
    flex: 1;
  }
} 