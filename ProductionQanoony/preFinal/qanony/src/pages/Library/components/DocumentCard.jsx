import React, { useState } from 'react';
import './DocumentCard.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

// File type icons mapping
const fileTypeIcons = {
  pdf: '📄',
  docx: '📝',
  pptx: '📊',
  xlsx: '📈',
  default: '📑'
};

const DocumentCard = ({ document }) => {
  const [downloadStatus, setDownloadStatus] = useState('');

  const getFileTypeIcon = (filename) => {
    const extension = filename?.split('.').pop()?.toLowerCase();
    return fileTypeIcons[extension] || fileTypeIcons.default;
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('ar-SA', options);
  };

  const handleDownload = async () => {
    try {
      setDownloadStatus('جاري التحميل...');
      const response = await fetch(`${API_BASE_URL}/api/library/documents/${document.id}/download/`);
      if (response.ok) {
        const data = await response.json();
        window.open(data.file_url, '_blank');
        setDownloadStatus('تم التحميل بنجاح');
        setTimeout(() => setDownloadStatus(''), 3000);
      } else {
        setDownloadStatus('فشل التحميل');
        setTimeout(() => setDownloadStatus(''), 3000);
      }
    } catch (error) {
      console.error('Error downloading document:', error);
      setDownloadStatus('فشل التحميل');
      setTimeout(() => setDownloadStatus(''), 3000);
    }
  };

  const handleShare = () => {
    const shareUrl = `${window.location.origin}/library/documents/${document.id}`;
    navigator.clipboard.writeText(shareUrl)
      .then(() => alert('تم نسخ رابط المستند'))
      .catch(() => alert('تعذر نسخ الرابط'));
  };

  return (
    <div className="document-card">
      <div className="document-icon">
        {getFileTypeIcon(document.file_name)}
      </div>
      <div className="document-info">
        <h3 className="document-title">{document.title_ar}</h3>
        <p className="document-subtitle">{document.title_en}</p>
        <div className="document-metadata">
          <span className="author">المؤلف: {document.author || 'غير معروف'}</span>
          <span className="date">تاريخ النشر: {formatDate(document.created_at)}</span>
          {document.download_count && (
            <span className="downloads">التحميلات: {document.download_count}</span>
          )}
        </div>
        <div className="document-category">
          <span className="category-tag">{document.category?.name_ar}</span>
        </div>
      </div>
      <div className="document-actions">
        <button 
          onClick={handleDownload} 
          className={`download-button ${downloadStatus ? 'loading' : ''}`}
          disabled={!!downloadStatus}
        >
          {downloadStatus || 'تحميل'}
        </button>
        <button onClick={handleShare} className="share-button">
          مشاركة
        </button>
      </div>
    </div>
  );
};

export default DocumentCard; 