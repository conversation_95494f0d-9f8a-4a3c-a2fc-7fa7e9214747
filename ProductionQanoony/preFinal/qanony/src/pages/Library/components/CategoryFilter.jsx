import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './CategoryFilter.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const CategoryFilter = ({ onCategoriesChange }) => {
  const [categories, setCategories] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data } = await axios.get(`${API_BASE_URL}/api/library/categories/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
        setCategories(Array.isArray(data) ? data : []);
      } catch (err) {
        setError('تعذر تحميل الفئات');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleCategoryToggle = (categoryId) => {
    const newSelected = selectedCategories.includes(categoryId)
      ? selectedCategories.filter(id => id !== categoryId)
      : [...selectedCategories, categoryId];
    
    setSelectedCategories(newSelected);
    onCategoriesChange(newSelected);
  };

  if (loading) return <div className="categories-loading">جاري تحميل الفئات...</div>;
  if (error) return <div className="categories-error">{error}</div>;

  return (
    <div className="category-filter">
      <h3 className="filter-title">تصفية حسب الفئة</h3>
      <div className="categories-list">
        {categories.map(category => (
          <button
            key={category.id}
            onClick={() => handleCategoryToggle(category.id)}
            className={`category-button ${selectedCategories.includes(category.id) ? 'active' : ''}`}
          >
            {category.name_ar}
            {selectedCategories.includes(category.id) && (
              <span className="checkmark">✓</span>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryFilter; 