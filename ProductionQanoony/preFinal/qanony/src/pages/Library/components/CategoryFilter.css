.category-filter {
  background: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-title {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: #333;
  font-weight: 600;
}

.categories-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border: 1px solid #eee;
  border-radius: 6px;
  background: #fff;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  text-align: right;
  width: 100%;
}

.category-button:hover {
  background: #f8f9fa;
  border-color: #ddd;
}

.category-button.active {
  background: #e8f4ff;
  border-color: #007bff;
  color: #007bff;
}

.checkmark {
  color: #007bff;
  font-size: 1rem;
}

.categories-loading {
  padding: 1rem;
  text-align: center;
  color: #666;
}

.categories-error {
  padding: 1rem;
  text-align: center;
  color: #dc3545;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

@media (max-width: 768px) {
  .category-filter {
    padding: 1rem;
  }
  
  .categories-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5rem;
  }
  
  .category-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }
} 