.search-bar {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  border: 2px solid #eee;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fff;
  color: #333;
  direction: rtl;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-input::placeholder {
  color: #999;
}

.search-button {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: color 0.2s ease;
}

.search-button:hover {
  color: #007bff;
}

.search-icon {
  width: 1.5rem;
  height: 1.5rem;
}

@media (max-width: 768px) {
  .search-bar {
    max-width: 100%;
  }
  
  .search-input {
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    font-size: 0.9rem;
  }
  
  .search-icon {
    width: 1.25rem;
    height: 1.25rem;
  }
} 