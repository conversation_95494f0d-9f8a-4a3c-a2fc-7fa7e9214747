import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import SearchBar from './components/SearchBar';
import CategoryFilter from './components/CategoryFilter';
import DocumentCard from './components/DocumentCard';
import SEOHead from '../../components/SEO/SEOHead';
import './LibraryPage.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const LibraryPage = () => {
  // State management
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [sortBy, setSortBy] = useState('newest'); // newest, downloads, alphabetical
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Fetch documents based on current filters
  const fetchDocuments = useCallback(async (isNewSearch = false) => {
    if (isNewSearch) {
      setPage(1);
      setDocuments([]);
    }

    try {
      setLoading(true);
      setError(null);

      let url = `${API_BASE_URL}/api/library/documents/?page=${isNewSearch ? 1 : page}`;
      
      if (searchTerm) {
        url += `&search=${encodeURIComponent(searchTerm)}`;
      }
      
      if (selectedCategories.length > 0) {
        url += `&categories=${selectedCategories.join(',')}`;
      }
      
      if (sortBy) {
        url += `&sort=${sortBy}`;
      }

      const { data } = await axios.get(url, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      
      if (isNewSearch) {
        setDocuments(data.results || []);
      } else {
        setDocuments(prev => [...prev, ...(data.results || [])]);
      }
      
      setHasMore(!!(data.next));
    } catch (err) {
      setError('تعذر تحميل المستندات');
    } finally {
      setLoading(false);
    }
  }, [searchTerm, selectedCategories, sortBy, page]);

  // Initial load
  useEffect(() => {
    fetchDocuments(true);
  }, []);

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    fetchDocuments(true);
  };

  // Handle category selection
  const handleCategoriesChange = (categories) => {
    setSelectedCategories(categories);
    fetchDocuments(true);
  };

  // Handle sort change
  const handleSortChange = (event) => {
    setSortBy(event.target.value);
    fetchDocuments(true);
  };

  // Handle infinite scroll
  const handleScroll = useCallback((event) => {
    const { scrollTop, clientHeight, scrollHeight } = event.target;
    
    if (scrollHeight - scrollTop <= clientHeight * 1.5) {
      if (!loading && hasMore) {
        setPage(prev => prev + 1);
        fetchDocuments();
      }
    }
  }, [loading, hasMore, fetchDocuments]);

  return (
    <>
      <SEOHead
        title="المكتبة القانونية الشاملة - مراجع ووثائق قانونية | منصة قانوني"
        description="استكشف المكتبة القانونية الشاملة في منصة قانوني. مجموعة واسعة من الكتب القانونية، القوانين، اللوائح، والمراجع القانونية المتخصصة لطلاب وخريجي كلية الحقوق."
        keywords="مكتبة قانونية، كتب قانونية، قوانين مصرية، لوائح قانونية، مراجع قانونية، وثائق قانونية، مكتبة الحقوق، كتب المحاماة، التشريعات المصرية، المراجع القانونية"
        url="https://qanony.com/library"
        type="website"
      />
      <div className="library-page" onScroll={handleScroll}>
      <div className="library-header">
        <h1>المكتبة</h1>
        <SearchBar onSearch={handleSearch} />
        <div className="sort-control">
          <select value={sortBy} onChange={handleSortChange} className="sort-select">
            <option value="newest">الأحدث</option>
            <option value="downloads">الأكثر تحميلاً</option>
            <option value="alphabetical">أبجدي</option>
          </select>
        </div>
      </div>

      <div className="library-content">
        <aside className="library-sidebar">
          <CategoryFilter onCategoriesChange={handleCategoriesChange} />
        </aside>

        <main className="library-main">
          {loading && documents.length === 0 ? (
            <div className="loading-state">جاري تحميل المستندات...</div>
          ) : error ? (
            <div className="error-state">{error}</div>
          ) : documents.length === 0 ? (
            <div className="empty-state">
              <p>لا توجد مستندات متطابقة مع معايير البحث</p>
            </div>
          ) : !Array.isArray(documents) ? (
            <div className="error-message">خطأ في تحميل المستندات</div>
          ) : documents.length === 0 ? (
            <div className="no-documents">لا توجد مستندات متاحة</div>
          ) : (
            <div className="documents-grid">
              {documents.map(doc => (
                <DocumentCard key={doc.id} document={doc} />
              ))}
            </div>
          )}
          
          {loading && documents.length > 0 && (
            <div className="loading-more">جاري تحميل المزيد...</div>
          )}
        </main>
      </div>
    </div>
    </>
  );
};

export default LibraryPage; 