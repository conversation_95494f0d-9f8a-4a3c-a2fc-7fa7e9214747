.library-page {
  padding: 2rem;
  min-height: calc(100vh - 64px);
  background: #f8f9fa;
  overflow-y: auto;
}

.library-header {
  margin-bottom: 2rem;
  text-align: center;
}

.library-header h1 {
  margin: 0 0 1.5rem 0;
  font-size: 2rem;
  color: #333;
  font-weight: 600;
}

.sort-control {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}

.sort-select {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  color: #333;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  direction: rtl;
}

.sort-select:hover {
  border-color: #007bff;
}

.sort-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.library-content {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.library-sidebar {
  position: sticky;
  top: 1rem;
  height: fit-content;
}

.library-main {
  min-height: 400px;
}

.documents-grid {
  display: grid;
  gap: 1rem;
}

.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 3rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-state {
  color: #666;
}

.error-state {
  color: #dc3545;
}

.empty-state {
  color: #666;
}

.loading-more {
  text-align: center;
  padding: 1rem;
  color: #666;
  font-size: 0.9rem;
}

@media (max-width: 1024px) {
  .library-content {
    grid-template-columns: 200px 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .library-page {
    padding: 1rem;
  }

  .library-content {
    grid-template-columns: 1fr;
  }

  .library-header h1 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .library-sidebar {
    position: static;
    margin-bottom: 1rem;
  }

  .documents-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .library-header h1 {
    font-size: 2rem;
  }

  .documents-grid {
    grid-template-columns: 1fr;
  }
} 