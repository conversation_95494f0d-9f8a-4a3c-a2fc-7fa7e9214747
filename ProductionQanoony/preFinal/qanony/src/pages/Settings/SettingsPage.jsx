import React, { useEffect, useState, useContext } from 'react';
import apiClient from '../../api';
import { AuthContext } from '../../context/AuthContext';

const SettingsPage = () => {
  // Academic year state
  const [years, setYears] = useState([]);
  const [selectedYear, setSelectedYear] = useState('');
  const [yearMsg, setYearMsg] = useState('');
  const [yearLoading, setYearLoading] = useState(true);

  // Subscription renewal state
  const [renewalStatus, setRenewalStatus] = useState(null);
  const [renewalFile, setRenewalFile] = useState(null);
  const [renewalMsg, setRenewalMsg] = useState('');
  const [renewalLoading, setRenewalLoading] = useState(false);
  const [plans, setPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState('');
  const [filePreview, setFilePreview] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const [hasSubscription, setHasSubscription] = useState(false);

  const { user } = useContext(AuthContext);

  // File handling functions
  const handleFileSelect = (file) => {
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      setRenewalMsg('نوع الملف غير مدعوم. يرجى رفع صورة (JPG, PNG, GIF) أو ملف PDF');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setRenewalMsg('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
      return;
    }

    setRenewalFile(file);
    setRenewalMsg('');

    // Create preview for images
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => setFilePreview(e.target.result);
      reader.readAsDataURL(file);
    } else {
      setFilePreview(null);
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  // Fetch academic years and current profile
  useEffect(() => {
    const fetchYears = async () => {
      try {
        // Get current student profile (contains university and academic_year)
        const profileRes = await apiClient.get('/api/students/profiles/me/');
        console.log('Student profile response:', profileRes.data);
        setSelectedYear(profileRes.data.academic_year || '');

        // Get all academic years (they are now independent of universities)
        const yearsRes = await apiClient.get('/api/universities/academic-years/');
        console.log('Academic years response:', yearsRes.data);
        setYears(Array.isArray(yearsRes.data) ? yearsRes.data : yearsRes.data.results || []);
      } catch (err) {
        console.log('Error fetching student profile or years:', err);
        setYears([]);
      }
      setYearLoading(false);
    };
    fetchYears();
  }, []);

  // Fetch latest renewal request status and check if user has subscription
  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      try {
        // Check renewal status
        const renewalRes = await apiClient.get('/api/subscriptions/renewal-requests/me/');
        setRenewalStatus(renewalRes.data);

        // Use the has_subscription flag from the API response
        setHasSubscription(renewalRes.data?.has_subscription || false);
      } catch (err) {
        setRenewalStatus(null);
        setHasSubscription(false);
      }
    };

    checkSubscriptionStatus();
  }, [renewalMsg, user]);

  // Fetch subscription plans
  useEffect(() => {
    apiClient.get('/api/subscriptions/plans/')
      .then(res => setPlans(res.data))
      .catch(() => setPlans([]));
  }, []);

  // Change academic year
  const handleYearChange = async (e) => {
    const academic_year = e.target.value;
    setYearMsg('');
    setYearLoading(true);
    try {
      await apiClient.patch('/api/students/profiles/me/', { academic_year });
      setSelectedYear(academic_year);
      setYearMsg('تم تحديث السنة الدراسية بنجاح');
    } catch {
      setYearMsg('فشل التحديث');
    }
    setYearLoading(false);
  };

  // Handle renewal file upload
  const handleRenewalSubmit = async (e) => {
    e.preventDefault();
    setRenewalMsg('');
    setRenewalLoading(true);

    // Validate required fields
    if (!renewalFile) {
      setRenewalMsg('يرجى اختيار صورة إثبات الدفع');
      setRenewalLoading(false);
      return;
    }

    if (!selectedPlan) {
      setRenewalMsg('يرجى اختيار خطة الاشتراك');
      setRenewalLoading(false);
      return;
    }

    const formData = new FormData();
    formData.append('plan_id', selectedPlan);
    formData.append('renewal_screenshot', renewalFile);

    try {
      await apiClient.post('/api/subscriptions/renewal-requests/', formData);
      setRenewalMsg('تم إرسال طلب تجديد الاشتراك بنجاح. سيتم مراجعته قريبًا.');
      setRenewalFile(null);
      setSelectedPlan('');
    } catch (err) {
      console.error('Renewal request error:', err);
      let errorMessage = 'حدث خطأ أثناء رفع الإيصال. حاول مرة أخرى.';

      if (err.response?.status === 400) {
        const errorData = err.response.data;
        if (errorData.code === 'no_subscription') {
          errorMessage = 'لا يوجد اشتراك سابق. يرجى التسجيل كطالب جديد أولاً.';
        } else if (errorData.detail) {
          errorMessage = errorData.detail;
        }
      }

      setRenewalMsg(errorMessage);
    }
    setRenewalLoading(false);
  };

  return (
    <div className="settings-container" style={{maxWidth: 600, margin: '40px auto', padding: 24}}>
      <h1 style={{textAlign: 'center', color: 'var(--primary)', fontWeight: 700, marginBottom: 32}}>الإعدادات</h1>

      {/* تغيير السنة الدراسية */}
      {!user?.is_instructor && (
      <div className="settings-card" style={{background: '#fff', borderRadius: 12, boxShadow: '0 2px 8px rgba(0,0,0,0.06)', padding: 24, marginBottom: 32}}>
        <h2 style={{color: 'var(--primary)', fontSize: 20, marginBottom: 12}}>السنة الدراسية الحالية</h2>
        {yearLoading ? (
          <div>جاري التحميل...</div>
        ) : years.length > 0 ? (
          <>
            <select value={selectedYear} onChange={handleYearChange} className="input" style={{marginBottom: 10, width: '100%'}}>
              <option value="" disabled>اختر السنة الدراسية</option>
              {years.map(y => (
                <option key={y.id} value={y.id}>{y.year_name_ar}</option>
              ))}
            </select>
            {yearMsg && <div style={{color: yearMsg.includes('نجاح') ? 'green' : '#d9534f', fontSize: 13, marginTop: 4}}>{yearMsg}</div>}
          </>
        ) : (
          <>
            {/* Show message if user has no university */}
            {selectedYear === '' && (
              <div style={{color: '#d9534f'}}>لم يتم ربط حسابك بأي جامعة. يرجى التواصل مع الدعم.</div>
            )}
            {/* Show message if user has university but no years */}
            {selectedYear !== '' && (
              <div style={{color: '#d9534f'}}>لم يتم العثور على سنوات دراسية مرتبطة بجامعتك.</div>
            )}
          </>
        )}
      </div>
      )}

      {/* تجديد الاشتراك - يظهر فقط للطلاب */}
      {user?.is_student && (
      <div className="settings-card" style={{background: '#fff', borderRadius: 12, boxShadow: '0 2px 8px rgba(0,0,0,0.06)', padding: 24}}>
        <h2 style={{color: 'var(--primary)', fontSize: 20, marginBottom: 12}}>تجديد الاشتراك</h2>

        {/* رسالة للمستخدمين الذين ليس لديهم اشتراك سابق */}
        {!hasSubscription && renewalStatus?.status === null && (
          <div style={{
            background: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: 8,
            padding: 16,
            marginBottom: 16,
            color: '#856404'
          }}>
            <p style={{margin: 0, fontSize: 14}}>
              <strong>ملاحظة:</strong> يبدو أنك لم تقم بالاشتراك من قبل.
              إذا كنت طالباً جديداً، يرجى التسجيل كطالب أولاً من خلال صفحة التسجيل.
            </p>
          </div>
        )}
        <form onSubmit={handleRenewalSubmit} style={{display: 'flex', flexDirection: 'column', gap: 12}}>
          <label>اختر الخطة</label>
          <select value={selectedPlan} onChange={e => setSelectedPlan(e.target.value)} className="input" required style={{marginBottom: 8}}>
            <option value="" disabled>اختر الخطة</option>
            {plans.map(plan => (
              <option key={plan.id} value={plan.id}>
                {plan.name} - {plan.price} جنيه / {plan.duration_days} يوم
              </option>
            ))}
          </select>
          <label>رفع إيصال الدفع</label>

          {/* Enhanced File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive
                ? 'border-blue-500 bg-blue-50'
                : renewalFile
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            {filePreview ? (
              <div className="space-y-3">
                <img
                  src={filePreview}
                  alt="معاينة الصورة"
                  className="max-w-full max-h-32 mx-auto rounded border"
                />
                <p className="text-sm text-green-600 font-medium">
                  ✓ تم اختيار الملف: {renewalFile?.name}
                </p>
                <button
                  type="button"
                  onClick={() => {
                    setRenewalFile(null);
                    setFilePreview(null);
                  }}
                  className="text-red-500 hover:text-red-700 text-sm underline"
                >
                  إزالة الملف
                </button>
              </div>
            ) : renewalFile ? (
              <div className="space-y-3">
                <div className="text-4xl text-green-500">📄</div>
                <p className="text-sm text-green-600 font-medium">
                  ✓ تم اختيار الملف: {renewalFile.name}
                </p>
                <button
                  type="button"
                  onClick={() => {
                    setRenewalFile(null);
                    setFilePreview(null);
                  }}
                  className="text-red-500 hover:text-red-700 text-sm underline"
                >
                  إزالة الملف
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="text-4xl text-gray-400">📁</div>
                <div>
                  <p className="text-gray-600 mb-2">
                    اسحب وأفلت الملف هنا أو
                    <label className="text-blue-500 hover:text-blue-700 cursor-pointer underline mx-1">
                      اختر ملف
                      <input
                        type="file"
                        accept="image/*,application/pdf"
                        onChange={e => handleFileSelect(e.target.files[0])}
                        className="hidden"
                        required
                      />
                    </label>
                  </p>
                  <p className="text-xs text-gray-500">
                    الملفات المدعومة: JPG, PNG, GIF, PDF (حد أقصى 5 ميجابايت)
                  </p>
                </div>
              </div>
            )}
          </div>
          <button type="submit" className="btn-primary" disabled={renewalLoading} style={{marginTop: 8}}>
            {renewalLoading ? 'جاري الإرسال...' : 'إرسال طلب التجديد'}
          </button>
        </form>
        {renewalMsg && <div style={{color: renewalMsg.includes('نجاح') ? 'green' : '#d9534f', fontSize: 13, marginTop: 4}}>{renewalMsg}</div>}
        {renewalStatus && (
          <div style={{marginTop: 16, background: '#f9f9f9', borderRadius: 8, padding: 12, fontSize: 14}}>
            <b>آخر حالة لطلب التجديد:</b><br/>
            الحالة: {renewalStatus.status === 'pending' ? 'قيد المراجعة' : renewalStatus.status === 'approved' ? 'مقبول' : renewalStatus.status === 'denied' ? 'مرفوض' : renewalStatus.status}<br/>
            {renewalStatus.rejection_reason && <span>سبب الرفض: {renewalStatus.rejection_reason}</span>}
          </div>
        )}
      </div>
      )}
    </div>
  );
};

export default SettingsPage; 