import React, { useState, useEffect, useContext } from 'react';
import { <PERSON>a<PERSON>ser, FaChartBar, FaBook, FaUsers, FaEdit, FaSave, FaTimes } from 'react-icons/fa';
import { AuthContext } from '../context/AuthContext';
import Layout from '../components/layout/Layout';
import InstructorProfileHeader from '../components/Instructor/InstructorProfileHeader';
import InstructorProfileForm from '../components/Instructor/InstructorProfileForm';
import InstructorStatsCards from '../components/Instructor/InstructorStatsCards';
import InstructorSubjectsList from '../components/Instructor/InstructorSubjectsList';
import InstructorStudentsList from '../components/Instructor/InstructorStudentsList';
import MobileHeader from '../components/Instructor/Mobile/MobileHeader';
import MobileTabs, { MobileTabContent } from '../components/Instructor/Mobile/MobileTabs';
import { MobileStatsGrid } from '../components/Instructor/Mobile/MobileStats';
import {
  LazyStatsCards,
  LazySubjectsList,
  LazyStudentsList,
  LazyProfileForm
} from '../components/Instructor/Performance/LazyComponents';
import {
  usePerformanceMetrics,
  useAPIPerformanceMonitor,
  PerformanceDashboard
} from '../components/Instructor/Performance/PerformanceMonitoring';
import { useToast } from '../components/UI/Toast';

const InstructorProfilePage = () => {
  const { user } = useContext(AuthContext);
  const { showSuccess, showError } = useToast();

  // Performance monitoring
  const performanceMetrics = usePerformanceMetrics('InstructorProfilePage');
  const { apiMetrics, trackAPICall } = useAPIPerformanceMonitor();
  const [showPerformanceDashboard, setShowPerformanceDashboard] = useState(false);

  // Debug info
  console.log('🔍 InstructorProfilePage - User:', user);
  console.log('🔍 InstructorProfilePage - Is Instructor:', user?.is_instructor);
  const [activeTab, setActiveTab] = useState('profile');
  const [instructor, setInstructor] = useState(null);
  const [stats, setStats] = useState(null);
  const [subjects, setSubjects] = useState([]);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [tabLoading, setTabLoading] = useState({
    profile: false,
    stats: false,
    subjects: false,
    students: false
  });

  // Cache for API responses
  const [dataCache, setDataCache] = useState({
    instructor: null,
    stats: null,
    subjects: null,
    students: null,
    lastFetch: {}
  });

  // Tabs configuration
  const tabs = [
    { key: 'profile', label: 'الملف الشخصي', icon: '👤' },
    { key: 'stats', label: 'الإحصائيات', icon: '📊' },
    { key: 'subjects', label: 'المواد', icon: '📚' },
    { key: 'students', label: 'الطلاب', icon: '👥' }
  ];

  // API Base URL
  const API_BASE = 'http://localhost:8000/api/auth';

  // Get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('access');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  };

  // Retry mechanism for API calls
  const retryApiCall = async (apiCall, maxRetries = 2) => {
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  };

  // Check if data is cached and fresh (5 minutes)
  const isCacheFresh = (key) => {
    const lastFetch = dataCache.lastFetch[key];
    if (!lastFetch) return false;
    const fiveMinutes = 5 * 60 * 1000;
    return Date.now() - lastFetch < fiveMinutes;
  };

  // Update cache
  const updateCache = (key, data) => {
    setDataCache(prev => ({
      ...prev,
      [key]: data,
      lastFetch: {
        ...prev.lastFetch,
        [key]: Date.now()
      }
    }));
  };

  // Fetch instructor profile data
  const fetchInstructorData = async (forceRefresh = false) => {
    // Check cache first
    if (!forceRefresh && isCacheFresh('instructor') && dataCache.instructor) {
      setInstructor(dataCache.instructor);
      return;
    }

    setTabLoading(prev => ({ ...prev, profile: true }));
    try {
      const response = await trackAPICall('fetchInstructorData', async () => {
        return await fetch(`${API_BASE}/instructors/profile/`, {
          headers: getAuthHeaders()
        });
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}: فشل في جلب بيانات المدرس`);
      }
      
      const data = await response.json();
      // Handle both direct data and wrapped data
      const instructorData = data.data || data;

      // Validate instructor data
      if (!instructorData || typeof instructorData !== 'object') {
        throw new Error('بيانات المدرس غير صحيحة');
      }

      setInstructor(instructorData);
      updateCache('instructor', instructorData);
      setError(null);
    } catch (err) {
      console.error('Error fetching instructor data:', err);
      setError('حدث خطأ في تحميل بيانات المدرس');
      showError('فشل في تحميل بيانات المدرس');
    } finally {
      setTabLoading(prev => ({ ...prev, profile: false }));
    }
  };

  // Fetch instructor stats
  const fetchStats = async () => {
    setTabLoading(prev => ({ ...prev, stats: true }));
    try {
      const response = await fetch(`${API_BASE}/instructors/stats/`, {
        headers: getAuthHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: فشل في جلب الإحصائيات`);
      }
      
      const data = await response.json();
      // Handle both direct data and wrapped data
      setStats(data.data || data);
    } catch (err) {
      console.error('Error fetching stats:', err);
      showError('فشل في تحميل الإحصائيات');
    } finally {
      setTabLoading(prev => ({ ...prev, stats: false }));
    }
  };

  // Fetch instructor subjects
  const fetchSubjects = async () => {
    setTabLoading(prev => ({ ...prev, subjects: true }));
    try {
      const response = await fetch(`${API_BASE}/instructors/subjects/`, {
        headers: getAuthHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: فشل في جلب المواد`);
      }
      
      const data = await response.json();
      setSubjects(Array.isArray(data) ? data : data.results || []);
    } catch (err) {
      console.error('Error fetching subjects:', err);
      showError('فشل في تحميل المواد');
    } finally {
      setTabLoading(prev => ({ ...prev, subjects: false }));
    }
  };

  // Fetch instructor students
  const fetchStudents = async () => {
    setTabLoading(prev => ({ ...prev, students: true }));
    try {
      const response = await fetch(`${API_BASE}/instructors/students/`, {
        headers: getAuthHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: فشل في جلب الطلاب`);
      }
      
      const data = await response.json();
      setStudents(Array.isArray(data) ? data : data.results || []);
    } catch (err) {
      console.error('Error fetching students:', err);
      showError('فشل في تحميل الطلاب');
    } finally {
      setTabLoading(prev => ({ ...prev, students: false }));
    }
  };

  // Handle profile update
  const handleProfileUpdate = async (formData) => {
    try {
      const response = await fetch(`${API_BASE}/instructors/profile/`, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'فشل في تحديث البيانات');
      }
      
      const updatedData = await response.json();
      setInstructor(updatedData.data || updatedData);
      setEditMode(false);
      showSuccess('تم تحديث البيانات بنجاح');
    } catch (err) {
      console.error('Error updating profile:', err);
      showError(err.message || 'حدث خطأ في تحديث البيانات');
      throw err;
    }
  };

  // Handle image upload
  const handleImageUpload = async (file) => {
    try {
      const formData = new FormData();
      formData.append('profile_image', file);
      
      const response = await fetch(`${API_BASE}/instructors/profile/image/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access')}`
        },
        body: formData
      });
      
      if (!response.ok) {
        throw new Error('فشل في رفع الصورة');
      }
      
      const data = await response.json();
      setInstructor(prev => ({
        ...prev,
        profile_image: data.profile_image
      }));
      showSuccess('تم رفع الصورة بنجاح');
    } catch (err) {
      console.error('Error uploading image:', err);
      showError('فشل في رفع الصورة');
    }
  };

  // Initial data fetch
  useEffect(() => {
    const initializeData = async () => {
      setLoading(true);
      await fetchInstructorData();
      setLoading(false);
    };
    
    initializeData();
  }, []);

  // Fetch data when tab changes
  useEffect(() => {
    switch (activeTab) {
      case 'stats':
        if (!stats) fetchStats();
        break;
      case 'subjects':
        if (subjects.length === 0) fetchSubjects();
        break;
      case 'students':
        if (students.length === 0) fetchStudents();
        break;
      default:
        break;
    }
  }, [activeTab]);

  // Loading state
  if (loading) {
    return (
      <Layout>
        <div className="max-w-6xl mx-auto p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-48 bg-gray-200 rounded-lg"></div>
            <div className="h-12 bg-gray-200 rounded-lg"></div>
            <div className="h-96 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </Layout>
    );
  }

  // Error state
  if (error && !instructor) {
    return (
      <Layout>
        <div className="max-w-6xl mx-auto p-6">
          <div className="text-center py-12">
            <p className="text-red-500 text-lg">{error}</p>
            <button
              onClick={fetchInstructorData}
              className="mt-4 px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Mobile Header */}
      <MobileHeader
        instructor={instructor}
        onMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        isMenuOpen={isMobileMenuOpen}
        notifications={[]} // Add notifications here when available
        onNotificationClick={() => {/* Handle notification click */}}
      />

      <div className="max-w-6xl mx-auto p-6 space-y-6">
        {/* Profile Header */}
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <InstructorProfileHeader
              instructor={instructor}
              onEditClick={() => setEditMode(true)}
              onImageClick={(file) => handleImageUpload(file)}
            />
          </div>
          <button
            onClick={() => {
              fetchInstructorData(true);
              fetchStats(true);
              fetchSubjects(true);
              fetchStudents(true);
            }}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
            title="تحديث البيانات"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            تحديث
          </button>

          {/* Performance Dashboard Toggle (Development Only) */}
          {process.env.NODE_ENV === 'development' && (
            <button
              onClick={() => setShowPerformanceDashboard(!showPerformanceDashboard)}
              className="px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm"
              title="عرض لوحة الأداء"
            >
              📊 الأداء
            </button>
          )}
        </div>
        
        {/* Desktop Tabs Navigation */}
        <div className="hidden lg:block border-b border-gray-200">
          <nav className="flex space-x-8 overflow-x-auto">
            {tabs.map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2 ${
                  activeTab === tab.key
                    ? 'border-yellow-500 text-yellow-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span>{tab.icon}</span>
                {tab.label}
                {tabLoading[tab.key] && (
                  <div className="w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin"></div>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Mobile Tabs Navigation */}
        <MobileTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          tabLoading={tabLoading}
        />
        
        {/* Desktop Tab Content */}
        <div className="hidden lg:block mt-6">
          {activeTab === 'profile' && (
            editMode ? (
              <InstructorProfileForm
                instructor={instructor}
                onSave={handleProfileUpdate}
                onCancel={() => setEditMode(false)}
                loading={tabLoading.profile}
              />
            ) : (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold mb-4">معلومات الملف الشخصي</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الاسم الكامل</label>
                    <p className="mt-1 text-lg text-gray-900">{instructor?.first_name} {instructor?.last_name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">البريد الإلكتروني</label>
                    <p className="mt-1 text-lg text-gray-900">{instructor?.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">رقم الهاتف</label>
                    <p className="mt-1 text-lg text-gray-900">{instructor?.phone_number || 'غير محدد'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">التخصص</label>
                    <p className="mt-1 text-lg text-gray-900">{instructor?.specialty || 'غير محدد'}</p>
                  </div>
                  {instructor?.bio && (
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">النبذة التعريفية</label>
                      <p className="mt-1 text-gray-900">{instructor.bio}</p>
                    </div>
                  )}
                </div>
              </div>
            )
          )}
          
          {activeTab === 'stats' && (
            <LazyStatsCards
              stats={stats}
              loading={tabLoading.stats}
            />
          )}

          {activeTab === 'subjects' && (
            <LazySubjectsList
              subjects={subjects}
              loading={tabLoading.subjects}
              onRefresh={fetchSubjects}
            />
          )}

          {activeTab === 'students' && (
            <LazyStudentsList
              students={students}
              loading={tabLoading.students}
              onRefresh={fetchStudents}
            />
          )}
        </div>

        {/* Mobile Tab Content */}
        <MobileTabContent
          activeTab={activeTab}
          tabs={tabs}
          onTabChange={setActiveTab}
          className="lg:hidden"
        >
          {activeTab === 'stats' && (
            <MobileStatsGrid
              stats={stats}
              onStatClick={(statKey) => {
                console.log('Stat clicked:', statKey);
              }}
            />
          )}

          {activeTab === 'profile' && (
            <div className="p-4 bg-white rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold mb-4">الملف الشخصي</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-500 mb-1">الاسم</label>
                  <p className="text-gray-900">{instructor?.first_name} {instructor?.last_name}</p>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 mb-1">البريد الإلكتروني</label>
                  <p className="text-gray-900 text-sm">{instructor?.email}</p>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 mb-1">التخصص</label>
                  <p className="text-gray-900">{instructor?.specialty || 'غير محدد'}</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'subjects' && (
            <div className="p-4">
              <InstructorSubjectsList
                subjects={subjects}
                loading={tabLoading.subjects}
                onRefresh={fetchSubjects}
              />
            </div>
          )}

          {activeTab === 'students' && (
            <div className="p-4">
              <InstructorStudentsList
                students={students}
                loading={tabLoading.students}
                onRefresh={fetchStudents}
              />
            </div>
          )}
        </MobileTabContent>
      </div>

      {/* Performance Dashboard */}
      <PerformanceDashboard show={showPerformanceDashboard} />
    </Layout>
  );
};

export default InstructorProfilePage;
