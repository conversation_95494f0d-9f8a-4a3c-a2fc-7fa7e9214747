import React, { useState, useEffect, useContext } from 'react';
import { Navigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../context/AuthContext';
import { useToast, useApiErrorHandler } from '../components/UI/Toast';
import { getErrorMessage } from '../utils/errorHandler';
import PrivateTutorTab from '../components/PrivateTutor/PrivateTutorTab';
import ChatWindow from '../components/ChatWindow/ChatWindow';
import { getYouTubeEmbedUrl, getYouTubeWatchUrl } from '../utils/youtubeUtils';
import { testYouTubeUrls, testSpecificVideoId } from '../utils/youtubeTest';
import './StudentProfilePage.css';
import { AnimatePresence, motion } from 'framer-motion';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const StudentProfilePage = () => {
  const { user } = useContext(AuthContext);
  const { showSuccess, showError } = useToast();
  const { handleError } = useApiErrorHandler();
  const [activeTab, setActiveTab] = useState('courses');

  // State for courses
  const [courses, setCourses] = useState([]);
  const [coursesLoading, setCoursesLoading] = useState(true);
  const [coursesError, setCoursesError] = useState(null);

  // State for student profile
  const [studentProfile, setStudentProfile] = useState(null);
  const [profileLoading, setProfileLoading] = useState(false);
  const [profileError, setProfileError] = useState(null);

  // State for lectures
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [lectures, setLectures] = useState([]);
  const [lecturesLoading, setLecturesLoading] = useState(false);
  const [lecturesError, setLecturesError] = useState(null);

  // NEW: State for a single selected lecture
  const [selectedLecture, setSelectedLecture] = useState(null);

  // State for quizzes
  const [quizzes, setQuizzes] = useState([]);
  const [quizzesLoading, setQuizzesLoading] = useState(false);
  const [quizzesError, setQuizzesError] = useState(null);

  // NEW: State for Quiz Taking
  const [selectedQuiz, setSelectedQuiz] = useState(null);
  const [quizQuestions, setQuizQuestions] = useState([]);
  const [userAnswers, setUserAnswers] = useState({}); // { questionId: answerId }
  const [quizResult, setQuizResult] = useState(null);
  const [quizTakingLoading, setQuizTakingLoading] = useState(false);

  // State for messages/conversations
  const [conversations, setConversations] = useState([]);
  const [conversationsLoading, setConversationsLoading] = useState(false);
  const [conversationsError, setConversationsError] = useState(null);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [showNewChatModal, setShowNewChatModal] = useState(false);
  const [instructors, setInstructors] = useState([]);
  const [instructorsLoading, setInstructorsLoading] = useState(false);

  // State for certificates
  const [certificates, setCertificates] = useState([]);
  const [certificatesLoading, setCertificatesLoading] = useState(false);
  const [certificatesError, setCertificatesError] = useState(null);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCourses, setFilteredCourses] = useState([]);

  // State for mobile sidebar
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const tabs = [
    { id: 'courses', label: 'المواد الدراسية' },
    { id: 'quizzes', label: 'الاختبارات' },
    { id: 'certificates', label: 'الشهادات' },
    { id: 'private-tutor', label: 'مدرسك الخاص' },
    { id: 'messages', label: 'الرسائل' },
  ];

  // Fetch student profile on component mount
  useEffect(() => {
    fetchStudentProfile();
  }, []);

  useEffect(() => {
    if (activeTab === 'courses' && !selectedSubject && studentProfile) {
      fetchCourses();
    } else if (activeTab === 'quizzes' && studentProfile) {
      fetchQuizzes();
    } else if (activeTab === 'messages') {
      if (!selectedConversation) {
        fetchConversations();
      }
    } else if (activeTab === 'certificates' && studentProfile) {
      fetchCertificates();
    }
  }, [activeTab, selectedSubject, selectedConversation, studentProfile]);

  // Effect for filtering courses based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredCourses(courses);
    } else {
      const filtered = courses.filter(course =>
        course.name_ar.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (course.description_ar && course.description_ar.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredCourses(filtered);
    }
  }, [searchTerm, courses]);

  // إضافة دالة الاختبار للـ window للاستخدام في console
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.testCurrentVideo = testCurrentVideo;
      window.testYouTubeUrls = testYouTubeUrls;
    }
  }, [selectedLecture]);



  const fetchStudentProfile = async () => {
    setProfileLoading(true);
    setProfileError(null);
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/students/profiles/me/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      setStudentProfile(data);
    } catch (err) {
      setProfileError('تعذر جلب معلومات الطالب');
      console.error('Error fetching student profile:', err);
    } finally {
      setProfileLoading(false);
    }
  };

  const fetchCourses = async () => {
    if (!studentProfile) {
      const errorMsg = 'يجب جلب معلومات الطالب أولاً';
      setCoursesError(errorMsg);
      showError(errorMsg);
      return;
    }

    setCoursesLoading(true);
    setCoursesError(null);
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/courses/subjects/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      const coursesData = Array.isArray(data) ? data : [];
      setCourses(coursesData);
      setFilteredCourses(coursesData);
    } catch (err) {
      let errorMsg;
      if (err.response?.status === 403) {
        errorMsg = 'يجب توثيق البريد الإلكتروني أو وجود اشتراك نشط للوصول إلى المواد الدراسية';
      } else {
        errorMsg = getErrorMessage(err, 'جلب المواد الدراسية');
      }
      setCoursesError(errorMsg);
      handleError(err, 'جلب المواد الدراسية');
    } finally {
      setCoursesLoading(false);
    }
  };

  const fetchQuizzes = async () => {
    if (!studentProfile) {
      const errorMsg = 'يجب جلب معلومات الطالب أولاً';
      setQuizzesError(errorMsg);
      showError(errorMsg);
      return;
    }

    setQuizzesLoading(true);
    setQuizzesError(null);
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/courses/quizzes/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      setQuizzes(Array.isArray(data) ? data : []);
    } catch (err) {
      const errorMsg = getErrorMessage(err, 'جلب الاختبارات');
      setQuizzesError(errorMsg);
      handleError(err, 'جلب الاختبارات');
    } finally {
      setQuizzesLoading(false);
    }
  };

  const fetchConversations = async () => {
    setConversationsLoading(true);
    setConversationsError(null);
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/communication/chatrooms/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      setConversations(Array.isArray(data) ? data : []);
    } catch (err) {
      const errorMsg = getErrorMessage(err, 'جلب المحادثات');
      setConversationsError(errorMsg);
      handleError(err, 'جلب المحادثات');
    } finally {
      setConversationsLoading(false);
    }
  };

  const fetchInstructors = async () => {
    setInstructorsLoading(true);
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/auth/instructors/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      console.log('📋 Instructors API Response:', data);
      const instructorsList = Array.isArray(data.results) ? data.results : Array.isArray(data) ? data : [];
      console.log('👨‍🏫 Instructors List:', instructorsList);
      setInstructors(instructorsList);
    } catch (err) {
      console.error('❌ Error fetching instructors:', err);
      const errorMsg = getErrorMessage(err, 'جلب قائمة المدرسين');
      showError(errorMsg);
      handleError(err, 'جلب قائمة المدرسين');
    } finally {
      setInstructorsLoading(false);
    }
  };

  const fetchLectures = async (subjectId) => {
    setLecturesLoading(true);
    setLecturesError(null);
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/courses/lectures/?subject=${subjectId}`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      setLectures(Array.isArray(data) ? data : []);
    } catch (err) {
      setLecturesError('تعذر جلب المحاضرات');
    } finally {
      setLecturesLoading(false);
    }
  };

  const handleSubjectClick = (subject) => {
    setSelectedSubject(subject);
    fetchLectures(subject.id);
  };

  const handleBackToSubjects = () => {
    setSelectedSubject(null);
    setLectures([]);
  };

  // NEW: Handlers for Quiz Logic
  const handleStartQuiz = async (quiz) => {
    setSelectedQuiz(quiz);
    setQuizResult(null);
    setUserAnswers({});
    setQuizTakingLoading(true);
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/courses/quizzes/${quiz.id}/questions/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      setQuizQuestions(Array.isArray(data) ? data : []);
    } catch (err) {
      setQuizzesError('تعذر تحميل أسئلة الاختبار');
    } finally {
      setQuizTakingLoading(false);
    }
  };

  const handleAnswerSelect = (questionId, answerId) => {
    setUserAnswers(prev => ({
      ...prev,
      [questionId]: answerId
    }));
  };

  const handleSubmitQuiz = async () => {
    setQuizTakingLoading(true);
    const answersPayload = Object.entries(userAnswers).map(([question_id, answer_id]) => ({
      question_id: parseInt(question_id),
      answer_id,
    }));

    try {
      const { data } = await axios.post(`${API_BASE_URL}/api/courses/quizzes/${selectedQuiz.id}/submit/`, {
        answers: answersPayload
      }, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      setQuizResult(data);
    } catch (err) {
      setQuizzesError('حدث خطأ أثناء إرسال الإجابات');
    } finally {
      setQuizTakingLoading(false);
      setSelectedQuiz(null); // Go to results view
    }
  };
  
  const handleBackToQuizzes = () => {
    setSelectedQuiz(null);
    setQuizQuestions([]);
    setQuizResult(null);
  };

  // NEW: Handler to select a lecture to view its details
  const handleLectureClick = (lecture) => {
    setSelectedLecture(lecture);
  };

  // NEW: Handler to go back from lecture detail view to the lectures list
  const handleBackToLectures = () => {
    setSelectedLecture(null);
  };

  // NEW: Handlers for Chat Logic
  const handleOpenChat = (conversation) => {
    setSelectedConversation(conversation);
  };

  const handleBackToConversations = () => {
    setSelectedConversation(null);
  };

  const handleNewChatClick = () => {
    setShowNewChatModal(true);
    fetchInstructors();
  };

  const handleCreateConversation = async (instructorId, title, firstMessage) => {
    try {
      // First create the chatroom
      const chatroomData = {
        name: title || 'محادثة جديدة',
        room_type: 'instructor_student',
        instructor: instructorId
      };

      const { data: chatroom } = await axios.post(`${API_BASE_URL}/api/communication/chatrooms/`, chatroomData, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });

      // Then send the first message
      const messageData = {
        room: chatroom.id,
        content: firstMessage,
        message_type: 'text'
      };

      await axios.post(`${API_BASE_URL}/api/communication/messages/`, messageData, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });

      showSuccess('تم إنشاء المحادثة بنجاح');
      setShowNewChatModal(false);
      fetchConversations(); // Refresh conversations list
      setSelectedConversation(chatroom); // Open the new conversation
    } catch (err) {
      const errorMsg = getErrorMessage(err, 'إنشاء المحادثة');
      showError(errorMsg);
      handleError(err, 'إنشاء المحادثة');
    }
  };

  // Fetch certificates
  const fetchCertificates = async () => {
    if (!studentProfile) {
      setCertificatesError('يجب جلب معلومات الطالب أولاً');
      return;
    }

    setCertificatesLoading(true);
    setCertificatesError(null);
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/courses/certificates/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      setCertificates(Array.isArray(data.results) ? data.results : []);
    } catch (err) {
      setCertificatesError('تعذر جلب الشهادات');
      console.error('Error fetching certificates:', err);
    } finally {
      setCertificatesLoading(false);
    }
  };

  // دالة لتحميل الشهادة
  const handleDownloadCertificate = async (certificateId, certificateNumber) => {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/courses/certificates/${certificateId}/download/`,
        {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` },
          responseType: 'blob'
        }
      );

      // إنشاء رابط تحميل مؤقت
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `certificate_${certificateNumber}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      showSuccess('تم تحميل الشهادة بنجاح');
    } catch (err) {
      console.error('Error downloading certificate:', err);
      showError('فشل في تحميل الشهادة');
    }
  };

  // دالة لتحميل PDF مع معالجة الأخطاء
  const handlePdfDownload = async (lectureId, lectureTitle) => {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/courses/lectures/${lectureId}/download-pdf/`,
        {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` },
          responseType: 'blob' // مهم للتحميل المباشر
        }
      );
      
      // إنشاء رابط تحميل مؤقت
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      
      // تعيين اسم الملف
      const filename = `${lectureTitle.replace(/ /g, '_')}_ملخص.pdf`;
      link.setAttribute('download', filename);
      
      // إضافة الرابط للصفحة والنقر عليه
      document.body.appendChild(link);
      link.click();
      
      // تنظيف
      link.remove();
      window.URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('خطأ في تحميل PDF:', error);
      if (error.response?.status === 404) {
        alert('الملف غير متاح');
      } else {
        alert('حدث خطأ في تحميل الملف');
      }
    }
  };

  // دالة لاختبار الفيديو الحالي
  const testCurrentVideo = () => {
    if (selectedLecture && selectedLecture.youtube_video_id) {
      console.log('=== Testing Current Video ===');
      console.log('Lecture:', selectedLecture);
      testSpecificVideoId(selectedLecture.youtube_video_id);
    } else {
      console.log('No video to test');
    }
  };



  const renderTabContent = () => {
    switch (activeTab) {
      case 'private-tutor':
        return <PrivateTutorTab />;
      case 'messages':
        if (selectedConversation) {
          // Use the chatroom directly as it's already compatible with ChatWindow
          return <ChatWindow room={selectedConversation} onBack={handleBackToConversations} aiMode={false} />;
        }
        return (
          <div className="conversations-container">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800">المحادثات</h2>
              <button
                onClick={handleNewChatClick}
                className="bg-yellow-400 hover:bg-yellow-500 text-white px-4 py-2 rounded-lg font-semibold transition-colors duration-200 flex items-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                محادثة جديدة
              </button>
            </div>

            {conversationsLoading ? (
              <div className="text-center py-8">
                <div className="text-lg text-gray-600">جاري تحميل المحادثات...</div>
              </div>
            ) : conversationsError ? (
              <div className="text-center py-8">
                <div className="text-red-600 mb-4">{conversationsError}</div>
                <button
                  onClick={fetchConversations}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                >
                  إعادة المحاولة
                </button>
              </div>
            ) : conversations.length === 0 ? (
              <div className="text-center py-12">
                <div className="mb-4">
                  <svg className="mx-auto h-16 w-16 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.013 8.013 0 01-7-4L5 20l4-1a8.013 8.013 0 01-2-7c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-600 mb-2">لا توجد محادثات بعد</h3>
                <p className="text-gray-500 mb-4">ابدأ محادثة جديدة مع أحد المدرسين</p>
                <button
                  onClick={handleNewChatClick}
                  className="bg-yellow-400 hover:bg-yellow-500 text-white px-6 py-3 rounded-lg font-semibold"
                >
                  بدء محادثة جديدة
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {conversations.map(conversation => (
                  <div key={conversation.id} className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow duration-200 border border-gray-200">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-800">
                              {conversation.name || 'محادثة'}
                            </h3>
                            <p className="text-sm text-gray-500">
                              مع: {conversation.instructor_name || conversation.instructor?.full_name || `${conversation.instructor?.first_name || ''} ${conversation.instructor?.last_name || ''}`.trim() || conversation.instructor?.email || 'مدرس'}
                            </p>
                          </div>
                        </div>
                        {conversation.messages && conversation.messages.length > 0 && (
                          <div className="text-sm text-gray-600 mb-2">
                            <span className="font-medium">آخر رسالة:</span> {conversation.messages[conversation.messages.length - 1]?.content || 'لا توجد رسائل'}
                          </div>
                        )}
                        <div className="text-xs text-gray-400">
                          {conversation.last_message_at ?
                            new Date(conversation.last_message_at).toLocaleDateString('ar-SA') :
                            new Date(conversation.created_at).toLocaleDateString('ar-SA')
                          }
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleOpenChat(conversation)}
                          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
                        >
                          فتح
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      case 'quizzes':
        if (selectedQuiz) {
          if (quizResult) {
            // Render quiz results
            return (
              <div className="quiz-results-container">
                <button onClick={handleBackToQuizzes} className="back-button mb-4">
                  &larr; العودة إلى الاختبارات
                </button>
                <div className="score-summary">
                  <p>النتيجة النهائية:</p>
                  <strong>{quizResult.score}%</strong>
                  <p>الإجابات الصحيحة: {quizResult.correct_answers} من {quizResult.total_questions}</p>
                </div>
                <div className="results-details">
                  {quizResult.questions.map((question, index) => (
                    <div key={index} className={`result-item ${question.is_correct ? 'correct' : 'incorrect'}`}>
                      <p><strong>السؤال {index + 1}:</strong> {question.question_text}</p>
                      <p>إجابتك: {question.user_answer}</p>
                      {!question.is_correct && <p>الإجابة الصحيحة: {question.correct_answer}</p>}
                    </div>
                  ))}
                </div>
              </div>
            );
          } else {
            // Render quiz taking interface
            return (
              <div className="quiz-taking-container">
                <button onClick={handleBackToQuizzes} className="back-button mb-4">
                  &larr; العودة إلى الاختبارات
                </button>
                <h2 className="text-2xl font-bold mb-4">{selectedQuiz.title}</h2>
                {quizTakingLoading ? (
                  <div className="text-center py-8">جاري تحميل الأسئلة...</div>
                ) : quizQuestions.length === 0 ? (
                  <div className="text-center py-8 text-gray-600">لا توجد أسئلة متاحة</div>
                ) : (
                  <div className="quiz-questions">
                    {quizQuestions.map((question, questionIndex) => (
                      <div key={question.id} className="question-card bg-white rounded-lg shadow-md p-6 mb-4">
                        <h3 className="text-lg font-semibold mb-4">السؤال {questionIndex + 1}: {question.text}</h3>
                        <div className="answers-list">
                          {question.answers.map((answer) => (
                            <label key={answer.id} className="answer-option">
                              <input
                                type="radio"
                                name={`question-${question.id}`}
                                value={answer.id}
                                checked={userAnswers[question.id] === answer.id}
                                onChange={() => handleAnswerSelect(question.id, answer.id)}
                              />
                              <span>{answer.text}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    ))}
                    <button 
                      onClick={handleSubmitQuiz}
                      disabled={Object.keys(userAnswers).length < quizQuestions.length}
                      className="submit-quiz-button"
                    >
                      إرسال الإجابات
                    </button>
                  </div>
                )}
              </div>
            );
          }
        }
        return (
          <div className="quizzes-container">
            <h2 className="text-2xl font-bold mb-4">الاختبارات</h2>
            {quizzesLoading ? (
              <div className="text-center py-8">جاري تحميل الاختبارات...</div>
            ) : quizzesError ? (
              <div className="text-red-600 text-center py-8">{quizzesError}</div>
            ) : !Array.isArray(quizzes) ? (
              <div className="text-center py-8 text-red-500">خطأ في تحميل الاختبارات</div>
            ) : quizzes.length === 0 ? (
              <div className="text-center py-8 text-gray-600">لا توجد اختبارات متاحة</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {quizzes.map(quiz => (
                  <div key={quiz.id} className="quiz-card bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">{quiz.title}</h3>
                    <p className="text-gray-600 mb-4">عدد الأسئلة: {quiz.question_count || 'غير محدد'}</p>
                    <button onClick={() => handleStartQuiz(quiz)} className="start-quiz-button">
                      بدء الاختبار
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      case 'certificates':
        return (
          <div className="certificates-container">
            <h2 className="text-2xl font-bold mb-4">الشهادات</h2>
            {certificatesLoading ? (
              <div className="text-center py-8">جاري تحميل الشهادات...</div>
            ) : certificatesError ? (
              <div className="text-red-600 text-center py-8">{certificatesError}</div>
            ) : !Array.isArray(certificates) ? (
              <div className="text-center py-8 text-red-500">خطأ في تحميل الشهادات</div>
            ) : certificates.length === 0 ? (
              <div className="text-center py-8 text-gray-600">لا توجد شهادات متاحة</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {certificates.map(cert => {
                  // تحديد عنوان الشهادة حسب النوع
                  let certificateTitle = 'شهادة إنجاز';
                  if (cert.quiz) {
                    certificateTitle = `شهادة اجتياز اختبار`;
                  } else if (cert.subject) {
                    certificateTitle = `شهادة إتمام مادة`;
                  } else if (cert.semester) {
                    certificateTitle = `شهادة إتمام فصل دراسي`;
                  }

                  return (
                    <div key={cert.id} className="certificate-card bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
                      <div className="flex items-center mb-3">
                        <svg className="w-6 h-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="text-lg font-semibold text-gray-800">{certificateTitle}</h3>
                      </div>

                      <div className="space-y-2 mb-4">
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">رقم الشهادة:</span> {cert.certificate_id}
                        </p>
                        {cert.score && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">الدرجة:</span> {cert.score}%
                          </p>
                        )}
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">تاريخ الإصدار:</span> {new Date(cert.issued_date).toLocaleDateString('ar-EG')}
                        </p>
                      </div>

                      <button
                        onClick={() => handleDownloadCertificate(cert.id, cert.certificate_id)}
                        className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        تحميل الشهادة
                      </button>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        );
      case 'courses':
        if (selectedLecture) {
          // Render details for the selected lecture
          return (
            <AnimatePresence mode="wait">
              <motion.div
                key="lecture-detail"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
              >
                <div className="lecture-detail-container">
                  <button onClick={handleBackToLectures} className="back-button mb-4">
                    &larr; العودة إلى المحاضرات
                  </button>
                  <h2 className="text-3xl font-bold mb-2">{selectedLecture.title_ar}</h2>
                  <p className="text-lg text-gray-600 mb-4">المادة: {selectedSubject.name_ar}</p>
                  
                  <div className="video-container my-6">
                    {selectedLecture.youtube_video_id ? (
                      (() => {
                        const embedUrl = getYouTubeEmbedUrl(selectedLecture.youtube_video_id);
                        console.log('YouTube Video Debug:', {
                          videoId: selectedLecture.youtube_video_id,
                          embedUrl: embedUrl,
                          isValid: embedUrl !== null
                        });
                        
                        if (embedUrl) {
                          return (
                            <iframe
                              width="100%"
                              height="400"
                              src={embedUrl}
                              title={selectedLecture.title_ar}
                              frameBorder="0"
                              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                              allowFullScreen
                              className="rounded-lg"
                            ></iframe>
                          );
                        } else {
                          return (
                            <div className="video-placeholder bg-gray-100 rounded-lg p-8 text-center">
                              <p className="text-red-600 mb-2">معرف الفيديو غير صحيح</p>
                              <p className="text-gray-600">معرف الفيديو: {selectedLecture.youtube_video_id}</p>
                              <p className="text-sm text-gray-500 mt-2">يرجى التواصل مع الإدارة لإصلاح هذا الفيديو</p>
                            </div>
                          );
                        }
                      })()
                    ) : (
                      <div className="video-placeholder bg-gray-100 rounded-lg p-8 text-center">
                        <p className="text-gray-600">لا يوجد فيديو متاح لهذه المحاضرة</p>
                      </div>
                    )}
                  </div>

                  <div className="lecture-info mb-6">
                    <p className="text-gray-600 mb-2">
                      <strong>مدة المحاضرة:</strong> {selectedLecture.duration_minutes} دقيقة
                    </p>
                    <p className="text-gray-600 mb-2">
                      <strong>الترتيب:</strong> {selectedLecture.order}
                    </p>
                    {selectedLecture.pdf_summary_url && (
                      <p className="text-gray-600 mb-2">
                        <strong>الملفات المرفقة:</strong> 
                        <span className="ml-2 px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                          📄 ملخص PDF متاح
                        </span>
                        {selectedLecture.pdf_summary_size && (
                          <span className="ml-2 text-xs text-gray-500">
                            ({(selectedLecture.pdf_summary_size / 1024 / 1024).toFixed(2)} MB)
                          </span>
                        )}
                      </p>
                    )}
                  </div>

                  <div className="mt-8">
                    {selectedLecture.pdf_summary_url && (
                      <button 
                        onClick={() => handlePdfDownload(selectedLecture.id, selectedLecture.title_ar)}
                        className="download-pdf-button"
                      >
                        📄 تحميل ملخص PDF
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          );
        }
        if (selectedSubject) {
          // Render lectures for the selected subject
          return (
            <AnimatePresence mode="wait">
              <motion.div
                key="lectures-list"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
              >
                <div className="lectures-container">
                  <button onClick={handleBackToSubjects} className="back-button mb-4">
                    &larr; العودة إلى المواد
                  </button>
                  <h2 className="text-2xl font-bold mb-4">محاضرات {selectedSubject.name_ar}</h2>
                  {lecturesLoading ? (
                    <div className="text-center py-8">جاري تحميل المحاضرات...</div>
                  ) : lecturesError ? (
                    <div className="text-red-600 text-center py-8">{lecturesError}</div>
                  ) : !Array.isArray(lectures) ? (
                    <div className="text-center py-8 text-red-500">خطأ في تحميل المحاضرات</div>
                  ) : lectures.length === 0 ? (
                    <div className="text-center py-8 text-gray-600">لا توجد محاضرات متاحة لهذه المادة</div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {lectures.map(lecture => (
                        <div key={lecture.id} className="lecture-card bg-white rounded-lg shadow-md p-6 cursor-pointer" onClick={() => handleLectureClick(lecture)}>
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-xl font-semibold text-gray-800">{lecture.title_ar}</h3>
                            <div className="flex gap-2">
                              {lecture.youtube_video_id && (() => {
                                const isValid = getYouTubeEmbedUrl(lecture.youtube_video_id) !== null;
                                return (
                                  <span className={`px-2 py-1 text-xs rounded-full ${
                                    isValid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {isValid ? '🎥 فيديو' : '🎥 معطل'}
                                  </span>
                                );
                              })()}
                              {lecture.pdf_summary_url && (
                                <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                                  📄 PDF
                                </span>
                              )}
                            </div>
                          </div>
                          <p className="text-gray-600 mb-4">المدة: {lecture.duration_minutes || 'غير محدد'} دقيقة</p>
                          <span className="watch-button-small">التفاصيل</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </motion.div>
            </AnimatePresence>
          );
        }
        // Render subjects list
        return (
          <AnimatePresence mode="wait">
            <motion.div
              key="courses-list"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
            >
              <div className="courses-container">
                {/* Search Bar */}
                <div className="mb-6">
                  <div className="relative max-w-md">
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                    <input
                      type="text"
                      placeholder="البحث في المواد الدراسية..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  {searchTerm && (
                    <p className="mt-2 text-sm text-gray-600">
                      عرض {filteredCourses.length} من أصل {courses.length} مادة
                    </p>
                  )}
                </div>

                {coursesLoading ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
                    <p className="text-gray-600">جاري تحميل المواد الدراسية...</p>
                  </div>
                ) : coursesError ? (
                  <div className="text-center py-12">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                      <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h3 className="text-lg font-medium text-red-800 mb-2">خطأ في التحميل</h3>
                      <p className="text-red-600 mb-4">{coursesError}</p>
                      <button
                        onClick={fetchCourses}
                        className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                      >
                        إعادة المحاولة
                      </button>
                    </div>
                  </div>
                ) : !Array.isArray(courses) ? (
                  <div className="text-center py-8 text-red-500">خطأ في تحميل المواد</div>
                ) : filteredCourses.length === 0 && searchTerm ? (
                  <div className="text-center py-12">
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-8 max-w-md mx-auto">
                      <svg className="w-16 h-16 text-yellow-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      <h3 className="text-lg font-medium text-yellow-800 mb-2">لا توجد نتائج</h3>
                      <p className="text-yellow-600 mb-4">لم يتم العثور على مواد تحتوي على "{searchTerm}"</p>
                      <button
                        onClick={() => setSearchTerm('')}
                        className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors"
                      >
                        مسح البحث
                      </button>
                    </div>
                  </div>
                ) : courses.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 max-w-md mx-auto">
                      <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                      <h3 className="text-lg font-medium text-gray-800 mb-2">لا توجد مواد متاحة</h3>
                      <p className="text-gray-600">لم يتم العثور على أي مواد دراسية لسنتك الدراسية حالياً</p>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredCourses.map(course => {
                      // حساب التقدم (يمكن تحسينه لاحقاً بناءً على البيانات الفعلية)
                      const lectureCount = course.lecture_count || 0;
                      const completedLectures = Math.floor(Math.random() * lectureCount); // مؤقت
                      const progressPercentage = lectureCount > 0 ? (completedLectures / lectureCount) * 100 : 0;

                      return (
                        <div
                          key={course.id}
                          className="course-card bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 cursor-pointer border border-gray-100 hover:border-blue-200 group"
                          onClick={() => handleSubjectClick(course)}
                        >
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                                {course.name_ar}
                              </h3>
                              <p className="text-gray-600 text-sm">
                                {course.description_ar || 'مادة دراسية أساسية'}
                              </p>
                            </div>
                            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                              </svg>
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-600">المحاضرات</span>
                              <span className="font-medium text-gray-800">
                                {completedLectures}/{lectureCount}
                              </span>
                            </div>

                            {/* Progress Bar */}
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500"
                                style={{ width: `${progressPercentage}%` }}
                              ></div>
                            </div>

                            <div className="flex items-center justify-between">
                              <span className="text-xs text-gray-500">
                                {progressPercentage.toFixed(0)}% مكتمل
                              </span>
                              <div className="flex items-center text-xs text-blue-600">
                                <span>عرض المحاضرات</span>
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </motion.div>
          </AnimatePresence>
        );
      // سيتم إضافة المزيد من التبويبات لاحقاً
      default:
        return <div className="text-center py-8">قريباً...</div>;
    }
  };

  // دالة لإرجاع أيقونة التبويب
  const getTabIcon = (tabId) => {
    const iconClass = "w-5 h-5";
    switch (tabId) {
      case 'courses':
        return (
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        );
      case 'quizzes':
        return (
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
        );
      case 'certificates':
        return (
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
          </svg>
        );
      case 'private-tutor':
        return (
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      case 'messages':
        return (
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      default:
        return (
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        );
    }
  };

  // Show loading state while fetching student profile
  if (profileLoading) {
    return (
      <div className="student-profile-layout">
        <div className="text-center py-8">
          <div className="text-lg">جاري تحميل معلومات الطالب...</div>
        </div>
      </div>
    );
  }

  // Show error state if profile fetch failed
  if (profileError) {
    return (
      <div className="student-profile-layout">
        <div className="text-center py-8">
          <div className="text-red-600 text-lg mb-4">{profileError}</div>
          <button
            onClick={fetchStudentProfile}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="student-profile-layout">
      {/* Profile Header */}
      <div className="profile-header bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg mb-6">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse mb-4 md:mb-0">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold">{user?.first_name} {user?.last_name}</h1>
              <p className="text-blue-100">
                {studentProfile?.academic_year?.year_name_ar || 'طالب'} - {studentProfile?.university?.name_ar || 'الجامعة'}
              </p>
              <p className="text-blue-100 text-sm">رقم الطالب: {studentProfile?.student_id || 'غير محدد'}</p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-white bg-opacity-20 rounded-lg p-3">
              <div className="text-2xl font-bold">{courses.length}</div>
              <div className="text-sm text-blue-100">المواد</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg p-3">
              <div className="text-2xl font-bold">{quizzes.length}</div>
              <div className="text-sm text-blue-100">الاختبارات</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg p-3">
              <div className="text-2xl font-bold">{certificates.length}</div>
              <div className="text-sm text-blue-100">الشهادات</div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu Button */}
      <div className="lg:hidden mb-4">
        <button
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
          className="bg-white border border-gray-300 rounded-lg px-4 py-2 flex items-center space-x-2 space-x-reverse shadow-sm hover:bg-gray-50 transition-colors"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <span className="text-gray-700 font-medium">القائمة</span>
        </button>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar */}
        <aside className={`profile-sidebar lg:w-64 flex-shrink-0 ${isSidebarOpen ? 'block' : 'hidden lg:block'}`}>
          <nav className="space-y-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`w-full text-right px-4 py-3 rounded-lg transition-all duration-200 flex items-center space-x-3 space-x-reverse ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 shadow-sm'
                }`}
                onClick={() => {
                  setActiveTab(tab.id);
                  setIsSidebarOpen(false); // Close sidebar on mobile after selection
                }}
              >
                {getTabIcon(tab.id)}
                <span className="font-medium">{tab.label}</span>
              </button>
            ))}
          </nav>
        </aside>

        <main className="profile-content flex-1 min-w-0">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
          >
            {renderTabContent()}
          </motion.div>
        </AnimatePresence>
        </main>
      </div>

      {/* New Chat Modal */}
      {showNewChatModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">محادثة جديدة</h3>
              <button
                onClick={() => setShowNewChatModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <NewChatForm
              instructors={instructors}
              instructorsLoading={instructorsLoading}
              onSubmit={handleCreateConversation}
              onCancel={() => setShowNewChatModal(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

// New Chat Form Component
const NewChatForm = ({ instructors, instructorsLoading, onSubmit, onCancel }) => {
  const [selectedInstructor, setSelectedInstructor] = useState('');
  const [chatTitle, setChatTitle] = useState('');
  const [firstMessage, setFirstMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!selectedInstructor) {
      alert('يرجى اختيار مدرس للمحادثة');
      return;
    }

    if (!firstMessage.trim()) {
      alert('يرجى كتابة رسالة للبدء');
      return;
    }

    setLoading(true);
    try {
      await onSubmit(selectedInstructor, chatTitle, firstMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Instructor Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          اختر المدرس
        </label>
        {instructorsLoading ? (
          <div className="text-center py-4 text-gray-500">جاري تحميل المدرسين...</div>
        ) : (
          <select
            value={selectedInstructor}
            onChange={(e) => setSelectedInstructor(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
            required
          >
            <option value="">-- اختر مدرساً --</option>
            {instructors.map((instructor) => (
              <option key={instructor.id} value={instructor.id}>
                {instructor.full_name || `${instructor.first_name || ''} ${instructor.last_name || ''}`.trim() || instructor.email}
              </option>
            ))}
          </select>
        )}
      </div>

      {/* Chat Title */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          عنوان المحادثة (اختياري)
        </label>
        <input
          type="text"
          value={chatTitle}
          onChange={(e) => setChatTitle(e.target.value)}
          placeholder="مثال: سؤال حول القانون المدني"
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
          maxLength={100}
        />
      </div>

      {/* First Message */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          رسالتك الأولى
        </label>
        <textarea
          value={firstMessage}
          onChange={(e) => setFirstMessage(e.target.value)}
          placeholder="اكتب رسالتك هنا..."
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
          rows={4}
          required
          maxLength={1000}
        />
        <div className="text-xs text-gray-500 mt-1">
          {firstMessage.length}/1000 حرف
        </div>
      </div>

      {/* Buttons */}
      <div className="flex gap-3 pt-4">
        <button
          type="submit"
          disabled={loading || !selectedInstructor || !firstMessage.trim()}
          className="flex-1 bg-yellow-400 hover:bg-yellow-500 disabled:bg-gray-300 text-white py-3 rounded-lg font-semibold transition-colors duration-200"
        >
          {loading ? 'جاري الإنشاء...' : 'بدء المحادثة'}
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
        >
          إلغاء
        </button>
      </div>
    </form>
  );
};

// Component wrapper للتحقق من نوع المستخدم
const StudentProfilePageWrapper = () => {
  const { user } = useContext(AuthContext);

  // السماح لجميع المستخدمين بالوصول لصفحة الملف الدراسي
  return <StudentProfilePage />;
};

export default StudentProfilePageWrapper;