import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { AuthProvider } from './context/AuthContext';
import { ToastProvider } from './components/UI/Toast';
import RegisterForm from './components/RegisterForm';
import EmailVerification from './components/EmailVerification';
import LoginForm from './components/LoginForm';
import ProtectedRoute from './components/ProtectedRoute';
import InstructorProtectedRoute from './components/InstructorProtectedRoute';
import StudentApplication from './components/StudentApplication';
import AdminDashboard from './components/AdminDashboard';
import Layout from './components/layout/Layout';
import HomePage from './components/Home/HomePage';

import StudentProfilePage from './pages/StudentProfilePage';
import LibraryPage from './pages/Library/LibraryPage';
import JobListingsPage from './pages/Careers/JobListingsPage';
import ForumHomePage from './pages/Forum/ForumHomePage';
import TopicListPage from './pages/Forum/TopicListPage';
import SettingsPage from './pages/Settings/SettingsPage';
import PasswordResetPage from './pages/Auth/PasswordResetPage';
import JoinInstructorPage from './pages/JoinInstructorPage';
import CreateTopicPage from './pages/Forum/CreateTopicPage';
import TopicDetailsPage from './pages/Forum/TopicDetailsPage';
import AboutPage from './pages/AboutPage';
import InstructorDashboard from './pages/InstructorDashboard';
import InstructorProfilePage from './pages/InstructorProfilePage';
import './App.css';
import { motion, AnimatePresence } from 'framer-motion';
import { AIAssistantButton } from './components/shared';
import FontOptimizer from './components/SEO/FontOptimizer';

const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  in: {
    opacity: 1,
    y: 0,
  },
  out: {
    opacity: 0,
    y: -20,
  },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.4,
};

const AnimatedPage = ({ children }) => (
  <motion.div
    initial="initial"
    animate="in"
    exit="out"
    variants={pageVariants}
    transition={pageTransition}
  >
    {children}
  </motion.div>
);

const AppRoutes = () => {
  const location = useLocation();
  return (
    <AnimatePresence mode="wait">
      <Routes location={location} key={location.pathname}>
        <Route path="/" element={<Layout />}>
          <Route index element={<AnimatedPage><HomePage /></AnimatedPage>} />
          <Route path="about" element={<AnimatedPage><AboutPage /></AnimatedPage>} />
          <Route
            path="student-profile"
            element={<AnimatedPage><ProtectedRoute><StudentProfilePage /></ProtectedRoute></AnimatedPage>}
          />
          <Route
            path="library"
            element={<AnimatedPage><ProtectedRoute><LibraryPage /></ProtectedRoute></AnimatedPage>}
          />
          <Route
            path="jobs"
            element={<AnimatedPage><JobListingsPage /></AnimatedPage>}
          />
          <Route
            path="forum"
            element={<AnimatedPage><ProtectedRoute><ForumHomePage /></ProtectedRoute></AnimatedPage>}
          />
          <Route
            path="forum/category/:categoryId"
            element={<AnimatedPage><ProtectedRoute><TopicListPage /></ProtectedRoute></AnimatedPage>}
          />
          <Route
            path="forum/create-topic"
            element={<AnimatedPage><ProtectedRoute><CreateTopicPage /></ProtectedRoute></AnimatedPage>}
          />
          <Route
            path="forum/topic/:id"
            element={<AnimatedPage><ProtectedRoute><TopicDetailsPage /></ProtectedRoute></AnimatedPage>}
          />
          <Route
            path="student-application"
            element={<AnimatedPage><ProtectedRoute><StudentApplication /></ProtectedRoute></AnimatedPage>}
          />
          <Route
            path="settings"
            element={<AnimatedPage><ProtectedRoute><SettingsPage /></ProtectedRoute></AnimatedPage>}
          />
        </Route>

        {/* Standalone animated routes */}
        <Route path="/register" element={<AnimatedPage><RegisterForm /></AnimatedPage>} />
        <Route path="/login" element={<AnimatedPage><LoginForm /></AnimatedPage>} />
        <Route path="/verify-email" element={<AnimatedPage><EmailVerification /></AnimatedPage>} />
        
        <Route path="/admin-user" element={<AnimatedPage><ProtectedRoute><AdminDashboard /></ProtectedRoute></AnimatedPage>} />
        
        <Route path="/password-reset" element={<AnimatedPage><PasswordResetPage /></AnimatedPage>} />
        <Route path="/join-instructor" element={<AnimatedPage><JoinInstructorPage /></AnimatedPage>} />
        
        <Route path="/instructor-dashboard" element={<AnimatedPage><InstructorProtectedRoute><InstructorDashboard /></InstructorProtectedRoute></AnimatedPage>} />
        <Route path="/instructor-profile" element={<AnimatedPage><InstructorProtectedRoute><InstructorProfilePage /></InstructorProtectedRoute></AnimatedPage>} />

        <Route path="/test-debug" element={<div style={{color:'red',fontSize:30, textAlign:'center', marginTop:100}}>TEST ROUTE WORKS</div>} />
        
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </AnimatePresence>
  );
};

function ScrollToTop() {
  const { pathname } = useLocation();
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);
  return null;
}

function App() {
  return (
    <HelmetProvider>
      <FontOptimizer>
        <AuthProvider>
          <ToastProvider>
            <AIAssistantButton />
            <Router>
              <ScrollToTop />
              <AppRoutes />
            </Router>
          </ToastProvider>
        </AuthProvider>
      </FontOptimizer>
    </HelmetProvider>
  );
}

export default App;
