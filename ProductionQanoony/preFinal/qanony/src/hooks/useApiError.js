import { useState, useCallback } from 'react';

/**
 * Hook لمعالجة أخطاء الـ API بشكل موحد
 */
export const useApiError = () => {
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleApiCall = useCallback(async (apiCall, options = {}) => {
    const { 
      onSuccess, 
      onError, 
      loadingState = true,
      errorContext = 'API Call'
    } = options;

    if (loadingState) setLoading(true);
    setError(null);

    try {
      const result = await apiCall();
      if (onSuccess) onSuccess(result);
      return result;
    } catch (err) {
      console.error(`${errorContext} Error:`, err);
      
      const errorInfo = {
        status: err.response?.status,
        message: getErrorMessage(err),
        context: errorContext,
        originalError: err
      };
      
      setError(errorInfo);
      if (onError) onError(errorInfo);
      throw err;
    } finally {
      if (loadingState) setLoading(false);
    }
  }, []);

  const getErrorMessage = (error) => {
    if (error.response?.status === 403) {
      return 'يجب توثيق البريد الإلكتروني أو وجود اشتراك نشط للوصول إلى هذا المحتوى';
    }
    
    if (error.response?.status === 404) {
      return 'المحتوى المطلوب غير موجود';
    }
    
    if (error.response?.status === 401) {
      return 'يجب تسجيل الدخول للوصول إلى هذا المحتوى';
    }
    
    if (error.response?.status >= 500) {
      return 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً';
    }
    
    if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
      return 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى';
    }
    
    return error.response?.data?.detail || 
           error.response?.data?.message || 
           error.message || 
           'حدث خطأ غير متوقع';
  };

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const retry = useCallback((apiCall, options = {}) => {
    return handleApiCall(apiCall, options);
  }, [handleApiCall]);

  return {
    error,
    loading,
    handleApiCall,
    clearError,
    retry,
    getErrorMessage
  };
};

export default useApiError;
