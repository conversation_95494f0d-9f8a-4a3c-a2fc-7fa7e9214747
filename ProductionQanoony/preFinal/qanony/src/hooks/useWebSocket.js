import { useState, useEffect, useRef, useCallback } from 'react';
import { 
  createAuthenticatedWebSocket, 
  sendWebSocketMessage, 
  isWebSocketConnected, 
  closeWebSocket 
} from '../utils/websocket';

/**
 * Custom hook for WebSocket connections
 * @param {string} endpoint - WebSocket endpoint
 * @param {Object} options - Hook options
 * @returns {Object} WebSocket state and methods
 */
export const useWebSocket = (endpoint, options = {}) => {
  const {
    autoConnect = true,
    reconnectAttempts = 3,
    reconnectInterval = 3000,
    onMessage,
    onOpen,
    onClose,
    onError,
  } = options;

  const [connectionState, setConnectionState] = useState('disconnected');
  const [lastMessage, setLastMessage] = useState(null);
  const [error, setError] = useState(null);
  
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectCountRef = useRef(0);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current && isWebSocketConnected(wsRef.current)) {
      return; // Already connected
    }

    setConnectionState('connecting');
    setError(null);

    try {
      wsRef.current = createAuthenticatedWebSocket(endpoint, {
        onOpen: (event) => {
          setConnectionState('connected');
          reconnectCountRef.current = 0;
          if (onOpen) onOpen(event);
        },
        
        onMessage: (data) => {
          setLastMessage(data);
          if (onMessage) onMessage(data);
        },
        
        onClose: (event) => {
          setConnectionState('disconnected');
          if (onClose) onClose(event);
          
          // Auto-reconnect if not manually closed
          if (event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
            reconnectCountRef.current += 1;
            reconnectTimeoutRef.current = setTimeout(() => {
              connect();
            }, reconnectInterval);
          }
        },
        
        onError: (error) => {
          setError(error);
          setConnectionState('error');
          if (onError) onError(error);
        },
      });
    } catch (err) {
      setError(err);
      setConnectionState('error');
    }
  }, [endpoint, onMessage, onOpen, onClose, onError, reconnectAttempts, reconnectInterval]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      closeWebSocket(wsRef.current);
      wsRef.current = null;
    }
    
    setConnectionState('disconnected');
  }, []);

  // Send message
  const sendMessage = useCallback((message) => {
    if (wsRef.current && isWebSocketConnected(wsRef.current)) {
      sendWebSocketMessage(wsRef.current, message);
      return true;
    } else {
      console.warn('WebSocket is not connected. Cannot send message:', message);
      return false;
    }
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  return {
    connectionState,
    lastMessage,
    error,
    isConnected: connectionState === 'connected',
    connect,
    disconnect,
    sendMessage,
  };
};

/**
 * Hook for chat room WebSocket connection
 * @param {string} roomId - Chat room ID
 * @param {Object} options - Hook options
 * @returns {Object} Chat WebSocket state and methods
 */
export const useChatWebSocket = (roomId, options = {}) => {
  const endpoint = `ws/chat/${roomId}/`;
  
  const {
    lastMessage,
    sendMessage: sendRawMessage,
    ...webSocketState
  } = useWebSocket(endpoint, options);

  // Send chat message
  const sendChatMessage = useCallback((message, messageType = 'chat_message') => {
    return sendRawMessage({
      type: messageType,
      message: message,
      timestamp: new Date().toISOString(),
    });
  }, [sendRawMessage]);

  // Send typing indicator
  const sendTyping = useCallback((isTyping = true) => {
    return sendRawMessage({
      type: 'typing',
      typing: isTyping,
    });
  }, [sendRawMessage]);

  return {
    ...webSocketState,
    lastMessage,
    sendChatMessage,
    sendTyping,
  };
};

/**
 * Hook for notifications WebSocket connection
 * @param {Object} options - Hook options
 * @returns {Object} Notifications WebSocket state and methods
 */
export const useNotificationsWebSocket = (options = {}) => {
  const endpoint = 'ws/notifications/';
  
  return useWebSocket(endpoint, options);
};

/**
 * Hook for AI Assistant WebSocket connection
 * @param {Object} options - Hook options
 * @returns {Object} AI Assistant WebSocket state and methods
 */
export const useAIAssistantWebSocket = (options = {}) => {
  const endpoint = 'ws/ai-assistant/';
  
  const {
    sendMessage: sendRawMessage,
    ...webSocketState
  } = useWebSocket(endpoint, options);

  // Send AI query
  const sendAIQuery = useCallback((query, context = {}) => {
    return sendRawMessage({
      type: 'ai_query',
      query: query,
      context: context,
      timestamp: new Date().toISOString(),
    });
  }, [sendRawMessage]);

  return {
    ...webSocketState,
    sendAIQuery,
  };
};

export default useWebSocket;
