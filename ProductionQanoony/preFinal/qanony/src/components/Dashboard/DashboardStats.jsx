import React, { useState, useEffect } from 'react';
import axios from 'axios';
import CountUp from 'react-countup';
import {
  Fa<PERSON><PERSON>s,
  FaChalkboardTeacher,
  FaBookOpen,
  FaVideo,
  FaQuestionCircle,
  FaUserCheck,
  FaUserTimes,
  FaRobot
} from 'react-icons/fa';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const DashboardStats = () => {
  const [stats, setStats] = useState(null);
  const [subscriptionStats, setSubscriptionStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchStats();
    fetchSubscriptionStats();
  }, []);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access');
      const response = await axios.get(`${API_BASE_URL}/api/auth/dashboard/stats/`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      setStats(response.data);
    } catch (err) {
      console.error('Error fetching dashboard stats:', err);
      setError('تعذر جلب الإحصائيات');
    } finally {
      setLoading(false);
    }
  };

  const fetchSubscriptionStats = async () => {
    try {
      const token = localStorage.getItem('access');
      const response = await axios.get(`${API_BASE_URL}/api/subscriptions/admin/stats/`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      setSubscriptionStats(response.data);
    } catch (err) {
      console.error('Error fetching subscription stats:', err);
      // Don't set error here, just log it
    }
  };



  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-yellow-600 text-lg">جاري تحميل الإحصائيات...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center p-4">
        {error}
        <button
          onClick={() => {
            fetchStats();
            fetchSubscriptionStats();
          }}
          className="ml-2 text-yellow-600 hover:underline"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  if (!stats) return null;

  const StatCard = ({ title, value, icon, color, bgColor, textColor }) => (
    <div className={`${bgColor} rounded-lg p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-shadow`}>
      <div className="flex items-center justify-between">
        <div>
          <p className={`${textColor} text-sm font-medium mb-1`}>{title}</p>
          <p className="text-2xl font-bold text-gray-800">
            <CountUp end={value} duration={2} separator="," />
          </p>
        </div>
        <div className={`${color} text-white rounded-full p-3`}>
          {icon}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-800">📊 لوحة المعلومات</h2>
        <button
          onClick={() => {
            fetchStats();
            fetchSubscriptionStats();
          }}
          className="bg-yellow-400 hover:bg-yellow-500 text-yellow-900 px-4 py-2 rounded-lg font-semibold transition-colors"
        >
          🔄 تحديث
        </button>
      </div>

      {/* Basic Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="إجمالي المحاضرات"
          value={stats.basic_stats.total_lectures}
          icon={<FaVideo size={24} />}
          color="bg-orange-500"
          bgColor="bg-orange-50"
          textColor="text-orange-700"
        />
        <StatCard
          title="إجمالي المواد"
          value={stats.basic_stats.total_subjects}
          icon={<FaBookOpen size={24} />}
          color="bg-purple-500"
          bgColor="bg-purple-50"
          textColor="text-purple-700"
        />
        <StatCard
          title="إجمالي المدرسين"
          value={stats.basic_stats.total_instructors}
          icon={<FaChalkboardTeacher size={24} />}
          color="bg-green-500"
          bgColor="bg-green-50"
          textColor="text-green-700"
        />
        <StatCard
          title="إجمالي الطلاب"
          value={stats.basic_stats.total_students}
          icon={<FaUsers size={24} />}
          color="bg-blue-500"
          bgColor="bg-blue-50"
          textColor="text-blue-700"
        />
      </div>

      {/* Secondary Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="محادثات الذكاء الاصطناعي"
          value={stats.basic_stats.ai_conversations}
          icon={<FaRobot size={24} />}
          color="bg-yellow-500"
          bgColor="bg-yellow-50"
          textColor="text-yellow-700"
        />
        <StatCard
          title="الاشتراكات المنتهية"
          value={stats.basic_stats.expired_subscriptions}
          icon={<FaUserTimes size={24} />}
          color="bg-red-500"
          bgColor="bg-red-50"
          textColor="text-red-700"
        />
        <StatCard
          title="الاشتراكات النشطة"
          value={stats.basic_stats.active_subscriptions}
          icon={<FaUserCheck size={24} />}
          color="bg-green-500"
          bgColor="bg-green-50"
          textColor="text-green-700"
        />
        <StatCard
          title="إجمالي الاختبارات"
          value={stats.basic_stats.total_quizzes}
          icon={<FaQuestionCircle size={24} />}
          color="bg-blue-500"
          bgColor="bg-blue-50"
          textColor="text-blue-700"
        />
      </div>

      {/* Applications Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
          <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
            <FaUsers className="text-blue-500" />
            طلبات الطلاب
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">
                <CountUp end={stats.applications.students.pending} duration={1.5} />
              </p>
              <p className="text-sm text-gray-600">قيد المراجعة</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                <CountUp end={stats.applications.students.approved} duration={1.5} />
              </p>
              <p className="text-sm text-gray-600">مقبولة</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
          <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
            <FaChalkboardTeacher className="text-green-500" />
            طلبات المدرسين
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">
                <CountUp end={stats.applications.instructors.pending} duration={1.5} />
              </p>
              <p className="text-sm text-gray-600">قيد المراجعة</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                <CountUp end={stats.applications.instructors.approved} duration={1.5} />
              </p>
              <p className="text-sm text-gray-600">مقبولة</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      {subscriptionStats && (
        <div className="space-y-8 mt-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">📊 تحليلات الاشتراكات والإيرادات</h2>

          {/* Revenue and Plan Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Monthly Revenue Trend */}
            {subscriptionStats && subscriptionStats.monthly_revenue && subscriptionStats.monthly_revenue.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
                <h3 className="text-lg font-bold text-gray-800 mb-4">📈 اتجاه الإيرادات الشهرية</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={subscriptionStats.monthly_revenue}>
                    <defs>
                      <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis
                      dataKey="month"
                      tick={{ fontSize: 11 }}
                      axisLine={{ stroke: '#e5e7eb' }}
                    />
                    <YAxis
                      tick={{ fontSize: 11 }}
                      axisLine={{ stroke: '#e5e7eb' }}
                      tickFormatter={(value) => `${value.toLocaleString()}`}
                    />
                    <Tooltip
                      formatter={(value) => [`${value.toLocaleString()} ج.م`, 'الإيرادات']}
                      labelStyle={{ color: '#374151' }}
                      contentStyle={{
                        backgroundColor: '#f9fafb',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        fontSize: '12px'
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#10b981"
                      strokeWidth={2}
                      fill="url(#revenueGradient)"
                      dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            )}

            {/* Plan Distribution */}
            {subscriptionStats && subscriptionStats.plan_stats && subscriptionStats.plan_stats.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
                <h3 className="text-lg font-bold text-gray-800 mb-4">🥧 توزيع الخطط النشطة</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={subscriptionStats.plan_stats.filter(plan => plan.active_count > 0)}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="active_count"
                    >
                      {subscriptionStats.plan_stats.filter(plan => plan.active_count > 0).map((entry, index) => {
                        const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
                        return <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />;
                      })}
                    </Pie>
                    <Tooltip
                      formatter={(value, name) => [value, 'اشتراكات نشطة']}
                      contentStyle={{
                        backgroundColor: '#f9fafb',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        fontSize: '12px'
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>

                {/* Legend */}
                <div className="mt-4 space-y-2">
                  {subscriptionStats.plan_stats.filter(plan => plan.active_count > 0).map((plan, index) => {
                    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
                    return (
                      <div key={plan.name} className="flex items-center gap-2 text-sm">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: colors[index % colors.length] }}
                        ></div>
                        <span className="text-gray-700 truncate">{plan.name}</span>
                        <span className="text-gray-500">({plan.active_count})</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {/* Revenue by Plan - Cards Layout */}
          {subscriptionStats && subscriptionStats.plan_stats && subscriptionStats.plan_stats.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
              <h3 className="text-lg font-bold text-gray-800 mb-6">💰 الإيرادات حسب الخطة</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {subscriptionStats.plan_stats
                  .filter(plan => plan.active_count > 0)
                  .sort((a, b) => (b.active_count * b.price) - (a.active_count * a.price))
                  .map((plan, index) => {
                    const revenue = plan.active_count * plan.price;
                    const colors = [
                      'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
                      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500'
                    ];
                    const bgColors = [
                      'bg-blue-50', 'bg-green-50', 'bg-yellow-50',
                      'bg-purple-50', 'bg-pink-50', 'bg-indigo-50'
                    ];
                    const textColors = [
                      'text-blue-700', 'text-green-700', 'text-yellow-700',
                      'text-purple-700', 'text-pink-700', 'text-indigo-700'
                    ];

                    return (
                      <div
                        key={plan.id}
                        className={`${bgColors[index % bgColors.length]} rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow`}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className={`w-4 h-4 rounded-full ${colors[index % colors.length]}`}></div>
                          <span className="text-xs text-gray-500">#{index + 1}</span>
                        </div>

                        <h4 className="font-semibold text-gray-800 mb-2 text-sm leading-tight">
                          {plan.name}
                        </h4>

                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-600">السعر:</span>
                            <span className="text-sm font-medium text-gray-800">
                              {plan.price.toLocaleString()} ج.م
                            </span>
                          </div>

                          <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-600">اشتراكات نشطة:</span>
                            <span className="text-sm font-medium text-gray-800">
                              {plan.active_count}
                            </span>
                          </div>

                          <div className="border-t border-gray-200 pt-2 mt-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-gray-700">إجمالي الإيرادات:</span>
                              <span className={`text-lg font-bold ${textColors[index % textColors.length]}`}>
                                {revenue.toLocaleString()} ج.م
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>

              {/* Total Revenue Summary */}
              <div className="mt-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold">إجمالي الإيرادات من جميع الخطط:</span>
                  <span className="text-2xl font-bold">
                    {subscriptionStats.plan_stats
                      .reduce((total, plan) => total + (plan.active_count * plan.price), 0)
                      .toLocaleString()} ج.م
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Subscription Status Overview */}
          {subscriptionStats && subscriptionStats.basic_stats && (
            <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
              <h3 className="text-lg font-bold text-gray-800 mb-4">📈 نظرة عامة على حالات الاشتراكات</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'نشط', value: subscriptionStats.basic_stats.active_subscriptions, color: '#10b981' },
                        { name: 'منتهي', value: subscriptionStats.basic_stats.expired_subscriptions, color: '#ef4444' },
                        { name: 'في انتظار التجديد', value: subscriptionStats.basic_stats.pending_renewals, color: '#f59e0b' }
                      ].filter(item => item.value > 0)}
                      cx="50%"
                      cy="50%"
                      innerRadius={50}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {[
                        { name: 'نشط', value: subscriptionStats.basic_stats.active_subscriptions, color: '#10b981' },
                        { name: 'منتهي', value: subscriptionStats.basic_stats.expired_subscriptions, color: '#ef4444' },
                        { name: 'في انتظار التجديد', value: subscriptionStats.basic_stats.pending_renewals, color: '#f59e0b' }
                      ].filter(item => item.value > 0).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value, name) => [value, 'اشتراكات']}
                      contentStyle={{
                        backgroundColor: '#f9fafb',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        fontSize: '12px'
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>

                {/* Stats Cards */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 rounded-full bg-green-500"></div>
                      <span className="text-sm font-medium text-gray-700">اشتراكات نشطة</span>
                    </div>
                    <span className="text-lg font-bold text-green-600">
                      {subscriptionStats.basic_stats.active_subscriptions}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 rounded-full bg-red-500"></div>
                      <span className="text-sm font-medium text-gray-700">اشتراكات منتهية</span>
                    </div>
                    <span className="text-lg font-bold text-red-600">
                      {subscriptionStats.basic_stats.expired_subscriptions}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 rounded-full bg-yellow-500"></div>
                      <span className="text-sm font-medium text-gray-700">في انتظار التجديد</span>
                    </div>
                    <span className="text-lg font-bold text-yellow-600">
                      {subscriptionStats.basic_stats.pending_renewals}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                      <span className="text-sm font-medium text-gray-700">إجمالي الإيرادات</span>
                    </div>
                    <span className="text-lg font-bold text-blue-600">
                      {subscriptionStats.basic_stats.total_revenue.toLocaleString()} ج.م
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

    </div>
  );
};

export default DashboardStats;
