import React from 'react';
import {
  <PERSON>Chart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

const DashboardCharts = ({ stats }) => {
  if (!stats || !stats.monthly_growth) return null;

  // Prepare data for charts
  const monthlyData = [...stats.monthly_growth].reverse(); // Show oldest to newest

  // Subscription status data for pie chart
  const subscriptionData = [
    { name: 'نشطة', value: stats.basic_stats.active_subscriptions, color: '#10b981' },
    { name: 'منتهية', value: stats.basic_stats.expired_subscriptions, color: '#ef4444' }
  ];

  // User types data for pie chart
  const userTypesData = [
    { name: 'طلاب', value: stats.basic_stats.total_students, color: '#3b82f6' },
    { name: 'مدرسين', value: stats.basic_stats.total_instructors, color: '#10b981' }
  ];

  // Content stats for bar chart
  const contentData = [
    { name: 'المواد', value: stats.basic_stats.total_subjects, color: '#8b5cf6' },
    { name: 'المحاضرات', value: stats.basic_stats.total_lectures, color: '#f59e0b' },
    { name: 'الاختبارات', value: stats.basic_stats.total_quizzes, color: '#6366f1' }
  ];

  // Custom tooltip for Arabic
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg">
          <p className="font-semibold">{`الشهر: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }}>
              {`${entry.dataKey === 'students' ? 'طلاب جدد' : 'اشتراكات جديدة'}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Monthly Growth Chart */}
      <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
        <h3 className="text-xl font-bold text-gray-800 mb-4">📈 نمو المنصة (آخر 6 شهور)</h3>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={monthlyData}>
            <defs>
              <linearGradient id="studentsGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
              </linearGradient>
              <linearGradient id="subscriptionsGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Area
              type="monotone"
              dataKey="students"
              stroke="#3b82f6"
              fillOpacity={1}
              fill="url(#studentsGradient)"
              name="طلاب جدد"
            />
            <Area
              type="monotone"
              dataKey="subscriptions"
              stroke="#10b981"
              fillOpacity={1}
              fill="url(#subscriptionsGradient)"
              name="اشتراكات جديدة"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Subscription Status Pie Chart */}
        <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
          <h3 className="text-xl font-bold text-gray-800 mb-4">🔄 حالة الاشتراكات</h3>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={subscriptionData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {subscriptionData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* User Types Pie Chart */}
        <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
          <h3 className="text-xl font-bold text-gray-800 mb-4">👥 توزيع المستخدمين</h3>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={userTypesData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {userTypesData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Content Statistics Bar Chart */}
      <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
        <h3 className="text-xl font-bold text-gray-800 mb-4">📚 إحصائيات المحتوى</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={contentData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="value" fill="#fbbf24" radius={[4, 4, 0, 0]}>
              {contentData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* AI Usage Chart */}
      {stats.basic_stats.ai_conversations > 0 && (
        <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
          <h3 className="text-xl font-bold text-gray-800 mb-4">🤖 استخدام الذكاء الاصطناعي</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-yellow-600 mb-2">
                {stats.basic_stats.ai_conversations}
              </div>
              <p className="text-gray-600">إجمالي المحادثات</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">
                {stats.basic_stats.active_ai_users}
              </div>
              <p className="text-gray-600">المستخدمين النشطين</p>
            </div>
          </div>
          <div className="mt-4">
            <div className="bg-gray-200 rounded-full h-4">
              <div 
                className="bg-gradient-to-r from-yellow-400 to-blue-500 h-4 rounded-full transition-all duration-1000"
                style={{ 
                  width: `${Math.min((stats.basic_stats.active_ai_users / stats.basic_stats.total_students) * 100, 100)}%` 
                }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 mt-2 text-center">
              معدل الاستخدام: {Math.round((stats.basic_stats.active_ai_users / stats.basic_stats.total_students) * 100)}%
            </p>
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
        <h3 className="text-xl font-bold text-gray-800 mb-4">⚡ مؤشرات الأداء</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 mb-2">
              {stats.basic_stats.total_lectures > 0 ? 
                Math.round((stats.basic_stats.total_quizzes / stats.basic_stats.total_lectures) * 100) : 0}%
            </div>
            <p className="text-gray-600">نسبة الاختبارات للمحاضرات</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {stats.basic_stats.total_subjects > 0 ? 
                Math.round(stats.basic_stats.total_lectures / stats.basic_stats.total_subjects) : 0}
            </div>
            <p className="text-gray-600">متوسط المحاضرات لكل مادة</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600 mb-2">
              {stats.basic_stats.total_students > 0 ? 
                Math.round((stats.basic_stats.active_subscriptions / stats.basic_stats.total_students) * 100) : 0}%
            </div>
            <p className="text-gray-600">معدل الاشتراكات النشطة</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardCharts;
