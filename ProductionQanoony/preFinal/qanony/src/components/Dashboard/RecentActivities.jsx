import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { processApiResponse } from '../../utils/arrayHelpers';
import {
  FaUserPlus,
  FaGraduationCap,
  FaBookOpen,
  FaVideo,
  FaQuestionCircle,
  FaRobot,
  FaMoneyBillWave,
  FaClock,
  FaCheckCircle,
  FaChalkboardTeacher
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

// Helper function to get icon component from string
const getIconComponent = (iconName) => {
  const iconMap = {
    'FaUserPlus': FaUserPlus,
    'FaGraduationCap': FaGraduationCap,
    'FaChalkboardTeacher': FaChalkboardTeacher,
    'FaMoneyBillWave': FaMoneyBillWave,
    'FaRobot': FaRobot,
    'FaBookOpen': FaBookOpen,
    'FaVideo': FaVideo,
    'FaQuestionCircle': FaQuestionCircle,
    'FaCheckCircle': FaCheckCircle
  };
  return iconMap[iconName] || FaCheckCircle;
};

const RecentActivities = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchRecentActivities();
  }, []);

  const fetchRecentActivities = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access');

      // Fetch real activities from API
      const response = await axios.get(`${API_BASE_URL}/api/auth/dashboard/recent-activities/`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      // Process API response safely
      const activitiesData = processApiResponse(response.data, 'Recent Activities');

      // Map API response to component format
      const apiActivities = activitiesData.map(activity => ({
        ...activity,
        timestamp: new Date(activity.timestamp),
        icon: getIconComponent(activity.icon)
      }));

      setActivities(apiActivities);
    } catch (err) {
      console.error('Error fetching recent activities:', err);

      // Fallback to mock data if API fails
      const mockActivities = [
        {
          id: 1,
          type: 'user_registration',
          title: 'تسجيل طالب جديد',
          description: 'انضم طالب جديد إلى المنصة',
          timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
          icon: FaUserPlus,
          color: 'bg-blue-500'
        },
        {
          id: 2,
          type: 'subscription',
          title: 'اشتراك جديد',
          description: 'تم تفعيل اشتراك جديد',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          icon: FaMoneyBillWave,
          color: 'bg-green-500'
        },
        {
          id: 3,
          type: 'course_completion',
          title: 'إنجاز مادة',
          description: 'طالب أكمل مادة القانون المدني',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
          icon: FaGraduationCap,
          color: 'bg-purple-500'
        },
        {
          id: 4,
          type: 'new_lecture',
          title: 'محاضرة جديدة',
          description: 'تم إضافة محاضرة جديدة في القانون التجاري',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
          icon: FaVideo,
          color: 'bg-orange-500'
        },
        {
          id: 5,
          type: 'quiz_completion',
          title: 'اختبار مكتمل',
          description: 'طالب أكمل اختبار في القانون الدستوري',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
          icon: FaQuestionCircle,
          color: 'bg-indigo-500'
        },
        {
          id: 6,
          type: 'ai_conversation',
          title: 'محادثة ذكية',
          description: 'طالب بدأ محادثة مع المساعد الذكي',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 10), // 10 hours ago
          icon: FaRobot,
          color: 'bg-yellow-500'
        },
        {
          id: 7,
          type: 'instructor_approval',
          title: 'موافقة على مدرس',
          description: 'تم قبول طلب مدرس جديد',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
          icon: FaCheckCircle,
          color: 'bg-green-600'
        },
        {
          id: 8,
          type: 'new_subject',
          title: 'مادة جديدة',
          description: 'تم إضافة مادة القانون الجنائي',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
          icon: FaBookOpen,
          color: 'bg-blue-600'
        }
      ];

      setActivities(mockActivities);
      setError('تعذر الاتصال بالخادم - تم عرض بيانات تجريبية');
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `منذ ${minutes} دقيقة`;
    } else if (hours < 24) {
      return `منذ ${hours} ساعة`;
    } else {
      return `منذ ${days} يوم`;
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
        <h3 className="text-xl font-bold text-gray-800 mb-4">🕒 الأنشطة الأخيرة</h3>
        <div className="flex justify-center items-center h-32">
          <div className="text-yellow-600">جاري التحميل...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
        <h3 className="text-xl font-bold text-gray-800 mb-4">🕒 الأنشطة الأخيرة</h3>
        <div className="text-red-500 text-center p-4">
          {error}
          <button 
            onClick={fetchRecentActivities} 
            className="ml-2 text-yellow-600 hover:underline"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-200">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold text-gray-800">🕒 الأنشطة الأخيرة</h3>
        <button 
          onClick={fetchRecentActivities}
          className="text-yellow-600 hover:text-yellow-700 text-sm font-medium"
        >
          🔄 تحديث
        </button>
      </div>
      
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {activities.map((activity) => {
          const IconComponent = activity.icon;
          return (
            <div key={activity.id} className="flex items-start gap-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div className={`${activity.color} text-white rounded-full p-2 flex-shrink-0`}>
                <IconComponent size={16} />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-gray-800 text-sm">{activity.title}</h4>
                <p className="text-gray-600 text-sm mt-1">{activity.description}</p>
                <div className="flex items-center gap-2 mt-2">
                  <FaClock className="text-gray-400" size={12} />
                  <span className="text-gray-500 text-xs">{formatTimeAgo(activity.timestamp)}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {activities.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          لا توجد أنشطة حديثة
        </div>
      )}
      
      <div className="mt-4 pt-4 border-t border-gray-200">
        <button className="w-full text-center text-yellow-600 hover:text-yellow-700 font-medium text-sm">
          عرض جميع الأنشطة
        </button>
      </div>
    </div>
  );
};

export default RecentActivities;
