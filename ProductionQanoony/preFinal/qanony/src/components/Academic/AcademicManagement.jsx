import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  FaUniversity,
  FaGraduationCap,
  FaChartLine,
  FaUsers,
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaCheck,
  FaTimes
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const AcademicManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Universities state
  const [universities, setUniversities] = useState([]);
  const [showUniversityModal, setShowUniversityModal] = useState(false);
  const [editingUniversity, setEditingUniversity] = useState(null);
  const [universityForm, setUniversityForm] = useState({
    name_ar: '',
    name_en: '',
    code: '',
    city: '',
    is_active: true
  });

  // Academic Years state
  const [academicYears, setAcademicYears] = useState([]);
  const [showYearModal, setShowYearModal] = useState(false);
  const [editingYear, setEditingYear] = useState(null);
  const [yearForm, setYearForm] = useState({
    year_number: '',
    year_name_ar: '',
    year_name_en: '',
    is_active: true
  });

  // Stats state
  const [stats, setStats] = useState({
    total_universities: 0,
    active_universities: 0,
    total_years: 0,
    active_years: 0,
    total_students: 0,
    students_by_year: []
  });

  // Student Progress state
  const [studentProgress, setStudentProgress] = useState([]);
  const [quizAttempts, setQuizAttempts] = useState([]);
  const [progressStats, setProgressStats] = useState({
    total_progress_records: 0,
    avg_completion_rate: 0,
    total_quiz_attempts: 0,
    avg_quiz_score: 0,
    top_performers: [],
    struggling_students: []
  });

  const tabs = [
    { key: 'overview', label: 'نظرة عامة', icon: <FaChartLine /> },
    { key: 'universities', label: 'الجامعات', icon: <FaUniversity /> },
    { key: 'academic-years', label: 'السنوات الدراسية', icon: <FaGraduationCap /> },
    { key: 'student-progress', label: 'تقدم الطلاب', icon: <FaUsers /> }
  ];

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      // Fetch universities
      const universitiesRes = await axios.get(`${API_BASE_URL}/api/universities/universities/`, { headers });
      setUniversities(Array.isArray(universitiesRes.data) ? universitiesRes.data : universitiesRes.data.results || []);

      // Fetch academic years
      const yearsRes = await axios.get(`${API_BASE_URL}/api/universities/academic-years/`, { headers });
      setAcademicYears(Array.isArray(yearsRes.data) ? yearsRes.data : yearsRes.data.results || []);

      // Fetch student progress
      let progressRes = null;
      try {
        progressRes = await axios.get(`${API_BASE_URL}/api/students/progress/`, { headers });
        setStudentProgress(Array.isArray(progressRes.data) ? progressRes.data : progressRes.data.results || []);
      } catch (err) {
        console.log('Student progress not available:', err);
        setStudentProgress([]);
      }

      // Fetch quiz attempts
      let attemptsRes = null;
      try {
        attemptsRes = await axios.get(`${API_BASE_URL}/api/courses/quiz-attempts/`, { headers });
        setQuizAttempts(Array.isArray(attemptsRes.data) ? attemptsRes.data : attemptsRes.data.results || []);
      } catch (err) {
        console.log('Quiz attempts not available:', err);
        setQuizAttempts([]);
      }

      // Calculate stats
      const universitiesData = Array.isArray(universitiesRes.data) ? universitiesRes.data : universitiesRes.data.results || [];
      const yearsData = Array.isArray(yearsRes.data) ? yearsRes.data : yearsRes.data.results || [];

      const totalUniversities = universitiesData.length || 0;
      const activeUniversities = universitiesData.filter(u => u.is_active).length || 0;
      const totalYears = yearsData.length || 0;
      const activeYears = yearsData.filter(y => y.is_active).length || 0;

      // Calculate progress stats
      const progressData = progressRes ? (Array.isArray(progressRes.data) ? progressRes.data : progressRes.data.results || []) : [];
      const attemptsData = attemptsRes ? (Array.isArray(attemptsRes.data) ? attemptsRes.data : attemptsRes.data.results || []) : [];

      const totalProgressRecords = progressData.length;
      const avgCompletionRate = totalProgressRecords > 0
        ? progressData.reduce((sum, p) => sum + (p.total_lectures > 0 ? (p.completed_lectures / p.total_lectures) * 100 : 0), 0) / totalProgressRecords
        : 0;

      const totalQuizAttempts = attemptsData.length;
      const avgQuizScore = totalQuizAttempts > 0
        ? attemptsData.reduce((sum, a) => sum + (a.total_points > 0 ? (a.score / a.total_points) * 100 : 0), 0) / totalQuizAttempts
        : 0;

      setStats({
        total_universities: totalUniversities,
        active_universities: activeUniversities,
        total_years: totalYears,
        active_years: activeYears,
        total_students: totalProgressRecords, // Based on progress records
        students_by_year: []
      });

      setProgressStats({
        total_progress_records: totalProgressRecords,
        avg_completion_rate: Math.round(avgCompletionRate),
        total_quiz_attempts: totalQuizAttempts,
        avg_quiz_score: Math.round(avgQuizScore),
        top_performers: attemptsData
          .filter(a => a.total_points > 0)
          .sort((a, b) => (b.score / b.total_points) - (a.score / a.total_points))
          .slice(0, 5),
        struggling_students: progressData
          .filter(p => p.total_lectures > 0 && (p.completed_lectures / p.total_lectures) < 0.5)
          .slice(0, 5)
      });

    } catch (err) {
      console.error('Error fetching academic data:', err);
      setError('تعذر جلب البيانات الأكاديمية');
    } finally {
      setLoading(false);
    }
  };

  // University management functions
  const openUniversityModal = (university = null) => {
    if (university) {
      setEditingUniversity(university);
      setUniversityForm({
        name_ar: university.name_ar,
        name_en: university.name_en,
        code: university.code,
        city: university.city,
        is_active: university.is_active
      });
    } else {
      setEditingUniversity(null);
      setUniversityForm({
        name_ar: '',
        name_en: '',
        code: '',
        city: '',
        is_active: true
      });
    }
    setShowUniversityModal(true);
  };

  const closeUniversityModal = () => {
    setShowUniversityModal(false);
    setEditingUniversity(null);
    setUniversityForm({
      name_ar: '',
      name_en: '',
      code: '',
      city: '',
      is_active: true
    });
  };

  const handleUniversitySubmit = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      if (editingUniversity) {
        await axios.patch(`${API_BASE_URL}/api/universities/universities/${editingUniversity.id}/`, universityForm, { headers });
      } else {
        await axios.post(`${API_BASE_URL}/api/universities/universities/`, universityForm, { headers });
      }

      closeUniversityModal();
      fetchData();
    } catch (err) {
      console.error('Error saving university:', err);
      alert('حدث خطأ أثناء حفظ الجامعة');
    }
  };

  const handleDeleteUniversity = async (universityId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الجامعة؟')) {
      return;
    }

    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };
      
      await axios.delete(`${API_BASE_URL}/api/universities/universities/${universityId}/`, { headers });
      fetchData();
    } catch (err) {
      console.error('Error deleting university:', err);
      alert('حدث خطأ أثناء حذف الجامعة');
    }
  };

  // Academic Year management functions
  const openYearModal = (year = null) => {
    if (year) {
      setEditingYear(year);
      setYearForm({
        year_number: year.year_number,
        year_name_ar: year.year_name_ar,
        year_name_en: year.year_name_en,
        is_active: year.is_active
      });
    } else {
      setEditingYear(null);
      setYearForm({
        year_number: '',
        year_name_ar: '',
        year_name_en: '',
        is_active: true
      });
    }
    setShowYearModal(true);
  };

  const closeYearModal = () => {
    setShowYearModal(false);
    setEditingYear(null);
    setYearForm({
      year_number: '',
      year_name_ar: '',
      year_name_en: '',
      is_active: true
    });
  };

  const handleYearSubmit = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      const payload = {
        ...yearForm,
        year_number: parseInt(yearForm.year_number)
      };

      if (editingYear) {
        await axios.patch(`${API_BASE_URL}/api/universities/academic-years/${editingYear.id}/`, payload, { headers });
      } else {
        await axios.post(`${API_BASE_URL}/api/universities/academic-years/`, payload, { headers });
      }

      closeYearModal();
      fetchData();
    } catch (err) {
      console.error('Error saving academic year:', err);
      alert('حدث خطأ أثناء حفظ السنة الدراسية');
    }
  };

  const handleDeleteYear = async (yearId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه السنة الدراسية؟')) {
      return;
    }

    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };
      
      await axios.delete(`${API_BASE_URL}/api/universities/academic-years/${yearId}/`, { headers });
      fetchData();
    } catch (err) {
      console.error('Error deleting academic year:', err);
      alert('حدث خطأ أثناء حذف السنة الدراسية');
    }
  };

  if (loading) return <div className="text-center py-8">جاري التحميل...</div>;
  if (error) return <div className="text-center py-8 text-red-600">{error}</div>;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">الإدارة الأكاديمية</h2>
        <div className="flex gap-2">
          <button
            onClick={() => fetchData()}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            🔄 تحديث
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 space-x-reverse">
          {tabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === tab.key
                  ? 'border-yellow-500 text-yellow-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800">نظرة عامة على النظام الأكاديمي</h3>
            
            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-blue-500 text-white rounded-full p-3 mb-2">
                  <FaUniversity size={24} />
                </div>
                <div className="text-2xl font-bold text-blue-800">{stats.total_universities}</div>
                <div className="text-blue-700 mt-1">إجمالي الجامعات</div>
                <div className="text-sm text-blue-600 mt-1">
                  ({stats.active_universities} نشطة)
                </div>
              </div>

              <div className="bg-green-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-green-500 text-white rounded-full p-3 mb-2">
                  <FaGraduationCap size={24} />
                </div>
                <div className="text-2xl font-bold text-green-800">{stats.total_years}</div>
                <div className="text-green-700 mt-1">السنوات الدراسية</div>
                <div className="text-sm text-green-600 mt-1">
                  ({stats.active_years} نشطة)
                </div>
              </div>

              <div className="bg-yellow-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-yellow-500 text-white rounded-full p-3 mb-2">
                  <FaUsers size={24} />
                </div>
                <div className="text-2xl font-bold text-yellow-800">{stats.total_students}</div>
                <div className="text-yellow-700 mt-1">إجمالي الطلاب</div>
                <div className="text-sm text-yellow-600 mt-1">
                  موزعين على السنوات
                </div>
              </div>

              <div className="bg-purple-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-purple-500 text-white rounded-full p-3 mb-2">
                  <FaChartLine size={24} />
                </div>
                <div className="text-2xl font-bold text-purple-800">95%</div>
                <div className="text-purple-700 mt-1">معدل النشاط</div>
                <div className="text-sm text-purple-600 mt-1">
                  للجامعات والسنوات
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Universities Tab */}
        {activeTab === 'universities' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-bold text-gray-800">إدارة الجامعات ({universities.length})</h3>
              <button
                onClick={() => openUniversityModal()}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
              >
                <FaPlus size={16} />
                إضافة جامعة جديدة
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">ID</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالعربية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالإنجليزية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكود</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المدينة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الإنشاء</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {Array.isArray(universities) ? universities.map(university => (
                      <tr key={university.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{university.id}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{university.name_ar}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{university.name_en}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                            {university.code}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{university.city}</td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            university.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {university.is_active ? 'نشطة' : 'غير نشطة'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(university.created_at).toLocaleDateString('ar-EG')}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <button
                              onClick={() => openUniversityModal(university)}
                              className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                              title="تعديل"
                            >
                              <FaEdit size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteUniversity(university.id)}
                              className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                              title="حذف"
                            >
                              <FaTrash size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan="8" className="px-4 py-4 text-center text-red-500">
                          خطأ في تحميل بيانات الجامعات
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {universities.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد جامعات مسجلة حالياً
                </div>
              )}
            </div>
          </div>
        )}

        {/* Academic Years Tab */}
        {activeTab === 'academic-years' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-bold text-gray-800">إدارة السنوات الدراسية ({academicYears.length})</h3>
              <button
                onClick={() => openYearModal()}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
              >
                <FaPlus size={16} />
                إضافة سنة دراسية جديدة
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">ID</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم السنة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالعربية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالإنجليزية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {Array.isArray(academicYears) ? academicYears.map(year => (
                      <tr key={year.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{year.id}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                            السنة {year.year_number}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{year.year_name_ar}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{year.year_name_en}</td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            year.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {year.is_active ? 'نشطة' : 'غير نشطة'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <button
                              onClick={() => openYearModal(year)}
                              className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                              title="تعديل"
                            >
                              <FaEdit size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteYear(year.id)}
                              className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                              title="حذف"
                            >
                              <FaTrash size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan="6" className="px-4 py-4 text-center text-red-500">
                          خطأ في تحميل بيانات السنوات الدراسية
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {academicYears.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد سنوات دراسية مسجلة حالياً
                </div>
              )}
            </div>
          </div>
        )}

        {/* Student Progress Tab */}
        {activeTab === 'student-progress' && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800">تقدم الطلاب وإحصائيات الأداء</h3>

            {/* Progress Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-blue-500 text-white rounded-full p-3 mb-2">
                  <FaUsers size={24} />
                </div>
                <div className="text-2xl font-bold text-blue-800">{progressStats.total_progress_records}</div>
                <div className="text-blue-700 mt-1">سجلات التقدم</div>
              </div>

              <div className="bg-green-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-green-500 text-white rounded-full p-3 mb-2">
                  <FaChartLine size={24} />
                </div>
                <div className="text-2xl font-bold text-green-800">{progressStats.avg_completion_rate}%</div>
                <div className="text-green-700 mt-1">متوسط الإكمال</div>
              </div>

              <div className="bg-yellow-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-yellow-500 text-white rounded-full p-3 mb-2">
                  <FaGraduationCap size={24} />
                </div>
                <div className="text-2xl font-bold text-yellow-800">{progressStats.total_quiz_attempts}</div>
                <div className="text-yellow-700 mt-1">محاولات الاختبارات</div>
              </div>

              <div className="bg-purple-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-purple-500 text-white rounded-full p-3 mb-2">
                  <FaEye size={24} />
                </div>
                <div className="text-2xl font-bold text-purple-800">{progressStats.avg_quiz_score}%</div>
                <div className="text-purple-700 mt-1">متوسط درجات الاختبارات</div>
              </div>
            </div>

            {/* Top Performers and Struggling Students */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Performers */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h4 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                  <FaCheck className="text-green-500" />
                  أفضل الطلاب أداءً
                </h4>
                <div className="space-y-3">
                  {progressStats.top_performers.length > 0 ? (
                    progressStats.top_performers.map((attempt, index) => (
                      <div key={attempt.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                            {index + 1}
                          </span>
                          <div>
                            <div className="font-medium text-gray-800">
                              {attempt.student?.user?.email || attempt.student?.user_email || 'طالب غير محدد'}
                            </div>
                            <div className="text-sm text-gray-600">
                              {attempt.quiz?.title_ar || 'اختبار غير محدد'}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-green-800">
                            {attempt.total_points > 0 ? Math.round((attempt.score / attempt.total_points) * 100) : 0}%
                          </div>
                          <div className="text-sm text-gray-600">
                            {attempt.score}/{attempt.total_points}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      لا توجد بيانات اختبارات حالياً
                    </div>
                  )}
                </div>
              </div>

              {/* Struggling Students */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h4 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                  <FaTimes className="text-red-500" />
                  الطلاب الذين يحتاجون مساعدة
                </h4>
                <div className="space-y-3">
                  {progressStats.struggling_students.length > 0 ? (
                    progressStats.struggling_students.map((progress, index) => (
                      <div key={progress.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <span className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                            !
                          </span>
                          <div>
                            <div className="font-medium text-gray-800">
                              {progress.student_id || 'طالب غير محدد'}
                            </div>
                            <div className="text-sm text-gray-600">
                              آخر وصول: {new Date(progress.last_accessed).toLocaleDateString('ar-EG')}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-red-800">
                            {progress.total_lectures > 0 ? Math.round((progress.completed_lectures / progress.total_lectures) * 100) : 0}%
                          </div>
                          <div className="text-sm text-gray-600">
                            {progress.completed_lectures}/{progress.total_lectures} محاضرة
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      جميع الطلاب يحققون تقدماً جيداً! 🎉
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Detailed Progress Table */}
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h4 className="text-lg font-bold text-gray-800">تفاصيل تقدم الطلاب</h4>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الطالب</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفصل الدراسي</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المحاضرات المكتملة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي المحاضرات</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">نسبة الإكمال</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">درجات الاختبارات</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر وصول</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {studentProgress.map(progress => {
                      const completionRate = progress.total_lectures > 0
                        ? (progress.completed_lectures / progress.total_lectures) * 100
                        : 0;

                      return (
                        <tr key={progress.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {progress.student_id || '—'}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {progress.semester?.name || '—'}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                              {progress.completed_lectures}
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                              {progress.total_lectures}
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div
                                  className={`h-2 rounded-full ${
                                    completionRate >= 80 ? 'bg-green-500' :
                                    completionRate >= 60 ? 'bg-yellow-500' :
                                    completionRate >= 40 ? 'bg-orange-500' : 'bg-red-500'
                                  }`}
                                  style={{ width: `${Math.min(completionRate, 100)}%` }}
                                ></div>
                              </div>
                              <span className={`text-xs font-medium ${
                                completionRate >= 80 ? 'text-green-800' :
                                completionRate >= 60 ? 'text-yellow-800' :
                                completionRate >= 40 ? 'text-orange-800' : 'text-red-800'
                              }`}>
                                {Math.round(completionRate)}%
                              </span>
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {progress.quiz_scores && Object.keys(progress.quiz_scores).length > 0 ? (
                              <div className="space-y-1">
                                {Object.entries(progress.quiz_scores).map(([quiz, score]) => (
                                  <div key={quiz} className="text-xs">
                                    <span className="font-medium">{quiz}:</span> {score}
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <span className="text-gray-400">لا توجد اختبارات</span>
                            )}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(progress.last_accessed).toLocaleDateString('ar-EG')}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {studentProgress.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد سجلات تقدم حالياً
                </div>
              )}
            </div>

            {/* Quiz Attempts Summary */}
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h4 className="text-lg font-bold text-gray-800">ملخص محاولات الاختبارات</h4>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الطالب</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاختبار</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">النتيجة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">النسبة المئوية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ البداية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الإكمال</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {quizAttempts.slice(0, 10).map(attempt => {
                      const percentage = attempt.total_points > 0
                        ? (attempt.score / attempt.total_points) * 100
                        : 0;

                      return (
                        <tr key={attempt.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {attempt.student?.user?.email || attempt.student?.user_email || '—'}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {attempt.quiz?.title_ar || '—'}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                              {attempt.score}/{attempt.total_points}
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              percentage >= 80 ? 'bg-green-100 text-green-800' :
                              percentage >= 60 ? 'bg-yellow-100 text-yellow-800' :
                              percentage >= 40 ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {Math.round(percentage)}%
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(attempt.started_at).toLocaleDateString('ar-EG')}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {attempt.completed_at
                              ? new Date(attempt.completed_at).toLocaleDateString('ar-EG')
                              : <span className="text-yellow-600">جاري...</span>
                            }
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {quizAttempts.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد محاولات اختبارات حالياً
                </div>
              )}

              {quizAttempts.length > 10 && (
                <div className="px-6 py-4 bg-gray-50 text-center">
                  <span className="text-sm text-gray-600">
                    عرض 10 من أصل {quizAttempts.length} محاولة
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* University Modal */}
      {showUniversityModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              {editingUniversity ? 'تعديل الجامعة' : 'إضافة جامعة جديدة'}
            </h3>

            <form onSubmit={handleUniversitySubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الاسم بالعربية
                </label>
                <input
                  type="text"
                  value={universityForm.name_ar}
                  onChange={(e) => setUniversityForm({...universityForm, name_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الاسم بالإنجليزية
                </label>
                <input
                  type="text"
                  value={universityForm.name_en}
                  onChange={(e) => setUniversityForm({...universityForm, name_en: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  كود الجامعة
                </label>
                <input
                  type="text"
                  value={universityForm.code}
                  onChange={(e) => setUniversityForm({...universityForm, code: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المدينة
                </label>
                <input
                  type="text"
                  value={universityForm.city}
                  onChange={(e) => setUniversityForm({...universityForm, city: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="university_is_active"
                  checked={universityForm.is_active}
                  onChange={(e) => setUniversityForm({...universityForm, is_active: e.target.checked})}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="university_is_active" className="mr-2 block text-sm text-gray-900">
                  جامعة نشطة
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  {editingUniversity ? 'تحديث' : 'إضافة'}
                </button>
                <button
                  type="button"
                  onClick={closeUniversityModal}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Academic Year Modal */}
      {showYearModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              {editingYear ? 'تعديل السنة الدراسية' : 'إضافة سنة دراسية جديدة'}
            </h3>

            <form onSubmit={handleYearSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  رقم السنة
                </label>
                <input
                  type="number"
                  value={yearForm.year_number}
                  onChange={(e) => setYearForm({...yearForm, year_number: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  min="1"
                  max="6"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الاسم بالعربية
                </label>
                <input
                  type="text"
                  value={yearForm.year_name_ar}
                  onChange={(e) => setYearForm({...yearForm, year_name_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  placeholder="مثال: الفرقة الأولى"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الاسم بالإنجليزية
                </label>
                <input
                  type="text"
                  value={yearForm.year_name_en}
                  onChange={(e) => setYearForm({...yearForm, year_name_en: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  placeholder="Example: First Year"
                  required
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="year_is_active"
                  checked={yearForm.is_active}
                  onChange={(e) => setYearForm({...yearForm, is_active: e.target.checked})}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="year_is_active" className="mr-2 block text-sm text-gray-900">
                  سنة نشطة
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  {editingYear ? 'تحديث' : 'إضافة'}
                </button>
                <button
                  type="button"
                  onClick={closeYearModal}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default AcademicManagement;
