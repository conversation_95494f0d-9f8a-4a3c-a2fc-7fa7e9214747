import React, { useEffect, useState } from 'react';
import { getSemesters, deleteSemester } from './api/semesters';
import SemesterForm from './SemesterForm';
import { ensureArray, processApiResponse, handleApiError } from '../../utils/arrayHelpers';

const SemesterTable = () => {
  const [semesters, setSemesters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [editSemester, setEditSemester] = useState(null);

  const fetchSemesters = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getSemesters();
      console.log('Semesters API Response:', response);
      const processedData = processApiResponse(response, 'Semesters API', 'results');
      setSemesters(processedData);
    } catch (err) {
      console.error('Semesters API Error:', err);
      const errorData = handleApiError(err, 'Semesters API', setError);
      setSemesters(errorData);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSemesters();
  }, []);

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الفصل؟')) {
      await deleteSemester(id);
      fetchSemesters();
    }
  };

  const handleAdd = () => {
    setEditSemester(null);
    setShowForm(true);
  };

  const handleEdit = (semester) => {
    setEditSemester(semester);
    setShowForm(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-yellow-600">جاري التحميل...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center p-4">
        {error}
        <button onClick={fetchSemesters} className="ml-2 text-yellow-600 hover:underline">
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-yellow-900">إدارة الفصول الدراسية</h2>
        <button 
          className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded-lg hover:bg-yellow-500 transition-colors font-semibold" 
          onClick={handleAdd}
        >
          إضافة فصل جديد
        </button>
      </div>

      {!Array.isArray(semesters) ? (
        <div className="text-center py-8 text-red-500">
          خطأ في تحميل البيانات - يرجى إعادة تحميل الصفحة
        </div>
      ) : semesters.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          لا توجد فصول دراسية حتى الآن
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-yellow-50">
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">ID</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">العنوان (عربي)</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">العنوان (إنجليزي)</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الوصف</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">السنة الأكاديمية</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الترتيب</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الحالة</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {semesters.map((sem) => (
                <tr key={sem.id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-4 py-2">{sem.id}</td>
                  <td className="border border-gray-300 px-4 py-2">{sem.title_ar}</td>
                  <td className="border border-gray-300 px-4 py-2">{sem.title_en}</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="max-w-xs truncate" title={sem.description_ar}>
                      {sem.description_ar}
                    </div>
                  </td>
                  <td className="border border-gray-300 px-4 py-2">{sem.academic_year}</td>
                  <td className="border border-gray-300 px-4 py-2">{sem.order}</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <span className={`px-2 py-1 rounded text-sm ${
                      sem.is_published 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {sem.is_published ? 'منشور' : 'غير منشور'}
                    </span>
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(sem)}
                        className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors"
                      >
                        تعديل
                      </button>
                      <button
                        onClick={() => handleDelete(sem.id)}
                        className="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 transition-colors"
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {showForm && (
        <SemesterForm onClose={() => setShowForm(false)} onSaved={fetchSemesters} semester={editSemester} />
      )}
    </div>
  );
};

export default SemesterTable; 