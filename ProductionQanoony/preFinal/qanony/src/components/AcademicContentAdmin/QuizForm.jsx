import React, { useState, useEffect } from 'react';
import { addQuiz, updateQuiz } from './api/quizzes';
import { getSubjects } from './api/subjects';
import { getLectures } from './api/lectures';

const defaultState = {
  subject: '',
  lecture: '',
  title_ar: '',
  title_en: '',
  instructions_ar: '',
  description_ar: '',
  description_en: '',
  time_limit_minutes: '',
  passing_score: '',
  is_active: false,
};

const QuizForm = ({ onClose, onSaved, quiz }) => {
  const [form, setForm] = useState(defaultState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [subjects, setSubjects] = useState([]);
  const [lectures, setLectures] = useState([]);
  const [subjectsLoading, setSubjectsLoading] = useState(true);
  const [lecturesLoading, setLecturesLoading] = useState(false);

  useEffect(() => {
    setSubjectsLoading(true);
    getSubjects().then(setSubjects).catch(() => setSubjects([])).finally(() => setSubjectsLoading(false));
  }, []);

  useEffect(() => {
    if (form.subject) {
      setLecturesLoading(true);
      getLectures(form.subject).then(setLectures).catch(() => setLectures([])).finally(() => setLecturesLoading(false));
    } else {
      setLectures([]);
    }
  }, [form.subject]);

  useEffect(() => {
    if (quiz) {
      setForm({
        subject: quiz.lecture?.subject?.id || '',
        lecture: quiz.lecture?.id || '',
        title_ar: quiz.title_ar || '',
        title_en: quiz.title_en || '',
        instructions_ar: quiz.instructions_ar || '',
        description_ar: quiz.description_ar || '',
        description_en: quiz.description_en || '',
        time_limit_minutes: quiz.time_limit_minutes || '',
        passing_score: quiz.passing_score || '',
        is_active: !!quiz.is_active,
      });
    } else {
      setForm(defaultState);
    }
  }, [quiz]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!form.subject || !form.lecture || !form.title_ar || !form.instructions_ar || !form.time_limit_minutes || !form.passing_score) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const payload = {
        ...form,
        lecture_id: Number(form.lecture),
        time_limit_minutes: Number(form.time_limit_minutes),
        passing_score: Number(form.passing_score),
      };
      delete payload.subject;
      delete payload.lecture;
      
      if (quiz) {
        await updateQuiz(quiz.id, payload);
      } else {
        await addQuiz(payload);
      }
      onSaved();
      onClose();
    } catch (err) {
      if (err.response && err.response.data) {
        setError(
          typeof err.response.data === 'string'
            ? err.response.data
            : Object.entries(err.response.data)
                .map(([key, val]) => `${key}: ${val}`)
                .join(' | ')
        );
      } else {
        setError('حدث خطأ أثناء الحفظ. تأكد من صحة البيانات.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-yellow-900">
            {quiz ? 'تعديل اختبار' : 'إضافة اختبار جديد'}
          </h2>
          <button onClick={onClose} className="text-red-500 hover:text-red-700 text-xl font-bold bg-transparent border-none shadow-none p-0 m-0 focus:outline-none hover:bg-transparent active:bg-transparent">
            ✕
          </button>
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block mb-1 font-semibold">المادة *</label>
            <select
              name="subject"
              value={form.subject}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              required
              disabled={subjectsLoading}
            >
              <option value="">اختر المادة</option>
              {subjects.map((subject) => (
                <option key={subject.id} value={subject.id}>
                  {subject.name_ar}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block mb-1 font-semibold">المحاضرة *</label>
            <select
              name="lecture"
              value={form.lecture}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              required
              disabled={lecturesLoading || !form.subject}
            >
              <option value="">اختر المحاضرة</option>
              {lectures.map((lecture) => (
                <option key={lecture.id} value={lecture.id}>
                  {lecture.title_ar}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block mb-1 font-semibold">عنوان الاختبار (عربي) *</label>
            <input
              type="text"
              name="title_ar"
              value={form.title_ar}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              required
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold">تعليمات الاختبار (عربي) *</label>
            <textarea
              name="instructions_ar"
              value={form.instructions_ar}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              rows="3"
              required
              placeholder="اكتب تعليمات الاختبار هنا..."
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold">عنوان الاختبار (إنجليزي)</label>
            <input
              type="text"
              name="title_en"
              value={form.title_en}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold">وصف الاختبار (عربي)</label>
            <textarea
              name="description_ar"
              value={form.description_ar}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              rows="3"
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold">وصف الاختبار (إنجليزي)</label>
            <textarea
              name="description_en"
              value={form.description_en}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              rows="3"
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold">الوقت المحدد (دقائق) *</label>
            <input
              type="number"
              name="time_limit_minutes"
              value={form.time_limit_minutes}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              min="1"
              required
              placeholder="مثال: 30"
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold">الدرجة المطلوبة للنجاح (%) *</label>
            <input
              type="number"
              name="passing_score"
              value={form.passing_score}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              min="0"
              max="100"
              required
              placeholder="مثال: 60"
            />
          </div>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="is_active"
              name="is_active"
              checked={form.is_active}
              onChange={handleChange}
              className="rounded"
            />
            <label htmlFor="is_active" className="font-semibold">نشط؟</label>
          </div>
          {error && <div className="text-red-500 text-sm mt-2">{error}</div>}
          <button type="submit" className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded mt-2 hover:bg-yellow-500 transition-colors" disabled={loading}>
            {loading ? 'جاري الحفظ...' : (quiz ? 'تحديث' : 'إضافة')}
          </button>
        </form>
      </div>
    </div>
  );
};

export default QuizForm; 