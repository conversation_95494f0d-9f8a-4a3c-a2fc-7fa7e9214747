import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';
const QUIZZES_URL = `${API_BASE_URL}/api/courses/quizzes/`;

export const getQuizzes = async () => {
  const res = await axios.get(QUIZZES_URL, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const addQuiz = async (data) => {
  const res = await axios.post(QUIZZES_URL, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const updateQuiz = async (id, data) => {
  const res = await axios.patch(`${QUIZZES_URL}${id}/`, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const deleteQuiz = async (id) => {
  const res = await axios.delete(`${QUIZZES_URL}${id}/`, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
}; 