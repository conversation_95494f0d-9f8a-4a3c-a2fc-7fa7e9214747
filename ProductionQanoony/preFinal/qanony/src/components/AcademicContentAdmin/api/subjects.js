import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';
const SUBJECTS_URL = `${API_BASE_URL}/api/courses/subjects/`;

export const getSubjects = async () => {
  const res = await axios.get(SUBJECTS_URL, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const addSubject = async (data) => {
  const res = await axios.post(SUBJECTS_URL, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const updateSubject = async (id, data) => {
  const res = await axios.patch(`${SUBJECTS_URL}${id}/`, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const deleteSubject = async (id) => {
  await axios.delete(`${SUBJECTS_URL}${id}/`, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
}; 