import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';
const ENDPOINT = `${API_BASE_URL}/api/courses/semesters/`;

export const getSemesters = async () => {
  const { data } = await axios.get(ENDPOINT, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
  return Array.isArray(data) ? data : (data.results || []);
};

export const addSemester = async (semester) => {
  const { data } = await axios.post(ENDPOINT, semester, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
  return data;
};

export const updateSemester = async (id, semester) => {
  const { data } = await axios.patch(`${ENDPOINT}${id}/`, semester, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
  return data;
};

export const deleteSemester = async (id) => {
  await axios.delete(`${ENDPOINT}${id}/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
}; 