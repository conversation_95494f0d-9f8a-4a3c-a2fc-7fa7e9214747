import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';
const QUIZ_ATTEMPTS_URL = `${API_BASE_URL}/api/courses/quiz-attempts/`;

export const getQuizAttempts = async (quizId = null) => {
  const url = quizId ? `${QUIZ_ATTEMPTS_URL}?quiz=${quizId}` : QUIZ_ATTEMPTS_URL;
  const res = await axios.get(url, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const addQuizAttempt = async (data) => {
  const res = await axios.post(QUIZ_ATTEMPTS_URL, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const updateQuizAttempt = async (id, data) => {
  const res = await axios.patch(`${QUIZ_ATTEMPTS_URL}${id}/`, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const deleteQuizAttempt = async (id) => {
  const res = await axios.delete(`${QUIZ_ATTEMPTS_URL}${id}/`, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
}; 