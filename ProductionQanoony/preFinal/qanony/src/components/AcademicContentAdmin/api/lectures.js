import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';
const LECTURES_URL = `${API_BASE_URL}/api/courses/lectures/`;

export const getLectures = async (subjectId = null) => {
  const params = subjectId ? { subject: subjectId } : {};
  const res = await axios.get(LECTURES_URL, {
    params,
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const addLecture = async (data, isMultipart = false) => {
  const headers = { 'Authorization': `Bearer ${localStorage.getItem('access')}` };
  if (isMultipart) headers['Content-Type'] = 'multipart/form-data';
  const res = await axios.post(LECTURES_URL, data, { headers });
  return res.data;
};

export const updateLecture = async (id, data, isMultipart = false) => {
  const headers = { 'Authorization': `Bearer ${localStorage.getItem('access')}` };
  if (isMultipart) headers['Content-Type'] = 'multipart/form-data';
  const res = await axios.patch(`${LECTURES_URL}${id}/`, data, { headers });
  return res.data;
};

export const deleteLecture = async (id) => {
  await axios.delete(`${LECTURES_URL}${id}/`, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
}; 