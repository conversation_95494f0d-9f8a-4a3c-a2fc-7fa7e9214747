import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';
const QUESTIONS_URL = `${API_BASE_URL}/api/courses/questions/`;

export const getQuestions = async (quizId = null) => {
  const url = quizId ? `${API_BASE_URL}/api/courses/quizzes/${quizId}/questions/` : QUESTIONS_URL;
  const res = await axios.get(url, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const addQuestion = async (data) => {
  const res = await axios.post(QUESTIONS_URL, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const updateQuestion = async (id, data) => {
  const res = await axios.patch(`${QUESTIONS_URL}${id}/`, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const deleteQuestion = async (id) => {
  const res = await axios.delete(`${QUESTIONS_URL}${id}/`, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
}; 