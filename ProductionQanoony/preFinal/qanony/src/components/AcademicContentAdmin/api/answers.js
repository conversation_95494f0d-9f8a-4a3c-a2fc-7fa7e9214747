import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';
const ANSWERS_URL = `${API_BASE_URL}/api/courses/answers/`;

export const getAnswers = async (questionId = null) => {
  const url = questionId ? `${ANSWERS_URL}?question=${questionId}` : ANSWERS_URL;
  const res = await axios.get(url, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const addAnswer = async (data) => {
  const res = await axios.post(ANSWERS_URL, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const updateAnswer = async (id, data) => {
  const res = await axios.patch(`${ANSWERS_URL}${id}/`, data, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
};

export const deleteAnswer = async (id) => {
  const res = await axios.delete(`${ANSWERS_URL}${id}/`, {
    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
  });
  return res.data;
}; 