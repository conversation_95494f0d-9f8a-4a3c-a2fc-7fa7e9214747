import React, { useEffect, useState } from 'react';
import { getSubjects, deleteSubject } from './api/subjects';
import SubjectForm from './SubjectForm';
import { ensureArray, processApiResponse, handleApiError } from '../../utils/arrayHelpers';

const SubjectTable = () => {
  const [subjects, setSubjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [editSubject, setEditSubject] = useState(null);

  const fetchSubjects = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getSubjects();
      console.log('Subjects API Response:', response);
      const processedData = processApiResponse(response, 'Subjects API', 'results');
      setSubjects(processedData);
    } catch (err) {
      console.error('Subjects API Error:', err);
      if (err.response?.status === 403) {
        setError('يجب توثيق البريد الإلكتروني أو وجود اشتراك نشط للوصول إلى المواد');
      } else {
        const errorData = handleApiError(err, 'Subjects API', setError);
      }
      setSubjects([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubjects();
  }, []);

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المادة؟')) {
      await deleteSubject(id);
      fetchSubjects();
    }
  };

  const handleAdd = () => {
    setEditSubject(null);
    setShowForm(true);
  };

  const handleEdit = (subject) => {
    setEditSubject(subject);
    setShowForm(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-yellow-600">جاري التحميل...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center p-4">
        {error}
        <button onClick={fetchSubjects} className="ml-2 text-yellow-600 hover:underline">
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-yellow-900">إدارة المواد</h2>
        <button 
          className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded-lg hover:bg-yellow-500 transition-colors font-semibold" 
          onClick={handleAdd}
        >
          إضافة مادة جديدة
        </button>
      </div>

      {!Array.isArray(subjects) ? (
        <div className="text-center py-8 text-red-500">
          خطأ في تحميل البيانات - يرجى إعادة تحميل الصفحة
        </div>
      ) : subjects.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          لا توجد مواد حتى الآن
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-yellow-50">
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">ID</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">اسم المادة (عربي)</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">اسم المادة (إنجليزي)</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الكود</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الوصف</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الفصل الدراسي</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">السنة الأكاديمية</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الحالة</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {subjects.map((subj) => (
                <tr key={subj.id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-4 py-2">{subj.id}</td>
                  <td className="border border-gray-300 px-4 py-2">{subj.name_ar}</td>
                  <td className="border border-gray-300 px-4 py-2">{subj.name_en}</td>
                  <td className="border border-gray-300 px-4 py-2">{subj.code}</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="max-w-xs truncate" title={subj.description_ar}>
                      {subj.description_ar}
                    </div>
                  </td>
                  <td className="border border-gray-300 px-4 py-2">{subj.semester}</td>
                  <td className="border border-gray-300 px-4 py-2">{subj.academic_year}</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <span className={`px-2 py-1 rounded text-sm ${
                      subj.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {subj.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(subj)}
                        className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors"
                      >
                        تعديل
                      </button>
                      <button
                        onClick={() => handleDelete(subj.id)}
                        className="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 transition-colors"
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {showForm && (
        <SubjectForm onClose={() => setShowForm(false)} onSaved={fetchSubjects} subject={editSubject} />
      )}
    </div>
  );
};

export default SubjectTable; 