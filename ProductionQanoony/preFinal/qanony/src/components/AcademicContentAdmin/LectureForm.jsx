import React, { useState, useEffect } from 'react';
import { addLecture, updateLecture } from './api/lectures';
import { getSubjects } from './api/subjects';
import { processYouTubeInput } from '../../utils/youtubeUtils';

const defaultState = {
  subject: '',
  title_ar: '',
  youtube_video_id: '',
  duration_minutes: '',
  order: '',
  is_published: false,
  pdf_summary: null,
};

const LectureForm = ({ onClose, onSaved, lecture }) => {
  const [form, setForm] = useState(defaultState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [subjects, setSubjects] = useState([]);
  const [subjectsLoading, setSubjectsLoading] = useState(true);
  const [youtubeInput, setYoutubeInput] = useState(''); // للعرض فقط
  const [youtubeValidation, setYoutubeValidation] = useState({ isValid: false, error: null });

  useEffect(() => {
    setSubjectsLoading(true);
    getSubjects().then(setSubjects).catch(() => setSubjects([])).finally(() => setSubjectsLoading(false));
  }, []);

  useEffect(() => {
    if (lecture) {
      setForm({
        subject: lecture.subject || '',
        title_ar: lecture.title_ar || '',
        youtube_video_id: lecture.youtube_video_id || '',
        duration_minutes: lecture.duration_minutes || '',
        order: lecture.order || '',
        is_published: !!lecture.is_published,
        pdf_summary: lecture.pdf_summary || null,
      });
      setYoutubeInput(lecture.youtube_video_id || '');
      setYoutubeValidation({ isValid: true, error: null });
    } else {
      setForm(defaultState);
      setYoutubeInput('');
      setYoutubeValidation({ isValid: false, error: null });
    }
  }, [lecture]);

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    
    if (name === 'youtube_video_id') {
      setYoutubeInput(value);
      const validation = processYouTubeInput(value);
      setYoutubeValidation(validation);
      
      setForm((prev) => ({
        ...prev,
        youtube_video_id: validation.videoId || '',
      }));
    } else if (name === 'pdf_summary') {
      // معالجة ملف PDF
      const file = files[0];
      if (file) {
        // التحقق من نوع الملف
        if (!file.type.includes('pdf')) {
          setError('يجب أن يكون الملف بصيغة PDF');
          return;
        }
        // التحقق من حجم الملف (أقل من 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setError('حجم الملف يجب أن يكون أقل من 10MB');
          return;
        }
        setForm((prev) => ({
          ...prev,
          pdf_summary: file,
        }));
        setError(null);
      }
    } else {
      setForm((prev) => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value,
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!form.subject) {
      setError('يجب اختيار المادة');
      return;
    }
    if (!youtubeValidation.isValid) {
      setError(youtubeValidation.error || 'يجب إدخال معرف فيديو يوتيوب صحيح');
      return;
    }
    setLoading(true);
    setError(null);
    try {
      let payload;
      let isMultipart = !!form.pdf_summary;
      if (isMultipart) {
        payload = new FormData();
        payload.append('subject_id', Number(form.subject));
        payload.append('title_ar', form.title_ar);
        payload.append('youtube_video_id', form.youtube_video_id);
        payload.append('duration_minutes', Number(form.duration_minutes));
        payload.append('order', Number(form.order));
        payload.append('is_published', form.is_published);
        payload.append('pdf_summary', form.pdf_summary);
      } else {
        payload = {
          ...form,
          subject_id: Number(form.subject),
          duration_minutes: Number(form.duration_minutes),
          order: Number(form.order),
        };
        delete payload.subject;
      }
      if (lecture) {
        await updateLecture(lecture.id, payload, isMultipart);
      } else {
        await addLecture(payload, isMultipart);
      }
      onSaved();
      onClose();
    } catch (err) {
      if (err.response && err.response.data) {
        setError(
          typeof err.response.data === 'string'
            ? err.response.data
            : Object.entries(err.response.data)
                .map(([key, val]) => `${key}: ${val}`)
                .join(' | ')
        );
      } else {
        setError('حدث خطأ أثناء الحفظ. تأكد من صحة البيانات.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md relative">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-yellow-900">
            {lecture ? 'تعديل درس' : 'إضافة درس جديد'}
          </h2>
          <button onClick={onClose} className="text-red-500 hover:text-red-700 text-xl font-bold bg-transparent border-none shadow-none p-0 m-0 focus:outline-none hover:bg-transparent active:bg-transparent">
            ✕
          </button>
        </div>
        <form onSubmit={handleSubmit} className="flex flex-col gap-3">
          <div>
            <label className="block mb-1 font-semibold">المادة</label>
            <select name="subject" value={form.subject} onChange={handleChange} className="w-full border rounded px-3 py-2" required>
              <option value="">اختر المادة</option>
              {subjectsLoading ? (
                <option disabled>جاري التحميل ...</option>
              ) : subjects.length === 0 ? (
                <option disabled>لا توجد مواد</option>
              ) : (
                subjects.map((s) => (
                  <option key={s.id} value={s.id}>{s.name_ar} ({s.name_en})</option>
                ))
              )}
            </select>
          </div>
          <div>
            <label className="block mb-1 font-semibold">عنوان الدرس (عربي)</label>
            <input type="text" name="title_ar" value={form.title_ar} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block mb-1 font-semibold">رابط فيديو يوتيوب</label>
            <input 
              type="text" 
              name="youtube_video_id" 
              value={youtubeInput} 
              onChange={handleChange} 
              placeholder="https://www.youtube.com/watch?v=VIDEO_ID أو معرف الفيديو فقط"
              className={`w-full border rounded px-3 py-2 ${
                youtubeInput && !youtubeValidation.isValid ? 'border-red-500' : 
                youtubeValidation.isValid ? 'border-green-500' : ''
              }`}
              required 
            />
            {youtubeInput && (
              <div className="mt-1">
                {youtubeValidation.isValid ? (
                  <p className="text-sm text-green-600">
                    ✓ معرف صحيح: {form.youtube_video_id}
                  </p>
                ) : (
                  <p className="text-sm text-red-600">
                    ✗ {youtubeValidation.error}
                  </p>
                )}
              </div>
            )}
            <p className="text-xs text-gray-500 mt-1">
              يمكنك إدخال الرابط الكامل أو معرف الفيديو فقط (مثل: dQw4w9WgXcQ)
            </p>
          </div>
          <div>
            <label className="block mb-1 font-semibold">مدة الدرس (دقائق)</label>
            <input type="number" name="duration_minutes" value={form.duration_minutes} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block mb-1 font-semibold">الترتيب</label>
            <input type="number" name="order" value={form.order} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" name="is_published" checked={form.is_published} onChange={handleChange} id="is_published" />
            <label htmlFor="is_published" className="font-semibold">منشور؟</label>
          </div>
          <div>
            <label className="block mb-1 font-semibold">ملف ملخص PDF</label>
            <input 
              type="file" 
              name="pdf_summary" 
              accept="application/pdf"
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
            />
            {form.pdf_summary && typeof form.pdf_summary === 'object' && (
              <p className="text-xs text-green-600 mt-1">تم اختيار الملف: {form.pdf_summary.name} ({(form.pdf_summary.size / 1024 / 1024).toFixed(2)} MB)</p>
            )}
          </div>
          {error && <div className="text-red-500 text-sm mt-2">{error}</div>}
          <button type="submit" className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded mt-2 hover:bg-yellow-500 transition-colors" disabled={loading}>
            {loading ? 'جارٍ الحفظ...' : lecture ? 'تحديث' : 'إضافة'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default LectureForm; 