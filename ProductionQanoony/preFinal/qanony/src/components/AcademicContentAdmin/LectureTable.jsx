import React, { useEffect, useState } from 'react';
import { getLectures, deleteLecture } from './api/lectures';
import LectureForm from './LectureForm';
import { processApiResponse, handleApiError } from '../../utils/arrayHelpers';

const LectureTable = () => {
  const [lectures, setLectures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [editLecture, setEditLecture] = useState(null);

  const fetchLectures = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getLectures();
      console.log('Lectures API Response:', response);
      const processedData = processApiResponse(response, 'Lectures API', 'results');
      setLectures(processedData);
    } catch (err) {
      console.error('Lectures API Error:', err);
      const errorData = handleApiError(err, 'Lectures API', setError);
      setLectures(errorData);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLectures();
  }, []);

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الدرس؟')) {
      await deleteLecture(id);
      fetchLectures();
    }
  };

  const handleAdd = () => {
    setEditLecture(null);
    setShowForm(true);
  };

  const handleEdit = (lecture) => {
    setEditLecture(lecture);
    setShowForm(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-yellow-600">جاري التحميل...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center p-4">
        {error}
        <button onClick={fetchLectures} className="ml-2 text-yellow-600 hover:underline">
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-yellow-900">إدارة المحاضرات</h2>
        <button 
          className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded-lg hover:bg-yellow-500 transition-colors font-semibold" 
          onClick={handleAdd}
        >
          إضافة محاضرة جديدة
        </button>
      </div>

      {!Array.isArray(lectures) ? (
        <div className="text-center py-8 text-red-500">
          خطأ في تحميل البيانات - يرجى إعادة تحميل الصفحة
        </div>
      ) : lectures.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          لا توجد محاضرات حتى الآن
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-yellow-50">
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">ID</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">المادة</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">عنوان المحاضرة (عربي)</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">معرف فيديو يوتيوب</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">مدة المحاضرة (دقائق)</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الترتيب</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الحالة</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {lectures.map((lec) => (
                <tr key={lec.id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-4 py-2">{lec.id}</td>
                  <td className="border border-gray-300 px-4 py-2">
                    {lec.subject ? (lec.subject.name_ar || lec.subject.id) : lec.subject}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">{lec.title_ar}</td>
                  <td className="border border-gray-300 px-4 py-2">{lec.youtube_video_id}</td>
                  <td className="border border-gray-300 px-4 py-2">{lec.duration_minutes}</td>
                  <td className="border border-gray-300 px-4 py-2">{lec.order}</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <span className={`px-2 py-1 rounded text-sm ${
                      lec.is_published 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {lec.is_published ? 'منشور' : 'غير منشور'}
                    </span>
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(lec)}
                        className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors"
                      >
                        تعديل
                      </button>
                      <button
                        onClick={() => handleDelete(lec.id)}
                        className="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 transition-colors"
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {showForm && (
        <LectureForm onClose={() => setShowForm(false)} onSaved={fetchLectures} lecture={editLecture} />
      )}
    </div>
  );
};

export default LectureTable; 