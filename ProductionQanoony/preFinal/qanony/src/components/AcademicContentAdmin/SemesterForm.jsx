import React, { useState, useEffect } from 'react';
import { addSemester, updateSemester } from './api/semesters';
import { getAcademicYears } from './api/academicYears';

const defaultState = {
  title_ar: '',
  title_en: '',
  description_ar: '',
  academic_year: '',
  order: '',
  is_published: false,
};

const SemesterForm = ({ onClose, onSaved, semester }) => {
  const [form, setForm] = useState(defaultState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [years, setYears] = useState([]);
  const [yearsLoading, setYearsLoading] = useState(true);

  useEffect(() => {
    setYearsLoading(true);
    getAcademicYears().then(setYears).catch(() => setYears([])).finally(() => setYearsLoading(false));
  }, []);

  useEffect(() => {
    if (semester) {
      setForm({
        title_ar: semester.title_ar || '',
        title_en: semester.title_en || '',
        description_ar: semester.description_ar || '',
        academic_year: semester.academic_year || '',
        order: semester.order || '',
        is_published: !!semester.is_published,
      });
    } else {
      setForm(defaultState);
    }
  }, [semester]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!form.academic_year) {
      setError('يجب اختيار السنة الأكاديمية');
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const payload = {
        ...form,
        academic_year: Number(form.academic_year),
        order: Number(form.order),
      };
      if (semester) {
        await updateSemester(semester.id, payload);
      } else {
        await addSemester(payload);
      }
      onSaved();
      onClose();
    } catch (err) {
      if (err.response && err.response.data) {
        setError(
          typeof err.response.data === 'string'
            ? err.response.data
            : Object.entries(err.response.data)
                .map(([key, val]) => `${key}: ${val}`)
                .join(' | ')
        );
      } else {
        setError('حدث خطأ أثناء الحفظ. تأكد من صحة البيانات.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md relative">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-yellow-900">
            {semester ? 'تعديل فصل دراسي' : 'إضافة فصل دراسي جديد'}
          </h2>
          <button onClick={onClose} className="text-red-500 hover:text-red-700 text-xl font-bold bg-transparent border-none shadow-none p-0 m-0 focus:outline-none hover:bg-transparent active:bg-transparent">
            ✕
          </button>
        </div>
        <form onSubmit={handleSubmit} className="flex flex-col gap-3">
          <div>
            <label className="block mb-1 font-semibold">العنوان (عربي)</label>
            <input type="text" name="title_ar" value={form.title_ar} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block mb-1 font-semibold">العنوان (إنجليزي)</label>
            <input type="text" name="title_en" value={form.title_en} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block mb-1 font-semibold">الوصف</label>
            <textarea name="description_ar" value={form.description_ar} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block mb-1 font-semibold">السنة الأكاديمية</label>
            <select name="academic_year" value={form.academic_year} onChange={handleChange} className="w-full border rounded px-3 py-2" required>
              <option value="">اختر السنة الأكاديمية</option>
              {yearsLoading ? (
                <option disabled>جاري التحميل ...</option>
              ) : years.length === 0 ? (
                <option disabled>لا توجد سنوات أكاديمية</option>
              ) : (
                years.map((y) => (
                  <option key={y.id} value={y.id}>
                    {y.year_name_ar} ({y.year_name_en})
                  </option>
                ))
              )}
            </select>
          </div>
          <div>
            <label className="block mb-1 font-semibold">الترتيب</label>
            <input type="number" name="order" value={form.order} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" name="is_published" checked={form.is_published} onChange={handleChange} id="is_published" />
            <label htmlFor="is_published" className="font-semibold">منشور؟</label>
          </div>
          {error && <div className="text-red-500 text-sm mt-2">{error}</div>}
          <button type="submit" className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded mt-2 hover:bg-yellow-500 transition-colors" disabled={loading}>
            {loading ? 'جارٍ الحفظ...' : semester ? 'تحديث' : 'إضافة'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default SemesterForm; 