import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { FaEye, FaDownload, FaTimes } from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const QuizAttemptsTable = () => {
  const [attempts, setAttempts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [selectedAttempt, setSelectedAttempt] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const fetchAttempts = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(`${API_BASE_URL}/api/courses/quiz-attempts/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      setAttempts(Array.isArray(response.data) ? response.data : (response.data.results || []));
    } catch (err) {
      setError('تعذر جلب محاولات الاختبارات');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAttempts();
  }, []);

  // Toast notification function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  const handleViewDetails = (attempt) => {
    setSelectedAttempt(attempt);
    setShowDetailsModal(true);
  };

  const exportAttempts = () => {
    const csvContent = "data:text/csv;charset=utf-8," 
      + "الطالب,الاختبار,النتيجة,النقاط الكلية,تاريخ البدء,تاريخ الانتهاء\n"
      + attempts.map(attempt => 
          `${attempt.student?.user?.first_name || 'غير محدد'},${attempt.quiz?.title_ar || 'غير محدد'},${attempt.score || 0},${attempt.total_points || 0},${attempt.started_at ? new Date(attempt.started_at).toLocaleString('ar-EG') : 'غير محدد'},${attempt.completed_at ? new Date(attempt.completed_at).toLocaleString('ar-EG') : 'لم ينته'}`
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "quiz_attempts.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    showMessage('تم تصدير البيانات بنجاح');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-yellow-600">جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {/* Toast Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
          <span className="block sm:inline">{successMessage}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setSuccessMessage(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          <span className="block sm:inline">{error}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setError(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-yellow-900">محاولات الاختبارات</h2>
        <div className="flex gap-2">
          <button
            onClick={exportAttempts}
            className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors font-semibold flex items-center gap-2"
          >
            <FaDownload /> تصدير CSV
          </button>
          <button
            onClick={fetchAttempts}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors font-semibold"
          >
            🔄 تحديث
          </button>
        </div>
      </div>

      {attempts.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          لا توجد محاولات اختبارات حتى الآن
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-yellow-50">
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">ID</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الطالب</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الاختبار</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">النتيجة</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">النقاط الكلية</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">النسبة المئوية</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">تاريخ البدء</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">تاريخ الانتهاء</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الحالة</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {attempts.map((attempt) => {
                const percentage = attempt.total_points > 0 ? ((attempt.score / attempt.total_points) * 100).toFixed(1) : 0;
                const isPassed = percentage >= (attempt.quiz?.passing_score || 60);
                
                return (
                  <tr key={attempt.id} className="hover:bg-gray-50">
                    <td className="border border-gray-300 px-4 py-2">{attempt.id}</td>
                    <td className="border border-gray-300 px-4 py-2">
                      {attempt.student?.user?.first_name || 'غير محدد'} {attempt.student?.user?.last_name || ''}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      {attempt.quiz?.title_ar || 'غير محدد'}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">{attempt.score || 0}</td>
                    <td className="border border-gray-300 px-4 py-2">{attempt.total_points || 0}</td>
                    <td className="border border-gray-300 px-4 py-2">
                      <span className={`px-2 py-1 rounded text-sm ${
                        isPassed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {percentage}%
                      </span>
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      {attempt.started_at ? new Date(attempt.started_at).toLocaleString('ar-EG') : 'غير محدد'}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      {attempt.completed_at ? new Date(attempt.completed_at).toLocaleString('ar-EG') : 'لم ينته'}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      <span className={`px-2 py-1 rounded text-sm ${
                        attempt.completed_at 
                          ? (isPassed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800')
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {attempt.completed_at ? (isPassed ? 'نجح' : 'رسب') : 'جاري'}
                      </span>
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      <button
                        onClick={() => handleViewDetails(attempt)}
                        className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors flex items-center gap-1"
                      >
                        <FaEye /> عرض التفاصيل
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}

      {/* Details Modal */}
      {showDetailsModal && selectedAttempt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-96 overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">تفاصيل المحاولة</h3>
              <button
                onClick={() => setShowDetailsModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <FaTimes />
              </button>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <strong>الطالب:</strong> {selectedAttempt.student?.user?.first_name || 'غير محدد'}
                </div>
                <div>
                  <strong>الاختبار:</strong> {selectedAttempt.quiz?.title_ar || 'غير محدد'}
                </div>
                <div>
                  <strong>النتيجة:</strong> {selectedAttempt.score || 0} من {selectedAttempt.total_points || 0}
                </div>
                <div>
                  <strong>النسبة:</strong> {selectedAttempt.total_points > 0 ? ((selectedAttempt.score / selectedAttempt.total_points) * 100).toFixed(1) : 0}%
                </div>
              </div>
              {selectedAttempt.answers && (
                <div>
                  <strong>الإجابات:</strong>
                  <pre className="bg-gray-100 p-2 rounded mt-2 text-sm">
                    {JSON.stringify(selectedAttempt.answers, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuizAttemptsTable;
