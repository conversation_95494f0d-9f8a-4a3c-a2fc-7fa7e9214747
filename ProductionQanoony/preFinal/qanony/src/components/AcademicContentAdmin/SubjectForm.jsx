import React, { useState, useEffect } from 'react';
import { addSubject, updateSubject } from './api/subjects';
import { getAcademicYears } from './api/academicYears';
import { getSemesters } from './api/semesters';

const defaultState = {
  name_ar: '',
  name_en: '',
  code: '',
  description_ar: '',
  semester: '',
  academic_year: '',
  is_active: true,
};

const SubjectForm = ({ onClose, onSaved, subject }) => {
  const [form, setForm] = useState(defaultState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [years, setYears] = useState([]);
  const [semesters, setSemesters] = useState([]);
  const [yearsLoading, setYearsLoading] = useState(true);
  const [semestersLoading, setSemestersLoading] = useState(true);

  useEffect(() => {
    setYearsLoading(true);
    getAcademicYears().then(setYears).catch(() => setYears([])).finally(() => setYearsLoading(false));
    setSemestersLoading(true);
    getSemesters().then(setSemesters).catch(() => setSemesters([])).finally(() => setSemestersLoading(false));
  }, []);

  useEffect(() => {
    if (subject) {
      setForm({
        name_ar: subject.name_ar || '',
        name_en: subject.name_en || '',
        code: subject.code || '',
        description_ar: subject.description_ar || '',
        semester: subject.semester || '',
        academic_year: subject.academic_year || '',
        is_active: subject.is_active !== undefined ? subject.is_active : true,
      });
    } else {
      setForm(defaultState);
    }
  }, [subject]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!form.academic_year) {
      setError('يجب اختيار السنة الأكاديمية');
      return;
    }
    if (!form.semester) {
      setError('يجب اختيار الفصل الدراسي');
      return;
    }
    setLoading(true);
    setError(null);
    try {
      if (subject) {
        await updateSubject(subject.id, form);
      } else {
        await addSubject(form);
      }
      onSaved();
      onClose();
    } catch (err) {
      setError('حدث خطأ أثناء الحفظ. تأكد من صحة البيانات.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md relative">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-yellow-900">
            {subject ? 'تعديل مادة' : 'إضافة مادة جديدة'}
          </h2>
          <button onClick={onClose} className="text-red-500 hover:text-red-700 text-xl font-bold bg-transparent border-none shadow-none p-0 m-0 focus:outline-none hover:bg-transparent active:bg-transparent">
            ✕
          </button>
        </div>
        <form onSubmit={handleSubmit} className="flex flex-col gap-3">
          <div>
            <label className="block mb-1 font-semibold">اسم المادة (عربي)</label>
            <input type="text" name="name_ar" value={form.name_ar} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block mb-1 font-semibold">اسم المادة (إنجليزي)</label>
            <input type="text" name="name_en" value={form.name_en} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block mb-1 font-semibold">كود المادة</label>
            <input type="text" name="code" value={form.code} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block mb-1 font-semibold">الوصف</label>
            <textarea name="description_ar" value={form.description_ar} onChange={handleChange} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block mb-1 font-semibold">السنة الأكاديمية</label>
            <select name="academic_year" value={form.academic_year} onChange={handleChange} className="w-full border rounded px-3 py-2" required>
              <option value="">اختر السنة الأكاديمية</option>
              {yearsLoading ? (
                <option disabled>جاري التحميل ...</option>
              ) : years.length === 0 ? (
                <option disabled>لا توجد سنوات أكاديمية</option>
              ) : (
                years.map((y) => (
                  <option key={y.id} value={y.id}>
                    {y.year_name_ar} ({y.year_name_en})
                  </option>
                ))
              )}
            </select>
          </div>
          <div>
            <label className="block mb-1 font-semibold">الفصل الدراسي</label>
            <select name="semester" value={form.semester} onChange={handleChange} className="w-full border rounded px-3 py-2" required>
              <option value="">اختر الفصل الدراسي</option>
              {semestersLoading ? (
                <option disabled>جاري التحميل ...</option>
              ) : semesters.length === 0 ? (
                <option disabled>لا توجد فصول دراسية</option>
              ) : (
                semesters.map((s) => (
                  <option key={s.id} value={s.id}>
                    {s.title_ar} ({s.title_en})
                  </option>
                ))
              )}
            </select>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" name="is_active" checked={form.is_active} onChange={handleChange} id="is_active" />
            <label htmlFor="is_active" className="font-semibold">نشطة؟</label>
          </div>
          {error && <div className="text-red-500 text-sm mt-2">{error}</div>}
          <button type="submit" className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded mt-2 hover:bg-yellow-500 transition-colors" disabled={loading}>
            {loading ? 'جارٍ الحفظ...' : subject ? 'تحديث' : 'إضافة'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default SubjectForm; 