import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { FaCheck, FaTimes } from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const AdminPrivateTutorSessions = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [actionLoading, setActionLoading] = useState({});

  const fetchSessions = async () => {
    setLoading(true);
    setError(null);
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/courses/private-tutor/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      setSessions(Array.isArray(data) ? data : (data.results || []));
    } catch (err) {
      setError('تعذر جلب الجلسات');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSessions();
  }, []);

  // Toast notification function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  const handleApprove = async (sessionId) => {
    if (!window.confirm('هل أنت متأكد من الموافقة على هذه الجلسة؟')) return;
    
    setActionLoading(prev => ({ ...prev, [sessionId]: true }));
    try {
      await axios.post(`${API_BASE_URL}/api/courses/private-tutor/${sessionId}/approve/`, {
        meeting_link: 'https://meet.google.com/xxx-yyyy-zzz' // رابط افتراضي
      }, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      await fetchSessions(); // إعادة جلب البيانات
      showMessage('تمت الموافقة على الجلسة بنجاح');
    } catch (err) {
      showMessage('فشل في الموافقة على الجلسة', true);
    } finally {
      setActionLoading(prev => ({ ...prev, [sessionId]: false }));
    }
  };

  const handleReject = async (sessionId) => {
    const reason = window.prompt('سبب الرفض:');
    if (!reason) return;
    
    setActionLoading(prev => ({ ...prev, [sessionId]: true }));
    try {
      await axios.post(`${API_BASE_URL}/api/courses/private-tutor/${sessionId}/reject/`, {
        reason: reason
      }, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      await fetchSessions(); // إعادة جلب البيانات
      showMessage('تم رفض الجلسة بنجاح');
    } catch (err) {
      showMessage('فشل في رفض الجلسة', true);
    } finally {
      setActionLoading(prev => ({ ...prev, [sessionId]: false }));
    }
  };

  return (
    <div className="w-full">
      {/* Toast Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
          <span className="block sm:inline">{successMessage}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setSuccessMessage(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          <span className="block sm:inline">{error}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setError(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}

      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold flex items-center gap-2">الجلسات الخاصة</h2>
      </div>
      {loading ? (
        <div className="flex items-center justify-center h-40 text-gray-400 text-lg">جاري التحميل ...</div>
      ) : error ? (
        <div className="flex items-center justify-center h-40 text-red-500 text-lg">{error}</div>
      ) : sessions.length === 0 ? (
        <div className="flex items-center justify-center h-40 text-gray-400 text-lg">لا توجد جلسات حالياً.</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full text-sm border border-gray-200 rounded-lg overflow-hidden">
            <thead className="bg-blue-50 text-blue-800">
              <tr>
                <th className="px-2 py-2 font-bold text-right">ID</th>
                <th className="px-2 py-2 font-bold text-right">الطالب</th>
                <th className="px-2 py-2 font-bold text-right">المدرس</th>
                <th className="px-2 py-2 font-bold text-right">المادة</th>
                <th className="px-2 py-2 font-bold text-right">الموعد</th>
                <th className="px-2 py-2 font-bold text-right">الحالة</th>
                <th className="px-2 py-2 font-bold text-right">إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {sessions.map((session, idx) => (
                <tr key={session.id} className={`transition ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-yellow-50`}>
                  <td className="px-2 py-2 whitespace-nowrap">{session.id}</td>
                  <td className="px-2 py-2 whitespace-nowrap">{session.student_name || '—'}</td>
                  <td className="px-2 py-2 whitespace-nowrap">{session.instructor_name || '—'}</td>
                  <td className="px-2 py-2 whitespace-nowrap">{session.subject_name || '—'}</td>
                  <td className="px-2 py-2 whitespace-nowrap">{session.scheduled_at ? new Date(session.scheduled_at).toLocaleString('ar-EG') : '—'}</td>
                  <td className="px-2 py-2 whitespace-nowrap">{session.status_display || session.status}</td>
                  <td className="px-2 py-2 whitespace-nowrap flex gap-2">
                    {session.status === 'pending' && (
                      <>
                        <button 
                          className="text-green-600 hover:text-green-800 disabled:opacity-50" 
                          title="موافقة"
                          onClick={() => handleApprove(session.id)}
                          disabled={actionLoading[session.id]}
                        >
                          <FaCheck />
                        </button>
                        <button 
                          className="text-red-600 hover:text-red-800 disabled:opacity-50" 
                          title="رفض"
                          onClick={() => handleReject(session.id)}
                          disabled={actionLoading[session.id]}
                        >
                          <FaTimes />
                        </button>
                      </>
                    )}
                    {session.status !== 'pending' && (
                      <span className="text-gray-500 text-xs">
                        {session.status === 'approved' ? 'تمت الموافقة' : 
                         session.status === 'rejected' ? 'مرفوض' : 
                         session.status === 'completed' ? 'مكتمل' : 'ملغي'}
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default AdminPrivateTutorSessions; 