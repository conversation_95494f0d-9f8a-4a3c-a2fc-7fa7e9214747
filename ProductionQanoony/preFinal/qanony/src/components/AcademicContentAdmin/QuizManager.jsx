import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaPlus, FaTrash, FaEye, FaSave, FaTimes, FaCheck, FaQuestion, FaList } from 'react-icons/fa';
import { ensureArray, processApiResponse, handleApiError } from '../../utils/arrayHelpers';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const QuizManager = () => {
  const [quizzes, setQuizzes] = useState([]);
  const [lectures, setLectures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  
  // Quiz form state
  const [quizForm, setQuizForm] = useState({
    title: '',
    description: '',
    lecture: '',
    time_limit: 30,
    passing_score: 60
  });
  
  // Question form state
  const [questionForm, setQuestionForm] = useState({
    text: '',
    question_type: 'multiple_choice',
    points: 1
  });
  
  // Answer form state
  const [answerForm, setAnswerForm] = useState({
    text: '',
    is_correct: false
  });
  
  // UI state
  const [showQuizForm, setShowQuizForm] = useState(false);
  const [showQuestionForm, setShowQuestionForm] = useState(false);
  const [editingQuiz, setEditingQuiz] = useState(null);
  const [selectedQuiz, setSelectedQuiz] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [answers, setAnswers] = useState([]);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [showAnswerForm, setShowAnswerForm] = useState(false);
  const [newAnswer, setNewAnswer] = useState({ text: '', is_correct: false });
  const [editingAnswerId, setEditingAnswerId] = useState(null);

  useEffect(() => {
    fetchQuizzes();
    fetchLectures();
  }, []);

  // Toast notification function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  const fetchQuizzes = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/courses/quizzes/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      console.log('Quizzes API Response:', response.data);
      const processedData = processApiResponse(response.data, 'Quizzes API', 'results');
      setQuizzes(processedData);
    } catch (err) {
      console.error('Quizzes API Error:', err);
      const errorData = handleApiError(err, 'Quizzes API');
      setQuizzes(errorData);
      setError('فشل في جلب الاختبارات');
    } finally {
      setLoading(false);
    }
  };

  const fetchLectures = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/courses/lectures/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      console.log('Lectures API Response:', response.data);
      const processedData = processApiResponse(response.data, 'Lectures API', 'results');
      setLectures(processedData);
    } catch (err) {
      console.error('Lectures API Error:', err);
      const errorData = handleApiError(err, 'Lectures API');
      setLectures(errorData);
      console.error('فشل في جلب الدروس:', err);
    }
  };

  const fetchQuestions = async (quizId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/courses/quizzes/${quizId}/questions/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      setQuestions(response.data);
    } catch (err) {
      console.error('فشل في جلب الأسئلة:', err);
    }
  };

  const fetchAnswers = async (questionId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/courses/answers/?question=${questionId}`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      setAnswers(response.data);
    } catch (err) {
      setAnswers([]);
    }
  };

  const handleQuizSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingQuiz) {
        await axios.put(`${API_BASE_URL}/api/courses/quizzes/${editingQuiz.id}/`, quizForm, {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
        });
      } else {
        await axios.post(`${API_BASE_URL}/api/courses/quizzes/`, quizForm, {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
        });
      }
      setShowQuizForm(false);
      setEditingQuiz(null);
      setQuizForm({ title: '', description: '', lecture: '', time_limit: 30, passing_score: 60 });
      fetchQuizzes();
      showMessage(editingQuiz ? 'تم تحديث الاختبار بنجاح' : 'تم إضافة الاختبار بنجاح');
    } catch (err) {
      showMessage('فشل في حفظ الاختبار', true);
    }
  };

  const handleQuestionSubmit = async (e) => {
    e.preventDefault();
    if (!selectedQuiz) return;
    try {
      const questionData = {
        quiz: selectedQuiz.id,
        quiz_id: selectedQuiz.id,
        question_text_ar: questionForm.text,
        question_type: questionForm.question_type,
        order: 1,
        points: questionForm.points || 1
      };

      const response = await axios.post(`${API_BASE_URL}/api/courses/questions/`, questionData, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });

      const newQuestion = response.data;
      setQuestions([...questions, newQuestion]);
      setQuestionForm({ text: '', question_type: 'multiple_choice', points: 1 });
      setShowQuestionForm(false);
      setCurrentQuestion(newQuestion);

      // Clear answers for new question
      setAnswers([]);
      showMessage('تم إضافة السؤال بنجاح');
    } catch (err) {
      showMessage('فشل في حفظ السؤال', true);
    }
  };

  const handleAnswerSubmit = async (e) => {
    e.preventDefault();
    if (!currentQuestion) return;
    
    try {
      const answerData = {
        ...answerForm,
        question: currentQuestion.id
      };
      
      const response = await axios.post(`${API_BASE_URL}/api/courses/answers/`, answerData, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      
      setAnswers([...answers, response.data]);
      setAnswerForm({ text: '', is_correct: false });
      showMessage('تم إضافة الإجابة بنجاح');
    } catch (err) {
      showMessage('فشل في حفظ الإجابة', true);
    }
  };

  const handleQuizSelect = async (quiz) => {
    setSelectedQuiz(quiz);
    await fetchQuestions(quiz.id);
    setCurrentQuestion(null);
    setAnswers([]);
  };

  const handleQuestionSelect = async (question) => {
    setCurrentQuestion(question);
    setQuestionForm({
      text: question.question_text_ar || question.text || '',
      question_type: question.question_type || 'multiple_choice',
      points: question.points || 1
    });
    await fetchAnswers(question.id);
  };

  const deleteQuiz = async (quizId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الاختبار؟')) return;
    
    try {
      await axios.delete(`${API_BASE_URL}/api/courses/quizzes/${quizId}/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      fetchQuizzes();
      if (selectedQuiz?.id === quizId) {
        setSelectedQuiz(null);
        setQuestions([]);
        setCurrentQuestion(null);
        setAnswers([]);
      }
      showMessage('تم حذف الاختبار بنجاح');
    } catch (err) {
      showMessage('فشل في حذف الاختبار', true);
    }
  };

  const deleteQuestion = async (questionId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا السؤال؟')) return;
    
    try {
      await axios.delete(`${API_BASE_URL}/api/courses/questions/${questionId}/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      setQuestions(questions.filter(q => q.id !== questionId));
      if (currentQuestion?.id === questionId) {
        setCurrentQuestion(null);
        setAnswers([]);
      }
      showMessage('تم حذف السؤال بنجاح');
    } catch (err) {
      showMessage('فشل في حذف السؤال', true);
    }
  };

  const deleteAnswer = async (answerId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الإجابة؟')) return;
    
    try {
      await axios.delete(`${API_BASE_URL}/api/courses/answers/${answerId}/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
      });
      setAnswers(answers.filter(a => a.id !== answerId));
      showMessage('تم حذف الإجابة بنجاح');
    } catch (err) {
      showMessage('فشل في حذف الإجابة', true);
    }
  };

  if (loading) return <div className="text-center py-8">جاري التحميل...</div>;
  if (error) return <div className="text-center py-8 text-red-600">{error}</div>;

  return (
    <div className="space-y-6">
      {/* Toast Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{successMessage}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setSuccessMessage(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{error}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setError(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}

      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">إدارة الاختبارات</h2>
        <button
          onClick={() => setShowQuizForm(true)}
          className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded-lg hover:bg-yellow-500 flex items-center gap-2"
        >
          <FaPlus /> إضافة اختبار جديد
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quiz List */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">الاختبارات</h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {!Array.isArray(quizzes) ? (
              <div className="text-center py-4 text-red-500">
                خطأ في تحميل الاختبارات
              </div>
            ) : quizzes.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                لا توجد اختبارات
              </div>
            ) : (
              quizzes.map(quiz => (
              <div
                key={quiz.id}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedQuiz?.id === quiz.id
                    ? 'border-yellow-500 bg-yellow-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleQuizSelect(quiz)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-800">{quiz.title_ar || quiz.title}</h4>
                    <p className="text-sm text-gray-600">{quiz.description_ar || quiz.description}</p>
                    <p className="text-xs text-gray-500">الوقت: {quiz.time_limit_minutes || quiz.time_limit} دقيقة</p>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteQuiz(quiz.id);
                    }}
                    className="text-red-500 hover:text-red-700 ml-2"
                  >
                    <FaTrash size={14} />
                  </button>
                </div>
              </div>
              ))
            )}
          </div>
        </div>

        {/* Questions List */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">الأسئلة</h3>
            {selectedQuiz && (
              <button
                onClick={() => {
                  setQuestionForm({ text: '', question_type: 'multiple_choice', points: 1 });
                  setShowQuestionForm(true);
                }}
                className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded text-sm hover:bg-yellow-500 flex items-center gap-1"
              >
                <FaPlus size={12} /> إضافة سؤال
              </button>
            )}
          </div>
          {selectedQuiz ? (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {!Array.isArray(questions) ? (
                <div className="text-center py-4 text-red-500">
                  خطأ في تحميل الأسئلة
                </div>
              ) : questions.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  لا توجد أسئلة لهذا الاختبار
                </div>
              ) : (
                questions.map(question => (
                <div
                  key={question.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    currentQuestion?.id === question.id
                      ? 'border-yellow-500 bg-yellow-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleQuestionSelect(question)}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-800 text-sm">{question.question_text_ar || question.text}</h4>
                      <p className="text-xs text-gray-500">النوع: {question.question_type}</p>
                      <p className="text-xs text-gray-500">النقاط: {question.points}</p>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteQuestion(question.id);
                      }}
                      className="text-red-500 hover:text-red-700 ml-2"
                    >
                      <FaTrash size={12} />
                    </button>
                  </div>
                </div>
                ))
              )}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">اختر اختباراً لعرض أسئلته</p>
          )}
        </div>

        {/* Answers List */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">الإجابات</h3>
            {currentQuestion && (
              <button
                onClick={() => setShowAnswerForm(true)}
                className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded text-sm hover:bg-yellow-500 flex items-center gap-1"
              >
                <FaPlus size={12} /> إضافة إجابة
              </button>
            )}
          </div>
          {currentQuestion ? (
            !Array.isArray(answers) ? (
              <div className="text-center py-4 text-red-500">
                خطأ في تحميل الإجابات
              </div>
            ) : answers.length === 0 ? (
              <p className="text-gray-500 text-center py-8">لا توجد إجابات بعد</p>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {answers.map(answer => (
                  <div
                    key={answer.id}
                    className={`p-3 rounded-lg border ${answer.is_correct ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}
                    onClick={() => {
                      setNewAnswer({
                        text: answer.answer_text_ar || answer.text || '',
                        is_correct: answer.is_correct || false
                      });
                      setEditingAnswerId(answer.id);
                      setShowAnswerForm(true);
                    }}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <p className="text-sm text-gray-800">{answer.answer_text_ar}</p>
                        {answer.is_correct && (
                          <span className="text-xs text-green-600 font-semibold">إجابة صحيحة</span>
                        )}
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteAnswer(answer.id);
                        }}
                        className="text-red-500 hover:text-red-700 ml-2"
                      >
                        <FaTrash size={12} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )
          ) : (
            <p className="text-gray-500 text-center py-8">اختر سؤالاً لعرض إجاباته</p>
          )}
        </div>
      </div>

      {/* Quiz Form Modal */}
      {showQuizForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              {editingQuiz ? 'تعديل الاختبار' : 'إضافة اختبار جديد'}
            </h3>
            <form onSubmit={handleQuizSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">عنوان الاختبار</label>
                <input
                  type="text"
                  value={quizForm.title}
                  onChange={(e) => setQuizForm({...quizForm, title: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">وصف الاختبار</label>
                <textarea
                  value={quizForm.description}
                  onChange={(e) => setQuizForm({...quizForm, description: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400"
                  rows="3"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">الدرس</label>
                <select
                  value={quizForm.lecture}
                  onChange={(e) => setQuizForm({...quizForm, lecture: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400"
                  required
                >
                  <option value="">اختر الدرس</option>
                  {Array.isArray(lectures) ? lectures.map(lecture => (
                    <option key={lecture.id} value={lecture.id}>{lecture.title_ar || lecture.title}</option>
                  )) : (
                    <option disabled>خطأ في تحميل المحاضرات</option>
                  )}
                </select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الوقت المحدد (دقيقة)</label>
                  <input
                    type="number"
                    value={quizForm.time_limit}
                    onChange={(e) => setQuizForm({...quizForm, time_limit: parseInt(e.target.value)})}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400"
                    min="1"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الدرجة المطلوبة للنجاح</label>
                  <input
                    type="number"
                    value={quizForm.passing_score}
                    onChange={(e) => setQuizForm({...quizForm, passing_score: parseInt(e.target.value)})}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400"
                    min="0"
                    max="100"
                    required
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-400 text-yellow-900 py-2 rounded-lg hover:bg-yellow-500"
                >
                  {editingQuiz ? 'تحديث' : 'إضافة'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowQuizForm(false);
                    setEditingQuiz(null);
                    setQuizForm({ title: '', description: '', lecture: '', time_limit: 30, passing_score: 60 });
                  }}
                  className="flex-1 bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Question Form Modal */}
      {showQuestionForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">إضافة سؤال جديد</h3>
            <form onSubmit={handleQuestionSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">نص السؤال</label>
                <textarea
                  value={questionForm.text}
                  onChange={(e) => setQuestionForm({...questionForm, text: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400"
                  rows="3"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">نوع السؤال</label>
                <select
                  value={questionForm.question_type}
                  onChange={(e) => setQuestionForm({...questionForm, question_type: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400"
                >
                  <option value="multiple_choice">اختيار متعدد</option>
                  <option value="true_false">صح أو خطأ</option>
                  <option value="short_answer">إجابة قصيرة</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">النقاط</label>
                <input
                  type="number"
                  value={questionForm.points}
                  onChange={(e) => setQuestionForm({...questionForm, points: parseInt(e.target.value)})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400"
                  min="1"
                  required
                />
              </div>
              <div className="flex gap-2">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-400 text-yellow-900 py-2 rounded-lg hover:bg-yellow-500"
                >
                  إضافة
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowQuestionForm(false);
                    setQuestionForm({ text: '', question_type: 'multiple_choice', points: 1 });
                  }}
                  className="flex-1 bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Answer Form Modal */}
      {showAnswerForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">إضافة إجابة جديدة</h3>
            <form onSubmit={async (e) => {
              e.preventDefault();
              if (!currentQuestion) return;
              try {
                const answerData = {
                  answer_text_ar: newAnswer.text,
                  is_correct: newAnswer.is_correct,
                  order: answers.length + 1,
                  question: currentQuestion.id
                };
                if (editingAnswerId) {
                  await axios.patch(`${API_BASE_URL}/api/courses/answers/${editingAnswerId}/`, answerData, {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
                  });
                } else {
                  await axios.post(`${API_BASE_URL}/api/courses/answers/`, answerData, {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` }
                  });
                }
                setShowAnswerForm(false);
                setNewAnswer({ text: '', is_correct: false });
                setEditingAnswerId(null);
                fetchAnswers(currentQuestion.id);
                showMessage(editingAnswerId ? 'تم تحديث الإجابة بنجاح' : 'تم إضافة الإجابة بنجاح');
              } catch {
                showMessage('فشل في حفظ الإجابة', true);
              }
            }} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">نص الإجابة</label>
                <textarea
                  value={newAnswer.text}
                  onChange={e => setNewAnswer({ ...newAnswer, text: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400"
                  rows="2"
                  required
                />
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_correct"
                  checked={newAnswer.is_correct}
                  onChange={e => setNewAnswer({ ...newAnswer, is_correct: e.target.checked })}
                  className="mr-2"
                />
                <label htmlFor="is_correct" className="text-sm font-medium text-gray-700">
                  إجابة صحيحة
                </label>
              </div>
              <div className="flex gap-2">
                <button type="submit" className="flex-1 bg-yellow-400 text-yellow-900 py-2 rounded-lg hover:bg-yellow-500">إضافة</button>
                <button type="button" onClick={() => { setShowAnswerForm(false); setEditingAnswerId(null); }} className="flex-1 bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600">إلغاء</button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuizManager; 