import React, { useEffect, useState } from 'react';
import { getQuizzes, deleteQuiz } from './api/quizzes';
import QuizForm from './QuizForm';
import { ensureArray, processApiResponse, handleApiError } from '../../utils/arrayHelpers';

const QuizTable = () => {
  const [quizzes, setQuizzes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [editQuiz, setEditQuiz] = useState(null);

  const fetchQuizzes = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getQuizzes();
      console.log('Quizzes API Response:', response);
      const processedData = processApiResponse(response, 'Quizzes API', 'results');
      setQuizzes(processedData);
    } catch (err) {
      console.error('Quizzes API Error:', err);
      const errorData = handleApiError(err, 'Quizzes API', setError);
      setQuizzes(errorData);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQuizzes();
  }, []);

  const handleDelete = async (id) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الاختبار؟')) {
      return;
    }
    try {
      await deleteQuiz(id);
      setQuizzes(quizzes.filter(quiz => quiz.id !== id));
    } catch (err) {
      setError('تعذر حذف الاختبار');
    }
  };

  const handleEdit = (quiz) => {
    setEditQuiz(quiz);
    setShowForm(true);
  };

  const handleAdd = () => {
    setEditQuiz(null);
    setShowForm(true);
  };

  const handleSaved = () => {
    fetchQuizzes();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-yellow-600">جاري التحميل...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center p-4">
        {error}
        <button onClick={fetchQuizzes} className="ml-2 text-yellow-600 hover:underline">
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-yellow-900">إدارة الاختبارات</h2>
        <button
          onClick={handleAdd}
          className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded-lg hover:bg-yellow-500 transition-colors font-semibold"
        >
          إضافة اختبار جديد
        </button>
      </div>

      {!Array.isArray(quizzes) ? (
        <div className="text-center py-8 text-red-500">
          خطأ في تحميل البيانات - يرجى إعادة تحميل الصفحة
        </div>
      ) : quizzes.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          لا توجد اختبارات حتى الآن
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-yellow-50">
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">المادة</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">المحاضرة</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">العنوان (عربي)</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">التعليمات</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الوقت المحدد</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">درجة النجاح</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الحالة</th>
                <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {quizzes.map((quiz) => (
                <tr key={quiz.id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-4 py-2">
                    {quiz.lecture?.subject?.name_ar || 'غير محدد'}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    {quiz.lecture?.title_ar || 'غير محدد'}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    {quiz.title_ar}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="max-w-xs truncate" title={quiz.instructions_ar}>
                      {quiz.instructions_ar}
                    </div>
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    {quiz.time_limit_minutes ? `${quiz.time_limit_minutes} دقيقة` : 'غير محدد'}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    {quiz.passing_score ? `${quiz.passing_score}%` : 'غير محدد'}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    <span className={`px-2 py-1 rounded text-sm ${
                      quiz.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {quiz.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(quiz)}
                        className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors"
                      >
                        تعديل
                      </button>
                      <button
                        onClick={() => handleDelete(quiz.id)}
                        className="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 transition-colors"
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {showForm && (
        <QuizForm
          onClose={() => setShowForm(false)}
          onSaved={handleSaved}
          quiz={editQuiz}
        />
      )}
    </div>
  );
};

export default QuizTable; 