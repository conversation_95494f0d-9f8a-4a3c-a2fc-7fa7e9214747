import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useWebSocket } from '../../hooks/useWebSocket';
import {
  FaComments,
  FaUsers,
  FaPlus,
  FaCheck,
  FaThumbtack,
  FaLock,
  FaSearch,
  FaChartBar,
  FaExclamationTriangle
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const CommunicationManagement = () => {
  // State Management
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Data States
  const [stats, setStats] = useState({});
  const [chatRooms, setChatRooms] = useState([]);
  const [messages, setMessages] = useState([]);
  const [forumCategories, setForumCategories] = useState([]);
  const [forumTopics, setForumTopics] = useState([]);
  const [forumPosts, setForumPosts] = useState([]);

  // Filter States
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [roomTypeFilter, setRoomTypeFilter] = useState('');

  // Modal States
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');

  // WebSocket for real-time updates
  const {
    isConnected: wsConnected,
    lastMessage: wsLastMessage,
  } = useWebSocket('ws/communication/', {
    onMessage: (data) => {
      console.log('Communication WebSocket message:', data);

      // Handle different types of real-time updates
      switch (data.type) {
        case 'new_message':
          // Add new message to the list
          setMessages(prev => {
            const exists = prev.some(msg => msg.id === data.message.id);
            if (exists) return prev;
            return [...prev, data.message];
          });

          // Update stats
          setStats(prev => ({
            ...prev,
            total_messages: (prev.total_messages || 0) + 1,
            recent_messages: (prev.recent_messages || 0) + 1
          }));
          break;

        case 'new_room':
          // Add new chat room
          setChatRooms(prev => {
            const exists = prev.some(room => room.id === data.room.id);
            if (exists) return prev;
            return [...prev, data.room];
          });

          // Update stats
          setStats(prev => ({
            ...prev,
            total_rooms: (prev.total_rooms || 0) + 1
          }));
          break;

        case 'room_updated':
          // Update existing room
          setChatRooms(prev =>
            prev.map(room =>
              room.id === data.room.id ? { ...room, ...data.room } : room
            )
          );
          break;

        case 'stats_update':
          // Update statistics
          setStats(prev => ({ ...prev, ...data.stats }));
          break;

        default:
          console.log('Unknown WebSocket message type:', data.type);
      }
    },
    onOpen: () => {
      console.log('Communication WebSocket connected');
    },
    onClose: () => {
      console.log('Communication WebSocket disconnected');
    },
    onError: (error) => {
      console.error('Communication WebSocket error:', error);
    }
  });
  const [editingItem, setEditingItem] = useState(null);
  const [availableUsers, setAvailableUsers] = useState([]);
  const [selectedParticipants, setSelectedParticipants] = useState([]);

  // Toast notification function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  // Fetch data functions
  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      const response = await axios.get(`${API_BASE_URL}/api/communication/admin/stats/`, { headers });
      const data = response.data;

      setStats({
        totalChatRooms: data.basic_stats?.total_chat_rooms || 0,
        activeChatRooms: data.basic_stats?.active_chat_rooms || 0,
        totalMessages: data.basic_stats?.total_messages || 0,
        unreadMessages: data.basic_stats?.unread_messages || 0,
        totalForumTopics: data.basic_stats?.total_forum_topics || 0,
        pendingPosts: data.basic_stats?.pending_posts || 0,
        totalForumCategories: data.basic_stats?.total_forum_categories || 0,
        activeForumCategories: data.basic_stats?.active_forum_categories || 0,
        pinnedTopics: data.basic_stats?.pinned_topics || 0,
        lockedTopics: data.basic_stats?.locked_topics || 0,
        totalForumPosts: data.basic_stats?.total_forum_posts || 0,
        approvedPosts: data.basic_stats?.approved_posts || 0,
        recentActivity: data.recent_activity || {},
        distributions: data.distributions || {},
        topContent: data.top_content || {}
      });
    } catch (err) {
      console.error('Error fetching stats:', err);
      setStats({});
      showMessage('حدث خطأ أثناء تحميل الإحصائيات', true);
    }
  };

  const fetchChatRooms = async () => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      const response = await axios.get(`${API_BASE_URL}/api/communication/chatrooms/`, { headers });
      setChatRooms(response.data.results || response.data);
    } catch (err) {
      console.error('Error fetching chat rooms:', err);
      setChatRooms([]);
      showMessage('حدث خطأ أثناء تحميل غرف الدردشة', true);
    }
  };

  const fetchMessages = async () => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      const response = await axios.get(`${API_BASE_URL}/api/communication/messages/`, { headers });
      setMessages(response.data.results || response.data);
    } catch (err) {
      console.error('Error fetching messages:', err);
      setMessages([]);
      showMessage('حدث خطأ أثناء تحميل الرسائل', true);
    }
  };

  const fetchForumData = async () => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      // Fetch forum categories
      const categoriesResponse = await axios.get(`${API_BASE_URL}/api/communication/forum-categories/`, { headers });
      setForumCategories(categoriesResponse.data.results || categoriesResponse.data);

      // Fetch forum topics
      const topicsResponse = await axios.get(`${API_BASE_URL}/api/communication/forum-topics/`, { headers });
      setForumTopics(topicsResponse.data.results || topicsResponse.data);

      // Fetch forum posts
      const postsResponse = await axios.get(`${API_BASE_URL}/api/communication/forum-posts/`, { headers });
      setForumPosts(postsResponse.data.results || postsResponse.data);
    } catch (err) {
      console.error('Error fetching forum data:', err);
      setForumCategories([]);
      setForumTopics([]);
      setForumPosts([]);
      showMessage('حدث خطأ أثناء تحميل بيانات المنتدى', true);
    }
  };

  const fetchAvailableUsers = async () => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      const users = [];

      // Fetch instructors
      try {
        const instructorsResponse = await axios.get(`${API_BASE_URL}/api/auth/instructors/`, { headers });
        const instructors = instructorsResponse.data.results || instructorsResponse.data;
        instructors.forEach(instructor => {
          users.push({
            id: instructor.id,
            email: instructor.email,
            name: `${instructor.first_name} ${instructor.last_name}`,
            type: 'instructor'
          });
        });
      } catch (err) {
        console.log('Error fetching instructors:', err);
      }

      // Fetch students
      try {
        const studentsResponse = await axios.get(`${API_BASE_URL}/api/students/profiles/`, { headers });
        const students = studentsResponse.data.results || studentsResponse.data;
        students.forEach(student => {
          if (student.user) {
            users.push({
              id: student.user.id || student.user,
              email: student.user.email || `Student ${student.id}`,
              name: student.user.first_name ? `${student.user.first_name} ${student.user.last_name}` : `Student ${student.id}`,
              type: 'student'
            });
          }
        });
      } catch (err) {
        console.log('Error fetching students:', err);
      }

      setAvailableUsers(users);
    } catch (err) {
      console.error('Error fetching users:', err);
      setAvailableUsers([]);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchStats(),
        fetchChatRooms(),
        fetchMessages(),
        fetchForumData()
      ]);
    } catch (err) {
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Action functions
  const handleCreateChatRoom = async (formData) => {
    try {
      const token = localStorage.getItem('access');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      // Get current user ID
      let currentUserId = null;
      try {
        const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
        currentUserId = userInfo.id || userInfo.user_id;
      } catch (e) {
        console.error('Error parsing user info:', e);
      }

      // Include selected participants plus current user
      const participants = [...(formData.participants || [])];
      if (currentUserId && !participants.includes(currentUserId)) {
        participants.push(currentUserId);
      }

      const requestData = {
        name: formData.name,
        room_type: formData.room_type,
        participants: participants
      };

      console.log('Sending request data:', requestData);

      await axios.post(`${API_BASE_URL}/api/communication/chatrooms/`, requestData, { headers });
      showMessage('تم إنشاء غرفة الدردشة بنجاح');
      setShowModal(false);
      setSelectedParticipants([]);
      fetchChatRooms();
    } catch (err) {
      console.error('Error creating chat room:', err);
      console.error('Error response data:', err.response?.data);
      console.error('Error status:', err.response?.status);
      console.error('Error headers:', err.response?.headers);

      let errorMessage = 'حدث خطأ أثناء إنشاء غرفة الدردشة';

      if (err.response?.data) {
        if (typeof err.response.data === 'string') {
          errorMessage = err.response.data;
        } else if (err.response.data.detail) {
          errorMessage = err.response.data.detail;
        } else if (err.response.data.message) {
          errorMessage = err.response.data.message;
        } else {
          // Show validation errors if any
          const errors = [];
          Object.keys(err.response.data).forEach(key => {
            if (Array.isArray(err.response.data[key])) {
              errors.push(`${key}: ${err.response.data[key].join(', ')}`);
            } else {
              errors.push(`${key}: ${err.response.data[key]}`);
            }
          });
          if (errors.length > 0) {
            errorMessage = errors.join('\n');
          }
        }
      }

      showMessage(errorMessage, true);
    }
  };

  const handleApprovePost = async (postId) => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      await axios.post(`${API_BASE_URL}/api/communication/forum-posts/${postId}/approve/`, {}, { headers });
      showMessage('تمت الموافقة على المشاركة بنجاح');
      fetchForumData();
    } catch (err) {
      console.error('Error approving post:', err);
      showMessage('حدث خطأ أثناء الموافقة على المشاركة', true);
    }
  };

  const handlePinTopic = async (topicId) => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };
      
      await axios.post(`${API_BASE_URL}/api/communication/forum-topics/${topicId}/pin/`, {}, { headers });
      showMessage('تم تثبيت الموضوع بنجاح');
      fetchForumData();
    } catch (err) {
      console.error('Error pinning topic:', err);
      showMessage('حدث خطأ أثناء تثبيت الموضوع', true);
    }
  };

  const handleLockTopic = async (topicId) => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };
      
      await axios.post(`${API_BASE_URL}/api/communication/forum-topics/${topicId}/lock/`, {}, { headers });
      showMessage('تم قفل الموضوع بنجاح');
      fetchForumData();
    } catch (err) {
      console.error('Error locking topic:', err);
      showMessage('حدث خطأ أثناء قفل الموضوع', true);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Tabs configuration
  const tabs = [
    { key: 'overview', label: 'نظرة عامة', icon: <FaChartBar /> },
    { key: 'chatrooms', label: 'غرف الدردشة', icon: <FaComments /> },
    { key: 'messages', label: 'الرسائل', icon: <FaComments /> },
    { key: 'forums', label: 'المنتديات', icon: <FaUsers /> },
    { key: 'moderation', label: 'الإشراف', icon: <FaExclamationTriangle /> }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500"></div>
        <span className="mr-3 text-gray-600">جاري تحميل بيانات التواصل...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl sm:text-3xl font-bold text-yellow-900">إدارة التواصل</h1>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                wsConnected
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                <span className={`w-2 h-2 rounded-full mr-1 ${
                  wsConnected ? 'bg-green-400' : 'bg-red-400'
                }`}></span>
                {wsConnected ? 'متصل' : 'غير متصل'}
              </span>
            </div>
            <p className="text-sm sm:text-base text-gray-600 mt-1 sm:mt-2">إدارة المنتديات والدردشة والرسائل</p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => {
                setModalType('chatroom');
                setEditingItem(null);
                setSelectedParticipants([]);
                fetchAvailableUsers();
                setShowModal(true);
              }}
              className="bg-yellow-400 text-yellow-900 px-4 py-2 rounded-lg hover:bg-yellow-500 transition-colors font-semibold flex items-center gap-2"
            >
              <FaPlus size={16} />
              إضافة غرفة دردشة
            </button>
          </div>
        </div>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {successMessage}
        </div>
      )}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex overflow-x-auto px-4 sm:px-6" dir="ltr">
            {tabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`py-3 sm:py-4 px-2 sm:px-4 border-b-2 font-medium text-xs sm:text-sm flex items-center gap-1 sm:gap-2 whitespace-nowrap flex-shrink-0 ${
                  activeTab === tab.key
                    ? 'border-yellow-500 text-yellow-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="text-sm sm:text-base">{tab.icon}</span>
                <span className="hidden sm:inline">{tab.label}</span>
                <span className="sm:hidden text-xs">{tab.label.split(' ')[0]}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-3 sm:p-4 md:p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Stats Cards Row 1 */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-100 rounded-lg p-6 flex flex-col items-center shadow">
                  <div className="bg-blue-500 text-white rounded-full p-3 mb-2">
                    <FaComments size={24} />
                  </div>
                  <div className="text-2xl font-bold text-blue-800">{stats.totalChatRooms || 0}</div>
                  <div className="text-blue-700 mt-1">إجمالي غرف الدردشة</div>
                </div>

                <div className="bg-green-100 rounded-lg p-6 flex flex-col items-center shadow">
                  <div className="bg-green-500 text-white rounded-full p-3 mb-2">
                    <FaUsers size={24} />
                  </div>
                  <div className="text-2xl font-bold text-green-800">{stats.activeChatRooms || 0}</div>
                  <div className="text-green-700 mt-1">غرف نشطة</div>
                </div>

                <div className="bg-purple-100 rounded-lg p-6 flex flex-col items-center shadow">
                  <div className="bg-purple-500 text-white rounded-full p-3 mb-2">
                    <FaComments size={24} />
                  </div>
                  <div className="text-2xl font-bold text-purple-800">{stats.totalMessages || 0}</div>
                  <div className="text-purple-700 mt-1">إجمالي الرسائل</div>
                </div>

                <div className="bg-orange-100 rounded-lg p-6 flex flex-col items-center shadow">
                  <div className="bg-orange-500 text-white rounded-full p-3 mb-2">
                    <FaExclamationTriangle size={24} />
                  </div>
                  <div className="text-2xl font-bold text-orange-800">{stats.pendingPosts || 0}</div>
                  <div className="text-orange-700 mt-1">مشاركات معلقة</div>
                </div>
              </div>

              {/* Stats Cards Row 2 */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-indigo-100 rounded-lg p-6 flex flex-col items-center shadow">
                  <div className="bg-indigo-500 text-white rounded-full p-3 mb-2">
                    <FaUsers size={24} />
                  </div>
                  <div className="text-2xl font-bold text-indigo-800">{stats.totalForumTopics || 0}</div>
                  <div className="text-indigo-700 mt-1">مواضيع المنتدى</div>
                </div>

                <div className="bg-yellow-100 rounded-lg p-6 flex flex-col items-center shadow">
                  <div className="bg-yellow-500 text-white rounded-full p-3 mb-2">
                    <FaThumbtack size={24} />
                  </div>
                  <div className="text-2xl font-bold text-yellow-800">{stats.pinnedTopics || 0}</div>
                  <div className="text-yellow-700 mt-1">مواضيع مثبتة</div>
                </div>

                <div className="bg-red-100 rounded-lg p-6 flex flex-col items-center shadow">
                  <div className="bg-red-500 text-white rounded-full p-3 mb-2">
                    <FaLock size={24} />
                  </div>
                  <div className="text-2xl font-bold text-red-800">{stats.lockedTopics || 0}</div>
                  <div className="text-red-700 mt-1">مواضيع مقفلة</div>
                </div>

                <div className="bg-teal-100 rounded-lg p-6 flex flex-col items-center shadow">
                  <div className="bg-teal-500 text-white rounded-full p-3 mb-2">
                    <FaCheck size={24} />
                  </div>
                  <div className="text-2xl font-bold text-teal-800">{stats.approvedPosts || 0}</div>
                  <div className="text-teal-700 mt-1">مشاركات موافق عليها</div>
                </div>
              </div>

              {/* Recent Activity */}
              {stats.recentActivity && (
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">النشاط الأخير (آخر 7 أيام)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-white rounded-lg p-4 border">
                      <div className="flex items-center gap-3">
                        <FaComments className="text-blue-500" size={20} />
                        <div>
                          <div className="text-lg font-semibold text-gray-800">
                            {stats.recentActivity.recent_messages || 0}
                          </div>
                          <div className="text-sm text-gray-600">رسائل جديدة</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg p-4 border">
                      <div className="flex items-center gap-3">
                        <FaUsers className="text-green-500" size={20} />
                        <div>
                          <div className="text-lg font-semibold text-gray-800">
                            {stats.recentActivity.recent_topics || 0}
                          </div>
                          <div className="text-sm text-gray-600">مواضيع جديدة</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg p-4 border">
                      <div className="flex items-center gap-3">
                        <FaCheck className="text-purple-500" size={20} />
                        <div>
                          <div className="text-lg font-semibold text-gray-800">
                            {stats.recentActivity.recent_posts || 0}
                          </div>
                          <div className="text-sm text-gray-600">مشاركات جديدة</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'chatrooms' && (
            <div className="space-y-4">
              {/* Search and Filter */}
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="البحث في غرف الدردشة..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-sm sm:text-base"
                    />
                  </div>
                </div>
                <select
                  value={roomTypeFilter}
                  onChange={(e) => setRoomTypeFilter(e.target.value)}
                  className="px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-sm sm:text-base"
                >
                  <option value="">جميع الأنواع</option>
                  <option value="private">خاص</option>
                  <option value="group">مجموعة</option>
                  <option value="instructor_student">مدرس-طالب</option>
                </select>
              </div>

              {/* Chat Rooms Table */}
              <div className="bg-white rounded-lg border overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        اسم الغرفة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        النوع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المشاركون
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الإنشاء
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {chatRooms.map((room) => (
                      <tr key={room.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {room.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            room.room_type === 'private' ? 'bg-blue-100 text-blue-800' :
                            room.room_type === 'group' ? 'bg-green-100 text-green-800' :
                            'bg-purple-100 text-purple-800'
                          }`}>
                            {room.room_type === 'private' ? 'خاص' :
                             room.room_type === 'group' ? 'مجموعة' : 'مدرس-طالب'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {room.participants?.length || 0} مشارك
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(room.created_at).toLocaleDateString('ar-EG')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            room.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {room.is_active ? 'نشط' : 'غير نشط'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <span className="text-gray-500 text-sm">لا توجد إجراءات متاحة</span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'messages' && (
            <div className="space-y-4">
              <div className="bg-white rounded-lg border overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المرسل
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الغرفة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المحتوى
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        النوع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        التاريخ
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {messages.map((message) => (
                      <tr key={message.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {message.sender?.email || 'غير محدد'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {message.room?.name || 'غير محدد'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                          {message.content || 'ملف مرفق'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            message.message_type === 'text' ? 'bg-blue-100 text-blue-800' :
                            message.message_type === 'file' ? 'bg-green-100 text-green-800' :
                            'bg-purple-100 text-purple-800'
                          }`}>
                            {message.message_type === 'text' ? 'نص' :
                             message.message_type === 'file' ? 'ملف' : 'صورة'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(message.timestamp).toLocaleDateString('ar-EG')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            message.is_read ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {message.is_read ? 'مقروء' : 'غير مقروء'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'forums' && (
            <div className="space-y-6">
              {/* Forum Categories */}
              <div className="bg-white rounded-lg border">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-800">فئات المنتدى</h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {forumCategories.map((category) => (
                      <div key={category.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 className="font-semibold text-gray-800">{category.name_ar}</h4>
                        <p className="text-sm text-gray-600 mt-2">{category.description_ar}</p>
                        <div className="flex justify-between items-center mt-4">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            category.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {category.is_active ? 'نشط' : 'غير نشط'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Forum Topics */}
              <div className="bg-white rounded-lg border">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-800">مواضيع المنتدى</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          العنوان
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الفئة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الكاتب
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          المشاهدات
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الحالة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {forumTopics.map((topic) => (
                        <tr key={topic.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 text-sm font-medium text-gray-900">
                            <div className="flex items-center gap-2">
                              {topic.is_pinned && <FaThumbtack className="text-yellow-500" size={12} />}
                              {topic.is_locked && <FaLock className="text-red-500" size={12} />}
                              {topic.title_ar}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {topic.category?.name_ar || 'غير محدد'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {topic.author?.email || 'غير محدد'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {topic.view_count}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div className="flex gap-1">
                              {topic.is_pinned && (
                                <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                                  مثبت
                                </span>
                              )}
                              {topic.is_locked && (
                                <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                                  مقفل
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex gap-2">
                              <button
                                onClick={() => handlePinTopic(topic.id)}
                                className="text-yellow-600 hover:text-yellow-900"
                                title="تثبيت"
                              >
                                <FaThumbtack size={14} />
                              </button>
                              <button
                                onClick={() => handleLockTopic(topic.id)}
                                className="text-red-600 hover:text-red-900"
                                title="قفل"
                              >
                                <FaLock size={14} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'moderation' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg border">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-800">المشاركات المعلقة</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الموضوع
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الكاتب
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          المحتوى
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          التاريخ
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {forumPosts.filter(post => !post.is_approved).map((post) => (
                        <tr key={post.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {post.topic?.title_ar || 'غير محدد'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {post.author?.email || 'غير محدد'}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                            {post.content_ar}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(post.created_at).toLocaleDateString('ar-EG')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex gap-2">
                              <button
                                onClick={() => handleApprovePost(post.id)}
                                className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs flex items-center gap-1"
                              >
                                <FaCheck size={12} />
                                موافقة
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Chat Room Modal */}
      {showModal && modalType === 'chatroom' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 sm:p-6 w-full max-w-md sm:max-w-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-base sm:text-lg font-semibold text-gray-800">إضافة غرفة دردشة جديدة</h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target);
              const data = {
                name: formData.get('name'),
                room_type: formData.get('room_type'),
                participants: selectedParticipants
              };
              handleCreateChatRoom(data);
            }}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اسم الغرفة
                  </label>
                  <input
                    type="text"
                    name="name"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-sm sm:text-base"
                    placeholder="أدخل اسم غرفة الدردشة"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الغرفة
                  </label>
                  <select
                    name="room_type"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-sm sm:text-base"
                  >
                    <option value="">اختر نوع الغرفة</option>
                    <option value="private">خاص</option>
                    <option value="group">مجموعة</option>
                    <option value="instructor_student">مدرس-طالب</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    المشاركون
                  </label>
                  <div className="max-h-24 sm:max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-2">
                    {availableUsers.length === 0 ? (
                      <p className="text-xs sm:text-sm text-gray-500 p-2">جاري تحميل المستخدمين...</p>
                    ) : (
                      availableUsers.map((user) => (
                        <label key={user.id} className="flex items-center space-x-2 p-1 hover:bg-gray-50 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={selectedParticipants.includes(user.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedParticipants([...selectedParticipants, user.id]);
                              } else {
                                setSelectedParticipants(selectedParticipants.filter(id => id !== user.id));
                              }
                            }}
                            className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                          />
                          <div className="flex flex-col flex-1 min-w-0">
                            <span className="text-xs sm:text-sm text-gray-700 truncate">{user.name}</span>
                            <span className="text-xs text-gray-500 truncate">{user.email} ({user.type})</span>
                          </div>
                        </label>
                      ))
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    اختر المشاركين في الغرفة (سيتم إضافتك تلقائياً)
                  </p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 mt-4 sm:mt-6">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-400 text-yellow-900 py-2 sm:py-3 px-4 rounded-lg hover:bg-yellow-500 transition-colors font-semibold text-sm sm:text-base"
                >
                  إنشاء الغرفة
                </button>
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 sm:py-3 px-4 rounded-lg hover:bg-gray-300 transition-colors font-semibold text-sm sm:text-base"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommunicationManagement;
