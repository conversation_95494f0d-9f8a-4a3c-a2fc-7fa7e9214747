import React, { useContext, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { AuthContext } from '../context/AuthContext';
import SEOHead from './SEO/SEOHead';
import './RegisterForm.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

// قائمة الجامعات المصرية التي بها كلية حقوق
// const UNIVERSITIES = [ ... ];
// السنوات الدراسية
// const ACADEMIC_YEARS = [ ... ];

const schema = yup.object().shape({
  // بيانات المستخدم الأساسية
  email: yup.string().email('صيغة بريد غير صالحة').required('مطلوب'),
  first_name: yup.string().required('مطلوب'),
  last_name: yup.string().required('مطلوب'),
  phone_number: yup.string().required('مطلوب'),
  password: yup.string().min(8, '8 أحرف على الأقل').required('مطلوب'),
  confirm_password: yup
    .string()
    .oneOf([yup.ref('password'), null], 'كلمتا المرور غير متطابقتين')
    .required('مطلوب'),
  
  // بيانات الطالب
  university_id: yup.string().required('مطلوب'),
  academic_year_id: yup.string().required('مطلوب'),
  
  // بيانات الاشتراك
  plan_id: yup.string().required('مطلوب'),
  payment_screenshot: yup.mixed().required('مطلوب إرفاق صورة إيصال الدفع'),
});

const RegisterForm = () => {
  const { register: registerUser, loading } = useContext(AuthContext);
  const [serverError, setServerError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [plans, setPlans] = useState([]);
  const [universities, setUniversities] = useState([]);
  const [academicYears, setAcademicYears] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm({ resolver: yupResolver(schema) });

  React.useEffect(() => {
    const fetchPlans = async () => {
      try {
        const res = await fetch(`${API_BASE_URL}/api/subscriptions/plans/`);
        if (res.ok) {
          const data = await res.json();
          setPlans(data);
        }
      } catch (e) {
        console.error('Failed to fetch plans', e);
      }
    };

    const fetchPaymentMethods = async () => {
      try {
        const res = await fetch(`${API_BASE_URL}/api/auth/payment-methods/`);
        if (res.ok) {
          const data = await res.json();
          setPaymentMethods(data);
        }
      } catch (e) {
        console.error('Failed to fetch payment methods', e);
      }
    };

    fetchPlans();
    fetchPaymentMethods();
  }, []);

  React.useEffect(() => {
    const fetchUniversities = async () => {
      try {
        const res = await fetch(`${API_BASE_URL}/api/universities/universities/active/`);
        if (res.ok) {
          const data = await res.json();
          setUniversities(data);
        }
      } catch (e) {
        console.error('Failed to fetch universities', e);
      }
    };
    fetchUniversities();
  }, []);

  // Fetch academic years (not tied to university)
  React.useEffect(() => {
    const fetchAcademicYears = async () => {
      try {
        const res = await fetch(`${API_BASE_URL}/api/universities/academic-years/`);
        if (res.ok) {
          const data = await res.json();
          setAcademicYears(Array.isArray(data) ? data : data.results || []);
        } else {
          setAcademicYears([]);
        }
      } catch (e) {
        setAcademicYears([]);
        console.error('Failed to fetch academic years', e);
      }
    };
    fetchAcademicYears();
  }, []);



  const onSubmit = async (data) => {
    const { confirm_password, ...payload } = data;

    if (payload.payment_screenshot && payload.payment_screenshot.length) {
      payload.payment_screenshot = payload.payment_screenshot[0];
    }

    const res = await registerUser(payload);
    if (res.success) {
      setSuccess(true);
    } else {
      setServerError(res.error?.detail || 'حدث خطأ ما');
    }
  };

  if (success) {
    return (
      <>
        <SEOHead
          title="تم إنشاء الحساب بنجاح - منصة قانوني التعليمية"
          description="تم إنشاء حسابك بنجاح في منصة قانوني التعليمية. تحقق من بريدك الإلكتروني لتفعيل الحساب والبدء في رحلتك التعليمية."
          keywords="تسجيل ناجح، تفعيل الحساب، منصة قانوني، تعليم قانوني"
          url="https://qanony.com/register"
          noIndex={true}
        />
        <div className="register-success">
          <h2>تم إنشاء الحساب بنجاح!</h2>
          <p>فضلاً تأكّد من بريدك الإلكتروني لتفعيل الحساب.</p>
        </div>
      </>
    );
  }

  return (
    <>
      <SEOHead
        title="إنشاء حساب جديد - انضم لمنصة قانوني التعليمية"
        description="سجل الآن في منصة قانوني التعليمية واحصل على أفضل تعليم قانوني. انضم لأكثر من 5000 طالب في كلية الحقوق واستفد من محاضرات تفاعلية ومكتبة قانونية شاملة."
        keywords="تسجيل جديد، إنشاء حساب، منصة قانوني، تعليم قانوني، كلية الحقوق، طلاب القانون، التسجيل في منصة تعليمية"
        url="https://qanony.com/register"
        type="website"
      />
    <form className="register-form" onSubmit={handleSubmit(onSubmit)}>
      <h2 className="title">إنشاء حساب جديد</h2>

      {/* بيانات المستخدم الأساسية */}
      <div className="form-section">
        <h3>البيانات الشخصية</h3>
        
        <div className="input-group">
          <label htmlFor="email">البريد الإلكتروني</label>
          <input id="email" type="email" {...register('email')} />
          {errors.email && <span className="error">{errors.email.message}</span>}
        </div>

        <div className="input-row">
          <div className="input-group">
            <label htmlFor="first_name">الاسم الأول</label>
            <input id="first_name" type="text" {...register('first_name')} />
            {errors.first_name && (
              <span className="error">{errors.first_name.message}</span>
            )}
          </div>

          <div className="input-group">
            <label htmlFor="last_name">اسم العائلة</label>
            <input id="last_name" type="text" {...register('last_name')} />
            {errors.last_name && (
              <span className="error">{errors.last_name.message}</span>
            )}
          </div>
        </div>

        <div className="input-group">
          <label htmlFor="phone_number">رقم الهاتف</label>
          <input id="phone_number" type="tel" {...register('phone_number')} />
          {errors.phone_number && (
            <span className="error">{errors.phone_number.message}</span>
          )}
        </div>

        <div className="input-row">
          <div className="input-group">
            <label htmlFor="password">كلمة المرور</label>
            <input id="password" type="password" {...register('password')} />
            {errors.password && (
              <span className="error">{errors.password.message}</span>
            )}
          </div>

          <div className="input-group">
            <label htmlFor="confirm_password">تأكيد كلمة المرور</label>
            <input id="confirm_password" type="password" {...register('confirm_password')} />
            {errors.confirm_password && (
              <span className="error">{errors.confirm_password.message}</span>
            )}
          </div>
        </div>
      </div>

      {/* بيانات الطالب */}
      <div className="form-section">
        <h3>البيانات الأكاديمية</h3>
        
        <div className="input-group">
          <label htmlFor="university_id">الجامعة</label>
          <select id="university_id" {...register('university_id')} defaultValue="">
            <option value="" disabled>اختر الجامعة</option>
            {universities.map((uni) => (
              <option key={uni.id} value={uni.id}>
                {uni.name_ar || uni.name_en}
              </option>
            ))}
          </select>
          {errors.university_id && <span className="error">{errors.university_id.message}</span>}
        </div>

        <div className="input-group">
          <label htmlFor="academic_year_id">السنة الدراسية</label>
          <select id="academic_year_id" {...register('academic_year_id')} value={watch('academic_year_id') || ''} disabled={!academicYears.length}>
            <option value="" disabled>اختر السنة الدراسية</option>
            {academicYears.map((year) => (
              <option key={year.id} value={year.id}>
                {year.year_name_ar || year.year_name_en}
              </option>
            ))}
          </select>
          {errors.academic_year_id && <span className="error">{errors.academic_year_id.message}</span>}
        </div>
      </div>

      {/* بيانات الاشتراك */}
      <div className="form-section">
        <h3>بيانات الاشتراك</h3>
        
        <div className="input-group">
          <label htmlFor="plan_id">خطة الاشتراك</label>
          <select id="plan_id" {...register('plan_id')} defaultValue="">
            <option value="" disabled>اختر الخطة</option>
            {plans.map((plan) => (
              <option key={plan.id} value={plan.id}>
                {plan.name} - {plan.price} جنيه / {plan.duration_days} يومًا
              </option>
            ))}
          </select>
          {errors.plan_id && <span className="error">{errors.plan_id.message}</span>}
        </div>

        {/* Payment instructions */}
        {paymentMethods && (
          <div className="payment-instructions" style={{
            background: '#fff',
            border: '1px solid #e0e0e0',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
            padding: '18px 20px',
            borderRadius: '10px',
            marginBottom: '20px',
            direction: 'ltr',
            textAlign: 'left',
            fontFamily: 'inherit',
          }}>
            <div style={{fontWeight: 'bold', fontSize: '1.08em', marginBottom: '10px', color: '#1a237e', textAlign: 'right'}}>
              {paymentMethods.payment_instructions || 'للاشتراك يرجى رفع صورة تحويل بأحد الوسائل الآتية:'}
            </div>
            <div style={{display: 'flex', flexDirection: 'column', gap: '10px'}}>
              {/* InstaPay */}
              {paymentMethods.instapay_enabled && paymentMethods.instapay_number_masked && (
                <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>
                  <span style={{
                    background:'#e3f2fd',
                    color:'#1565c0',
                    borderRadius:'16px',
                    padding:'4px 18px',
                    fontWeight:'bold',
                    minWidth:'120px',
                    display:'inline-block',
                    fontFamily:'Segoe UI, Arial, sans-serif',
                    fontSize:'1.04em',
                    letterSpacing:'1.5px',
                    textTransform:'uppercase',
                    boxShadow:'0 1px 4px rgba(21,101,192,0.07)'
                  }}>INSTAPAY</span>
                  <span style={{fontFamily:'monospace',fontSize:'1.07em',color:'#333'}}>
                    {paymentMethods.instapay_number_masked}
                  </span>
                </div>
              )}

              {/* Vodafone Cash */}
              {paymentMethods.vodafone_cash_enabled && paymentMethods.vodafone_cash_number_masked && (
                <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>
                  <span style={{
                    background:'#fff3e0',
                    color:'#ef6c00',
                    borderRadius:'16px',
                    padding:'4px 18px',
                    fontWeight:'bold',
                    minWidth:'120px',
                    display:'inline-block',
                    fontFamily:'Segoe UI, Arial, sans-serif',
                    fontSize:'1.04em',
                    letterSpacing:'1.5px',
                    textTransform:'uppercase',
                    boxShadow:'0 1px 4px rgba(239,108,0,0.07)'
                  }}>VODAFONE CASH</span>
                  <span style={{fontFamily:'monospace',fontSize:'1.07em',color:'#333'}}>
                    {paymentMethods.vodafone_cash_number_masked}
                  </span>
                </div>
              )}

              {/* Bank Account */}
              {paymentMethods.bank_enabled && paymentMethods.account_number_masked && (
                <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>
                  <span style={{
                    background:'#e8f5e9',
                    color:'#2e7d32',
                    borderRadius:'16px',
                    padding:'4px 18px',
                    fontWeight:'bold',
                    minWidth:'120px',
                    display:'inline-block',
                    fontFamily:'Segoe UI, Arial, sans-serif',
                    fontSize:'1.04em',
                    letterSpacing:'1.5px',
                    textTransform:'uppercase',
                    boxShadow:'0 1px 4px rgba(46,125,50,0.07)'
                  }}>BANK ACCOUNT</span>
                  <span style={{fontFamily:'monospace',fontSize:'1.07em',color:'#333'}}>
                    {paymentMethods.account_number_masked}
                  </span>
                </div>
              )}

              {/* Bank Details */}
              {paymentMethods.bank_enabled && paymentMethods.bank_name && (
                <div style={{marginTop: '8px', fontSize: '0.9em', color: '#666', textAlign: 'right'}}>
                  <div>البنك: {paymentMethods.bank_name}</div>
                  {paymentMethods.account_holder_name && (
                    <div>اسم صاحب الحساب: {paymentMethods.account_holder_name}</div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        <div className="input-group">
          <label htmlFor="payment_screenshot">صورة إيصال الدفع</label>
          <input id="payment_screenshot" type="file" accept="image/*" {...register('payment_screenshot')} />
          {errors.payment_screenshot && <span className="error">{errors.payment_screenshot.message}</span>}
        </div>
      </div>

      {serverError && <p className="server-error">{serverError}</p>}

      <button type="submit" className="submit-btn" disabled={loading}>
        {loading ? 'جاري الإرسال...' : 'تسجيل'}
      </button>
    </form>
    </>
  );
};

export default RegisterForm; 