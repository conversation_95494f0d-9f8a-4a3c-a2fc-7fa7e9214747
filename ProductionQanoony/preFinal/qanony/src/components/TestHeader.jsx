import React, { useState } from 'react';
import Header from './shared/Header';
import { useToast } from './UI/Toast';

/**
 * مكون لاختبار Header مع بيانات مختلفة
 */
const TestHeader = () => {
  const { showSuccess, showError, showInfo } = useToast();
  const [testMode, setTestMode] = useState('normal');

  const testHeaderWithDifferentData = (mode) => {
    setTestMode(mode);
    
    // محاكاة بيانات مختلفة للاختبار
    const mockNotifications = {
      normal: [
        { id: 1, subject_ar: 'إشعار تجريبي', content_ar: 'هذا إشعار للاختبار', read_at: null },
        { id: 2, subject_ar: 'إشعار مقروء', content_ar: 'هذا إشعار مقروء', read_at: '2024-01-01' }
      ],
      empty: [],
      invalid: null,
      mixed: [
        { id: 1, subject_ar: 'إشعار صحيح', content_ar: 'محتوى صحيح', read_at: null },
        null,
        undefined,
        { id: 2 }, // بدون محتوى
        'invalid string',
        { id: 3, subject_ar: 'إشعار آخر', content_ar: 'محتوى آخر', read_at: null }
      ]
    };

    // محاكاة تحديث البيانات في localStorage للاختبار
    const originalFetch = window.fetch;
    window.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockNotifications[mode])
      })
    );

    showInfo(`تم تغيير وضع الاختبار إلى: ${mode}`);

    // استعادة fetch الأصلي بعد ثانية
    setTimeout(() => {
      window.fetch = originalFetch;
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header للاختبار */}
      <Header />
      
      {/* أدوات التحكم في الاختبار */}
      <div className="container mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            🧪 اختبار Header Component
          </h2>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-700 mb-3">
              وضع الاختبار الحالي: <span className="text-blue-600">{testMode}</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <button
                onClick={() => testHeaderWithDifferentData('normal')}
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
              >
                بيانات عادية
              </button>
              
              <button
                onClick={() => testHeaderWithDifferentData('empty')}
                className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 transition-colors"
              >
                بيانات فارغة
              </button>
              
              <button
                onClick={() => testHeaderWithDifferentData('invalid')}
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
              >
                بيانات غير صحيحة
              </button>
              
              <button
                onClick={() => testHeaderWithDifferentData('mixed')}
                className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors"
              >
                بيانات مختلطة
              </button>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-800 mb-2">تعليمات الاختبار:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>بيانات عادية:</strong> إشعارات صحيحة مع بعضها مقروء وبعضها غير مقروء</li>
              <li>• <strong>بيانات فارغة:</strong> لا توجد إشعارات</li>
              <li>• <strong>بيانات غير صحيحة:</strong> null أو undefined</li>
              <li>• <strong>بيانات مختلطة:</strong> خليط من البيانات الصحيحة وغير الصحيحة</li>
            </ul>
          </div>

          <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-800 mb-2">ما يجب ملاحظته:</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Header يجب أن يعمل بدون أخطاء في جميع الحالات</li>
              <li>• عدد الإشعارات غير المقروءة يجب أن يظهر بشكل صحيح</li>
              <li>• قائمة الإشعارات يجب أن تعرض المحتوى المناسب</li>
              <li>• لا يجب أن تظهر أخطاء في الكونسول</li>
              <li>• Error Boundary يجب أن يحمي من الأخطاء الكبيرة</li>
            </ul>
          </div>

          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">اختبارات إضافية:</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
              <button
                onClick={() => {
                  showSuccess('اختبار Toast نجح!');
                }}
                className="bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600 transition-colors"
              >
                اختبار Toast
              </button>
              
              <button
                onClick={() => {
                  console.log('Current notifications state:', 
                    document.querySelector('[data-testid="notifications"]')?.textContent || 'Not found'
                  );
                  showInfo('تم طباعة حالة الإشعارات في الكونسول');
                }}
                className="bg-gray-500 text-white px-3 py-2 rounded text-sm hover:bg-gray-600 transition-colors"
              >
                فحص الحالة
              </button>
              
              <button
                onClick={() => {
                  // محاكاة خطأ في الشبكة
                  showError('محاكاة خطأ في تحميل الإشعارات', {
                    canRetry: true,
                    onRetry: () => showSuccess('تم إعادة المحاولة!')
                  });
                }}
                className="bg-red-500 text-white px-3 py-2 rounded text-sm hover:bg-red-600 transition-colors"
              >
                محاكاة خطأ
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestHeader;
