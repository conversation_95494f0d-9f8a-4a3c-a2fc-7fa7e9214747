import React, { useState, useEffect, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  InformationCircleIcon, 
  XCircleIcon,
  XMarkIcon 
} from '@heroicons/react/24/outline';

// إنشاء Context للـ Toast
const ToastContext = createContext();

// Hook لاستخدام Toast
export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// أنواع الـ Toast
export const TOAST_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// مكون Toast الفردي
const ToastItem = ({ toast, onRemove }) => {
  const { id, type, title, message, duration, canRetry, onRetry } = toast;

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        onRemove(id);
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [id, duration, onRemove]);

  const getIcon = () => {
    switch (type) {
      case TOAST_TYPES.SUCCESS:
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
      case TOAST_TYPES.ERROR:
        return <XCircleIcon className="w-6 h-6 text-red-500" />;
      case TOAST_TYPES.WARNING:
        return <ExclamationTriangleIcon className="w-6 h-6 text-yellow-500" />;
      case TOAST_TYPES.INFO:
        return <InformationCircleIcon className="w-6 h-6 text-blue-500" />;
      default:
        return <InformationCircleIcon className="w-6 h-6 text-gray-500" />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case TOAST_TYPES.SUCCESS:
        return 'bg-green-50 border-green-200';
      case TOAST_TYPES.ERROR:
        return 'bg-red-50 border-red-200';
      case TOAST_TYPES.WARNING:
        return 'bg-yellow-50 border-yellow-200';
      case TOAST_TYPES.INFO:
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.3 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, scale: 0.5, transition: { duration: 0.2 } }}
      className={`max-w-sm w-full ${getBackgroundColor()} border rounded-lg shadow-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden`}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="mr-3 w-0 flex-1 pt-0.5">
            {title && (
              <p className="text-sm font-medium text-gray-900 mb-1">
                {title}
              </p>
            )}
            <p className="text-sm text-gray-700">
              {message}
            </p>
            {canRetry && onRetry && (
              <div className="mt-3">
                <button
                  onClick={() => {
                    onRetry();
                    onRemove(id);
                  }}
                  className="text-sm font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline"
                >
                  إعادة المحاولة
                </button>
              </div>
            )}
          </div>
          <div className="mr-4 flex-shrink-0 flex">
            <button
              className="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              onClick={() => onRemove(id)}
            >
              <span className="sr-only">إغلاق</span>
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// مكون حاوي الـ Toasts
const ToastContainer = ({ toasts, removeToast }) => {
  return (
    <div className="fixed top-0 left-0 z-50 flex flex-col items-center w-full pointer-events-none">
      <div className="flex flex-col items-center space-y-4 px-4 py-6 sm:p-6">
        <AnimatePresence>
          {toasts.map((toast) => (
            <ToastItem
              key={toast.id}
              toast={toast}
              onRemove={removeToast}
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Provider للـ Toast
export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const addToast = (toast) => {
    const id = Date.now() + Math.random();
    const newToast = {
      id,
      duration: 5000, // 5 ثوان افتراضياً
      ...toast
    };
    setToasts(prev => [...prev, newToast]);
    return id;
  };

  const removeToast = (id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const removeAllToasts = () => {
    setToasts([]);
  };

  // دوال مساعدة لأنواع مختلفة من الـ Toasts
  const showSuccess = (message, options = {}) => {
    return addToast({
      type: TOAST_TYPES.SUCCESS,
      message,
      ...options
    });
  };

  const showError = (message, options = {}) => {
    return addToast({
      type: TOAST_TYPES.ERROR,
      message,
      duration: 7000, // أخطاء تبقى أطول
      ...options
    });
  };

  const showWarning = (message, options = {}) => {
    return addToast({
      type: TOAST_TYPES.WARNING,
      message,
      ...options
    });
  };

  const showInfo = (message, options = {}) => {
    return addToast({
      type: TOAST_TYPES.INFO,
      message,
      ...options
    });
  };

  const value = {
    toasts,
    addToast,
    removeToast,
    removeAllToasts,
    showSuccess,
    showError,
    showWarning,
    showInfo
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} removeToast={removeToast} />
    </ToastContext.Provider>
  );
};

// Hook مخصص لمعالجة أخطاء الـ API
export const useApiErrorHandler = () => {
  const { showError } = useToast();
  
  const handleError = (error, context = '') => {
    // استيراد دالة معالجة الأخطاء
    import('../../utils/errorHandler').then(({ handleApiError, requiresReauth }) => {
      const errorInfo = handleApiError(error, context);
      
      // إذا كان الخطأ يتطلب إعادة تسجيل الدخول
      if (requiresReauth(error)) {
        showError('انتهت صلاحية جلسة العمل. سيتم إعادة توجيهك لتسجيل الدخول.', {
          duration: 3000,
          onClose: () => {
            // إعادة توجيه لصفحة تسجيل الدخول
            window.location.href = '/login';
          }
        });
        return;
      }
      
      showError(errorInfo.fullMessage, {
        canRetry: errorInfo.type === 'network' || errorInfo.type === 'timeout',
        title: context ? `خطأ في ${context}` : 'حدث خطأ'
      });
    });
  };

  return { handleError };
};

export default ToastProvider;
