.app-container {
  max-width: 500px;
  margin: 40px auto;
  background: #fff;
  padding: 30px 40px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  direction: rtl;
}

.title {
  margin-bottom: 25px;
  text-align: center;
  color: var(--text);
}

.upload-form,
.status-card {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

label {
  font-weight: 600;
  color: var(--secondary-text);
}

input[type='file'] {
  border: 1px solid var(--secondary-bg);
  padding: 10px;
  border-radius: 6px;
  background: #fff;
}

button {
  padding: 12px;
  background: var(--primary);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

button:hover {
  background: #eab308;
}

.error {
  color: red;
}

.success {
  color: green;
}

.approved {
  color: green;
}
.pending {
  color: orange;
}
.rejected {
  color: red;
} 