import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import './PaymentMethodsManagement.css';
import {
  FaCreditCard,
  FaMobileAlt,
  FaUniversity,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaClock,
  FaFacebook,
  FaTwitter,
  FaInstagram,
  FaLinkedin,
  FaYoutube,
  FaEdit,
  FaSave,
  FaTimes,
  FaEye,
  FaEyeSlash,
  FaCheck,
  FaExclamationTriangle,
  FaInfoCircle,
  FaGlobe
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const PaymentMethodsManagement = () => {
  const { user, isAuthenticated } = useContext(AuthContext);
  const [activeTab, setActiveTab] = useState('overview');
  const [paymentData, setPaymentData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showSensitiveData, setShowSensitiveData] = useState(false);
  const [formData, setFormData] = useState({});
  const [saveLoading, setSaveLoading] = useState(false);

  // Fetch payment methods data
  const fetchPaymentData = async () => {
    setLoading(true);
    setError(null);
    try {
      // التحقق من وجود الـ token
      const token = localStorage.getItem('access');
      if (!token) {
        setError('لم يتم العثور على رمز المصادقة. يرجى تسجيل الدخول مرة أخرى.');
        return;
      }

      // استخدام axios مع الـ defaults headers (AuthContext يتعامل مع ده)
      const response = await axios.get(`${API_BASE_URL}/api/auth/payment-methods/admin/`);
      setPaymentData(response.data);
      setFormData(response.data);
    } catch (err) {
      console.error('Error fetching payment data:', err);

      if (err.response?.status === 401) {
        setError('انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.');
      } else if (err.response?.status === 403) {
        setError('ليس لديك صلاحية للوصول لهذه البيانات.');
      } else {
        const errorMessage = err.response?.data?.message || err.response?.data?.error || 'فشل في تحميل بيانات الدفع والتواصل';
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Debug: التحقق من الـ tokens والمستخدم
    const token = localStorage.getItem('access');
    console.log('PaymentMethods Debug:', {
      isAuthenticated,
      user: user,
      userIsStaff: user?.is_staff,
      hasToken: !!token,
      tokenLength: token?.length,
      axiosAuthHeader: axios.defaults.headers.common['Authorization']
    });

    // تحميل البيانات مباشرة - الـ API سيتعامل مع المصادقة والصلاحيات
    fetchPaymentData();
  }, [isAuthenticated, user]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  // Validate form data
  const validateForm = () => {
    const errors = [];

    // Validate phone numbers
    if (formData.instapay_enabled && !formData.instapay_number) {
      errors.push('رقم انستاباي مطلوب عند تفعيل الخدمة');
    }

    if (formData.vodafone_cash_enabled && !formData.vodafone_cash_number) {
      errors.push('رقم فودافون كاش مطلوب عند تفعيل الخدمة');
    }

    if (formData.bank_enabled && (!formData.bank_name || !formData.account_number)) {
      errors.push('اسم البنك ورقم الحساب مطلوبان عند تفعيل الحساب البنكي');
    }

    // Validate email format
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.push('صيغة البريد الإلكتروني غير صحيحة');
    }

    // Validate URLs
    const urlFields = ['facebook_url', 'twitter_url', 'instagram_url', 'linkedin_url', 'youtube_url'];
    urlFields.forEach(field => {
      if (formData[field] && !/^https?:\/\/.+/.test(formData[field])) {
        errors.push(`رابط ${field.replace('_url', '')} يجب أن يبدأ بـ http:// أو https://`);
      }
    });

    return errors;
  };

  // Save changes
  const handleSave = async () => {
    // Validate form first
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setError(validationErrors.join('. '));
      return;
    }

    setSaveLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // التحقق من وجود الـ token
      const token = localStorage.getItem('access');
      if (!token) {
        setError('لم يتم العثور على رمز المصادقة. يرجى تسجيل الدخول مرة أخرى.');
        return;
      }

      // استخدام axios مع الـ defaults headers
      const response = await axios.put(`${API_BASE_URL}/api/auth/payment-methods/admin/`, formData);
      
      if (response.data.success) {
        setPaymentData(response.data.data);
        setSuccessMessage('تم حفظ التغييرات بنجاح');
        setIsEditing(false);
        setTimeout(() => setSuccessMessage(null), 3000);
      }
    } catch (err) {
      console.error('Error saving payment data:', err);

      if (err.response?.status === 401) {
        setError('انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.');
      } else if (err.response?.status === 403) {
        setError('ليس لديك صلاحية لتعديل هذه البيانات.');
      } else {
        const errorMessage = err.response?.data?.message || err.response?.data?.error || 'فشل في حفظ التغييرات';
        setError(errorMessage);
      }
    } finally {
      setSaveLoading(false);
    }
  };

  // Cancel editing
  const handleCancel = () => {
    setFormData(paymentData);
    setIsEditing(false);
    setError(null);
  };

  // Render loading state
  if (loading) {
    return (
      <div className="payment-methods-management">
        <div className="loading-container">
          <div className="spinner"></div>
          <p>جاري تحميل بيانات الدفع والتواصل...</p>
        </div>
      </div>
    );
  }

  // Render error state for authentication/authorization
  if (error && (error.includes('تسجيل الدخول') || error.includes('صلاحية'))) {
    return (
      <div className="payment-methods-management">
        <div className="error-container">
          <div className="alert alert-error">
            <FaExclamationTriangle />
            {error}
          </div>
          <button
            className="btn btn-primary"
            onClick={() => window.location.href = '/login'}
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="payment-methods-management">
      {/* Header */}
      <div className="management-header">
        <div className="header-content">
          <h2>
            <FaCreditCard className="header-icon" />
            إدارة بيانات الدفع والتواصل
          </h2>
          <p>إدارة شاملة لجميع طرق الدفع ومعلومات التواصل</p>
        </div>
        
        <div className="header-actions">
          {!isEditing ? (
            <button 
              className="btn btn-primary"
              onClick={() => setIsEditing(true)}
            >
              <FaEdit /> تعديل البيانات
            </button>
          ) : (
            <div className="edit-actions">
              <button 
                className="btn btn-success"
                onClick={handleSave}
                disabled={saveLoading}
              >
                {saveLoading ? 'جاري الحفظ...' : <><FaSave /> حفظ</>}
              </button>
              <button 
                className="btn btn-secondary"
                onClick={handleCancel}
              >
                <FaTimes /> إلغاء
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div className="alert alert-error">
          <FaExclamationTriangle />
          {error}
        </div>
      )}
      
      {successMessage && (
        <div className="alert alert-success">
          <FaCheck />
          {successMessage}
        </div>
      )}

      {/* Tabs */}
      <div className="tabs-container">
        <div className="tabs">
          <button 
            className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <FaInfoCircle /> نظرة عامة
          </button>
          <button 
            className={`tab ${activeTab === 'payment' ? 'active' : ''}`}
            onClick={() => setActiveTab('payment')}
          >
            <FaCreditCard /> طرق الدفع
          </button>
          <button 
            className={`tab ${activeTab === 'contact' ? 'active' : ''}`}
            onClick={() => setActiveTab('contact')}
          >
            <FaPhone /> بيانات الاتصال
          </button>
          <button 
            className={`tab ${activeTab === 'social' ? 'active' : ''}`}
            onClick={() => setActiveTab('social')}
          >
            <FaGlobe /> وسائل التواصل
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'overview' && (
          <OverviewTab 
            paymentData={paymentData} 
            showSensitiveData={showSensitiveData}
            setShowSensitiveData={setShowSensitiveData}
          />
        )}
        
        {activeTab === 'payment' && (
          <PaymentMethodsTab 
            formData={formData}
            isEditing={isEditing}
            showSensitiveData={showSensitiveData}
            setShowSensitiveData={setShowSensitiveData}
            handleInputChange={handleInputChange}
          />
        )}
        
        {activeTab === 'contact' && (
          <ContactInfoTab 
            formData={formData}
            isEditing={isEditing}
            handleInputChange={handleInputChange}
          />
        )}
        
        {activeTab === 'social' && (
          <SocialMediaTab 
            formData={formData}
            isEditing={isEditing}
            handleInputChange={handleInputChange}
          />
        )}
      </div>
    </div>
  );
};

// Overview Tab Component
const OverviewTab = ({ paymentData, showSensitiveData, setShowSensitiveData }) => {
  if (!paymentData) return <div>لا توجد بيانات</div>;

  return (
    <div className="overview-tab">
      <div className="overview-grid">
        {/* Payment Methods Status */}
        <div className="overview-card">
          <div className="card-header">
            <FaCreditCard className="card-icon" />
            <h3>طرق الدفع</h3>
          </div>
          <div className="card-content">
            <div className="status-item">
              <span className="label">انستاباي:</span>
              <span className={`status ${paymentData.instapay_enabled ? 'active' : 'inactive'}`}>
                {paymentData.instapay_enabled ? 'مفعل' : 'غير مفعل'}
              </span>
            </div>
            <div className="status-item">
              <span className="label">فودافون كاش:</span>
              <span className={`status ${paymentData.vodafone_cash_enabled ? 'active' : 'inactive'}`}>
                {paymentData.vodafone_cash_enabled ? 'مفعل' : 'غير مفعل'}
              </span>
            </div>
            <div className="status-item">
              <span className="label">الحساب البنكي:</span>
              <span className={`status ${paymentData.bank_enabled ? 'active' : 'inactive'}`}>
                {paymentData.bank_enabled ? 'مفعل' : 'غير مفعل'}
              </span>
            </div>
          </div>
        </div>

        {/* Contact Info Status */}
        <div className="overview-card">
          <div className="card-header">
            <FaPhone className="card-icon" />
            <h3>بيانات الاتصال</h3>
          </div>
          <div className="card-content">
            <div className="status-item">
              <span className="label">الهاتف:</span>
              <span className={`status ${paymentData.phone_number ? 'active' : 'inactive'}`}>
                {paymentData.phone_number ? 'متوفر' : 'غير متوفر'}
              </span>
            </div>
            <div className="status-item">
              <span className="label">الإيميل:</span>
              <span className={`status ${paymentData.email ? 'active' : 'inactive'}`}>
                {paymentData.email ? 'متوفر' : 'غير متوفر'}
              </span>
            </div>
            <div className="status-item">
              <span className="label">العنوان:</span>
              <span className={`status ${paymentData.address ? 'active' : 'inactive'}`}>
                {paymentData.address ? 'متوفر' : 'غير متوفر'}
              </span>
            </div>
          </div>
        </div>

        {/* Social Media Status */}
        <div className="overview-card">
          <div className="card-header">
            <FaGlobe className="card-icon" />
            <h3>وسائل التواصل</h3>
          </div>
          <div className="card-content">
            <div className="social-grid">
              {[
                { key: 'facebook_url', icon: <FaFacebook />, name: 'فيسبوك' },
                { key: 'twitter_url', icon: <FaTwitter />, name: 'تويتر' },
                { key: 'instagram_url', icon: <FaInstagram />, name: 'إنستجرام' },
                { key: 'linkedin_url', icon: <FaLinkedin />, name: 'لينكد إن' },
                { key: 'youtube_url', icon: <FaYoutube />, name: 'يوتيوب' }
              ].map(social => (
                <div key={social.key} className="social-item">
                  {social.icon}
                  <span className={`status ${paymentData[social.key] ? 'active' : 'inactive'}`}>
                    {paymentData[social.key] ? 'متوفر' : 'غير متوفر'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sensitive Data Toggle */}
        <div className="overview-card">
          <div className="card-header">
            <FaEye className="card-icon" />
            <h3>عرض البيانات الحساسة</h3>
          </div>
          <div className="card-content">
            <button
              className={`toggle-btn ${showSensitiveData ? 'active' : ''}`}
              onClick={() => setShowSensitiveData(!showSensitiveData)}
            >
              {showSensitiveData ? <FaEyeSlash /> : <FaEye />}
              {showSensitiveData ? 'إخفاء الأرقام' : 'عرض الأرقام'}
            </button>
            <p className="toggle-note">
              {showSensitiveData
                ? 'الأرقام الحساسة مرئية حالياً'
                : 'الأرقام الحساسة مخفية للأمان'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Payment Methods Tab Component
const PaymentMethodsTab = ({ formData, isEditing, showSensitiveData, setShowSensitiveData, handleInputChange }) => {
  if (!formData) return <div>لا توجد بيانات</div>;

  const renderNumberField = (label, field, placeholder, icon) => (
    <div className="form-group">
      <label>
        {icon}
        {label}
      </label>
      {isEditing ? (
        <input
          type="text"
          value={formData[field] || ''}
          onChange={(e) => handleInputChange(field, e.target.value)}
          placeholder={placeholder}
          className="form-input"
        />
      ) : (
        <div className="display-value">
          {showSensitiveData
            ? (formData[field] || 'غير محدد')
            : (formData[field] ? formData[field].replace(/(\d{2})\d*(\d{2})/, '$1*******$2') : 'غير محدد')
          }
        </div>
      )}
    </div>
  );

  const renderToggleField = (label, field, icon) => (
    <div className="form-group">
      <label>
        {icon}
        {label}
      </label>
      {isEditing ? (
        <div className="toggle-switch">
          <input
            type="checkbox"
            id={field}
            checked={formData[field] || false}
            onChange={(e) => handleInputChange(field, e.target.checked)}
          />
          <label htmlFor={field} className="switch-label">
            <span className="switch-slider"></span>
          </label>
          <span className="switch-text">
            {formData[field] ? 'مفعل' : 'غير مفعل'}
          </span>
        </div>
      ) : (
        <div className={`status-badge ${formData[field] ? 'active' : 'inactive'}`}>
          {formData[field] ? 'مفعل' : 'غير مفعل'}
        </div>
      )}
    </div>
  );

  return (
    <div className="payment-methods-tab">
      {/* Sensitive Data Toggle */}
      {!isEditing && (
        <div className="sensitive-toggle">
          <button
            className={`toggle-btn ${showSensitiveData ? 'active' : ''}`}
            onClick={() => setShowSensitiveData(!showSensitiveData)}
          >
            {showSensitiveData ? <FaEyeSlash /> : <FaEye />}
            {showSensitiveData ? 'إخفاء الأرقام' : 'عرض الأرقام'}
          </button>
        </div>
      )}

      <div className="payment-sections">
        {/* InstaPay Section */}
        <div className="payment-section">
          <div className="section-header">
            <FaMobileAlt className="section-icon" />
            <h3>انستاباي (InstaPay)</h3>
          </div>
          <div className="section-content">
            {renderNumberField('رقم انستاباي', 'instapay_number', 'أدخل رقم انستاباي', <FaMobileAlt />)}
            {renderToggleField('تفعيل انستاباي', 'instapay_enabled', <FaCheck />)}
          </div>
        </div>

        {/* Vodafone Cash Section */}
        <div className="payment-section">
          <div className="section-header">
            <FaMobileAlt className="section-icon" />
            <h3>فودافون كاش (Vodafone Cash)</h3>
          </div>
          <div className="section-content">
            {renderNumberField('رقم فودافون كاش', 'vodafone_cash_number', 'أدخل رقم فودافون كاش', <FaMobileAlt />)}
            {renderToggleField('تفعيل فودافون كاش', 'vodafone_cash_enabled', <FaCheck />)}
          </div>
        </div>

        {/* Bank Account Section */}
        <div className="payment-section">
          <div className="section-header">
            <FaUniversity className="section-icon" />
            <h3>الحساب البنكي</h3>
          </div>
          <div className="section-content">
            <div className="form-group">
              <label>
                <FaUniversity />
                اسم البنك
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.bank_name || ''}
                  onChange={(e) => handleInputChange('bank_name', e.target.value)}
                  placeholder="أدخل اسم البنك"
                  className="form-input"
                />
              ) : (
                <div className="display-value">{formData.bank_name || 'غير محدد'}</div>
              )}
            </div>

            {renderNumberField('رقم الحساب', 'account_number', 'أدخل رقم الحساب', <FaCreditCard />)}

            <div className="form-group">
              <label>
                <FaUniversity />
                اسم صاحب الحساب
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.account_holder_name || ''}
                  onChange={(e) => handleInputChange('account_holder_name', e.target.value)}
                  placeholder="أدخل اسم صاحب الحساب"
                  className="form-input"
                />
              ) : (
                <div className="display-value">{formData.account_holder_name || 'غير محدد'}</div>
              )}
            </div>

            <div className="form-group">
              <label>
                <FaCreditCard />
                الآيبان (IBAN)
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.iban || ''}
                  onChange={(e) => handleInputChange('iban', e.target.value)}
                  placeholder="أدخل رقم الآيبان"
                  className="form-input"
                />
              ) : (
                <div className="display-value">{formData.iban || 'غير محدد'}</div>
              )}
            </div>

            <div className="form-group">
              <label>
                <FaCreditCard />
                كود السويفت (SWIFT)
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.swift_code || ''}
                  onChange={(e) => handleInputChange('swift_code', e.target.value)}
                  placeholder="أدخل كود السويفت"
                  className="form-input"
                />
              ) : (
                <div className="display-value">{formData.swift_code || 'غير محدد'}</div>
              )}
            </div>

            {renderToggleField('تفعيل الحساب البنكي', 'bank_enabled', <FaCheck />)}
          </div>
        </div>

        {/* Payment Instructions */}
        <div className="payment-section">
          <div className="section-header">
            <FaInfoCircle className="section-icon" />
            <h3>تعليمات الدفع</h3>
          </div>
          <div className="section-content">
            <div className="form-group">
              <label>
                <FaInfoCircle />
                نص تعليمات الدفع
              </label>
              {isEditing ? (
                <textarea
                  value={formData.payment_instructions || ''}
                  onChange={(e) => handleInputChange('payment_instructions', e.target.value)}
                  placeholder="أدخل تعليمات الدفع"
                  className="form-textarea"
                  rows="3"
                />
              ) : (
                <div className="display-value">{formData.payment_instructions || 'غير محدد'}</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Contact Info Tab Component
const ContactInfoTab = ({ formData, isEditing, handleInputChange }) => {
  if (!formData) return <div>لا توجد بيانات</div>;

  const renderField = (label, field, placeholder, icon, type = 'text') => (
    <div className="form-group">
      <label>
        {icon}
        {label}
      </label>
      {isEditing ? (
        type === 'textarea' ? (
          <textarea
            value={formData[field] || ''}
            onChange={(e) => handleInputChange(field, e.target.value)}
            placeholder={placeholder}
            className="form-textarea"
            rows="3"
          />
        ) : (
          <input
            type={type}
            value={formData[field] || ''}
            onChange={(e) => handleInputChange(field, e.target.value)}
            placeholder={placeholder}
            className="form-input"
          />
        )
      ) : (
        <div className="display-value">{formData[field] || 'غير محدد'}</div>
      )}
    </div>
  );

  return (
    <div className="contact-info-tab">
      <div className="contact-sections">
        {/* Basic Contact Info */}
        <div className="contact-section">
          <div className="section-header">
            <FaPhone className="section-icon" />
            <h3>معلومات الاتصال الأساسية</h3>
          </div>
          <div className="section-content">
            {renderField('رقم الهاتف', 'phone_number', 'أدخل رقم الهاتف', <FaPhone />, 'tel')}
            {renderField('البريد الإلكتروني', 'email', 'أدخل البريد الإلكتروني', <FaEnvelope />, 'email')}
            {renderField('العنوان', 'address', 'أدخل العنوان', <FaMapMarkerAlt />, 'textarea')}
            {renderField('ساعات العمل', 'working_hours', 'أدخل ساعات العمل', <FaClock />)}
          </div>
        </div>
      </div>
    </div>
  );
};

// Social Media Tab Component
const SocialMediaTab = ({ formData, isEditing, handleInputChange }) => {
  if (!formData) return <div>لا توجد بيانات</div>;

  const socialPlatforms = [
    { key: 'facebook_url', label: 'فيسبوك', icon: <FaFacebook />, placeholder: 'https://facebook.com/yourpage' },
    { key: 'twitter_url', label: 'تويتر', icon: <FaTwitter />, placeholder: 'https://twitter.com/yourpage' },
    { key: 'instagram_url', label: 'إنستجرام', icon: <FaInstagram />, placeholder: 'https://instagram.com/yourpage' },
    { key: 'linkedin_url', label: 'لينكد إن', icon: <FaLinkedin />, placeholder: 'https://linkedin.com/company/yourpage' },
    { key: 'youtube_url', label: 'يوتيوب', icon: <FaYoutube />, placeholder: 'https://youtube.com/channel/yourpage' }
  ];

  const renderSocialField = (platform) => (
    <div key={platform.key} className="form-group">
      <label>
        {platform.icon}
        {platform.label}
      </label>
      {isEditing ? (
        <input
          type="url"
          value={formData[platform.key] || ''}
          onChange={(e) => handleInputChange(platform.key, e.target.value)}
          placeholder={platform.placeholder}
          className="form-input"
        />
      ) : (
        <div className="display-value">
          {formData[platform.key] ? (
            <a href={formData[platform.key]} target="_blank" rel="noopener noreferrer" className="social-link">
              {formData[platform.key]}
            </a>
          ) : (
            'غير محدد'
          )}
        </div>
      )}
    </div>
  );

  return (
    <div className="social-media-tab">
      <div className="social-sections">
        <div className="social-section">
          <div className="section-header">
            <FaGlobe className="section-icon" />
            <h3>وسائل التواصل الاجتماعي</h3>
          </div>
          <div className="section-content">
            <div className="social-grid">
              {socialPlatforms.map(platform => renderSocialField(platform))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethodsManagement;
