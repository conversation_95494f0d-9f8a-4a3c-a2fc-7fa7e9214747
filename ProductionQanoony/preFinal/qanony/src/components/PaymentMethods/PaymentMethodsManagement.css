/* Payment Methods Management Styles */
.payment-methods-management {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header Styles */
.management-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content h2 {
  color: #2c3e50;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.8rem;
}

.header-icon {
  color: #f39c12;
}

.header-content p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.edit-actions {
  display: flex;
  gap: 10px;
}

/* Button Styles */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #e67e22, #d35400);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #f1c40f, #f39c12);
  color: #2c3e50;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(241, 196, 15, 0.3);
}

.btn-success:hover {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  box-shadow: 0 6px 20px rgba(241, 196, 15, 0.4);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Alert Styles */
.alert {
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
}

.alert-error {
  background: #fee;
  color: #c0392b;
  border: 1px solid #fadbd8;
}

.alert-success {
  background: #eafaf1;
  color: #27ae60;
  border: 1px solid #d5f4e6;
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

/* Error Container */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tabs Styles */
.tabs-container {
  margin-bottom: 30px;
}

.tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow-x: auto;
}

.tab {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #7f8c8d;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  white-space: nowrap;
}

.tab:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.tab.active {
  background: linear-gradient(135deg, #f1c40f, #f39c12);
  color: #2c3e50;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(241, 196, 15, 0.3);
}

/* Tab Content */
.tab-content {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Overview Tab Styles */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.overview-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.card-icon {
  color: #f39c12;
  font-size: 1.5rem;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
}

.status-item:last-child {
  border-bottom: none;
}

.status-item .label {
  font-weight: 500;
  color: #2c3e50;
}

.status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status.active {
  background: #d5f4e6;
  color: #27ae60;
}

.status.inactive {
  background: #fadbd8;
  color: #c0392b;
}

/* Social Grid */
.social-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.social-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.social-item svg {
  font-size: 1.5rem;
  color: #f39c12;
}

/* Toggle Button */
.toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #ecf0f1;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #2c3e50;
}

.toggle-btn:hover {
  background: #d5dbdb;
}

.toggle-btn.active {
  background: linear-gradient(135deg, #f1c40f, #f39c12);
  color: #2c3e50;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(241, 196, 15, 0.3);
}

.toggle-note {
  margin-top: 10px;
  font-size: 0.9rem;
  color: #7f8c8d;
  text-align: center;
}

/* Sensitive Data Toggle */
.sensitive-toggle {
  margin-bottom: 25px;
  text-align: center;
}

/* Payment Sections */
.payment-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.payment-section {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
}

.section-header {
  background: #f8f9fa;
  padding: 20px 25px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-icon {
  color: #f39c12;
  font-size: 1.3rem;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.section-content {
  padding: 25px;
}

/* Form Styles */
.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #f39c12;
  box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
}

.display-value {
  padding: 12px 15px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #2c3e50;
  min-height: 20px;
}

/* Toggle Switch */
.toggle-switch {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-switch input[type="checkbox"] {
  display: none;
}

.switch-label {
  position: relative;
  width: 50px;
  height: 24px;
  background: #ccc;
  border-radius: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.switch-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.toggle-switch input[type="checkbox"]:checked + .switch-label {
  background: #27ae60;
}

.toggle-switch input[type="checkbox"]:checked + .switch-label .switch-slider {
  transform: translateX(26px);
}

.switch-text {
  font-weight: 500;
  color: #2c3e50;
}

/* Status Badge */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  display: inline-block;
}

.status-badge.active {
  background: #d5f4e6;
  color: #27ae60;
}

.status-badge.inactive {
  background: #fadbd8;
  color: #c0392b;
}

/* Social Link */
.social-link {
  color: #f39c12;
  text-decoration: none;
}

.social-link:hover {
  text-decoration: underline;
}

/* Contact Sections */
.contact-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.contact-section {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
}

/* Social Sections */
.social-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.social-section {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-methods-management {
    padding: 15px;
  }
  
  .management-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .tabs {
    flex-direction: column;
  }
  
  .tab {
    justify-content: flex-start;
  }
  
  .tab-content {
    padding: 20px;
  }
  
  .section-content {
    padding: 20px;
  }
}
