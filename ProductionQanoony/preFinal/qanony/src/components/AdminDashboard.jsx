import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { FaUserGraduate, FaUser<PERSON>heck, FaUserTimes, FaChalkboardTeacher, FaChartBar, FaUsers, FaClipboardList, FaBookOpen, FaBook, FaBriefcase, FaComments, FaBell, FaRegCreditCard, FaUniversity, FaInfoCircle, FaDownload, FaPlus, FaEdit, FaTrash, FaEye, FaBars, FaTimes, FaCreditCard } from 'react-icons/fa';
import CountUp from 'react-countup';
import SemesterTable from './AcademicContentAdmin/SemesterTable';
import SubjectTable from './AcademicContentAdmin/SubjectTable';
import LectureTable from './AcademicContentAdmin/LectureTable';
import QuizTable from './AcademicContentAdmin/QuizTable';
// import QuestionTable from './AcademicContentAdmin/QuestionTable';
// import AnswerTable from './AcademicContentAdmin/AnswerTable';
// import QuizAttemptTable from './AcademicContentAdmin/QuizAttemptTable';
import QuizManager from './AcademicContentAdmin/QuizManager';
import AdminPrivateTutorSessions from './AcademicContentAdmin/AdminPrivateTutorSessions';
import QuizAttemptsTable from './AcademicContentAdmin/QuizAttemptsTable';


import DashboardStats from './Dashboard/DashboardStats';
import SubscriptionManagement from './Subscriptions/SubscriptionManagement';
import UserManagement from './Users/<USER>';
import AcademicManagement from './Academic/AcademicManagement';
import LibraryManagement from './Library/LibraryManagement';
import NotificationsManagement from './Notifications/NotificationsManagement';
import CareersManagement from './Careers/CareersManagement';
import CommunicationManagement from './Communication/CommunicationManagement';
import PaymentMethodsManagement from './PaymentMethods/PaymentMethodsManagement';


const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const SIDEBAR_TABS = [
  { key: 'dashboard', label: 'لوحة المعلومات', icon: <FaChartBar /> },
  { key: 'users', label: 'إدارة المستخدمين', icon: <FaUsers /> },
  { key: 'courses', label: 'الكورسات', icon: <FaBookOpen /> },
  { key: 'subscriptions', label: 'الاشتراكات', icon: <FaRegCreditCard /> },
  { key: 'payment-methods', label: 'طرق الدفع والتواصل', icon: <FaCreditCard /> },
  { key: 'library', label: 'المكتبة', icon: <FaBook /> },
  { key: 'universities', label: 'الجامعات', icon: <FaUniversity /> },
  { key: 'careers', label: 'الوظائف', icon: <FaBriefcase /> },
  { key: 'communication', label: 'التواصل', icon: <FaComments /> },
  { key: 'notifications', label: 'الإشعارات', icon: <FaBell /> },
];

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showSidebar, setShowSidebar] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const adminName = 'مستر أحمد';

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setShowSidebar(false); // Close sidebar on desktop
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);







  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile Header with Burger Menu */}
      {isMobile && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-md border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-3">
            <button
              onClick={() => setShowSidebar(true)}
              className="p-2 rounded-lg text-yellow-600 hover:bg-yellow-50 transition-colors"
            >
              <FaBars size={20} />
            </button>
            <h1 className="text-lg font-bold text-gray-800">لوحة التحكم</h1>
            <div className="w-8"></div> {/* Spacer for centering */}
          </div>
        </div>
      )}
      {/* Sidebar */}
      <aside className={`
        ${isMobile
          ? `fixed inset-y-0 right-0 z-40 w-72 bg-white shadow-xl transform transition-transform duration-300 ease-in-out ${showSidebar ? 'translate-x-0' : 'translate-x-full'}`
          : 'w-64 bg-white shadow-lg border-l border-gray-200'
        }
      `}>
        {/* Sidebar Header */}
        <div className={`px-4 py-4 border-b border-gray-200 ${isMobile ? 'pt-6' : ''}`}>
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-800">لوحة التحكم</h2>
            {isMobile && (
              <button
                onClick={() => setShowSidebar(false)}
                className="p-2 rounded-lg text-gray-500 hover:bg-gray-100 transition-colors"
              >
                <FaTimes size={18} />
              </button>
            )}
          </div>
          <p className="text-sm text-gray-600 mt-1">مرحباً {adminName}</p>
        </div>

        {/* Navigation */}
        <nav className="flex flex-col gap-1 px-3 py-4 overflow-y-auto">
          {SIDEBAR_TABS.map(tab => (
            <button
              key={tab.key}
              className={`
                flex items-center gap-3 px-4 py-3 rounded-lg text-right transition-all duration-200 w-full
                ${activeTab === tab.key
                  ? 'bg-yellow-500 text-white shadow-md font-semibold'
                  : 'text-gray-700 hover:bg-yellow-50 hover:text-yellow-700'
                }
              `}
              onClick={() => {
                setActiveTab(tab.key);
                if (isMobile) setShowSidebar(false);
              }}
            >
              <span className="text-lg">{tab.icon}</span>
              <span className="flex-1 text-sm font-medium">{tab.label}</span>
            </button>
          ))}
        </nav>
      </aside>
      {/* Mobile Overlay */}
      {isMobile && showSidebar && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50"
          onClick={() => setShowSidebar(false)}
        ></div>
      )}

      {/* Main Content */}
      <div className={`flex-1 flex flex-col overflow-auto ${isMobile ? 'pt-16' : ''}`}>
        <div className="flex-1 overflow-auto">
          {activeTab === 'dashboard' && (
            <div className={`w-full bg-white max-w-7xl mx-auto rounded-xl shadow ${isMobile ? 'p-3 m-2' : 'p-4 sm:p-6 md:p-8 mt-8'}`}>
              <DashboardStats />
            </div>
          )}
          {activeTab === 'users' && (
            <div className={`w-full bg-white max-w-7xl mx-auto rounded-xl shadow ${isMobile ? 'p-3 m-2' : 'p-4 sm:p-6 md:p-8 mt-8'}`}>
              <UserManagement />
            </div>
          )}




          {activeTab === 'courses' && (
            <div className={`w-full bg-white max-w-6xl mx-auto rounded-xl shadow ${isMobile ? 'p-3 m-2' : 'p-4 sm:p-6 md:p-8 mt-8'}`}>
              <CourseTabs />
            </div>
          )}

          {activeTab === 'communication' && (
            <div className={isMobile ? 'p-2' : ''}>
              <CommunicationManagement />
            </div>
          )}

          {activeTab === 'subscriptions' && (
            <div className={`w-full bg-white max-w-7xl mx-auto rounded-xl shadow ${isMobile ? 'p-3 m-2' : 'p-4 sm:p-6 md:p-8 mt-8'}`}>
              <SubscriptionManagement />
            </div>
          )}

          {activeTab === 'payment-methods' && (
            <div className={`w-full max-w-7xl mx-auto ${isMobile ? 'p-2' : 'mt-8'}`}>
              <PaymentMethodsManagement />
            </div>
          )}
          {activeTab === 'universities' && (
            <div className={isMobile ? 'p-2' : ''}>
              <AcademicManagement />
            </div>
          )}
          {activeTab === 'library' && (
            <div className={isMobile ? 'p-2' : ''}>
              <LibraryManagement />
            </div>
          )}
          {activeTab === 'notifications' && (
            <div className={isMobile ? 'p-2' : ''}>
              <NotificationsManagement />
            </div>
          )}
          {activeTab === 'careers' && (
            <div className={isMobile ? 'p-2' : ''}>
              <CareersManagement />
            </div>
          )}

        </div>
      </div>

    </div>
  );
};

function CourseTabs() {
  const [tab, setTab] = useState('semesters');
  return (
    <div className="w-full">
      <div className="flex gap-2 border-b border-gray-200 mb-6 mt-2 bg-white overflow-x-auto">
        <button
          className={`px-4 py-2 rounded-lg font-semibold transition-colors focus:outline-none whitespace-nowrap ${tab === 'semesters' ? 'bg-yellow-400 text-yellow-900 shadow' : 'bg-transparent text-yellow-900 hover:bg-yellow-100'}`}
          onClick={() => setTab('semesters')}
        >
          الفصول الدراسية
        </button>
        <button
          className={`px-4 py-2 rounded-lg font-semibold transition-colors focus:outline-none whitespace-nowrap ${tab === 'subjects' ? 'bg-yellow-400 text-yellow-900 shadow' : 'bg-transparent text-yellow-900 hover:bg-yellow-100'}`}
          onClick={() => setTab('subjects')}
        >
          المواد
        </button>
        <button
          className={`px-4 py-2 rounded-lg font-semibold transition-colors focus:outline-none whitespace-nowrap ${tab === 'lectures' ? 'bg-yellow-400 text-yellow-900 shadow' : 'bg-transparent text-yellow-900 hover:bg-yellow-100'}`}
          onClick={() => setTab('lectures')}
        >
          الدروس
        </button>
        <button
          className={`px-4 py-2 rounded-lg font-semibold transition-colors focus:outline-none whitespace-nowrap ${tab === 'quizzes' ? 'bg-yellow-400 text-yellow-900 shadow' : 'bg-transparent text-yellow-900 hover:bg-yellow-100'}`}
          onClick={() => setTab('quizzes')}
        >
          الاختبارات
        </button>
        <button
          className={`px-4 py-2 rounded-lg font-semibold transition-colors focus:outline-none whitespace-nowrap ${tab === 'quiz-manager' ? 'bg-yellow-400 text-yellow-900 shadow' : 'bg-transparent text-yellow-900 hover:bg-yellow-100'}`}
          onClick={() => setTab('quiz-manager')}
        >
          إدارة الاختبارات
        </button>
        <button
          className={`px-4 py-2 rounded-lg font-semibold transition-colors focus:outline-none whitespace-nowrap ${tab === 'private-tutor-sessions' ? 'bg-yellow-400 text-yellow-900 shadow' : 'bg-transparent text-yellow-900 hover:bg-yellow-100'}`}
          onClick={() => setTab('private-tutor-sessions')}
        >
          الجلسات الخاصة
        </button>
        <button
          className={`px-4 py-2 rounded-lg font-semibold transition-colors focus:outline-none whitespace-nowrap ${tab === 'quiz-attempts' ? 'bg-yellow-400 text-yellow-900 shadow' : 'bg-transparent text-yellow-900 hover:bg-yellow-100'}`}
          onClick={() => setTab('quiz-attempts')}
        >
          محاولات الاختبارات
        </button>

      </div>
      <div>
        {tab === 'semesters' && <SemesterTable />}
        {tab === 'subjects' && <SubjectTable />}
        {tab === 'lectures' && <LectureTable />}
        {tab === 'quizzes' && <QuizTable />}
        {tab === 'quiz-manager' && <QuizManager />}
        {tab === 'private-tutor-sessions' && <AdminPrivateTutorSessions />}
        {tab === 'quiz-attempts' && <QuizAttemptsTable />}

      </div>
    </div>
  );
}

export default AdminDashboard; 