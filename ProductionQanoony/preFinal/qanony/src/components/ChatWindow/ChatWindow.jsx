import React, { useState, useEffect, useRef } from 'react';
import apiClient from '../../api';
import { useChatWebSocket } from '../../hooks/useWebSocket';
import './ChatWindow.css';

const WS_BASE_URL = process.env.REACT_APP_WS_BASE_URL || 'ws://localhost:8000';

const DEFAULT_AI_TITLE = 'محادثة قانونية';
const DEFAULT_AI_CONTEXT = 'legal_question';

const ChatWindow = ({ room, onBack, aiMode = false }) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [newMessage, setNewMessage] = useState('');
  const [aiConversationId, setAiConversationId] = useState(null);
  const [sending, setSending] = useState(false);
  const [selectedContextType, setSelectedContextType] = useState('legal_question');
  const ws = useRef(null);
  const messagesEndRef = useRef(null);

  // WebSocket connection for regular chat (not AI)
  const {
    isConnected: wsConnected,
    connectionState,
    sendChatMessage,
    sendTyping,
    lastMessage: wsLastMessage,
  } = useChatWebSocket(room?.id && !aiMode ? room.id : null, {
    autoConnect: !!room?.id && !aiMode,
    onMessage: (data) => {
      if (data.type === 'chat_message') {
        // Add new message from WebSocket
        setMessages(prev => {
          // Avoid duplicates by checking if message already exists
          const exists = prev.some(msg => msg.id === data.id);
          if (exists) return prev;

          return [...prev, {
            id: data.id,
            content: data.message,
            sender: { id: data.sender_id, email: data.sender_email },
            timestamp: data.timestamp,
            message_type: data.message_type
          }];
        });
      }
    },
    onOpen: () => {
      console.log('Chat WebSocket connected for room:', room?.id);
    },
    onClose: () => {
      console.log('Chat WebSocket disconnected for room:', room?.id);
    },
    onError: (error) => {
      console.error('Chat WebSocket error:', error);
      setError('حدث خطأ في الاتصال. جاري المحاولة مرة أخرى...');
    }
  });

  // Legal context types for AI
  const legalContextTypes = [
    { value: 'legal_question', label: 'سؤال قانوني عام', placeholder: 'اكتب سؤالك القانوني...' },
    { value: 'civil_law', label: 'القانون المدني', placeholder: 'اسأل عن القانون المدني...' },
    { value: 'criminal_law', label: 'القانون الجنائي', placeholder: 'اسأل عن القانون الجنائي...' },
    { value: 'commercial_law', label: 'القانون التجاري', placeholder: 'اسأل عن القانون التجاري...' },
    { value: 'constitutional_law', label: 'القانون الدستوري', placeholder: 'اسأل عن القانون الدستوري...' },
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (!aiMode) return;

    // منع التكرار - تحقق من وجود محادثة نشطة
    if (aiConversationId) return;

    const token = localStorage.getItem('access');
    let conversationId = localStorage.getItem('ai_conversation_id');
    let studentId = null;
    let cancel = false;

    const fetchOrCreateConversation = async () => {
      setLoading(true);
      setError(null);
      try {
        // Get student profile (contains student ID) - مرة واحدة فقط
        const profileRes = await apiClient.get(`/api/students/profiles/me/`);
        console.log('AI Assistant: profile response', typeof profileRes.data);
        const profileId = profileRes.data.id;
        const isAdminAccess = profileRes.data.is_admin_access;
        console.log('AI Assistant: using profile id for conversation:', profileId, 'Admin access:', isAdminAccess);

        if (!profileId) {
          setError('حدث خطأ في الحصول على معلومات المستخدم.');
          return;
        }
        studentId = profileId;
        console.log('AI Assistant: profileId', studentId, typeof studentId);
        if (!conversationId) {
          try {
            let conversationData;
            if (isAdminAccess) {
              // للأدمن والمدرسين: استخدام endpoint خاص
              conversationData = await apiClient.post(
                `/api/ai-assistant/conversations/create_admin_conversation/`,
                {
                  title_ar: `محادثة إدارية - ${legalContextTypes.find(t => t.value === selectedContextType)?.label || 'قانونية'}`,
                  context_type: selectedContextType
                }
              );
            } else {
              // للطلاب: الطريقة العادية
              conversationData = await apiClient.post(
                `/api/ai-assistant/conversations/`,
                {
                  student: profileId,
                  title_ar: `محادثة ${legalContextTypes.find(t => t.value === selectedContextType)?.label || 'قانونية'}`,
                  context_type: selectedContextType
                }
              );
            }
            conversationId = conversationData.data.id;
            localStorage.setItem('ai_conversation_id', conversationId);
          } catch (err) {
            console.error('AI Assistant: error creating conversation', err?.response?.data || err);
            throw err;
          }
        }
        setAiConversationId(conversationId);

        // Try to get conversation history, handle 404 gracefully
        try {
          const historyResponse = await apiClient.get(
            `/api/ai-assistant/conversations/${conversationId}/history/`
          );
          if (!cancel) setMessages(Array.isArray(historyResponse.data) ? historyResponse.data : []);
        } catch (historyErr) {
          console.error('AI Assistant: error fetching history', historyErr);
          if (historyErr.response?.status === 404) {
            // Conversation doesn't exist or user doesn't have access
            // Clear the stored conversation ID and try to create a new one
            localStorage.removeItem('ai_conversation_id');
            conversationId = null;
            setError('المحادثة غير موجودة. سيتم إنشاء محادثة جديدة.');
            // Retry the whole process
            setTimeout(() => fetchOrCreateConversation(), 1000);
            return;
          }
          // For other errors, just start with empty messages
          if (!cancel) setMessages([]);
        }
      } catch (err) {
        setError('تعذر تحميل محادثة الذكاء الاصطناعي.');
      } finally {
        setLoading(false);
      }
    };
    fetchOrCreateConversation();
    return () => { cancel = true; };
  }, [aiMode]); // إزالة selectedContextType لمنع التكرار

  const handleSendAIMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !aiConversationId) return;
    setSending(true);
    setError(null);
    try {
      const userMsg = {
        id: `user-${Date.now()}`,
        message_type: 'user',
        content_ar: newMessage,
        timestamp: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, userMsg]);
      setNewMessage('');
      const { data } = await apiClient.post(
        `/api/ai-assistant/messages/send/`,
        { conversation: aiConversationId, content_ar: userMsg.content_ar }
      );
      setMessages((prev) => [
        ...prev.filter((m) => m.id !== userMsg.id),
        data.user_message,
        data.assistant_message
      ]);
    } catch (err) {
      setError('حدث خطأ أثناء إرسال الرسالة.');
    } finally {
      setSending(false);
    }
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  useEffect(() => {
    if (aiMode) return;
    if (!room) return;
    setLoading(true);
    setError(null);
    const fetchMessages = async () => {
      try {
        const { data } = await apiClient.get(
          `/api/communication/messages/?room=${room.id}&ordering=timestamp`
        );
        // Handle both paginated and non-paginated responses
        const messagesList = Array.isArray(data) ? data : (data.results || []);
        console.log(`📨 Loaded ${messagesList.length} messages for room ${room.id}`);
        setMessages(messagesList);
      } catch (err) {
        console.error('Error loading messages:', err);
        setError('تعذر تحميل الرسائل السابقة.');
      } finally {
        setLoading(false);
      }
    };
    fetchMessages();
  }, [room, aiMode]);

  const handleSendForumMessage = (e) => {
    e.preventDefault();
    if (!aiMode && (!newMessage.trim() || !wsConnected || sending)) return;
    if (aiMode) return handleSendAIMessage(e);

    setSending(true);

    // Send message via WebSocket
    const success = sendChatMessage(newMessage);

    if (success) {
      setNewMessage('');
      setError(null);
    } else {
      setError('فشل في إرسال الرسالة. تأكد من الاتصال.');
    }

    setSending(false);
  };

  if (aiMode && !aiConversationId && loading) {
    return <div className="chat-window"><div className="status-text">جاري تحميل محادثة الذكاء الاصطناعي...</div></div>;
  }
  if (aiMode && error) {
    return <div className="chat-window"><div className="status-text error">{error}</div></div>;
  }
  if (!aiMode && !room) {
    return null;
  }

  return (
    <div className="chat-window">
      <header className="chat-window-header">
        {onBack && (
          <button onClick={onBack} className="back-button">&larr;</button>
        )}
        <h2 className="room-name">{aiMode ? 'مساعد الذكاء الاصطناعي' : (room?.name || '')}</h2>
        {aiMode && (
          <div className="context-selector">
            <select
              value={selectedContextType}
              onChange={(e) => setSelectedContextType(e.target.value)}
              className="context-select"
            >
              {legalContextTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
        )}
        {!aiMode && (
          <div className="connection-status">
            <span className={`status-indicator ${wsConnected ? 'connected' : 'disconnected'}`}>
              {wsConnected ? '🟢 متصل' : '🔴 غير متصل'}
            </span>
          </div>
        )}
      </header>
      <main className="messages-area">
        {loading && <div className="status-text">جاري تحميل الرسائل...</div>}
        {error && <div className="status-text error">{error}</div>}
        {!loading && !error && (
          <>
            {messages.map((msg, idx) => {
              const isCurrentUser = msg.sender === room?.current_user_id ||
                                   (typeof msg.sender === 'object' && msg.sender?.id === room?.current_user_id);

              return (
                <div key={msg.id || msg.timestamp || idx} className={`message ${isCurrentUser ? 'sent' : 'received'}`}>
                  <div className="message-header">
                    <span className="sender">
                      {isCurrentUser ? 'أنت' : (msg.sender?.email || msg.sender_email || 'مستخدم')}
                    </span>
                    {msg.timestamp && (
                      <span className="timestamp">
                        {new Date(msg.timestamp).toLocaleTimeString('ar-SA', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    )}
                  </div>
                  <div className="message-content">
                    <p>{msg.content_ar || msg.content || msg.message}</p>
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </>
        )}
      </main>
      <form onSubmit={handleSendForumMessage} className="message-input-form">
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder={aiMode ? (legalContextTypes.find(t => t.value === selectedContextType)?.placeholder || 'اكتب سؤالك القانوني...') : 'اكتب رسالتك...'}
          disabled={loading || error || sending || (aiMode && !aiConversationId) || (!aiMode && !wsConnected)}
          onFocus={() => !aiMode && sendTyping && sendTyping(true)}
          onBlur={() => !aiMode && sendTyping && sendTyping(false)}
        />
        <button type="submit" disabled={loading || error || !newMessage.trim() || sending || (aiMode && !aiConversationId) || (!aiMode && !wsConnected)}>
          {sending ? 'جاري الإرسال...' : 'إرسال'}
        </button>
      </form>
    </div>
  );
};

export default ChatWindow; 