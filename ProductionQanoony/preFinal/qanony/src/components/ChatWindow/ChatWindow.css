.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
}

.chat-window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.chat-window-header .back-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  margin-right: 1rem;
  color: #333;
}

.chat-window-header .room-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}

.context-selector {
  margin-left: auto;
  margin-right: 1rem;
}

.context-select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
}

.context-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Connection Status */
.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.status-indicator.connected {
  background-color: #d1fae5;
  color: #065f46;
}

.status-indicator.disconnected {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Enhanced Message Styles */
.message {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}

.message.sent {
  align-items: flex-end;
}

.message.received {
  align-items: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  position: relative;
}

.message.sent .message-content {
  background-color: #3b82f6;
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.message.received .message-content {
  background-color: #f3f4f6;
  color: #1f2937;
  border-bottom-left-radius: 0.25rem;
}

.sender-name {
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  display: block;
}

.message.sent .sender-name {
  color: rgba(255, 255, 255, 0.8);
}

.message.received .sender-name {
  color: #6b7280;
}

.message-text {
  margin: 0;
  line-height: 1.5;
  word-wrap: break-word;
}

.timestamp {
  font-size: 0.625rem;
  margin-top: 0.25rem;
  display: block;
}

.message.sent .timestamp {
  color: rgba(255, 255, 255, 0.7);
}

.message.received .timestamp {
  color: #9ca3af;
}

/* Send Button States */
.send-button {
  transition: all 0.2s ease;
}

.send-button:hover:not(:disabled) {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.send-button.disabled {
  background-color: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
}

.typing-dot {
  width: 0.375rem;
  height: 0.375rem;
  background-color: #9ca3af;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.messages-area {
  flex-grow: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.status-text {
  margin: auto;
  color: #888;
  font-size: 1.1rem;
}

.status-text.error {
  color: #e53e3e;
}

.message {
  padding: 0.5rem 1rem;
  border-radius: 18px;
  margin-bottom: 0.75rem;
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

/* Default to received message style */
.message {
  background-color: #ffffff;
  align-self: flex-start;
  border: 1px solid #eee;
}

/* Later, we can add a class like "sent" to distinguish user's own messages */
/*
.message.sent {
  background-color: #dcf8c6;
  align-self: flex-end;
}
*/

.message .sender {
  font-size: 0.8rem;
  font-weight: bold;
  color: #555;
  margin-bottom: 0.25rem;
}

.message p {
  margin: 0;
  line-height: 1.4;
  color: #333;
}

.message .timestamp {
  font-size: 0.7rem;
  color: #999;
  align-self: flex-end;
  margin-top: 0.25rem;
}

.message-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #888;
  font-size: 1.1rem;
}

.message-input-form {
  display: flex;
  padding: 1rem;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
}

.message-input-form input {
  flex-grow: 1;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 20px;
  margin-right: 0.5rem;
  font-size: 1rem;
}

.message-input-form button {
  padding: 0.75rem 1.5rem;
  border: none;
  background-color: #facc15; /* Gold color */
  color: white;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
}

.message-input-form button:hover {
  background-color: #eab308;
}

.send-button {
  background-color: #facc15; /* Gold color */
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
} 