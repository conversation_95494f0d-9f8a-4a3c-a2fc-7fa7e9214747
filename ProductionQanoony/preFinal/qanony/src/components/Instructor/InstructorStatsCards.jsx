import React from 'react';
import { FaBook, FaUsers, FaCalendarCheck, FaStar, FaClock, FaComments, FaChartLine } from 'react-icons/fa';

const StatCard = ({ title, value, icon, color, subtitle }) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-blue-500',
    green: 'bg-green-500 text-green-500',
    purple: 'bg-purple-500 text-purple-500',
    yellow: 'bg-yellow-500 text-yellow-500',
    red: 'bg-red-500 text-red-500',
    indigo: 'bg-indigo-500 text-indigo-500'
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-gray-600 text-sm font-medium mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mb-1">{value}</p>
          {subtitle && (
            <p className="text-gray-500 text-xs">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]?.replace('text-', 'bg-').replace('500', '100')}`}>
          <div className={`${colorClasses[color]} text-xl`}>
            {icon}
          </div>
        </div>
      </div>
    </div>
  );
};

const RecentActivityItem = ({ activity }) => {
  const getActivityIcon = (type) => {
    switch (type) {
      case 'session_completed':
        return <FaCalendarCheck className="text-green-500" />;
      case 'new_message':
        return <FaComments className="text-blue-500" />;
      case 'course_published':
        return <FaBook className="text-purple-500" />;
      case 'student_enrolled':
        return <FaUsers className="text-indigo-500" />;
      default:
        return <FaChartLine className="text-gray-500" />;
    }
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'منذ قليل';
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `منذ ${diffInDays} يوم`;
  };

  return (
    <div className="flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      <div className="flex-shrink-0">
        {getActivityIcon(activity.type)}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {activity.title}
        </p>
        <p className="text-xs text-gray-500 truncate">
          {activity.description}
        </p>
      </div>
      <div className="flex-shrink-0">
        <span className="text-xs text-gray-400">
          {formatTime(activity.timestamp)}
        </span>
      </div>
    </div>
  );
};

const InstructorStatsCards = ({ stats, loading }) => {
  if (loading) {
    return (
      <div className="space-y-6">
        {/* Stats Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">لا توجد إحصائيات متاحة</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="إجمالي المواد"
          value={stats.total_subjects || 0}
          icon={<FaBook />}
          color="blue"
          subtitle={`${stats.active_subjects || 0} نشط`}
        />
        <StatCard
          title="إجمالي الطلاب"
          value={stats.total_students || 0}
          icon={<FaUsers />}
          color="green"
          subtitle={`${stats.active_students || 0} نشط`}
        />
        <StatCard
          title="الجلسات المكتملة"
          value={stats.completed_sessions || 0}
          icon={<FaCalendarCheck />}
          color="purple"
          subtitle={`${stats.pending_sessions || 0} معلقة`}
        />
        <StatCard
          title="متوسط التقييم"
          value={stats.average_rating ? `${stats.average_rating}/5` : 'لا يوجد'}
          icon={<FaStar />}
          color="yellow"
          subtitle={`${stats.total_ratings || 0} تقييم`}
        />
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard
          title="الجلسات هذا الشهر"
          value={stats.this_month_sessions || 0}
          icon={<FaClock />}
          color="indigo"
        />
        <StatCard
          title="إجمالي الرسائل"
          value={stats.total_messages || 0}
          icon={<FaComments />}
          color="red"
          subtitle={`${stats.unread_messages || 0} غير مقروءة`}
        />
        <StatCard
          title="الطلاب الجدد"
          value={stats.this_month_students || 0}
          icon={<FaUsers />}
          color="green"
          subtitle="هذا الشهر"
        />
      </div>

      {/* Recent Activity */}
      {stats.recent_activity && stats.recent_activity.length > 0 && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <FaChartLine className="text-yellow-500" />
            النشاط الحديث
          </h3>
          <div className="space-y-1">
            {stats.recent_activity.slice(0, 5).map((activity, index) => (
              <RecentActivityItem key={index} activity={activity} />
            ))}
          </div>
          {stats.recent_activity.length > 5 && (
            <div className="mt-4 text-center">
              <button className="text-yellow-500 hover:text-yellow-600 text-sm font-medium">
                عرض المزيد
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default InstructorStatsCards;
