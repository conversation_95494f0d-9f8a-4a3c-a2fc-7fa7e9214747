import React, { useState, useEffect } from 'react';
import { FaSave, FaTimes, FaUser, FaPhone, FaGraduationCap, FaFileAlt } from 'react-icons/fa';

const InstructorProfileForm = ({ instructor, onSave, onCancel, loading }) => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    phone_number: '',
    specialty: '',
    bio: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (instructor) {
      setFormData({
        first_name: instructor.first_name || '',
        last_name: instructor.last_name || '',
        phone_number: instructor.phone_number || '',
        specialty: instructor.specialty || '',
        bio: instructor.bio || ''
      });
    }
  }, [instructor]);

  const validateForm = () => {
    const newErrors = {};

    // First name validation
    if (!formData.first_name.trim()) {
      newErrors.first_name = 'الاسم الأول مطلوب';
    } else if (formData.first_name.length > 50) {
      newErrors.first_name = 'الاسم الأول يجب أن يكون أقل من 50 حرف';
    }

    // Last name validation
    if (!formData.last_name.trim()) {
      newErrors.last_name = 'اسم العائلة مطلوب';
    } else if (formData.last_name.length > 50) {
      newErrors.last_name = 'اسم العائلة يجب أن يكون أقل من 50 حرف';
    }

    // Phone number validation (optional but must be valid if provided)
    if (formData.phone_number && !/^[\+]?[0-9\-\(\)\s]+$/.test(formData.phone_number)) {
      newErrors.phone_number = 'رقم الهاتف غير صحيح';
    }

    // Specialty validation
    if (formData.specialty && formData.specialty.length > 100) {
      newErrors.specialty = 'التخصص يجب أن يكون أقل من 100 حرف';
    }

    // Bio validation
    if (formData.bio && formData.bio.length > 1000) {
      newErrors.bio = 'النبذة التعريفية يجب أن تكون أقل من 1000 حرف';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
          <FaUser className="text-yellow-500" />
          تعديل البيانات الشخصية
        </h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الاسم الأول *
            </label>
            <input
              type="text"
              name="first_name"
              value={formData.first_name}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 ${
                errors.first_name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="أدخل الاسم الأول"
              required
            />
            {errors.first_name && (
              <p className="mt-1 text-sm text-red-600">{errors.first_name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اسم العائلة *
            </label>
            <input
              type="text"
              name="last_name"
              value={formData.last_name}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 ${
                errors.last_name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="أدخل اسم العائلة"
              required
            />
            {errors.last_name && (
              <p className="mt-1 text-sm text-red-600">{errors.last_name}</p>
            )}
          </div>
        </div>

        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
            <FaPhone className="text-gray-400" />
            رقم الهاتف
          </label>
          <input
            type="tel"
            name="phone_number"
            value={formData.phone_number}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 ${
              errors.phone_number ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="مثال: +966501234567"
          />
          {errors.phone_number && (
            <p className="mt-1 text-sm text-red-600">{errors.phone_number}</p>
          )}
        </div>

        {/* Specialty */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
            <FaGraduationCap className="text-gray-400" />
            التخصص
          </label>
          <input
            type="text"
            name="specialty"
            value={formData.specialty}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 ${
              errors.specialty ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="مثال: القانون المدني والتجاري"
          />
          {errors.specialty && (
            <p className="mt-1 text-sm text-red-600">{errors.specialty}</p>
          )}
        </div>

        {/* Bio */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
            <FaFileAlt className="text-gray-400" />
            النبذة التعريفية
          </label>
          <textarea
            name="bio"
            value={formData.bio}
            onChange={handleInputChange}
            rows={4}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 ${
              errors.bio ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="اكتب نبذة تعريفية عن نفسك وخبرتك في التدريس..."
          />
          <div className="flex justify-between items-center mt-1">
            {errors.bio && (
              <p className="text-sm text-red-600">{errors.bio}</p>
            )}
            <p className="text-xs text-gray-500 mr-auto">
              {formData.bio.length}/1000 حرف
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-6">
          <button
            type="submit"
            disabled={isSubmitting || loading}
            className="flex-1 bg-yellow-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-yellow-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
          >
            <FaSave />
            {isSubmitting ? 'جاري الحفظ...' : 'حفظ التغييرات'}
          </button>
          
          <button
            type="button"
            onClick={onCancel}
            disabled={isSubmitting}
            className="flex-1 bg-gray-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
          >
            <FaTimes />
            إلغاء
          </button>
        </div>
      </form>
    </div>
  );
};

export default InstructorProfileForm;
