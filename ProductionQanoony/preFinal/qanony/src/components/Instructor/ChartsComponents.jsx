import React from 'react';
import { FaChart<PERSON>ine, FaChartBar, FaChart<PERSON>ie, FaArrowUp, FaArrowDown } from 'react-icons/fa';

// Simple Bar Chart Component
export const SimpleBarChart = ({ data, title, color = 'yellow' }) => {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        لا توجد بيانات لعرضها
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));
  
  const colorClasses = {
    yellow: 'bg-yellow-500',
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    red: 'bg-red-500',
    purple: 'bg-purple-500'
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center gap-2 mb-4">
        <FaChartBar className="text-yellow-500" />
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center gap-3">
            <div className="w-20 text-sm text-gray-600 text-right">
              {item.label}
            </div>
            <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
              <div 
                className={`${colorClasses[color]} h-6 rounded-full transition-all duration-500 ease-out flex items-center justify-end pr-2`}
                style={{ width: `${(item.value / maxValue) * 100}%` }}
              >
                <span className="text-white text-xs font-medium">
                  {item.value}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Progress Ring Chart Component
export const ProgressRingChart = ({ 
  percentage, 
  title, 
  subtitle,
  size = 120,
  strokeWidth = 8,
  color = '#F59E0B' // yellow-500
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 text-center">
      <div className="relative inline-block">
        <svg width={size} height={size} className="transform -rotate-90">
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#E5E7EB"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-1000 ease-out"
          />
        </svg>
        
        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {percentage}%
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-4">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {subtitle && (
          <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
        )}
      </div>
    </div>
  );
};

// Line Chart Component (Simple)
export const SimpleLineChart = ({ data, title, color = 'yellow' }) => {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        لا توجد بيانات لعرضها
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const range = maxValue - minValue || 1;

  const colorClasses = {
    yellow: 'stroke-yellow-500 fill-yellow-100',
    blue: 'stroke-blue-500 fill-blue-100',
    green: 'stroke-green-500 fill-green-100',
    red: 'stroke-red-500 fill-red-100'
  };

  // Generate SVG path
  const width = 400;
  const height = 200;
  const padding = 20;
  
  const points = data.map((item, index) => {
    const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
    const y = height - padding - ((item.value - minValue) / range) * (height - 2 * padding);
    return `${x},${y}`;
  }).join(' ');

  const pathData = `M ${points.split(' ').join(' L ')}`;

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center gap-2 mb-4">
        <FaChartLine className="text-yellow-500" />
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      
      <div className="relative">
        <svg width="100%" height={height} viewBox={`0 0 ${width} ${height}`}>
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          {/* Area under line */}
          <path
            d={`${pathData} L ${width - padding},${height - padding} L ${padding},${height - padding} Z`}
            className={colorClasses[color].split(' ')[1]}
            opacity="0.3"
          />
          
          {/* Line */}
          <path
            d={pathData}
            fill="none"
            className={colorClasses[color].split(' ')[0]}
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data points */}
          {data.map((item, index) => {
            const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
            const y = height - padding - ((item.value - minValue) / range) * (height - 2 * padding);
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="4"
                className={colorClasses[color].split(' ')[0]}
                fill="white"
                strokeWidth="2"
              />
            );
          })}
        </svg>
        
        {/* X-axis labels */}
        <div className="flex justify-between mt-2 px-5">
          {data.map((item, index) => (
            <span key={index} className="text-xs text-gray-500">
              {item.label}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

// Stats Comparison Chart
export const StatsComparisonChart = ({ currentMonth, previousMonth, title }) => {
  const percentage = previousMonth > 0 
    ? ((currentMonth - previousMonth) / previousMonth) * 100 
    : 0;
  
  const isPositive = percentage >= 0;

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <div className={`flex items-center gap-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
          {isPositive ? <FaArrowUp /> : <FaArrowDown />}
          <span className="text-sm font-medium">
            {Math.abs(percentage).toFixed(1)}%
          </span>
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-gray-600">هذا الشهر</span>
          <span className="text-2xl font-bold text-gray-900">{currentMonth}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-gray-600">الشهر الماضي</span>
          <span className="text-lg text-gray-600">{previousMonth}</span>
        </div>
        
        <div className="pt-2 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">التغيير</span>
            <span className={`text-sm font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {isPositive ? '+' : ''}{currentMonth - previousMonth}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Donut Chart Component
export const DonutChart = ({ data, title, centerText }) => {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        لا توجد بيانات لعرضها
      </div>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);
  const size = 200;
  const strokeWidth = 30;
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;

  let cumulativePercentage = 0;

  const colors = [
    '#F59E0B', // yellow-500
    '#3B82F6', // blue-500
    '#10B981', // green-500
    '#EF4444', // red-500
    '#8B5CF6', // purple-500
    '#F97316', // orange-500
  ];

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center gap-2 mb-4">
        <FaChartPie className="text-yellow-500" />
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      
      <div className="flex items-center gap-6">
        {/* Chart */}
        <div className="relative">
          <svg width={size} height={size} className="transform -rotate-90">
            {data.map((item, index) => {
              const percentage = (item.value / total) * 100;
              const strokeDasharray = circumference;
              const strokeDashoffset = circumference - (percentage / 100) * circumference;
              
              const rotation = (cumulativePercentage / 100) * 360;
              cumulativePercentage += percentage;
              
              return (
                <circle
                  key={index}
                  cx={size / 2}
                  cy={size / 2}
                  r={radius}
                  stroke={colors[index % colors.length]}
                  strokeWidth={strokeWidth}
                  fill="transparent"
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  strokeLinecap="round"
                  style={{
                    transformOrigin: `${size / 2}px ${size / 2}px`,
                    transform: `rotate(${rotation}deg)`
                  }}
                  className="transition-all duration-1000 ease-out"
                />
              );
            })}
          </svg>
          
          {/* Center text */}
          {centerText && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-xl font-bold text-gray-900">
                  {centerText}
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Legend */}
        <div className="space-y-2">
          {data.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <div 
                className="w-4 h-4 rounded"
                style={{ backgroundColor: colors[index % colors.length] }}
              ></div>
              <span className="text-sm text-gray-600">{item.label}</span>
              <span className="text-sm font-medium text-gray-900">
                {item.value} ({((item.value / total) * 100).toFixed(1)}%)
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Activity Heatmap Component
export const ActivityHeatmap = ({ data, title }) => {
  const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  const weeks = 12; // Show last 12 weeks
  
  const getIntensity = (value) => {
    if (value === 0) return 'bg-gray-100';
    if (value <= 2) return 'bg-yellow-200';
    if (value <= 5) return 'bg-yellow-400';
    if (value <= 8) return 'bg-yellow-500';
    return 'bg-yellow-600';
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center gap-2 mb-4">
        <FaChartLine className="text-yellow-500" />
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      
      <div className="space-y-2">
        {/* Days labels */}
        <div className="flex gap-1">
          <div className="w-12"></div>
          {Array.from({ length: weeks }, (_, i) => (
            <div key={i} className="w-3 h-3"></div>
          ))}
        </div>
        
        {/* Heatmap grid */}
        {days.map((day, dayIndex) => (
          <div key={day} className="flex items-center gap-1">
            <div className="w-12 text-xs text-gray-600 text-right">{day}</div>
            {Array.from({ length: weeks }, (_, weekIndex) => {
              const value = data?.[dayIndex]?.[weekIndex] || 0;
              return (
                <div
                  key={weekIndex}
                  className={`w-3 h-3 rounded-sm ${getIntensity(value)} border border-gray-200`}
                  title={`${day}: ${value} نشاط`}
                ></div>
              );
            })}
          </div>
        ))}
        
        {/* Legend */}
        <div className="flex items-center gap-2 mt-4 text-xs text-gray-600">
          <span>أقل</span>
          <div className="flex gap-1">
            <div className="w-3 h-3 bg-gray-100 rounded-sm border border-gray-200"></div>
            <div className="w-3 h-3 bg-yellow-200 rounded-sm border border-gray-200"></div>
            <div className="w-3 h-3 bg-yellow-400 rounded-sm border border-gray-200"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-sm border border-gray-200"></div>
            <div className="w-3 h-3 bg-yellow-600 rounded-sm border border-gray-200"></div>
          </div>
          <span>أكثر</span>
        </div>
      </div>
    </div>
  );
};
