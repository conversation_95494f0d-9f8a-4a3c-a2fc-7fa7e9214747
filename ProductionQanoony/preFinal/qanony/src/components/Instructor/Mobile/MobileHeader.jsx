import React, { useState } from 'react';
import { FaBars, FaTimes, FaUser, FaCog, FaSignOutAlt, FaBell } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';

const MobileHeader = ({ 
  instructor, 
  onMenuToggle, 
  isMenuOpen = false,
  notifications = [],
  onNotificationClick 
}) => {
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const navigate = useNavigate();

  const getProfileImageUrl = () => {
    if (instructor?.profile_image) {
      if (instructor.profile_image.startsWith('http')) {
        return instructor.profile_image;
      }
      return `http://localhost:8000${instructor.profile_image}`;
    }
    return '/default-instructor-avatar.png';
  };

  const handleLogout = () => {
    localStorage.removeItem('access');
    localStorage.removeItem('refresh');
    navigate('/login');
  };

  const unreadCount = notifications.filter(n => !n.is_read).length;

  return (
    <div className="lg:hidden">
      {/* Mobile Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Left: Menu Button */}
          <button
            onClick={onMenuToggle}
            className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors"
          >
            {isMenuOpen ? <FaTimes size={20} /> : <FaBars size={20} />}
          </button>

          {/* Center: Logo/Title */}
          <div className="flex items-center gap-2">
            <Link to="/" className="text-xl font-bold text-yellow-500">
              قانوني
            </Link>
          </div>

          {/* Right: Profile & Notifications */}
          <div className="flex items-center gap-2">
            {/* Notifications */}
            <button
              onClick={onNotificationClick}
              className="relative p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors"
            >
              <FaBell size={18} />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>

            {/* Profile */}
            <div className="relative">
              <button
                onClick={() => setShowProfileMenu(!showProfileMenu)}
                className="flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <img
                  src={getProfileImageUrl()}
                  alt="Profile"
                  className="w-8 h-8 rounded-full object-cover border border-gray-200"
                  onError={(e) => {
                    e.target.src = '/default-instructor-avatar.png';
                  }}
                />
              </button>

              {/* Profile Dropdown */}
              {showProfileMenu && (
                <div className="absolute left-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                  <div className="p-3 border-b border-gray-100">
                    <p className="font-medium text-gray-900 text-sm">
                      {instructor?.first_name} {instructor?.last_name}
                    </p>
                    <p className="text-xs text-gray-500">{instructor?.email}</p>
                  </div>
                  
                  <div className="py-1">
                    <Link
                      to="/instructor-profile"
                      className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      onClick={() => setShowProfileMenu(false)}
                    >
                      <FaUser className="text-gray-400" />
                      الملف الشخصي
                    </Link>
                    
                    <Link
                      to="/settings"
                      className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      onClick={() => setShowProfileMenu(false)}
                    >
                      <FaCog className="text-gray-400" />
                      الإعدادات
                    </Link>
                    
                    <button
                      onClick={() => {
                        setShowProfileMenu(false);
                        handleLogout();
                      }}
                      className="flex items-center gap-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      <FaSignOutAlt className="text-red-400" />
                      تسجيل الخروج
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={onMenuToggle}
        />
      )}

      {/* Mobile Menu Sidebar */}
      <div className={`
        fixed top-0 right-0 h-full w-80 bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out
        ${isMenuOpen ? 'translate-x-0' : 'translate-x-full'}
      `}>
        {/* Menu Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">القائمة</h2>
            <button
              onClick={onMenuToggle}
              className="p-2 rounded-lg text-gray-600 hover:bg-gray-100"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Menu Content */}
        <div className="p-4 space-y-4">
          {/* Profile Summary */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <img
              src={getProfileImageUrl()}
              alt="Profile"
              className="w-12 h-12 rounded-full object-cover border border-gray-200"
              onError={(e) => {
                e.target.src = '/default-instructor-avatar.png';
              }}
            />
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">
                {instructor?.first_name} {instructor?.last_name}
              </p>
              <p className="text-sm text-gray-500 truncate">{instructor?.email}</p>
              {instructor?.specialty && (
                <p className="text-xs text-gray-400 truncate">{instructor.specialty}</p>
              )}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-blue-50 p-3 rounded-lg text-center">
              <p className="text-2xl font-bold text-blue-600">
                {instructor?.subjects_count || 0}
              </p>
              <p className="text-xs text-blue-600">المواد</p>
            </div>
            <div className="bg-green-50 p-3 rounded-lg text-center">
              <p className="text-2xl font-bold text-green-600">
                {instructor?.students_count || 0}
              </p>
              <p className="text-xs text-green-600">الطلاب</p>
            </div>
          </div>

          {/* Menu Links */}
          <div className="space-y-2">
            <Link
              to="/instructor-profile"
              className="flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              onClick={onMenuToggle}
            >
              <FaUser className="text-gray-400" />
              <span>الملف الشخصي</span>
            </Link>
            
            <Link
              to="/settings"
              className="flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              onClick={onMenuToggle}
            >
              <FaCog className="text-gray-400" />
              <span>الإعدادات</span>
            </Link>
          </div>

          {/* Logout Button */}
          <div className="pt-4 border-t border-gray-200">
            <button
              onClick={() => {
                onMenuToggle();
                handleLogout();
              }}
              className="flex items-center gap-3 w-full p-3 rounded-lg text-red-600 hover:bg-red-50 transition-colors"
            >
              <FaSignOutAlt className="text-red-400" />
              <span>تسجيل الخروج</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileHeader;
