import { useState, useEffect, useRef } from 'react';

// Basic Swipe Hook
export const useSwipe = (onSwipeLeft, onSwipeRight, threshold = 50) => {
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);

  const onTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > threshold;
    const isRightSwipe = distance < -threshold;

    if (isLeftSwipe && onSwipeLeft) {
      onSwipeLeft();
    }
    if (isRightSwipe && onSwipeRight) {
      onSwipeRight();
    }
  };

  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd
  };
};

// Advanced Swipe Hook with Direction and Velocity
export const useAdvancedSwipe = (callbacks = {}, options = {}) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onSwipeStart,
    onSwipeEnd
  } = callbacks;

  const {
    threshold = 50,
    velocityThreshold = 0.3,
    preventDefaultTouchmoveEvent = false,
    trackMouse = false
  } = options;

  const [isSwiping, setIsSwiping] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0, time: 0 });
  const [currentPos, setCurrentPos] = useState({ x: 0, y: 0 });

  const getEventData = (e) => {
    const touch = e.touches ? e.touches[0] : e;
    return {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
  };

  const handleStart = (e) => {
    const eventData = getEventData(e);
    setStartPos(eventData);
    setCurrentPos({ x: eventData.x, y: eventData.y });
    setIsSwiping(true);
    
    if (onSwipeStart) {
      onSwipeStart(eventData);
    }
  };

  const handleMove = (e) => {
    if (!isSwiping) return;
    
    if (preventDefaultTouchmoveEvent) {
      e.preventDefault();
    }

    const eventData = getEventData(e);
    setCurrentPos({ x: eventData.x, y: eventData.y });
  };

  const handleEnd = (e) => {
    if (!isSwiping) return;
    
    setIsSwiping(false);
    
    const endPos = getEventData(e);
    const deltaX = endPos.x - startPos.x;
    const deltaY = endPos.y - startPos.y;
    const deltaTime = endPos.time - startPos.time;
    
    const velocityX = Math.abs(deltaX) / deltaTime;
    const velocityY = Math.abs(deltaY) / deltaTime;
    
    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);
    
    // Determine swipe direction
    if (absX > threshold || velocityX > velocityThreshold) {
      if (absX > absY) {
        if (deltaX > 0) {
          onSwipeRight && onSwipeRight({ deltaX, deltaY, velocity: velocityX });
        } else {
          onSwipeLeft && onSwipeLeft({ deltaX, deltaY, velocity: velocityX });
        }
      }
    }
    
    if (absY > threshold || velocityY > velocityThreshold) {
      if (absY > absX) {
        if (deltaY > 0) {
          onSwipeDown && onSwipeDown({ deltaX, deltaY, velocity: velocityY });
        } else {
          onSwipeUp && onSwipeUp({ deltaX, deltaY, velocity: velocityY });
        }
      }
    }
    
    if (onSwipeEnd) {
      onSwipeEnd({ deltaX, deltaY, velocityX, velocityY });
    }
  };

  const handlers = {
    onTouchStart: handleStart,
    onTouchMove: handleMove,
    onTouchEnd: handleEnd
  };

  if (trackMouse) {
    handlers.onMouseDown = handleStart;
    handlers.onMouseMove = handleMove;
    handlers.onMouseUp = handleEnd;
  }

  return {
    ...handlers,
    isSwiping,
    swipeProgress: {
      x: currentPos.x - startPos.x,
      y: currentPos.y - startPos.y
    }
  };
};

// Swipe Navigation Hook for Tabs
export const useSwipeNavigation = (items, currentIndex, onIndexChange, options = {}) => {
  const { 
    threshold = 50,
    enableLoop = true,
    animationDuration = 300
  } = options;

  const [isAnimating, setIsAnimating] = useState(false);

  const goToNext = () => {
    if (isAnimating) return;
    
    const nextIndex = currentIndex + 1;
    if (nextIndex < items.length) {
      setIsAnimating(true);
      onIndexChange(nextIndex);
      setTimeout(() => setIsAnimating(false), animationDuration);
    } else if (enableLoop) {
      setIsAnimating(true);
      onIndexChange(0);
      setTimeout(() => setIsAnimating(false), animationDuration);
    }
  };

  const goToPrevious = () => {
    if (isAnimating) return;
    
    const prevIndex = currentIndex - 1;
    if (prevIndex >= 0) {
      setIsAnimating(true);
      onIndexChange(prevIndex);
      setTimeout(() => setIsAnimating(false), animationDuration);
    } else if (enableLoop) {
      setIsAnimating(true);
      onIndexChange(items.length - 1);
      setTimeout(() => setIsAnimating(false), animationDuration);
    }
  };

  const swipeHandlers = useSwipe(goToNext, goToPrevious, threshold);

  return {
    ...swipeHandlers,
    goToNext,
    goToPrevious,
    isAnimating,
    canGoNext: currentIndex < items.length - 1 || enableLoop,
    canGoPrevious: currentIndex > 0 || enableLoop
  };
};

// Pull to Refresh Hook
export const usePullToRefresh = (onRefresh, options = {}) => {
  const {
    threshold = 80,
    maxPullDistance = 120,
    refreshingText = 'جاري التحديث...',
    pullText = 'اسحب للتحديث',
    releaseText = 'اتركه للتحديث'
  } = options;

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  const containerRef = useRef(null);

  const handleStart = (e) => {
    if (containerRef.current && containerRef.current.scrollTop === 0) {
      setIsPulling(true);
    }
  };

  const handleMove = (e) => {
    if (!isPulling || isRefreshing) return;

    const touch = e.touches[0];
    const startY = touch.clientY;
    
    // Calculate pull distance
    const distance = Math.min(startY / 2, maxPullDistance);
    setPullDistance(distance);
  };

  const handleEnd = async () => {
    if (!isPulling) return;
    
    setIsPulling(false);
    
    if (pullDistance >= threshold && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
        setPullDistance(0);
      }
    } else {
      setPullDistance(0);
    }
  };

  const getStatusText = () => {
    if (isRefreshing) return refreshingText;
    if (pullDistance >= threshold) return releaseText;
    return pullText;
  };

  const getProgress = () => {
    return Math.min(pullDistance / threshold, 1);
  };

  return {
    containerRef,
    onTouchStart: handleStart,
    onTouchMove: handleMove,
    onTouchEnd: handleEnd,
    isRefreshing,
    isPulling,
    pullDistance,
    statusText: getStatusText(),
    progress: getProgress(),
    style: {
      transform: `translateY(${pullDistance}px)`,
      transition: isPulling ? 'none' : 'transform 0.3s ease-out'
    }
  };
};

// Swipe to Delete Hook
export const useSwipeToDelete = (onDelete, options = {}) => {
  const {
    threshold = 100,
    deleteText = 'حذف',
    confirmDelete = true
  } = options;

  const [swipeDistance, setSwipeDistance] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleSwipeMove = (e) => {
    const touch = e.touches[0];
    const distance = Math.max(0, -touch.clientX);
    setSwipeDistance(Math.min(distance, threshold * 1.5));
  };

  const handleSwipeEnd = async () => {
    if (swipeDistance >= threshold) {
      if (confirmDelete) {
        const confirmed = window.confirm('هل أنت متأكد من الحذف؟');
        if (!confirmed) {
          setSwipeDistance(0);
          return;
        }
      }
      
      setIsDeleting(true);
      try {
        await onDelete();
      } finally {
        setIsDeleting(false);
        setSwipeDistance(0);
      }
    } else {
      setSwipeDistance(0);
    }
  };

  const getDeleteButtonOpacity = () => {
    return Math.min(swipeDistance / threshold, 1);
  };

  const shouldShowDeleteButton = () => {
    return swipeDistance > threshold * 0.3;
  };

  return {
    onTouchMove: handleSwipeMove,
    onTouchEnd: handleSwipeEnd,
    swipeDistance,
    isDeleting,
    deleteButtonOpacity: getDeleteButtonOpacity(),
    showDeleteButton: shouldShowDeleteButton(),
    deleteText,
    style: {
      transform: `translateX(-${swipeDistance}px)`,
      transition: isDeleting ? 'transform 0.3s ease-out' : 'none'
    }
  };
};

// Gesture Recognition Hook
export const useGestureRecognition = (callbacks = {}) => {
  const [gesture, setGesture] = useState(null);
  const touchPoints = useRef([]);

  const handleTouchStart = (e) => {
    touchPoints.current = Array.from(e.touches).map(touch => ({
      id: touch.identifier,
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    }));
  };

  const handleTouchMove = (e) => {
    if (touchPoints.current.length === 2 && e.touches.length === 2) {
      // Pinch gesture detection
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      
      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      const initialTouch1 = touchPoints.current.find(t => t.id === touch1.identifier);
      const initialTouch2 = touchPoints.current.find(t => t.id === touch2.identifier);
      
      if (initialTouch1 && initialTouch2) {
        const initialDistance = Math.sqrt(
          Math.pow(initialTouch2.x - initialTouch1.x, 2) +
          Math.pow(initialTouch2.y - initialTouch1.y, 2)
        );
        
        const scale = currentDistance / initialDistance;
        
        if (scale > 1.1) {
          setGesture('pinch-out');
          callbacks.onPinchOut && callbacks.onPinchOut(scale);
        } else if (scale < 0.9) {
          setGesture('pinch-in');
          callbacks.onPinchIn && callbacks.onPinchIn(scale);
        }
      }
    }
  };

  const handleTouchEnd = () => {
    setGesture(null);
    touchPoints.current = [];
  };

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    currentGesture: gesture
  };
};

export default {
  useSwipe,
  useAdvancedSwipe,
  useSwipeNavigation,
  usePullToRefresh,
  useSwipeToDelete,
  useGestureRecognition
};
