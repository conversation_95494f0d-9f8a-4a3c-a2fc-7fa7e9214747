import React, { useRef, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const MobileTabs = ({ 
  tabs, 
  activeTab, 
  onTabChange, 
  tabLoading = {},
  className = '' 
}) => {
  const scrollContainerRef = useRef(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // Check scroll position
  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollPosition();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollPosition);
      return () => container.removeEventListener('scroll', checkScrollPosition);
    }
  }, [tabs]);

  // Scroll to active tab
  useEffect(() => {
    if (scrollContainerRef.current) {
      const activeTabElement = scrollContainerRef.current.querySelector(`[data-tab="${activeTab}"]`);
      if (activeTabElement) {
        activeTabElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  }, [activeTab]);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -150,
        behavior: 'smooth'
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 150,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className={`lg:hidden relative ${className}`}>
      {/* Scroll Left Button */}
      {canScrollLeft && (
        <button
          onClick={scrollLeft}
          className="absolute left-0 top-0 bottom-0 z-10 bg-gradient-to-r from-white to-transparent w-8 flex items-center justify-start pl-1"
        >
          <div className="w-6 h-6 bg-white rounded-full shadow-md flex items-center justify-center">
            <svg className="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </div>
        </button>
      )}

      {/* Scroll Right Button */}
      {canScrollRight && (
        <button
          onClick={scrollRight}
          className="absolute right-0 top-0 bottom-0 z-10 bg-gradient-to-l from-white to-transparent w-8 flex items-center justify-end pr-1"
        >
          <div className="w-6 h-6 bg-white rounded-full shadow-md flex items-center justify-center">
            <svg className="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </button>
      )}

      {/* Tabs Container */}
      <div 
        ref={scrollContainerRef}
        className="flex overflow-x-auto scrollbar-hide border-b border-gray-200 bg-white"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {tabs.map((tab, index) => (
          <motion.button
            key={tab.key}
            data-tab={tab.key}
            onClick={() => onTabChange(tab.key)}
            className={`
              relative flex-shrink-0 px-4 py-3 text-sm font-medium whitespace-nowrap
              flex items-center gap-2 min-w-max transition-all duration-200
              ${activeTab === tab.key
                ? 'text-yellow-600 border-b-2 border-yellow-500 bg-yellow-50'
                : 'text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300'
              }
            `}
            whileTap={{ scale: 0.95 }}
            layout
          >
            {/* Tab Icon */}
            <span className={`
              transition-colors duration-200
              ${activeTab === tab.key ? 'text-yellow-600' : 'text-gray-400'}
            `}>
              {tab.icon}
            </span>

            {/* Tab Label */}
            <span className="font-medium">
              {tab.label}
            </span>

            {/* Loading Indicator */}
            {tabLoading[tab.key] && (
              <div className="w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin"></div>
            )}

            {/* Badge (if any) */}
            {tab.badge && (
              <span className="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {tab.badge > 9 ? '9+' : tab.badge}
              </span>
            )}

            {/* Active Tab Indicator */}
            {activeTab === tab.key && (
              <motion.div
                layoutId="activeTabIndicator"
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-yellow-500"
                initial={false}
                transition={{
                  type: "spring",
                  stiffness: 500,
                  damping: 30
                }}
              />
            )}
          </motion.button>
        ))}
      </div>

      {/* Tab Content Indicator */}
      <div className="bg-white px-4 py-2 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-yellow-600">
              {tabs.find(tab => tab.key === activeTab)?.icon}
            </span>
            <span className="text-sm font-medium text-gray-900">
              {tabs.find(tab => tab.key === activeTab)?.label}
            </span>
          </div>
          
          {/* Tab Counter */}
          <span className="text-xs text-gray-500">
            {tabs.findIndex(tab => tab.key === activeTab) + 1} من {tabs.length}
          </span>
        </div>
      </div>
    </div>
  );
};

// Swipe Gesture Hook
export const useSwipeGestures = (onSwipeLeft, onSwipeRight, threshold = 50) => {
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);

  const onTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > threshold;
    const isRightSwipe = distance < -threshold;

    if (isLeftSwipe && onSwipeLeft) {
      onSwipeLeft();
    }
    if (isRightSwipe && onSwipeRight) {
      onSwipeRight();
    }
  };

  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd
  };
};

// Mobile Tab Content Wrapper with Swipe Support
export const MobileTabContent = ({ 
  children, 
  activeTab, 
  tabs, 
  onTabChange,
  className = '' 
}) => {
  const currentIndex = tabs.findIndex(tab => tab.key === activeTab);
  
  const handleSwipeLeft = () => {
    if (currentIndex < tabs.length - 1) {
      onTabChange(tabs[currentIndex + 1].key);
    }
  };

  const handleSwipeRight = () => {
    if (currentIndex > 0) {
      onTabChange(tabs[currentIndex - 1].key);
    }
  };

  const swipeHandlers = useSwipeGestures(handleSwipeLeft, handleSwipeRight);

  return (
    <div 
      className={`lg:hidden ${className}`}
      {...swipeHandlers}
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.2 }}
          className="min-h-[200px]"
        >
          {children}
        </motion.div>
      </AnimatePresence>

      {/* Swipe Hint */}
      <div className="flex justify-center items-center gap-2 py-2 text-xs text-gray-400">
        {currentIndex > 0 && (
          <span className="flex items-center gap-1">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            اسحب يميناً
          </span>
        )}
        
        <span className="flex gap-1">
          {tabs.map((_, index) => (
            <div
              key={index}
              className={`w-1.5 h-1.5 rounded-full transition-colors ${
                index === currentIndex ? 'bg-yellow-500' : 'bg-gray-300'
              }`}
            />
          ))}
        </span>

        {currentIndex < tabs.length - 1 && (
          <span className="flex items-center gap-1">
            اسحب يساراً
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </span>
        )}
      </div>
    </div>
  );
};

export default MobileTabs;
