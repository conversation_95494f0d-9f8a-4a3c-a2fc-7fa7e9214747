import React, { useState } from 'react';
import { FaBook, FaUsers, FaCalendarCheck, FaStar, FaClock, FaComments, FaChartLine, FaEye, FaChevronRight } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';

const MobileStatCard = ({ title, value, icon, color, subtitle, trend, onClick }) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-blue-500 border-blue-200',
    green: 'bg-green-500 text-green-500 border-green-200',
    purple: 'bg-purple-500 text-purple-500 border-purple-200',
    yellow: 'bg-yellow-500 text-yellow-500 border-yellow-200',
    red: 'bg-red-500 text-red-500 border-red-200',
    indigo: 'bg-indigo-500 text-indigo-500 border-indigo-200'
  };

  return (
    <motion.div
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={`
        bg-white rounded-xl shadow-sm border-2 p-4 cursor-pointer
        hover:shadow-md transition-all duration-200
        ${colorClasses[color]?.replace('bg-', 'border-').replace('500', '100')}
      `}
    >
      <div className="flex items-center justify-between mb-3">
        <div className={`
          p-2 rounded-lg
          ${colorClasses[color]?.replace('text-', 'bg-').replace('500', '100')}
        `}>
          <div className={`${colorClasses[color]} text-lg`}>
            {icon}
          </div>
        </div>
        
        {trend && (
          <div className={`
            flex items-center gap-1 text-xs px-2 py-1 rounded-full
            ${trend.type === 'up' 
              ? 'bg-green-100 text-green-600' 
              : trend.type === 'down' 
                ? 'bg-red-100 text-red-600'
                : 'bg-gray-100 text-gray-600'
            }
          `}>
            {trend.type === 'up' && '↗'}
            {trend.type === 'down' && '↘'}
            {trend.type === 'neutral' && '→'}
            {trend.value}
          </div>
        )}
      </div>

      <div className="space-y-1">
        <p className="text-2xl font-bold text-gray-900">{value}</p>
        <p className="text-sm font-medium text-gray-700">{title}</p>
        {subtitle && (
          <p className="text-xs text-gray-500">{subtitle}</p>
        )}
      </div>

      <div className="flex items-center justify-end mt-2">
        <FaChevronRight className="text-gray-400 text-xs" />
      </div>
    </motion.div>
  );
};

const MobileStatsGrid = ({ stats, onStatClick }) => {
  const defaultStats = [
    {
      key: 'subjects',
      title: 'المواد الدراسية',
      value: stats?.subjects_count || 0,
      icon: <FaBook />,
      color: 'blue',
      subtitle: 'مادة نشطة',
      trend: { type: 'up', value: '+2' }
    },
    {
      key: 'students',
      title: 'إجمالي الطلاب',
      value: stats?.total_students || 0,
      icon: <FaUsers />,
      color: 'green',
      subtitle: 'طالب مسجل',
      trend: { type: 'up', value: '+12' }
    },
    {
      key: 'sessions',
      title: 'الجلسات المكتملة',
      value: stats?.completed_sessions || 0,
      icon: <FaCalendarCheck />,
      color: 'purple',
      subtitle: 'هذا الشهر',
      trend: { type: 'up', value: '+5' }
    },
    {
      key: 'rating',
      title: 'التقييم العام',
      value: stats?.average_rating || '0.0',
      icon: <FaStar />,
      color: 'yellow',
      subtitle: 'من 5 نجوم',
      trend: { type: 'up', value: '+0.2' }
    },
    {
      key: 'hours',
      title: 'ساعات التدريس',
      value: stats?.teaching_hours || 0,
      icon: <FaClock />,
      color: 'indigo',
      subtitle: 'هذا الشهر',
      trend: { type: 'up', value: '+8' }
    },
    {
      key: 'messages',
      title: 'الرسائل الجديدة',
      value: stats?.unread_messages || 0,
      icon: <FaComments />,
      color: 'red',
      subtitle: 'غير مقروءة',
      trend: { type: 'neutral', value: '0' }
    }
  ];

  return (
    <div className="lg:hidden p-4">
      <div className="grid grid-cols-2 gap-3">
        {defaultStats.map((stat, index) => (
          <motion.div
            key={stat.key}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <MobileStatCard
              {...stat}
              onClick={() => onStatClick && onStatClick(stat.key)}
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
};

const MobileStatsCarousel = ({ stats, onStatClick }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const statsArray = [
    {
      key: 'overview',
      title: 'نظرة عامة',
      items: [
        { label: 'المواد', value: stats?.subjects_count || 0, icon: <FaBook />, color: 'blue' },
        { label: 'الطلاب', value: stats?.total_students || 0, icon: <FaUsers />, color: 'green' },
        { label: 'الجلسات', value: stats?.completed_sessions || 0, icon: <FaCalendarCheck />, color: 'purple' }
      ]
    },
    {
      key: 'performance',
      title: 'الأداء',
      items: [
        { label: 'التقييم', value: stats?.average_rating || '0.0', icon: <FaStar />, color: 'yellow' },
        { label: 'الساعات', value: stats?.teaching_hours || 0, icon: <FaClock />, color: 'indigo' },
        { label: 'المشاهدات', value: stats?.profile_views || 0, icon: <FaEye />, color: 'red' }
      ]
    }
  ];

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % statsArray.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + statsArray.length) % statsArray.length);
  };

  return (
    <div className="lg:hidden p-4">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100">
          <h3 className="font-semibold text-gray-900">
            {statsArray[currentIndex].title}
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={prevSlide}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
            >
              <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <span className="text-xs text-gray-500">
              {currentIndex + 1}/{statsArray.length}
            </span>
            <button
              onClick={nextSlide}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
            >
              <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
            className="p-4"
          >
            <div className="grid grid-cols-3 gap-3">
              {statsArray[currentIndex].items.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => onStatClick && onStatClick(item.key)}
                  className="text-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer"
                >
                  <div className={`
                    inline-flex p-2 rounded-lg mb-2
                    ${item.color === 'blue' && 'bg-blue-100 text-blue-600'}
                    ${item.color === 'green' && 'bg-green-100 text-green-600'}
                    ${item.color === 'purple' && 'bg-purple-100 text-purple-600'}
                    ${item.color === 'yellow' && 'bg-yellow-100 text-yellow-600'}
                    ${item.color === 'indigo' && 'bg-indigo-100 text-indigo-600'}
                    ${item.color === 'red' && 'bg-red-100 text-red-600'}
                  `}>
                    {item.icon}
                  </div>
                  <p className="text-lg font-bold text-gray-900">{item.value}</p>
                  <p className="text-xs text-gray-600">{item.label}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Indicators */}
        <div className="flex justify-center gap-1 p-3 border-t border-gray-100">
          {statsArray.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentIndex ? 'bg-yellow-500' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

const MobileQuickActions = ({ onActionClick }) => {
  const actions = [
    { key: 'add-subject', label: 'إضافة مادة', icon: <FaBook />, color: 'blue' },
    { key: 'view-students', label: 'عرض الطلاب', icon: <FaUsers />, color: 'green' },
    { key: 'schedule-session', label: 'جدولة جلسة', icon: <FaCalendarCheck />, color: 'purple' },
    { key: 'view-messages', label: 'الرسائل', icon: <FaComments />, color: 'red' }
  ];

  return (
    <div className="lg:hidden p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">إجراءات سريعة</h3>
      <div className="grid grid-cols-2 gap-3">
        {actions.map((action, index) => (
          <motion.button
            key={action.key}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onActionClick && onActionClick(action.key)}
            className={`
              p-4 rounded-xl border-2 bg-white hover:shadow-md transition-all duration-200
              ${action.color === 'blue' && 'border-blue-100 hover:border-blue-200'}
              ${action.color === 'green' && 'border-green-100 hover:border-green-200'}
              ${action.color === 'purple' && 'border-purple-100 hover:border-purple-200'}
              ${action.color === 'red' && 'border-red-100 hover:border-red-200'}
            `}
          >
            <div className={`
              inline-flex p-3 rounded-lg mb-2
              ${action.color === 'blue' && 'bg-blue-100 text-blue-600'}
              ${action.color === 'green' && 'bg-green-100 text-green-600'}
              ${action.color === 'purple' && 'bg-purple-100 text-purple-600'}
              ${action.color === 'red' && 'bg-red-100 text-red-600'}
            `}>
              {action.icon}
            </div>
            <p className="text-sm font-medium text-gray-900">{action.label}</p>
          </motion.button>
        ))}
      </div>
    </div>
  );
};

export { MobileStatCard, MobileStatsGrid, MobileStatsCarousel, MobileQuickActions };
export default MobileStatsGrid;
