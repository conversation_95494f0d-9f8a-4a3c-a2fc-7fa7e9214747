import React from 'react';
import { FaCamera, FaEdit, FaEnvelope, FaPhone, FaCalendar, FaUser } from 'react-icons/fa';

const InstructorProfileHeader = ({ instructor, onEditClick, onImageClick }) => {
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getProfileImageUrl = () => {
    if (instructor?.profile_image) {
      // إذا كان URL كامل
      if (instructor.profile_image.startsWith('http')) {
        return instructor.profile_image;
      }
      // إذا كان مسار نسبي
      return `http://localhost:8000${instructor.profile_image}`;
    }
    return '/default-instructor-avatar.png';
  };

  return (
    <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-lg p-6 text-white shadow-lg">
      <div className="flex flex-col md:flex-row items-center gap-6">
        {/* Profile Image */}
        <div className="relative">
          <img 
            src={getProfileImageUrl()}
            alt={`${instructor?.first_name || ''} ${instructor?.last_name || ''}`}
            className="w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-white object-cover"
            onError={(e) => {
              e.target.src = '/default-instructor-avatar.png';
            }}
          />
          <button 
            onClick={onImageClick}
            className="absolute bottom-0 right-0 bg-white text-yellow-500 rounded-full p-2 shadow-lg hover:bg-yellow-50 transition-colors"
            title="تغيير الصورة الشخصية"
          >
            <FaCamera size={16} />
          </button>
        </div>
        
        {/* Profile Info */}
        <div className="flex-1 text-center md:text-right">
          <h1 className="text-3xl md:text-4xl font-bold mb-2">
            {instructor?.first_name || ''} {instructor?.last_name || ''}
          </h1>
          <p className="text-yellow-100 text-lg md:text-xl mb-3 flex items-center justify-center md:justify-start gap-2">
            <FaUser size={16} />
            {instructor?.specialty || 'مدرس قانون'}
          </p>
          
          <div className="flex flex-col md:flex-row gap-4 text-yellow-200">
            <div className="flex items-center justify-center md:justify-start gap-2">
              <FaEnvelope size={16} />
              <span>{instructor?.email || ''}</span>
            </div>
            {instructor?.phone_number && (
              <div className="flex items-center justify-center md:justify-start gap-2">
                <FaPhone size={16} />
                <span>{instructor.phone_number}</span>
              </div>
            )}
            <div className="flex items-center justify-center md:justify-start gap-2">
              <FaCalendar size={16} />
              <span>انضم في {formatDate(instructor?.date_joined)}</span>
            </div>
          </div>
        </div>
        
        {/* Edit Button */}
        <button 
          onClick={onEditClick}
          className="bg-white text-yellow-500 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-50 transition-colors flex items-center gap-2 shadow-lg"
        >
          <FaEdit size={16} />
          تعديل البيانات
        </button>
      </div>
      
      {/* Bio */}
      {instructor?.bio && (
        <div className="mt-6 pt-6 border-t border-yellow-300">
          <p className="text-yellow-100 leading-relaxed">
            {instructor.bio}
          </p>
        </div>
      )}
    </div>
  );
};

export default InstructorProfileHeader;
