import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aSort, FaUsers, FaEye, FaEdit } from 'react-icons/fa';

const SubjectCard = ({ subject, onView, onEdit }) => {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {subject.name_ar}
          </h3>
          {subject.name_en && (
            <p className="text-sm text-gray-600 mb-2">{subject.name_en}</p>
          )}
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span className="flex items-center gap-1">
              <FaBook className="text-yellow-500" />
              كود: {subject.code}
            </span>
            <span className="flex items-center gap-1">
              <FaUsers className="text-blue-500" />
              {subject.students_count || 0} طالب
            </span>
          </div>
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => onView(subject)}
            className="p-2 text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
            title="عرض التفاصيل"
          >
            <FaEye />
          </button>
          <button
            onClick={() => onEdit(subject)}
            className="p-2 text-yellow-500 hover:bg-yellow-50 rounded-lg transition-colors"
            title="تعديل"
          >
            <FaEdit />
          </button>
        </div>
      </div>

      {/* Subject Details */}
      <div className="space-y-2">
        {subject.semester && (
          <div className="text-sm">
            <span className="font-medium text-gray-700">الفصل الدراسي: </span>
            <span className="text-gray-600">{subject.semester.title_ar}</span>
          </div>
        )}
        {subject.academic_year && (
          <div className="text-sm">
            <span className="font-medium text-gray-700">السنة الأكاديمية: </span>
            <span className="text-gray-600">{subject.academic_year.title_ar}</span>
          </div>
        )}
        {subject.description_ar && (
          <div className="text-sm">
            <span className="font-medium text-gray-700">الوصف: </span>
            <span className="text-gray-600">{subject.description_ar}</span>
          </div>
        )}
      </div>

      {/* Stats */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-lg font-semibold text-gray-900">{subject.lectures_count || 0}</p>
            <p className="text-xs text-gray-500">محاضرة</p>
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900">{subject.quizzes_count || 0}</p>
            <p className="text-xs text-gray-500">اختبار</p>
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900">{subject.students_count || 0}</p>
            <p className="text-xs text-gray-500">طالب</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const InstructorSubjectsList = ({ subjects, loading, onRefresh }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name_ar');
  const [filteredSubjects, setFilteredSubjects] = useState([]);

  useEffect(() => {
    if (!subjects) return;

    let filtered = [...subjects];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(subject =>
        subject.name_ar?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        subject.name_en?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        subject.code?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name_ar':
          return (a.name_ar || '').localeCompare(b.name_ar || '');
        case '-name_ar':
          return (b.name_ar || '').localeCompare(a.name_ar || '');
        case 'code':
          return (a.code || '').localeCompare(b.code || '');
        case '-code':
          return (b.code || '').localeCompare(a.code || '');
        case 'students_count':
          return (a.students_count || 0) - (b.students_count || 0);
        case '-students_count':
          return (b.students_count || 0) - (a.students_count || 0);
        default:
          return 0;
      }
    });

    setFilteredSubjects(filtered);
  }, [subjects, searchTerm, sortBy]);

  const handleView = (subject) => {
    console.log('View subject:', subject);
    // TODO: Implement view functionality
  };

  const handleEdit = (subject) => {
    console.log('Edit subject:', subject);
    // TODO: Implement edit functionality
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
              <div className="h-6 bg-gray-200 rounded mb-4"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="grid grid-cols-3 gap-4">
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
          <FaBook className="text-yellow-500" />
          المواد التي أدرسها ({subjects?.length || 0})
        </h2>
        
        <button
          onClick={onRefresh}
          className="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
        >
          تحديث
        </button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 relative">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="البحث في المواد..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <FaSort className="text-gray-400" />
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
          >
            <option value="name_ar">الاسم (أ-ي)</option>
            <option value="-name_ar">الاسم (ي-أ)</option>
            <option value="code">الكود (أ-ي)</option>
            <option value="-code">الكود (ي-أ)</option>
            <option value="-students_count">عدد الطلاب (الأكثر)</option>
            <option value="students_count">عدد الطلاب (الأقل)</option>
          </select>
        </div>
      </div>

      {/* Subjects Grid */}
      {!subjects || subjects.length === 0 ? (
        <div className="text-center py-12">
          <FaBook className="mx-auto text-6xl text-gray-300 mb-4" />
          <p className="text-gray-500 text-lg">لا توجد مواد متاحة</p>
        </div>
      ) : filteredSubjects.length === 0 ? (
        <div className="text-center py-12">
          <FaSearch className="mx-auto text-6xl text-gray-300 mb-4" />
          <p className="text-gray-500 text-lg">لا توجد نتائج للبحث</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSubjects.map((subject) => (
            <SubjectCard
              key={subject.id}
              subject={subject}
              onView={handleView}
              onEdit={handleEdit}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default InstructorSubjectsList;
