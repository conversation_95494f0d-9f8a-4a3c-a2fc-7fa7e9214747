import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';

// Virtual Scrolling Hook
export const useVirtualScrolling = ({
  items,
  itemHeight,
  containerHeight,
  overscan = 5
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef(null);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.floor((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = useMemo(() => {
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      ...item,
      index: startIndex + index
    }));
  }, [items, startIndex, endIndex]);

  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return {
    scrollElementRef,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    startIndex,
    endIndex
  };
};

// Virtual List Component
export const VirtualList = ({
  items,
  itemHeight,
  containerHeight = 400,
  renderItem,
  className = '',
  onScroll,
  ...props
}) => {
  const {
    scrollElementRef,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll
  } = useVirtualScrolling({
    items,
    itemHeight,
    containerHeight
  });

  const combinedHandleScroll = useCallback((e) => {
    handleScroll(e);
    onScroll?.(e);
  }, [handleScroll, onScroll]);

  return (
    <div
      ref={scrollElementRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={combinedHandleScroll}
      {...props}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item) => (
            <div key={item.id || item.index} style={{ height: itemHeight }}>
              {renderItem(item, item.index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Virtual Grid Component
export const VirtualGrid = ({
  items,
  itemWidth,
  itemHeight,
  containerWidth,
  containerHeight = 400,
  renderItem,
  className = '',
  gap = 0,
  ...props
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef(null);

  const columnsCount = Math.floor((containerWidth + gap) / (itemWidth + gap));
  const rowsCount = Math.ceil(items.length / columnsCount);

  const startRowIndex = Math.max(0, Math.floor(scrollTop / (itemHeight + gap)) - 2);
  const endRowIndex = Math.min(
    rowsCount - 1,
    Math.floor((scrollTop + containerHeight) / (itemHeight + gap)) + 2
  );

  const visibleItems = useMemo(() => {
    const visible = [];
    for (let rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {
      for (let colIndex = 0; colIndex < columnsCount; colIndex++) {
        const itemIndex = rowIndex * columnsCount + colIndex;
        if (itemIndex < items.length) {
          visible.push({
            ...items[itemIndex],
            index: itemIndex,
            rowIndex,
            colIndex,
            x: colIndex * (itemWidth + gap),
            y: rowIndex * (itemHeight + gap)
          });
        }
      }
    }
    return visible;
  }, [items, startRowIndex, endRowIndex, columnsCount, itemWidth, itemHeight, gap]);

  const totalHeight = rowsCount * (itemHeight + gap) - gap;
  const offsetY = startRowIndex * (itemHeight + gap);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return (
    <div
      ref={scrollElementRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
      {...props}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item) => (
            <div
              key={item.id || item.index}
              style={{
                position: 'absolute',
                left: item.x,
                top: item.y - offsetY,
                width: itemWidth,
                height: itemHeight
              }}
            >
              {renderItem(item, item.index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Virtual Table Component
export const VirtualTable = ({
  data,
  columns,
  rowHeight = 50,
  headerHeight = 40,
  containerHeight = 400,
  className = '',
  onRowClick,
  ...props
}) => {
  const {
    scrollElementRef,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll
  } = useVirtualScrolling({
    items: data,
    itemHeight: rowHeight,
    containerHeight: containerHeight - headerHeight
  });

  const handleRowClick = useCallback((item, index) => {
    onRowClick?.(item, index);
  }, [onRowClick]);

  return (
    <div className={`border border-gray-200 rounded-lg overflow-hidden ${className}`} {...props}>
      {/* Table Header */}
      <div 
        className="bg-gray-50 border-b border-gray-200 flex"
        style={{ height: headerHeight }}
      >
        {columns.map((column, index) => (
          <div
            key={column.key || index}
            className="px-4 py-2 font-medium text-gray-900 border-r border-gray-200 last:border-r-0 flex items-center"
            style={{ width: column.width || 'auto', minWidth: column.minWidth || 100 }}
          >
            {column.title}
          </div>
        ))}
      </div>

      {/* Table Body */}
      <div
        ref={scrollElementRef}
        className="overflow-auto"
        style={{ height: containerHeight - headerHeight }}
        onScroll={handleScroll}
      >
        <div style={{ height: totalHeight, position: 'relative' }}>
          <div style={{ transform: `translateY(${offsetY}px)` }}>
            {visibleItems.map((item) => (
              <div
                key={item.id || item.index}
                className="flex border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                style={{ height: rowHeight }}
                onClick={() => handleRowClick(item, item.index)}
              >
                {columns.map((column, colIndex) => (
                  <div
                    key={column.key || colIndex}
                    className="px-4 py-2 border-r border-gray-100 last:border-r-0 flex items-center"
                    style={{ width: column.width || 'auto', minWidth: column.minWidth || 100 }}
                  >
                    {column.render ? column.render(item[column.key], item, item.index) : item[column.key]}
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Infinite Scroll Hook
export const useInfiniteScroll = ({
  hasNextPage,
  fetchNextPage,
  threshold = 100
}) => {
  const [isFetching, setIsFetching] = useState(false);
  const scrollElementRef = useRef(null);

  const handleScroll = useCallback(() => {
    const element = scrollElementRef.current;
    if (!element || isFetching || !hasNextPage) return;

    const { scrollTop, scrollHeight, clientHeight } = element;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    if (distanceFromBottom < threshold) {
      setIsFetching(true);
      fetchNextPage().finally(() => {
        setIsFetching(false);
      });
    }
  }, [isFetching, hasNextPage, fetchNextPage, threshold]);

  useEffect(() => {
    const element = scrollElementRef.current;
    if (!element) return;

    element.addEventListener('scroll', handleScroll);
    return () => element.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return { scrollElementRef, isFetching };
};

// Infinite Scroll Component
export const InfiniteScrollList = ({
  items,
  renderItem,
  hasNextPage,
  fetchNextPage,
  loading = false,
  itemHeight,
  containerHeight = 400,
  threshold = 100,
  loadingComponent = <div className="p-4 text-center">جاري التحميل...</div>,
  className = ''
}) => {
  const { scrollElementRef, isFetching } = useInfiniteScroll({
    hasNextPage,
    fetchNextPage,
    threshold
  });

  // Use virtual scrolling if itemHeight is provided
  if (itemHeight) {
    const {
      visibleItems,
      totalHeight,
      offsetY,
      handleScroll
    } = useVirtualScrolling({
      items,
      itemHeight,
      containerHeight
    });

    const combinedHandleScroll = useCallback((e) => {
      handleScroll(e);
      
      // Check for infinite scroll
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      
      if (distanceFromBottom < threshold && hasNextPage && !isFetching) {
        fetchNextPage();
      }
    }, [handleScroll, hasNextPage, isFetching, fetchNextPage, threshold]);

    return (
      <div
        ref={scrollElementRef}
        className={`overflow-auto ${className}`}
        style={{ height: containerHeight }}
        onScroll={combinedHandleScroll}
      >
        <div style={{ height: totalHeight, position: 'relative' }}>
          <div style={{ transform: `translateY(${offsetY}px)` }}>
            {visibleItems.map((item) => (
              <div key={item.id || item.index} style={{ height: itemHeight }}>
                {renderItem(item, item.index)}
              </div>
            ))}
          </div>
        </div>
        {(loading || isFetching) && loadingComponent}
      </div>
    );
  }

  // Regular infinite scroll without virtualization
  return (
    <div
      ref={scrollElementRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
    >
      {items.map((item, index) => (
        <div key={item.id || index}>
          {renderItem(item, index)}
        </div>
      ))}
      {(loading || isFetching) && loadingComponent}
    </div>
  );
};

// Performance optimized list with search and filter
export const OptimizedList = ({
  items,
  renderItem,
  searchTerm = '',
  filters = {},
  sortBy = '',
  sortOrder = 'asc',
  itemHeight,
  containerHeight = 400,
  className = ''
}) => {
  // Memoized filtering and sorting
  const processedItems = useMemo(() => {
    let filtered = items;

    // Apply search
    if (searchTerm.trim()) {
      const lowercaseSearch = searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        Object.values(item).some(value =>
          typeof value === 'string' && value.toLowerCase().includes(lowercaseSearch)
        )
      );
    }

    // Apply filters
    if (Object.keys(filters).length > 0) {
      filtered = filtered.filter(item =>
        Object.entries(filters).every(([key, value]) => {
          if (!value || (Array.isArray(value) && value.length === 0)) return true;
          if (Array.isArray(value)) return value.includes(item[key]);
          return item[key] === value;
        })
      );
    }

    // Apply sorting
    if (sortBy) {
      filtered = [...filtered].sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });
    }

    return filtered;
  }, [items, searchTerm, filters, sortBy, sortOrder]);

  if (itemHeight) {
    return (
      <VirtualList
        items={processedItems}
        itemHeight={itemHeight}
        containerHeight={containerHeight}
        renderItem={renderItem}
        className={className}
      />
    );
  }

  return (
    <div className={`overflow-auto ${className}`} style={{ height: containerHeight }}>
      {processedItems.map((item, index) => (
        <div key={item.id || index}>
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  );
};

export default {
  VirtualList,
  VirtualGrid,
  VirtualTable,
  InfiniteScrollList,
  OptimizedList,
  useVirtualScrolling,
  useInfiniteScroll
};
