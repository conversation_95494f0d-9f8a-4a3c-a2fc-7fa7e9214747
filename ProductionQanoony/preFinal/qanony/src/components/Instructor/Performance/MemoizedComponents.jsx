import React, { memo, useMemo, useCallback, useState, useEffect } from 'react';

// Memoized Stat Card Component
export const MemoizedStatCard = memo(({ title, value, icon, color, subtitle, trend }) => {
  const colorClasses = useMemo(() => ({
    blue: 'bg-blue-500 text-blue-500',
    green: 'bg-green-500 text-green-500',
    purple: 'bg-purple-500 text-purple-500',
    yellow: 'bg-yellow-500 text-yellow-500',
    red: 'bg-red-500 text-red-500',
    indigo: 'bg-indigo-500 text-indigo-500'
  }), []);

  const cardStyle = useMemo(() => 
    colorClasses[color] || colorClasses.blue
  , [color, colorClasses]);

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-gray-600 text-sm font-medium mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mb-1">{value}</p>
          {subtitle && (
            <p className="text-gray-500 text-xs">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${cardStyle.replace('text-', 'bg-').replace('500', '100')}`}>
          <div className={`${cardStyle} text-xl`}>
            {icon}
          </div>
        </div>
      </div>
      {trend && (
        <div className="mt-2 flex items-center text-sm">
          <span className={`${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {trend.isPositive ? '↗' : '↘'} {trend.value}
          </span>
          <span className="text-gray-500 ml-1">من الشهر الماضي</span>
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function
  return (
    prevProps.title === nextProps.title &&
    prevProps.value === nextProps.value &&
    prevProps.color === nextProps.color &&
    prevProps.subtitle === nextProps.subtitle &&
    JSON.stringify(prevProps.trend) === JSON.stringify(nextProps.trend)
  );
});

MemoizedStatCard.displayName = 'MemoizedStatCard';

// Memoized Subject Card Component
export const MemoizedSubjectCard = memo(({ subject, onView, onEdit }) => {
  const handleView = useCallback(() => {
    onView(subject);
  }, [subject, onView]);

  const handleEdit = useCallback(() => {
    onEdit(subject);
  }, [subject, onEdit]);

  const studentsCount = useMemo(() => 
    subject.students_count || 0
  , [subject.students_count]);

  const subjectCode = useMemo(() => 
    subject.code || 'غير محدد'
  , [subject.code]);

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {subject.name_ar}
          </h3>
          {subject.name_en && (
            <p className="text-sm text-gray-600 mb-2">{subject.name_en}</p>
          )}
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span className="flex items-center gap-1">
              كود: {subjectCode}
            </span>
            <span className="flex items-center gap-1">
              {studentsCount} طالب
            </span>
          </div>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleView}
            className="p-2 text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
            title="عرض التفاصيل"
          >
            👁
          </button>
          <button
            onClick={handleEdit}
            className="p-2 text-yellow-500 hover:bg-yellow-50 rounded-lg transition-colors"
            title="تعديل"
          >
            ✏️
          </button>
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.subject.id === nextProps.subject.id &&
    prevProps.subject.name_ar === nextProps.subject.name_ar &&
    prevProps.subject.students_count === nextProps.subject.students_count &&
    prevProps.onView === nextProps.onView &&
    prevProps.onEdit === nextProps.onEdit
  );
});

MemoizedSubjectCard.displayName = 'MemoizedSubjectCard';

// Memoized Student Card Component
export const MemoizedStudentCard = memo(({ student, onView, onContact }) => {
  const handleView = useCallback(() => {
    onView(student);
  }, [student, onView]);

  const handleContact = useCallback(() => {
    onContact(student);
  }, [student, onContact]);

  const profileImageUrl = useMemo(() => {
    if (student?.profile_image) {
      if (student.profile_image.startsWith('http')) {
        return student.profile_image;
      }
      return `http://localhost:8000${student.profile_image}`;
    }
    return '/default-student-avatar.png';
  }, [student?.profile_image]);

  const fullName = useMemo(() => 
    `${student.first_name} ${student.last_name}`
  , [student.first_name, student.last_name]);

  const formattedDate = useMemo(() => {
    if (!student.date_joined) return '';
    return new Date(student.date_joined).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }, [student.date_joined]);

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
      <div className="flex items-start gap-4">
        <img 
          src={profileImageUrl}
          alt={fullName}
          className="w-16 h-16 rounded-full object-cover border-2 border-yellow-200"
          onError={(e) => {
            e.target.src = '/default-student-avatar.png';
          }}
        />
        
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {fullName}
          </h3>
          <p className="text-sm text-gray-600 mb-2">{student.email}</p>
          
          <div className="space-y-1 text-sm text-gray-500">
            {student.phone_number && (
              <div className="flex items-center gap-2">
                <span>📞 {student.phone_number}</span>
              </div>
            )}
            {formattedDate && (
              <div className="flex items-center gap-2">
                <span>📅 انضم في {formattedDate}</span>
              </div>
            )}
          </div>
        </div>

        <div className="flex gap-2">
          <button
            onClick={handleView}
            className="p-2 text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
            title="عرض الملف الشخصي"
          >
            👁
          </button>
          <button
            onClick={handleContact}
            className="p-2 text-green-500 hover:bg-green-50 rounded-lg transition-colors"
            title="التواصل"
          >
            💬
          </button>
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.student.id === nextProps.student.id &&
    prevProps.student.first_name === nextProps.student.first_name &&
    prevProps.student.last_name === nextProps.student.last_name &&
    prevProps.student.email === nextProps.student.email &&
    prevProps.student.profile_image === nextProps.student.profile_image &&
    prevProps.onView === nextProps.onView &&
    prevProps.onContact === nextProps.onContact
  );
});

MemoizedStudentCard.displayName = 'MemoizedStudentCard';

// Memoized List Component with Virtual Scrolling Support
export const MemoizedVirtualList = memo(({ 
  items, 
  renderItem, 
  itemHeight = 100, 
  containerHeight = 400,
  className = ''
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return items.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      index: startIndex + index
    }));
  }, [items, scrollTop, itemHeight, containerHeight]);

  const totalHeight = useMemo(() => 
    items.length * itemHeight
  , [items.length, itemHeight]);

  const offsetY = useMemo(() => 
    Math.floor(scrollTop / itemHeight) * itemHeight
  , [scrollTop, itemHeight]);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return (
    <div 
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item) => (
            <div key={item.id || item.index} style={{ height: itemHeight }}>
              {renderItem(item, item.index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.items.length === nextProps.items.length &&
    prevProps.itemHeight === nextProps.itemHeight &&
    prevProps.containerHeight === nextProps.containerHeight &&
    prevProps.renderItem === nextProps.renderItem
  );
});

MemoizedVirtualList.displayName = 'MemoizedVirtualList';

// Performance Monitoring Hook
export const usePerformanceMonitor = (componentName) => {
  const [renderTime, setRenderTime] = useState(0);
  const [renderCount, setRenderCount] = useState(0);

  useEffect(() => {
    const startTime = performance.now();
    setRenderCount(prev => prev + 1);
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      setRenderTime(duration);
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render time: ${duration.toFixed(2)}ms (render #${renderCount + 1})`);
      }
    };
  });

  return { renderTime, renderCount };
};

// Debounced Search Hook
export const useDebouncedSearch = (searchTerm, delay = 300) => {
  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm, delay]);

  return debouncedTerm;
};

// Memoized Search Results
export const useMemoizedSearch = (items, searchTerm, searchFields = []) => {
  return useMemo(() => {
    if (!searchTerm.trim()) return items;
    
    const lowercaseSearch = searchTerm.toLowerCase();
    
    return items.filter(item => {
      if (searchFields.length === 0) {
        // Search in all string properties
        return Object.values(item).some(value => 
          typeof value === 'string' && 
          value.toLowerCase().includes(lowercaseSearch)
        );
      }
      
      // Search in specific fields
      return searchFields.some(field => {
        const value = item[field];
        return typeof value === 'string' && 
               value.toLowerCase().includes(lowercaseSearch);
      });
    });
  }, [items, searchTerm, searchFields]);
};

// Memoized Filter Results
export const useMemoizedFilter = (items, filters) => {
  return useMemo(() => {
    if (!filters || Object.keys(filters).length === 0) return items;
    
    return items.filter(item => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value || (Array.isArray(value) && value.length === 0)) return true;
        
        if (Array.isArray(value)) {
          return value.includes(item[key]);
        }
        
        return item[key] === value;
      });
    });
  }, [items, filters]);
};

// Memoized Sort Results
export const useMemoizedSort = (items, sortBy, sortOrder = 'asc') => {
  return useMemo(() => {
    if (!sortBy) return items;
    
    return [...items].sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      // Handle different data types
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });
  }, [items, sortBy, sortOrder]);
};

export default {
  MemoizedStatCard,
  MemoizedSubjectCard,
  MemoizedStudentCard,
  MemoizedVirtualList,
  usePerformanceMonitor,
  useDebouncedSearch,
  useMemoizedSearch,
  useMemoizedFilter,
  useMemoizedSort
};
