import React, { useEffect, useRef, useState, useCallback } from 'react';

// Performance Metrics Hook
export const usePerformanceMetrics = (componentName) => {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    renderCount: 0,
    memoryUsage: 0,
    lastRenderTime: 0
  });

  const startTimeRef = useRef(0);
  const renderCountRef = useRef(0);

  useEffect(() => {
    startTimeRef.current = performance.now();
    renderCountRef.current += 1;

    // Measure memory usage (if available)
    const measureMemory = () => {
      if ('memory' in performance) {
        return performance.memory.usedJSHeapSize / 1024 / 1024; // MB
      }
      return 0;
    };

    const endTime = performance.now();
    const renderTime = endTime - startTimeRef.current;
    const memoryUsage = measureMemory();

    setMetrics(prev => ({
      renderTime: (prev.renderTime + renderTime) / renderCountRef.current,
      renderCount: renderCountRef.current,
      memoryUsage,
      lastRenderTime: renderTime
    }));

    // Log performance in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} Performance:`, {
        renderTime: renderTime.toFixed(2) + 'ms',
        renderCount: renderCountRef.current,
        memoryUsage: memoryUsage.toFixed(2) + 'MB'
      });
    }
  });

  return metrics;
};

// API Performance Monitor
export const useAPIPerformanceMonitor = () => {
  const [apiMetrics, setApiMetrics] = useState({});

  const trackAPICall = useCallback(async (apiName, apiCall) => {
    const startTime = performance.now();
    
    try {
      const result = await apiCall();
      const endTime = performance.now();
      const duration = endTime - startTime;

      setApiMetrics(prev => ({
        ...prev,
        [apiName]: {
          ...prev[apiName],
          lastCallDuration: duration,
          averageDuration: prev[apiName] 
            ? (prev[apiName].averageDuration + duration) / 2 
            : duration,
          successCount: (prev[apiName]?.successCount || 0) + 1,
          totalCalls: (prev[apiName]?.totalCalls || 0) + 1
        }
      }));

      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      setApiMetrics(prev => ({
        ...prev,
        [apiName]: {
          ...prev[apiName],
          lastCallDuration: duration,
          errorCount: (prev[apiName]?.errorCount || 0) + 1,
          totalCalls: (prev[apiName]?.totalCalls || 0) + 1
        }
      }));

      throw error;
    }
  }, []);

  return { apiMetrics, trackAPICall };
};

// Performance Dashboard Component
export const PerformanceDashboard = ({ show = false }) => {
  const [performanceData, setPerformanceData] = useState({
    fps: 0,
    memoryUsage: 0,
    loadTime: 0,
    apiCalls: []
  });

  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());

  // FPS Monitor
  useEffect(() => {
    if (!show) return;

    const measureFPS = () => {
      frameCountRef.current++;
      const currentTime = performance.now();
      
      if (currentTime - lastTimeRef.current >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / (currentTime - lastTimeRef.current));
        
        setPerformanceData(prev => ({
          ...prev,
          fps,
          memoryUsage: 'memory' in performance 
            ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
            : 0
        }));

        frameCountRef.current = 0;
        lastTimeRef.current = currentTime;
      }

      requestAnimationFrame(measureFPS);
    };

    measureFPS();
  }, [show]);

  // Page Load Time
  useEffect(() => {
    if (!show) return;

    const navigation = performance.getEntriesByType('navigation')[0];
    if (navigation) {
      setPerformanceData(prev => ({
        ...prev,
        loadTime: Math.round(navigation.loadEventEnd - navigation.fetchStart)
      }));
    }
  }, [show]);

  if (!show) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-sm font-mono z-50">
      <div className="space-y-1">
        <div>FPS: {performanceData.fps}</div>
        <div>Memory: {performanceData.memoryUsage}MB</div>
        <div>Load: {performanceData.loadTime}ms</div>
      </div>
    </div>
  );
};

// Bundle Size Analyzer
export const useBundleAnalyzer = () => {
  const [bundleInfo, setBundleInfo] = useState({
    totalSize: 0,
    gzippedSize: 0,
    chunks: []
  });

  useEffect(() => {
    // This would typically be populated by webpack-bundle-analyzer data
    // For now, we'll estimate based on loaded scripts
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    let totalSize = 0;

    scripts.forEach(script => {
      // Estimate size (this is not accurate, just for demo)
      totalSize += script.src.length * 100; // rough estimate
    });

    setBundleInfo({
      totalSize: Math.round(totalSize / 1024), // KB
      gzippedSize: Math.round(totalSize / 1024 * 0.3), // estimated gzip
      chunks: scripts.map(script => ({
        name: script.src.split('/').pop(),
        size: script.src.length * 100
      }))
    });
  }, []);

  return bundleInfo;
};

// Lighthouse Score Monitor
export const useLighthouseScore = () => {
  const [scores, setScores] = useState({
    performance: 0,
    accessibility: 0,
    bestPractices: 0,
    seo: 0
  });

  useEffect(() => {
    // This would typically integrate with Lighthouse CI
    // For now, we'll simulate scores based on basic metrics
    const calculatePerformanceScore = () => {
      const navigation = performance.getEntriesByType('navigation')[0];
      if (!navigation) return 0;

      const loadTime = navigation.loadEventEnd - navigation.fetchStart;
      const fcp = performance.getEntriesByName('first-contentful-paint')[0];
      
      // Simple scoring algorithm (not accurate to real Lighthouse)
      let score = 100;
      if (loadTime > 3000) score -= 30;
      if (loadTime > 5000) score -= 30;
      if (fcp && fcp.startTime > 2000) score -= 20;

      return Math.max(0, score);
    };

    setScores({
      performance: calculatePerformanceScore(),
      accessibility: 85, // Would need actual accessibility audit
      bestPractices: 90, // Would need actual best practices audit
      seo: 80 // Would need actual SEO audit
    });
  }, []);

  return scores;
};

// Memory Leak Detector
export const useMemoryLeakDetector = () => {
  const [memoryTrend, setMemoryTrend] = useState([]);
  const intervalRef = useRef(null);

  useEffect(() => {
    if (!('memory' in performance)) return;

    intervalRef.current = setInterval(() => {
      const currentMemory = performance.memory.usedJSHeapSize / 1024 / 1024;
      
      setMemoryTrend(prev => {
        const newTrend = [...prev, {
          timestamp: Date.now(),
          memory: currentMemory
        }].slice(-20); // Keep last 20 measurements

        // Detect potential memory leak
        if (newTrend.length >= 10) {
          const recent = newTrend.slice(-5);
          const older = newTrend.slice(-10, -5);
          const recentAvg = recent.reduce((sum, item) => sum + item.memory, 0) / recent.length;
          const olderAvg = older.reduce((sum, item) => sum + item.memory, 0) / older.length;
          
          if (recentAvg > olderAvg * 1.5) {
            console.warn('Potential memory leak detected!', {
              recentAverage: recentAvg.toFixed(2) + 'MB',
              olderAverage: olderAvg.toFixed(2) + 'MB'
            });
          }
        }

        return newTrend;
      });
    }, 5000); // Check every 5 seconds

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const isLeaking = memoryTrend.length >= 10 && (() => {
    const recent = memoryTrend.slice(-5);
    const older = memoryTrend.slice(-10, -5);
    const recentAvg = recent.reduce((sum, item) => sum + item.memory, 0) / recent.length;
    const olderAvg = older.reduce((sum, item) => sum + item.memory, 0) / older.length;
    return recentAvg > olderAvg * 1.5;
  })();

  return { memoryTrend, isLeaking };
};

// Performance Optimization Suggestions
export const usePerformanceOptimizations = () => {
  const [suggestions, setSuggestions] = useState([]);

  useEffect(() => {
    const analyzePage = () => {
      const newSuggestions = [];

      // Check for large images
      const images = Array.from(document.querySelectorAll('img'));
      const largeImages = images.filter(img => {
        return img.naturalWidth > 1920 || img.naturalHeight > 1080;
      });

      if (largeImages.length > 0) {
        newSuggestions.push({
          type: 'images',
          severity: 'medium',
          message: `Found ${largeImages.length} large images that could be optimized`,
          action: 'Consider resizing images or using responsive images'
        });
      }

      // Check for unused CSS
      const stylesheets = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
      if (stylesheets.length > 5) {
        newSuggestions.push({
          type: 'css',
          severity: 'low',
          message: 'Many CSS files detected',
          action: 'Consider bundling CSS files to reduce HTTP requests'
        });
      }

      // Check for memory usage
      if ('memory' in performance) {
        const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
        if (memoryUsage > 100) {
          newSuggestions.push({
            type: 'memory',
            severity: 'high',
            message: `High memory usage: ${memoryUsage.toFixed(2)}MB`,
            action: 'Check for memory leaks and optimize component re-renders'
          });
        }
      }

      setSuggestions(newSuggestions);
    };

    // Analyze after page load
    setTimeout(analyzePage, 2000);
  }, []);

  return suggestions;
};

// Performance Report Component
export const PerformanceReport = ({ onClose }) => {
  const bundleInfo = useBundleAnalyzer();
  const lighthouseScores = useLighthouseScore();
  const { memoryTrend, isLeaking } = useMemoryLeakDetector();
  const suggestions = usePerformanceOptimizations();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">تقرير الأداء</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Lighthouse Scores */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">نقاط Lighthouse</h3>
            <div className="space-y-2">
              {Object.entries(lighthouseScores).map(([key, score]) => (
                <div key={key} className="flex items-center justify-between">
                  <span className="capitalize">{key}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          score >= 90 ? 'bg-green-500' : 
                          score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${score}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{score}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Bundle Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">معلومات الحزمة</h3>
            <div className="space-y-2 text-sm">
              <div>الحجم الإجمالي: {bundleInfo.totalSize}KB</div>
              <div>الحجم المضغوط: {bundleInfo.gzippedSize}KB</div>
              <div>عدد الملفات: {bundleInfo.chunks.length}</div>
            </div>
          </div>

          {/* Memory Status */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">حالة الذاكرة</h3>
            <div className="space-y-2">
              <div className={`text-sm ${isLeaking ? 'text-red-600' : 'text-green-600'}`}>
                {isLeaking ? '⚠️ تسريب محتمل في الذاكرة' : '✅ الذاكرة مستقرة'}
              </div>
              {memoryTrend.length > 0 && (
                <div className="text-sm">
                  الاستخدام الحالي: {memoryTrend[memoryTrend.length - 1]?.memory.toFixed(2)}MB
                </div>
              )}
            </div>
          </div>

          {/* Suggestions */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">اقتراحات التحسين</h3>
            <div className="space-y-2">
              {suggestions.length === 0 ? (
                <div className="text-green-600 text-sm">✅ لا توجد مشاكل واضحة</div>
              ) : (
                suggestions.map((suggestion, index) => (
                  <div key={index} className="text-sm">
                    <div className={`font-medium ${
                      suggestion.severity === 'high' ? 'text-red-600' :
                      suggestion.severity === 'medium' ? 'text-yellow-600' : 'text-blue-600'
                    }`}>
                      {suggestion.message}
                    </div>
                    <div className="text-gray-600 text-xs">{suggestion.action}</div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default {
  usePerformanceMetrics,
  useAPIPerformanceMonitor,
  PerformanceDashboard,
  useBundleAnalyzer,
  useLighthouseScore,
  useMemoryLeakDetector,
  usePerformanceOptimizations,
  PerformanceReport
};
