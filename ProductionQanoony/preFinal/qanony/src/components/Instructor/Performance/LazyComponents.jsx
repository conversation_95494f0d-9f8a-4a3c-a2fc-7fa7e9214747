import React, { Suspense, lazy } from 'react';
import { FaSpinner } from 'react-icons/fa';

// Loading Component
const LoadingSpinner = ({ message = 'جاري التحميل...' }) => (
  <div className="flex items-center justify-center py-8">
    <div className="text-center">
      <FaSpinner className="animate-spin text-yellow-500 text-2xl mx-auto mb-2" />
      <p className="text-gray-600">{message}</p>
    </div>
  </div>
);

// Skeleton Loading Components
const StatsCardSkeleton = () => (
  <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-8 bg-gray-200 rounded w-1/2 mb-1"></div>
        <div className="h-3 bg-gray-200 rounded w-1/3"></div>
      </div>
      <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
    </div>
  </div>
);

const SubjectCardSkeleton = () => (
  <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
    <div className="flex items-start justify-between mb-4">
      <div className="flex-1">
        <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="flex gap-4">
          <div className="h-3 bg-gray-200 rounded w-16"></div>
          <div className="h-3 bg-gray-200 rounded w-20"></div>
        </div>
      </div>
      <div className="flex gap-2">
        <div className="w-8 h-8 bg-gray-200 rounded"></div>
        <div className="w-8 h-8 bg-gray-200 rounded"></div>
      </div>
    </div>
    <div className="space-y-2">
      <div className="h-3 bg-gray-200 rounded w-full"></div>
      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
    </div>
  </div>
);

const StudentCardSkeleton = () => (
  <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
    <div className="flex items-start gap-4">
      <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
      <div className="flex-1">
        <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="space-y-1">
          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
      <div className="flex gap-2">
        <div className="w-8 h-8 bg-gray-200 rounded"></div>
        <div className="w-8 h-8 bg-gray-200 rounded"></div>
      </div>
    </div>
  </div>
);

// Lazy Loaded Components
const LazyInstructorStatsCards = lazy(() => 
  import('../InstructorStatsCards').then(module => ({
    default: module.default
  }))
);

const LazyInstructorSubjectsList = lazy(() => 
  import('../InstructorSubjectsList').then(module => ({
    default: module.default
  }))
);

const LazyInstructorStudentsList = lazy(() => 
  import('../InstructorStudentsList').then(module => ({
    default: module.default
  }))
);

const LazyInstructorProfileForm = lazy(() => 
  import('../InstructorProfileForm').then(module => ({
    default: module.default
  }))
);

const LazyChartsComponents = lazy(() => 
  import('../ChartsComponents').then(module => ({
    default: module.default
  }))
);

const LazySearchFilterSystem = lazy(() => 
  import('../SearchFilterSystem').then(module => ({
    default: module.default
  }))
);

// Wrapper Components with Suspense
export const LazyStatsCards = ({ stats, loading, ...props }) => (
  <Suspense fallback={
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <StatsCardSkeleton key={i} />
      ))}
    </div>
  }>
    <LazyInstructorStatsCards stats={stats} loading={loading} {...props} />
  </Suspense>
);

export const LazySubjectsList = ({ subjects, loading, ...props }) => (
  <Suspense fallback={
    <div className="space-y-6">
      {[...Array(3)].map((_, i) => (
        <SubjectCardSkeleton key={i} />
      ))}
    </div>
  }>
    <LazyInstructorSubjectsList subjects={subjects} loading={loading} {...props} />
  </Suspense>
);

export const LazyStudentsList = ({ students, loading, ...props }) => (
  <Suspense fallback={
    <div className="space-y-6">
      {[...Array(3)].map((_, i) => (
        <StudentCardSkeleton key={i} />
      ))}
    </div>
  }>
    <LazyInstructorStudentsList students={students} loading={loading} {...props} />
  </Suspense>
);

export const LazyProfileForm = ({ instructor, ...props }) => (
  <Suspense fallback={<LoadingSpinner message="جاري تحميل النموذج..." />}>
    <LazyInstructorProfileForm instructor={instructor} {...props} />
  </Suspense>
);

export const LazyCharts = ({ data, ...props }) => (
  <Suspense fallback={<LoadingSpinner message="جاري تحميل الرسوم البيانية..." />}>
    <LazyChartsComponents data={data} {...props} />
  </Suspense>
);

export const LazySearchFilter = ({ data, ...props }) => (
  <Suspense fallback={<LoadingSpinner message="جاري تحميل البحث..." />}>
    <LazySearchFilterSystem data={data} {...props} />
  </Suspense>
);

// Intersection Observer Hook for Lazy Loading
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = React.useState(false);
  const [hasIntersected, setHasIntersected] = React.useState(false);
  const elementRef = React.useRef(null);

  React.useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [hasIntersected, options]);

  return { elementRef, isIntersecting, hasIntersected };
};

// Lazy Load Container Component
export const LazyLoadContainer = ({ 
  children, 
  fallback = <LoadingSpinner />, 
  className = '',
  threshold = 0.1,
  rootMargin = '50px'
}) => {
  const { elementRef, hasIntersected } = useIntersectionObserver({
    threshold,
    rootMargin
  });

  return (
    <div ref={elementRef} className={className}>
      {hasIntersected ? children : fallback}
    </div>
  );
};

// Image Lazy Loading Component
export const LazyImage = ({ 
  src, 
  alt, 
  className = '', 
  placeholder = '/placeholder-image.png',
  ...props 
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);
  const { elementRef, hasIntersected } = useIntersectionObserver();

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(true);
  };

  return (
    <div ref={elementRef} className={`relative ${className}`}>
      {hasIntersected && (
        <>
          {!isLoaded && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse rounded"></div>
          )}
          <img
            src={hasError ? placeholder : src}
            alt={alt}
            onLoad={handleLoad}
            onError={handleError}
            className={`transition-opacity duration-300 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            } ${className}`}
            {...props}
          />
        </>
      )}
    </div>
  );
};

// Progressive Image Loading
export const ProgressiveImage = ({ 
  src, 
  lowQualitySrc, 
  alt, 
  className = '',
  ...props 
}) => {
  const [isHighQualityLoaded, setIsHighQualityLoaded] = React.useState(false);
  const { elementRef, hasIntersected } = useIntersectionObserver();

  return (
    <div ref={elementRef} className={`relative ${className}`}>
      {hasIntersected && (
        <>
          {/* Low quality image */}
          <img
            src={lowQualitySrc}
            alt={alt}
            className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-300 ${
              isHighQualityLoaded ? 'opacity-0' : 'opacity-100'
            } filter blur-sm`}
            {...props}
          />
          
          {/* High quality image */}
          <img
            src={src}
            alt={alt}
            onLoad={() => setIsHighQualityLoaded(true)}
            className={`w-full h-full object-cover transition-opacity duration-300 ${
              isHighQualityLoaded ? 'opacity-100' : 'opacity-0'
            } ${className}`}
            {...props}
          />
        </>
      )}
    </div>
  );
};

export default {
  LazyStatsCards,
  LazySubjectsList,
  LazyStudentsList,
  LazyProfileForm,
  LazyCharts,
  LazySearchFilter,
  LazyLoadContainer,
  LazyImage,
  ProgressiveImage,
  useIntersectionObserver
};
