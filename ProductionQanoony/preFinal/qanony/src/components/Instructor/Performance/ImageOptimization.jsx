import React, { useState, useRef, useEffect, useCallback } from 'react';

// Image Optimization Hook
export const useImageOptimization = () => {
  const canvasRef = useRef(null);

  const compressImage = useCallback((file, quality = 0.8, maxWidth = 800, maxHeight = 600) => {
    return new Promise((resolve) => {
      const canvas = canvasRef.current || document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(resolve, 'image/jpeg', quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }, []);

  const generateThumbnail = useCallback((file, size = 150) => {
    return new Promise((resolve) => {
      const canvas = canvasRef.current || document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = size;
        canvas.height = size;

        // Calculate crop area for square thumbnail
        const { width, height } = img;
        const minDimension = Math.min(width, height);
        const x = (width - minDimension) / 2;
        const y = (height - minDimension) / 2;

        ctx.drawImage(img, x, y, minDimension, minDimension, 0, 0, size, size);
        
        canvas.toBlob(resolve, 'image/jpeg', 0.8);
      };

      img.src = URL.createObjectURL(file);
    });
  }, []);

  const getImageDimensions = useCallback((file) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
          aspectRatio: img.width / img.height
        });
      };
      img.src = URL.createObjectURL(file);
    });
  }, []);

  return {
    canvasRef,
    compressImage,
    generateThumbnail,
    getImageDimensions
  };
};

// Optimized Image Component
export const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  placeholder = '/placeholder-image.png',
  quality = 'auto',
  loading = 'lazy',
  onLoad,
  onError,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder);
  const imgRef = useRef(null);

  // Generate responsive image URLs
  const generateSrcSet = useCallback((baseSrc) => {
    if (!baseSrc || baseSrc.startsWith('data:') || baseSrc.includes('placeholder')) {
      return '';
    }

    const sizes = [480, 768, 1024, 1280];
    return sizes.map(size => {
      // For local images, we'd need a service to resize them
      // For now, we'll use the original image
      return `${baseSrc} ${size}w`;
    }).join(', ');
  }, []);

  const handleLoad = useCallback((e) => {
    setIsLoaded(true);
    onLoad?.(e);
  }, [onLoad]);

  const handleError = useCallback((e) => {
    setHasError(true);
    setCurrentSrc(placeholder);
    onError?.(e);
  }, [onError, placeholder]);

  useEffect(() => {
    if (src && src !== placeholder) {
      setCurrentSrc(src);
      setIsLoaded(false);
      setHasError(false);
    }
  }, [src, placeholder]);

  return (
    <img
      ref={imgRef}
      src={currentSrc}
      srcSet={generateSrcSet(src)}
      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
      alt={alt}
      width={width}
      height={height}
      loading={loading}
      className={`transition-opacity duration-300 ${
        isLoaded ? 'opacity-100' : 'opacity-0'
      } ${className}`}
      onLoad={handleLoad}
      onError={handleError}
      {...props}
    />
  );
};

// Progressive Image Loading Component
export const ProgressiveImage = ({
  src,
  lowQualitySrc,
  alt,
  className = '',
  onLoad,
  ...props
}) => {
  const [isHighQualityLoaded, setIsHighQualityLoaded] = useState(false);
  const [isLowQualityLoaded, setIsLowQualityLoaded] = useState(false);

  const handleLowQualityLoad = useCallback(() => {
    setIsLowQualityLoaded(true);
  }, []);

  const handleHighQualityLoad = useCallback((e) => {
    setIsHighQualityLoaded(true);
    onLoad?.(e);
  }, [onLoad]);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Low quality image */}
      {lowQualitySrc && (
        <img
          src={lowQualitySrc}
          alt={alt}
          className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-500 ${
            isHighQualityLoaded ? 'opacity-0' : 'opacity-100'
          } ${isLowQualityLoaded ? '' : 'blur-sm'}`}
          onLoad={handleLowQualityLoad}
          {...props}
        />
      )}

      {/* High quality image */}
      <img
        src={src}
        alt={alt}
        className={`w-full h-full object-cover transition-opacity duration-500 ${
          isHighQualityLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={handleHighQualityLoad}
        {...props}
      />

      {/* Loading placeholder */}
      {!isLowQualityLoaded && !isHighQualityLoaded && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="text-gray-400">جاري التحميل...</div>
        </div>
      )}
    </div>
  );
};

// Image Gallery with Lazy Loading
export const LazyImageGallery = ({
  images,
  columns = 3,
  gap = 16,
  className = '',
  onImageClick
}) => {
  const [loadedImages, setLoadedImages] = useState(new Set());
  const observerRef = useRef(null);

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const imageId = entry.target.dataset.imageId;
            setLoadedImages(prev => new Set([...prev, imageId]));
            observerRef.current.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  const handleImageRef = useCallback((element, imageId) => {
    if (element && observerRef.current) {
      element.dataset.imageId = imageId;
      observerRef.current.observe(element);
    }
  }, []);

  return (
    <div 
      className={`grid ${className}`}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: `${gap}px`
      }}
    >
      {images.map((image, index) => (
        <div
          key={image.id || index}
          ref={(el) => handleImageRef(el, image.id || index)}
          className="aspect-square bg-gray-200 rounded-lg overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
          onClick={() => onImageClick?.(image, index)}
        >
          {loadedImages.has(image.id || index) ? (
            <OptimizedImage
              src={image.src}
              alt={image.alt || `Image ${index + 1}`}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
              <div className="text-gray-400 text-sm">📷</div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

// Image Upload with Compression
export const CompressedImageUpload = ({
  onImageUpload,
  maxSize = 5 * 1024 * 1024, // 5MB
  quality = 0.8,
  maxWidth = 1200,
  maxHeight = 800,
  className = '',
  children
}) => {
  const [isCompressing, setIsCompressing] = useState(false);
  const { compressImage } = useImageOptimization();
  const fileInputRef = useRef(null);

  const handleFileSelect = useCallback(async (files) => {
    if (!files || files.length === 0) return;

    setIsCompressing(true);
    
    try {
      const file = files[0];
      
      // Check file size
      if (file.size > maxSize) {
        // Compress the image
        const compressedFile = await compressImage(file, quality, maxWidth, maxHeight);
        
        // Create a new File object with the compressed data
        const compressedFileObj = new File(
          [compressedFile], 
          file.name, 
          { type: 'image/jpeg' }
        );
        
        onImageUpload(compressedFileObj);
      } else {
        onImageUpload(file);
      }
    } catch (error) {
      console.error('Error compressing image:', error);
      // Fallback to original file
      onImageUpload(files[0]);
    } finally {
      setIsCompressing(false);
    }
  }, [compressImage, maxSize, quality, maxWidth, maxHeight, onImageUpload]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    handleFileSelect(files);
  }, [handleFileSelect]);

  const handleFileInputChange = useCallback((e) => {
    handleFileSelect(e.target.files);
  }, [handleFileSelect]);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  return (
    <div
      className={`relative ${className}`}
      onDrop={handleDrop}
      onDragOver={(e) => e.preventDefault()}
      onClick={openFileDialog}
    >
      {children}
      
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
      />

      {isCompressing && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
          <div className="text-white text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white border-t-transparent rounded-full mx-auto mb-2"></div>
            <p className="text-sm">جاري ضغط الصورة...</p>
          </div>
        </div>
      )}
    </div>
  );
};

// WebP Support Detection
export const useWebPSupport = () => {
  const [supportsWebP, setSupportsWebP] = useState(false);

  useEffect(() => {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const dataURL = canvas.toDataURL('image/webp');
    setSupportsWebP(dataURL.indexOf('data:image/webp') === 0);
  }, []);

  return supportsWebP;
};

// Smart Image Format Component
export const SmartImage = ({ src, alt, ...props }) => {
  const supportsWebP = useWebPSupport();
  
  const getOptimalSrc = useCallback((originalSrc) => {
    if (!originalSrc) return originalSrc;
    
    // If WebP is supported and we have a service to convert images
    if (supportsWebP && !originalSrc.includes('.webp')) {
      // This would typically be handled by a CDN or image service
      // For now, we'll just use the original image
      return originalSrc;
    }
    
    return originalSrc;
  }, [supportsWebP]);

  return (
    <OptimizedImage
      src={getOptimalSrc(src)}
      alt={alt}
      {...props}
    />
  );
};

export default {
  OptimizedImage,
  ProgressiveImage,
  LazyImageGallery,
  CompressedImageUpload,
  SmartImage,
  useImageOptimization,
  useWebPSupport
};
