import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSearch, Fa<PERSON><PERSON><PERSON>, FaSort, FaEye, FaEnvelope, FaPhone, FaCalendar } from 'react-icons/fa';

const StudentCard = ({ student, onView, onContact }) => {
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getProfileImageUrl = () => {
    if (student?.profile_image) {
      if (student.profile_image.startsWith('http')) {
        return student.profile_image;
      }
      return `http://localhost:8000${student.profile_image}`;
    }
    return '/default-student-avatar.png';
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
      <div className="flex items-start gap-4">
        {/* Student Avatar */}
        <img 
          src={getProfileImageUrl()}
          alt={`${student.first_name} ${student.last_name}`}
          className="w-16 h-16 rounded-full object-cover border-2 border-yellow-200"
          onError={(e) => {
            e.target.src = '/default-student-avatar.png';
          }}
        />
        
        {/* Student Info */}
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {student.first_name} {student.last_name}
          </h3>
          <p className="text-sm text-gray-600 mb-2">{student.email}</p>
          
          <div className="space-y-1 text-sm text-gray-500">
            {student.phone_number && (
              <div className="flex items-center gap-2">
                <FaPhone className="text-gray-400" />
                <span>{student.phone_number}</span>
              </div>
            )}
            {student.academic_year && (
              <div className="flex items-center gap-2">
                <FaCalendar className="text-gray-400" />
                <span>{student.academic_year.title_ar}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <FaCalendar className="text-gray-400" />
              <span>انضم في {formatDate(student.date_joined)}</span>
            </div>
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="flex flex-col gap-2">
          <button
            onClick={() => onView(student)}
            className="p-2 text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
            title="عرض التفاصيل"
          >
            <FaEye />
          </button>
          <button
            onClick={() => onContact(student)}
            className="p-2 text-green-500 hover:bg-green-50 rounded-lg transition-colors"
            title="إرسال رسالة"
          >
            <FaEnvelope />
          </button>
        </div>
      </div>
      
      {/* Student Stats */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-lg font-semibold text-gray-900">{student.completed_sessions || 0}</p>
            <p className="text-xs text-gray-500">جلسة مكتملة</p>
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900">{student.total_quizzes || 0}</p>
            <p className="text-xs text-gray-500">اختبار</p>
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900">
              {student.average_grade ? `${student.average_grade}%` : 'لا يوجد'}
            </p>
            <p className="text-xs text-gray-500">متوسط الدرجات</p>
          </div>
        </div>
      </div>
      
      {/* Status Badge */}
      <div className="mt-3">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          student.is_active 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {student.is_active ? 'نشط' : 'غير نشط'}
        </span>
        {student.subscription_status && (
          <span className={`mr-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            student.subscription_status === 'active'
              ? 'bg-blue-100 text-blue-800'
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {student.subscription_status === 'active' ? 'مشترك' : 'انتهت الاشتراك'}
          </span>
        )}
      </div>
    </div>
  );
};

const InstructorStudentsList = ({ students, loading, onRefresh }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('first_name');
  const [filterBy, setFilterBy] = useState('all');
  const [filteredStudents, setFilteredStudents] = useState([]);

  useEffect(() => {
    if (!students) return;

    let filtered = [...students];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(student =>
        `${student.first_name} ${student.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.phone_number?.includes(searchTerm)
      );
    }

    // Status filter
    if (filterBy !== 'all') {
      filtered = filtered.filter(student => {
        switch (filterBy) {
          case 'active':
            return student.is_active;
          case 'inactive':
            return !student.is_active;
          case 'subscribed':
            return student.subscription_status === 'active';
          case 'expired':
            return student.subscription_status !== 'active';
          default:
            return true;
        }
      });
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'first_name':
          return (a.first_name || '').localeCompare(b.first_name || '');
        case '-first_name':
          return (b.first_name || '').localeCompare(a.first_name || '');
        case 'email':
          return (a.email || '').localeCompare(b.email || '');
        case '-email':
          return (b.email || '').localeCompare(a.email || '');
        case 'date_joined':
          return new Date(a.date_joined) - new Date(b.date_joined);
        case '-date_joined':
          return new Date(b.date_joined) - new Date(a.date_joined);
        case 'completed_sessions':
          return (a.completed_sessions || 0) - (b.completed_sessions || 0);
        case '-completed_sessions':
          return (b.completed_sessions || 0) - (a.completed_sessions || 0);
        default:
          return 0;
      }
    });

    setFilteredStudents(filtered);
  }, [students, searchTerm, sortBy, filterBy]);

  const handleView = (student) => {
    console.log('View student:', student);
    // TODO: Implement view student details
  };

  const handleContact = (student) => {
    console.log('Contact student:', student);
    // TODO: Implement contact student (open chat or send message)
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
              <div className="flex items-start gap-4">
                <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-5 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-3 gap-4">
                  <div className="h-8 bg-gray-200 rounded"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
          <FaUsers className="text-yellow-500" />
          الطلاب المرتبطين ({students?.length || 0})
        </h2>
        
        <button
          onClick={onRefresh}
          className="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
        >
          تحديث
        </button>
      </div>

      {/* Search, Filter and Sort */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 relative">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="البحث في الطلاب..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <FaFilter className="text-gray-400" />
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
          >
            <option value="all">جميع الطلاب</option>
            <option value="active">النشطين</option>
            <option value="inactive">غير النشطين</option>
            <option value="subscribed">المشتركين</option>
            <option value="expired">منتهي الاشتراك</option>
          </select>
        </div>
        
        <div className="flex items-center gap-2">
          <FaSort className="text-gray-400" />
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
          >
            <option value="first_name">الاسم (أ-ي)</option>
            <option value="-first_name">الاسم (ي-أ)</option>
            <option value="email">الإيميل (أ-ي)</option>
            <option value="-email">الإيميل (ي-أ)</option>
            <option value="-date_joined">الأحدث انضماماً</option>
            <option value="date_joined">الأقدم انضماماً</option>
            <option value="-completed_sessions">الأكثر جلسات</option>
            <option value="completed_sessions">الأقل جلسات</option>
          </select>
        </div>
      </div>

      {/* Students Grid */}
      {!students || students.length === 0 ? (
        <div className="text-center py-12">
          <FaUsers className="mx-auto text-6xl text-gray-300 mb-4" />
          <p className="text-gray-500 text-lg">لا يوجد طلاب مرتبطين</p>
        </div>
      ) : filteredStudents.length === 0 ? (
        <div className="text-center py-12">
          <FaSearch className="mx-auto text-6xl text-gray-300 mb-4" />
          <p className="text-gray-500 text-lg">لا توجد نتائج للبحث</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStudents.map((student) => (
            <StudentCard
              key={student.id}
              student={student}
              onView={handleView}
              onContact={handleContact}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default InstructorStudentsList;
