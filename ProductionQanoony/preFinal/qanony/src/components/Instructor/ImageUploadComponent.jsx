import React, { useState, useRef } from 'react';
import { FaCamera, FaUpload, FaTimes, FaCheck, FaSpinner } from 'react-icons/fa';

const ImageUploadComponent = ({ 
  currentImage, 
  onImageUpload, 
  onImageRemove,
  loading = false,
  className = '',
  size = 'lg',
  shape = 'circle',
  showRemoveButton = true,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp'],
  maxSizeInMB = 5
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [preview, setPreview] = useState(null);
  const [error, setError] = useState('');
  const fileInputRef = useRef(null);

  // Size classes
  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
    xl: 'w-40 h-40'
  };

  // Shape classes
  const shapeClasses = {
    circle: 'rounded-full',
    square: 'rounded-lg',
    rounded: 'rounded-xl'
  };

  const validateFile = (file) => {
    // Check file type
    if (!acceptedFormats.includes(file.type)) {
      return 'نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG أو PNG أو WebP';
    }

    // Check file size
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      return `حجم الملف كبير جداً. الحد الأقصى ${maxSizeInMB} ميجابايت`;
    }

    return null;
  };

  const handleFileSelect = (file) => {
    setError('');
    
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target.result);
    };
    reader.readAsDataURL(file);

    // Upload file
    if (onImageUpload) {
      onImageUpload(file);
    }
  };

  const handleFileInputChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleRemoveImage = () => {
    setPreview(null);
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (onImageRemove) {
      onImageRemove();
    }
  };

  const openFileDialog = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const getImageSrc = () => {
    if (preview) return preview;
    if (currentImage) {
      if (currentImage.startsWith('http')) {
        return currentImage;
      }
      return `http://localhost:8000${currentImage}`;
    }
    return null;
  };

  const imageSrc = getImageSrc();

  return (
    <div className={`relative ${className}`}>
      {/* Main Image Container */}
      <div 
        className={`
          ${sizeClasses[size]} 
          ${shapeClasses[shape]} 
          relative overflow-hidden border-4 border-gray-200 
          ${dragOver ? 'border-yellow-400 bg-yellow-50' : ''}
          ${loading ? 'opacity-50' : ''}
          transition-all duration-200
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {/* Current Image or Placeholder */}
        {imageSrc ? (
          <img 
            src={imageSrc}
            alt="Profile"
            className="w-full h-full object-cover"
            onError={(e) => {
              e.target.src = '/default-instructor-avatar.png';
            }}
          />
        ) : (
          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
            <FaCamera className="text-gray-400 text-2xl" />
          </div>
        )}

        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <FaSpinner className="text-white text-xl animate-spin" />
          </div>
        )}

        {/* Drag Overlay */}
        {dragOver && (
          <div className="absolute inset-0 bg-yellow-400 bg-opacity-20 flex items-center justify-center">
            <FaUpload className="text-yellow-600 text-xl" />
          </div>
        )}
      </div>

      {/* Upload Button */}
      <button 
        onClick={openFileDialog}
        disabled={loading}
        className="absolute bottom-0 right-0 bg-yellow-500 text-white rounded-full p-2 shadow-lg hover:bg-yellow-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        title="تغيير الصورة"
      >
        <FaCamera size={16} />
      </button>

      {/* Remove Button */}
      {showRemoveButton && imageSrc && !loading && (
        <button 
          onClick={handleRemoveImage}
          className="absolute top-0 left-0 bg-red-500 text-white rounded-full p-1 shadow-lg hover:bg-red-600 transition-colors"
          title="حذف الصورة"
        >
          <FaTimes size={12} />
        </button>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* Error Message */}
      {error && (
        <div className="absolute top-full left-0 right-0 mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-600 text-sm">
          {error}
        </div>
      )}

      {/* Upload Instructions */}
      {!imageSrc && !loading && (
        <div className="absolute top-full left-0 right-0 mt-2 text-center">
          <p className="text-xs text-gray-500">
            اضغط أو اسحب صورة هنا
          </p>
          <p className="text-xs text-gray-400">
            {acceptedFormats.map(format => format.split('/')[1].toUpperCase()).join(', ')} - حتى {maxSizeInMB}MB
          </p>
        </div>
      )}
    </div>
  );
};

// Advanced Image Upload Modal Component
export const ImageUploadModal = ({ 
  isOpen, 
  onClose, 
  onUpload, 
  currentImage,
  title = 'تغيير الصورة الشخصية'
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleFileSelect = (file) => {
    setSelectedFile(file);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) return;
    
    setLoading(true);
    try {
      await onUpload(selectedFile);
      onClose();
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setPreview(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">{title}</h3>
          <button 
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <FaTimes />
          </button>
        </div>

        <div className="space-y-4">
          {/* Current Image */}
          {currentImage && !preview && (
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-2">الصورة الحالية:</p>
              <img 
                src={currentImage.startsWith('http') ? currentImage : `http://localhost:8000${currentImage}`}
                alt="Current"
                className="w-32 h-32 rounded-full mx-auto object-cover border-2 border-gray-200"
              />
            </div>
          )}

          {/* Upload Component */}
          <ImageUploadComponent
            currentImage={preview || currentImage}
            onImageUpload={handleFileSelect}
            loading={loading}
            size="xl"
            className="mx-auto"
            showRemoveButton={false}
          />

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              onClick={handleUpload}
              disabled={!selectedFile || loading}
              className="flex-1 bg-yellow-500 text-white py-2 px-4 rounded-lg hover:bg-yellow-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <FaSpinner className="animate-spin" />
                  جاري الرفع...
                </>
              ) : (
                <>
                  <FaCheck />
                  حفظ الصورة
                </>
              )}
            </button>
            <button
              onClick={handleClose}
              disabled={loading}
              className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 disabled:opacity-50 transition-colors"
            >
              إلغاء
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageUploadComponent;
