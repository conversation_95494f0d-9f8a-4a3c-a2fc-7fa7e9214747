import React from 'react';

// Loading skeleton for profile header
export const ProfileHeaderSkeleton = () => (
  <div className="bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg p-6 animate-pulse">
    <div className="flex flex-col md:flex-row items-center gap-6">
      {/* Profile Image Skeleton */}
      <div className="w-24 h-24 md:w-32 md:h-32 rounded-full bg-gray-300"></div>
      
      {/* Profile Info Skeleton */}
      <div className="flex-1 text-center md:text-right space-y-3">
        <div className="h-8 bg-gray-300 rounded w-64 mx-auto md:mx-0"></div>
        <div className="h-6 bg-gray-300 rounded w-48 mx-auto md:mx-0"></div>
        <div className="flex flex-col md:flex-row gap-4 justify-center md:justify-start">
          <div className="h-4 bg-gray-300 rounded w-32"></div>
          <div className="h-4 bg-gray-300 rounded w-28"></div>
          <div className="h-4 bg-gray-300 rounded w-36"></div>
        </div>
      </div>
      
      {/* Edit Button Skeleton */}
      <div className="h-12 w-32 bg-gray-300 rounded-lg"></div>
    </div>
    
    {/* Bio Skeleton */}
    <div className="mt-6 pt-6 border-t border-gray-300">
      <div className="space-y-2">
        <div className="h-4 bg-gray-300 rounded w-full"></div>
        <div className="h-4 bg-gray-300 rounded w-3/4"></div>
      </div>
    </div>
  </div>
);

// Loading skeleton for stats cards
export const StatsCardsSkeleton = () => (
  <div className="space-y-6">
    {/* Main Stats Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded mb-2 w-20"></div>
              <div className="h-8 bg-gray-200 rounded mb-1 w-16"></div>
              <div className="h-3 bg-gray-200 rounded w-12"></div>
            </div>
            <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
          </div>
        </div>
      ))}
    </div>

    {/* Additional Stats */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {[1, 2, 3].map((i) => (
        <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded mb-2 w-24"></div>
              <div className="h-8 bg-gray-200 rounded mb-1 w-12"></div>
            </div>
            <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
          </div>
        </div>
      ))}
    </div>

    {/* Recent Activity Skeleton */}
    <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
      <div className="h-6 bg-gray-200 rounded mb-4 w-32"></div>
      <div className="space-y-3">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="flex items-center gap-3 p-3">
            <div className="w-6 h-6 bg-gray-200 rounded"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded mb-1 w-48"></div>
              <div className="h-3 bg-gray-200 rounded w-32"></div>
            </div>
            <div className="h-3 bg-gray-200 rounded w-16"></div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

// Loading skeleton for subjects list
export const SubjectsListSkeleton = () => (
  <div className="space-y-6">
    {/* Header Skeleton */}
    <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
      <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
      <div className="h-10 bg-gray-200 rounded w-20 animate-pulse"></div>
    </div>

    {/* Search and Filter Skeleton */}
    <div className="flex flex-col md:flex-row gap-4">
      <div className="flex-1 h-10 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
    </div>

    {/* Subjects Grid Skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[1, 2, 3, 4, 5, 6].map((i) => (
        <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="h-6 bg-gray-200 rounded mb-2 w-40"></div>
              <div className="h-4 bg-gray-200 rounded mb-2 w-32"></div>
              <div className="flex items-center gap-4">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-4 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
            <div className="flex gap-2">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
            </div>
          </div>
          
          <div className="space-y-2 mb-4">
            <div className="h-3 bg-gray-200 rounded w-full"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
          </div>
          
          <div className="pt-4 border-t border-gray-200">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="h-8 bg-gray-200 rounded"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Loading skeleton for students list
export const StudentsListSkeleton = () => (
  <div className="space-y-6">
    {/* Header Skeleton */}
    <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
      <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
      <div className="h-10 bg-gray-200 rounded w-20 animate-pulse"></div>
    </div>

    {/* Search, Filter and Sort Skeleton */}
    <div className="flex flex-col md:flex-row gap-4">
      <div className="flex-1 h-10 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
      <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
    </div>

    {/* Students Grid Skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[1, 2, 3, 4, 5, 6].map((i) => (
        <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
          <div className="flex items-start gap-4">
            {/* Student Avatar Skeleton */}
            <div className="w-16 h-16 rounded-full bg-gray-200"></div>
            
            {/* Student Info Skeleton */}
            <div className="flex-1">
              <div className="h-5 bg-gray-200 rounded mb-1 w-32"></div>
              <div className="h-4 bg-gray-200 rounded mb-2 w-40"></div>
              
              <div className="space-y-1">
                <div className="h-3 bg-gray-200 rounded w-28"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
                <div className="h-3 bg-gray-200 rounded w-32"></div>
              </div>
            </div>
            
            {/* Action Buttons Skeleton */}
            <div className="flex flex-col gap-2">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
            </div>
          </div>
          
          {/* Student Stats Skeleton */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="h-8 bg-gray-200 rounded"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          </div>
          
          {/* Status Badge Skeleton */}
          <div className="mt-3 flex gap-2">
            <div className="h-6 bg-gray-200 rounded-full w-12"></div>
            <div className="h-6 bg-gray-200 rounded-full w-16"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Loading skeleton for profile form
export const ProfileFormSkeleton = () => (
  <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
    <div className="h-8 bg-gray-200 rounded mb-6 w-48"></div>
    
    <div className="space-y-6">
      {/* Name Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <div className="h-4 bg-gray-200 rounded mb-2 w-20"></div>
          <div className="h-10 bg-gray-200 rounded w-full"></div>
        </div>
        <div>
          <div className="h-4 bg-gray-200 rounded mb-2 w-24"></div>
          <div className="h-10 bg-gray-200 rounded w-full"></div>
        </div>
      </div>

      {/* Phone Number */}
      <div>
        <div className="h-4 bg-gray-200 rounded mb-2 w-20"></div>
        <div className="h-10 bg-gray-200 rounded w-full"></div>
      </div>

      {/* Specialty */}
      <div>
        <div className="h-4 bg-gray-200 rounded mb-2 w-16"></div>
        <div className="h-10 bg-gray-200 rounded w-full"></div>
      </div>

      {/* Bio */}
      <div>
        <div className="h-4 bg-gray-200 rounded mb-2 w-28"></div>
        <div className="h-24 bg-gray-200 rounded w-full"></div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 pt-6">
        <div className="flex-1 h-12 bg-gray-200 rounded"></div>
        <div className="flex-1 h-12 bg-gray-200 rounded"></div>
      </div>
    </div>
  </div>
);

// Spinner component
export const Spinner = ({ size = 'md', color = 'yellow' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const colorClasses = {
    yellow: 'border-yellow-500',
    blue: 'border-blue-500',
    green: 'border-green-500',
    red: 'border-red-500'
  };

  return (
    <div className={`${sizeClasses[size]} border-2 ${colorClasses[color]} border-t-transparent rounded-full animate-spin`}></div>
  );
};

// Loading overlay
export const LoadingOverlay = ({ message = 'جاري التحميل...' }) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-6 flex flex-col items-center gap-4">
      <Spinner size="lg" />
      <p className="text-gray-700 font-medium">{message}</p>
    </div>
  </div>
);

// Page loading component
export const PageLoading = () => (
  <div className="max-w-6xl mx-auto p-6">
    <div className="animate-pulse space-y-6">
      <ProfileHeaderSkeleton />
      <div className="h-12 bg-gray-200 rounded-lg"></div>
      <div className="h-96 bg-gray-200 rounded-lg"></div>
    </div>
  </div>
);
