import React, { useState, useEffect, useMemo } from 'react';
import { FaS<PERSON>ch, Fa<PERSON>ilter, FaSort, FaTimes, FaChevronDown } from 'react-icons/fa';

// Advanced Search Component
export const AdvancedSearch = ({ 
  onSearch, 
  placeholder = 'البحث...', 
  searchFields = [],
  className = '' 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedField, setSelectedField] = useState('all');
  const [isAdvanced, setIsAdvanced] = useState(false);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      onSearch({
        term: searchTerm,
        field: selectedField
      });
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchTerm, selectedField, onSearch]);

  const clearSearch = () => {
    setSearchTerm('');
    setSelectedField('all');
  };

  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center gap-2">
        {/* Main Search Input */}
        <div className="flex-1 relative">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder={placeholder}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
          />
          {searchTerm && (
            <button
              onClick={clearSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <FaTimes />
            </button>
          )}
        </div>

        {/* Advanced Search Toggle */}
        {searchFields.length > 0 && (
          <button
            onClick={() => setIsAdvanced(!isAdvanced)}
            className={`px-3 py-2 border rounded-lg transition-colors ${
              isAdvanced 
                ? 'bg-yellow-500 text-white border-yellow-500' 
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            }`}
          >
            <FaFilter />
          </button>
        )}
      </div>

      {/* Advanced Search Options */}
      {isAdvanced && searchFields.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              البحث في:
            </label>
            <select
              value={selectedField}
              onChange={(e) => setSelectedField(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
            >
              <option value="all">جميع الحقول</option>
              {searchFields.map((field) => (
                <option key={field.value} value={field.value}>
                  {field.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}
    </div>
  );
};

// Multi-Select Filter Component
export const MultiSelectFilter = ({ 
  options, 
  selectedValues, 
  onChange, 
  placeholder = 'اختر الفلاتر...',
  title 
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggle = (value) => {
    const newValues = selectedValues.includes(value)
      ? selectedValues.filter(v => v !== value)
      : [...selectedValues, value];
    onChange(newValues);
  };

  const clearAll = () => {
    onChange([]);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-right flex items-center justify-between hover:bg-gray-50"
      >
        <span className="text-gray-700">
          {selectedValues.length > 0 
            ? `${selectedValues.length} محدد` 
            : placeholder
          }
        </span>
        <FaChevronDown className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-60 overflow-y-auto">
          {title && (
            <div className="px-3 py-2 border-b border-gray-200 font-medium text-gray-900">
              {title}
            </div>
          )}
          
          {selectedValues.length > 0 && (
            <div className="px-3 py-2 border-b border-gray-200">
              <button
                onClick={clearAll}
                className="text-sm text-red-600 hover:text-red-800"
              >
                مسح الكل
              </button>
            </div>
          )}

          <div className="py-1">
            {options.map((option) => (
              <label
                key={option.value}
                className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={selectedValues.includes(option.value)}
                  onChange={() => handleToggle(option.value)}
                  className="mr-2 text-yellow-500 focus:ring-yellow-500"
                />
                <span className="text-gray-700">{option.label}</span>
                {option.count && (
                  <span className="mr-auto text-sm text-gray-500">
                    ({option.count})
                  </span>
                )}
              </label>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Sort Component
export const SortComponent = ({ 
  options, 
  value, 
  onChange, 
  className = '' 
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <FaSort className="text-gray-400" />
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

// Date Range Filter Component
export const DateRangeFilter = ({ 
  startDate, 
  endDate, 
  onStartDateChange, 
  onEndDateChange,
  className = '' 
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex items-center gap-2">
        <label className="text-sm text-gray-600">من:</label>
        <input
          type="date"
          value={startDate}
          onChange={(e) => onStartDateChange(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
        />
      </div>
      <div className="flex items-center gap-2">
        <label className="text-sm text-gray-600">إلى:</label>
        <input
          type="date"
          value={endDate}
          onChange={(e) => onEndDateChange(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
        />
      </div>
    </div>
  );
};

// Complete Search & Filter System
export const SearchFilterSystem = ({ 
  data,
  onFilteredDataChange,
  searchFields = [],
  filterOptions = [],
  sortOptions = [],
  enableDateFilter = false,
  className = ''
}) => {
  const [searchConfig, setSearchConfig] = useState({ term: '', field: 'all' });
  const [selectedFilters, setSelectedFilters] = useState({});
  const [sortBy, setSortBy] = useState(sortOptions[0]?.value || '');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Initialize filters
  useEffect(() => {
    const initialFilters = {};
    filterOptions.forEach(filter => {
      initialFilters[filter.key] = [];
    });
    setSelectedFilters(initialFilters);
  }, [filterOptions]);

  // Filter and sort data
  const filteredData = useMemo(() => {
    if (!data) return [];

    let filtered = [...data];

    // Apply search
    if (searchConfig.term) {
      filtered = filtered.filter(item => {
        if (searchConfig.field === 'all') {
          return searchFields.some(field => 
            String(item[field.value] || '').toLowerCase().includes(searchConfig.term.toLowerCase())
          );
        } else {
          return String(item[searchConfig.field] || '').toLowerCase().includes(searchConfig.term.toLowerCase());
        }
      });
    }

    // Apply filters
    Object.entries(selectedFilters).forEach(([filterKey, filterValues]) => {
      if (filterValues.length > 0) {
        filtered = filtered.filter(item => 
          filterValues.includes(item[filterKey])
        );
      }
    });

    // Apply date filter
    if (enableDateFilter && (startDate || endDate)) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.date || item.created_at || item.date_joined);
        const start = startDate ? new Date(startDate) : null;
        const end = endDate ? new Date(endDate) : null;
        
        if (start && itemDate < start) return false;
        if (end && itemDate > end) return false;
        return true;
      });
    }

    // Apply sorting
    if (sortBy) {
      filtered.sort((a, b) => {
        const [field, direction] = sortBy.includes('-') 
          ? [sortBy.substring(1), 'desc'] 
          : [sortBy, 'asc'];
        
        let aValue = a[field];
        let bValue = b[field];
        
        // Handle different data types
        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }
        
        if (direction === 'desc') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });
    }

    return filtered;
  }, [data, searchConfig, selectedFilters, sortBy, startDate, endDate, searchFields, enableDateFilter]);

  // Notify parent of filtered data changes
  useEffect(() => {
    onFilteredDataChange(filteredData);
  }, [filteredData, onFilteredDataChange]);

  const handleFilterChange = (filterKey, values) => {
    setSelectedFilters(prev => ({
      ...prev,
      [filterKey]: values
    }));
  };

  const clearAllFilters = () => {
    setSearchConfig({ term: '', field: 'all' });
    const clearedFilters = {};
    filterOptions.forEach(filter => {
      clearedFilters[filter.key] = [];
    });
    setSelectedFilters(clearedFilters);
    setSortBy(sortOptions[0]?.value || '');
    setStartDate('');
    setEndDate('');
  };

  const hasActiveFilters = searchConfig.term || 
    Object.values(selectedFilters).some(values => values.length > 0) ||
    startDate || endDate;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Bar */}
      <AdvancedSearch
        onSearch={setSearchConfig}
        placeholder="البحث في البيانات..."
        searchFields={searchFields}
      />

      {/* Filters Row */}
      <div className="flex flex-wrap gap-4 items-center">
        {/* Multi-select filters */}
        {filterOptions.map((filter) => (
          <MultiSelectFilter
            key={filter.key}
            options={filter.options}
            selectedValues={selectedFilters[filter.key] || []}
            onChange={(values) => handleFilterChange(filter.key, values)}
            placeholder={filter.placeholder}
            title={filter.title}
          />
        ))}

        {/* Date Range Filter */}
        {enableDateFilter && (
          <DateRangeFilter
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
          />
        )}

        {/* Sort */}
        {sortOptions.length > 0 && (
          <SortComponent
            options={sortOptions}
            value={sortBy}
            onChange={setSortBy}
          />
        )}

        {/* Clear All Button */}
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="px-3 py-2 text-red-600 hover:text-red-800 text-sm font-medium"
          >
            مسح جميع الفلاتر
          </button>
        )}
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <span>
          عرض {filteredData.length} من أصل {data?.length || 0} نتيجة
        </span>
        {hasActiveFilters && (
          <span className="text-yellow-600">
            تم تطبيق فلاتر
          </span>
        )}
      </div>
    </div>
  );
};
