import React from 'react';
import { FaExclamationTriangle, FaRedo, FaWifi, FaServer, FaLock, FaQuestionCircle } from 'react-icons/fa';

// Generic error component
export const ErrorMessage = ({ 
  title = 'حدث خطأ', 
  message, 
  onRetry, 
  retryText = 'إعادة المحاولة',
  type = 'error' 
}) => {
  const getIcon = () => {
    switch (type) {
      case 'network':
        return <FaWifi className="text-4xl text-red-500" />;
      case 'server':
        return <FaServer className="text-4xl text-red-500" />;
      case 'permission':
        return <FaLock className="text-4xl text-red-500" />;
      case 'warning':
        return <FaExclamationTriangle className="text-4xl text-yellow-500" />;
      default:
        return <FaExclamationTriangle className="text-4xl text-red-500" />;
    }
  };

  const getBgColor = () => {
    switch (type) {
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      default:
        return 'bg-red-50 border-red-200';
    }
  };

  return (
    <div className={`rounded-lg border p-6 text-center ${getBgColor()}`}>
      <div className="flex flex-col items-center gap-4">
        {getIcon()}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
          {message && (
            <p className="text-gray-600 mb-4">{message}</p>
          )}
        </div>
        {onRetry && (
          <button
            onClick={onRetry}
            className="inline-flex items-center gap-2 px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
          >
            <FaRedo />
            {retryText}
          </button>
        )}
      </div>
    </div>
  );
};

// Network error component
export const NetworkError = ({ onRetry }) => (
  <ErrorMessage
    type="network"
    title="مشكلة في الاتصال"
    message="تعذر الاتصال بالخادم. تأكد من اتصالك بالإنترنت وحاول مرة أخرى."
    onRetry={onRetry}
    retryText="إعادة الاتصال"
  />
);

// Server error component
export const ServerError = ({ onRetry, statusCode }) => (
  <ErrorMessage
    type="server"
    title="خطأ في الخادم"
    message={`حدث خطأ في الخادم ${statusCode ? `(${statusCode})` : ''}. يرجى المحاولة لاحقاً أو الاتصال بالدعم الفني.`}
    onRetry={onRetry}
    retryText="إعادة المحاولة"
  />
);

// Permission error component
export const PermissionError = ({ message }) => (
  <ErrorMessage
    type="permission"
    title="غير مصرح لك"
    message={message || "ليس لديك صلاحية للوصول إلى هذه الصفحة أو البيانات."}
  />
);

// Data not found component
export const DataNotFound = ({ 
  title = 'لا توجد بيانات', 
  message = 'لم يتم العثور على أي بيانات لعرضها.',
  icon = <FaQuestionCircle className="text-4xl text-gray-400" />,
  action
}) => (
  <div className="text-center py-12">
    <div className="flex flex-col items-center gap-4">
      {icon}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{message}</p>
      </div>
      {action}
    </div>
  </div>
);

// Form validation error component
export const FormError = ({ errors }) => {
  if (!errors || Object.keys(errors).length === 0) return null;

  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
      <div className="flex items-start gap-3">
        <FaExclamationTriangle className="text-red-500 mt-0.5" />
        <div>
          <h4 className="text-red-800 font-medium mb-2">يرجى تصحيح الأخطاء التالية:</h4>
          <ul className="text-red-700 text-sm space-y-1">
            {Object.entries(errors).map(([field, error]) => (
              <li key={field}>• {error}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

// API error handler
export const getErrorMessage = (error) => {
  if (!error) return 'حدث خطأ غير معروف';

  // Network errors
  if (!error.response) {
    return 'تعذر الاتصال بالخادم. تأكد من اتصالك بالإنترنت.';
  }

  const { status, data } = error.response;

  // Handle different status codes
  switch (status) {
    case 400:
      return data?.detail || data?.message || 'البيانات المرسلة غير صحيحة';
    case 401:
      return 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.';
    case 403:
      return 'ليس لديك صلاحية للوصول إلى هذه البيانات';
    case 404:
      return 'البيانات المطلوبة غير موجودة';
    case 422:
      return 'البيانات المرسلة غير صالحة';
    case 429:
      return 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً.';
    case 500:
      return 'حدث خطأ في الخادم. يرجى المحاولة لاحقاً.';
    case 502:
    case 503:
    case 504:
      return 'الخادم غير متاح حالياً. يرجى المحاولة لاحقاً.';
    default:
      return data?.detail || data?.message || `حدث خطأ (${status})`;
  }
};

// Error boundary fallback component
export const ErrorFallback = ({ error, resetErrorBoundary }) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="max-w-md w-full">
      <ErrorMessage
        title="حدث خطأ غير متوقع"
        message="عذراً، حدث خطأ في التطبيق. يرجى إعادة تحميل الصفحة أو الاتصال بالدعم الفني."
        onRetry={resetErrorBoundary}
        retryText="إعادة تحميل الصفحة"
      />
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-4 p-4 bg-gray-100 rounded-lg">
          <summary className="cursor-pointer font-medium">تفاصيل الخطأ (للمطورين)</summary>
          <pre className="mt-2 text-xs text-gray-600 overflow-auto">
            {error.stack}
          </pre>
        </details>
      )}
    </div>
  </div>
);

// Inline error component for form fields
export const FieldError = ({ error }) => {
  if (!error) return null;
  
  return (
    <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
      <FaExclamationTriangle className="text-xs" />
      {error}
    </p>
  );
};

// Toast error component (for use with toast notifications)
export const ToastError = ({ title, message }) => (
  <div className="flex items-start gap-3">
    <FaExclamationTriangle className="text-red-500 mt-0.5 flex-shrink-0" />
    <div>
      <h4 className="font-medium text-gray-900">{title}</h4>
      {message && <p className="text-sm text-gray-600 mt-1">{message}</p>}
    </div>
  </div>
);

// Empty state component
export const EmptyState = ({ 
  icon, 
  title, 
  description, 
  action,
  className = '' 
}) => (
  <div className={`text-center py-12 ${className}`}>
    <div className="flex flex-col items-center gap-4">
      {icon && <div className="text-6xl text-gray-300">{icon}</div>}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        {description && (
          <p className="text-gray-600 mb-4 max-w-md">{description}</p>
        )}
      </div>
      {action}
    </div>
  </div>
);

// Error boundary hook
export const useErrorHandler = () => {
  const handleError = (error, context = '') => {
    console.error(`Error ${context}:`, error);
    
    // Log to error reporting service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { extra: { context } });
    }
    
    return getErrorMessage(error);
  };

  return { handleError, getErrorMessage };
};

// Retry wrapper component
export const RetryWrapper = ({ 
  children, 
  onRetry, 
  error, 
  loading,
  retryCount = 0,
  maxRetries = 3 
}) => {
  if (loading) {
    return children;
  }

  if (error) {
    const canRetry = retryCount < maxRetries;
    
    return (
      <ErrorMessage
        title="فشل في تحميل البيانات"
        message={getErrorMessage(error)}
        onRetry={canRetry ? onRetry : undefined}
        retryText={`إعادة المحاولة ${retryCount > 0 ? `(${retryCount + 1}/${maxRetries})` : ''}`}
      />
    );
  }

  return children;
};
