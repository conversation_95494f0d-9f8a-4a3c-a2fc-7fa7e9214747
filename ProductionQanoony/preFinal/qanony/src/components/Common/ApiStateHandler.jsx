import React from 'react';
import { Loader2, RefreshCw } from 'lucide-react';
import PermissionError from '../ErrorMessage/PermissionError';

/**
 * مكون لمعالجة حالات الـ API (تحميل، خطأ، نجاح)
 */
const ApiStateHandler = ({ 
  loading, 
  error, 
  data, 
  children, 
  onRetry,
  loadingMessage = 'جاري التحميل...',
  emptyMessage = 'لا توجد بيانات متاحة',
  showEmptyState = true 
}) => {
  
  // حالة التحميل
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <Loader2 className="w-8 h-8 text-blue-500 animate-spin mb-3" />
        <p className="text-gray-600">{loadingMessage}</p>
      </div>
    );
  }

  // حالة الخطأ
  if (error) {
    return (
      <div className="space-y-4">
        <PermissionError error={error} />
        {onRetry && (
          <div className="flex justify-center">
            <button
              onClick={onRetry}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              إعادة المحاولة
            </button>
          </div>
        )}
      </div>
    );
  }

  // حالة البيانات الفارغة
  if (showEmptyState && (!data || (Array.isArray(data) && data.length === 0))) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-gray-500">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <p>{emptyMessage}</p>
      </div>
    );
  }

  // حالة النجاح - عرض المحتوى
  return children;
};

export default ApiStateHandler;
