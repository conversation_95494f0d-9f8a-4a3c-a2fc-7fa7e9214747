import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  FaBook,
  FaFolderOpen,
  FaChartLine,
  FaDownload,
  FaPlus,
  FaEdit,
  FaEye,
  FaSearch,
  FaUpload,
  FaStar,
  FaUsers
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const LibraryManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Categories state
  const [categories, setCategories] = useState([]);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [categoryForm, setCategoryForm] = useState({
    name_ar: '',
    name_en: '',
    parent: null,
    order: 0,
    is_active: true
  });

  // Documents state
  const [documents, setDocuments] = useState([]);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [editingDocument, setEditingDocument] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [documentForm, setDocumentForm] = useState({
    title_ar: '',
    title_en: '',
    category_id: '',
    description_ar: '',
    file_type: 'pdf',
    author: '',
    publication_date: '',
    tags: '',
    is_featured: false
  });

  // Access logs state
  const [accessLogs, setAccessLogs] = useState([]);

  // Stats state
  const [stats, setStats] = useState({
    total_documents: 0,
    total_categories: 0,
    total_downloads: 0,
    featured_documents: 0,
    recent_uploads: 0,
    top_documents: []
  });

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [sortBy, setSortBy] = useState('created_at');

  const tabs = [
    { key: 'overview', label: 'نظرة عامة', icon: <FaChartLine /> },
    { key: 'categories', label: 'الفئات', icon: <FaFolderOpen /> },
    { key: 'documents', label: 'الوثائق', icon: <FaBook /> },
    { key: 'access-logs', label: 'سجلات الوصول', icon: <FaUsers /> }
  ];

  useEffect(() => {
    fetchData();
  }, []);

  // Show message function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    // Clear message after 5 seconds
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      // Fetch categories
      const categoriesRes = await axios.get(`${API_BASE_URL}/api/library/categories/`, { headers });
      setCategories(Array.isArray(categoriesRes.data) ? categoriesRes.data : categoriesRes.data.results || []);

      // Fetch documents
      const documentsRes = await axios.get(`${API_BASE_URL}/api/library/documents/`, { headers });
      console.log('Documents API response:', documentsRes.data);

      const documentsArray = Array.isArray(documentsRes.data) ? documentsRes.data : documentsRes.data.results || [];
      setDocuments(documentsArray);

      // Fetch access logs
      try {
        const accessRes = await axios.get(`${API_BASE_URL}/api/library/access/`, { headers });
        console.log('Access logs API response:', accessRes.data);
        setAccessLogs(Array.isArray(accessRes.data) ? accessRes.data : accessRes.data.results || []);
      } catch (err) {
        console.log('Access logs not available:', err);
        setAccessLogs([]);
      }

      // Calculate stats using the processed arrays
      const categoriesArray = Array.isArray(categoriesRes.data) ? categoriesRes.data : categoriesRes.data.results || [];
      const totalDocuments = documentsArray.length || 0;
      const totalCategories = categoriesArray.length || 0;
      const featuredDocuments = documentsArray.filter(doc => doc.is_featured).length || 0;
      const totalDownloads = documentsArray.reduce((sum, doc) => sum + (doc.download_count || 0), 0);

      setStats({
        total_documents: totalDocuments,
        total_categories: totalCategories,
        total_downloads: totalDownloads,
        featured_documents: featuredDocuments,
        recent_uploads: documentsArray.filter(doc => {
          const uploadDate = new Date(doc.created_at);
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          return uploadDate > weekAgo;
        }).length || 0,
        top_documents: documentsArray
          .sort((a, b) => (b.download_count || 0) - (a.download_count || 0))
          .slice(0, 5)
      });

    } catch (err) {
      console.error('Error fetching library data:', err);
      setError('تعذر جلب بيانات المكتبة');
    } finally {
      setLoading(false);
    }
  };

  // Category management functions
  const openCategoryModal = (category = null) => {
    if (category) {
      setEditingCategory(category);
      setCategoryForm({
        name_ar: category.name_ar,
        name_en: category.name_en,
        parent: category.parent,
        order: category.order,
        is_active: category.is_active
      });
    } else {
      setEditingCategory(null);
      setCategoryForm({
        name_ar: '',
        name_en: '',
        parent: null,
        order: 0,
        is_active: true
      });
    }
    setShowCategoryModal(true);
  };

  const closeCategoryModal = () => {
    setShowCategoryModal(false);
    setEditingCategory(null);
    setCategoryForm({
      name_ar: '',
      name_en: '',
      parent: null,
      order: 0,
      is_active: true
    });
  };

  const handleCategorySubmit = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      if (editingCategory) {
        await axios.patch(`${API_BASE_URL}/api/library/categories/${editingCategory.id}/`, categoryForm, { headers });
      } else {
        await axios.post(`${API_BASE_URL}/api/library/categories/`, categoryForm, { headers });
      }

      closeCategoryModal();
      fetchData();
      showMessage('تم حفظ الفئة بنجاح');
    } catch (err) {
      console.error('Error saving category:', err);
      showMessage('حدث خطأ أثناء حفظ الفئة', true);
    }
  };



  // Document management functions
  const openDocumentModal = (document = null) => {
    if (document) {
      setEditingDocument(document);
      setDocumentForm({
        title_ar: document.title_ar,
        title_en: document.title_en,
        category_id: document.category?.id || '',
        description_ar: document.description_ar,
        file_type: document.file_type,
        author: document.author,
        publication_date: document.publication_date,
        tags: document.tags,
        is_featured: document.is_featured
      });
      setSelectedFile(null); // Reset file selection for editing
    } else {
      setEditingDocument(null);
      setDocumentForm({
        title_ar: '',
        title_en: '',
        category_id: '',
        description_ar: '',
        file_type: 'pdf',
        author: '',
        publication_date: '',
        tags: '',
        is_featured: false
      });
      setSelectedFile(null);
    }
    setShowDocumentModal(true);
  };

  const closeDocumentModal = () => {
    setShowDocumentModal(false);
    setEditingDocument(null);
    setSelectedFile(null);
    setDocumentForm({
      title_ar: '',
      title_en: '',
      category_id: '',
      description_ar: '',
      file_type: 'pdf',
      author: '',
      publication_date: '',
      tags: '',
      is_featured: false
    });
  };

  // File handling function
  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
    if (!allowedTypes.includes(file.type)) {
      showMessage('نوع الملف غير مدعوم. يرجى رفع ملف PDF أو Word أو نص', true);
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      showMessage('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت', true);
      return;
    }

    setSelectedFile(file);

    // Auto-detect file type
    let fileType = 'pdf';
    if (file.type.includes('word') || file.type.includes('document')) {
      fileType = file.name.endsWith('.docx') ? 'docx' : 'doc';
    } else if (file.type.includes('text')) {
      fileType = 'txt';
    }

    setDocumentForm({...documentForm, file_type: fileType});
  };

  const handleDocumentSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    if (!editingDocument && !selectedFile) {
      showMessage('يرجى اختيار ملف للرفع', true);
      return;
    }

    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      // Create FormData for file upload
      const formData = new FormData();

      // Add form fields
      formData.append('title_ar', documentForm.title_ar);
      formData.append('title_en', documentForm.title_en);
      formData.append('category_id', documentForm.category_id);
      formData.append('description_ar', documentForm.description_ar);
      formData.append('file_type', documentForm.file_type);
      formData.append('author', documentForm.author);
      formData.append('publication_date', documentForm.publication_date);
      formData.append('tags', documentForm.tags);
      formData.append('is_featured', documentForm.is_featured);

      // Add file if selected
      if (selectedFile) {
        formData.append('file_url', selectedFile);
      }

      // Set headers for multipart form data
      const multipartHeaders = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data'
      };

      if (editingDocument) {
        await axios.patch(`${API_BASE_URL}/api/library/documents/${editingDocument.id}/`, formData, { headers: multipartHeaders });
      } else {
        await axios.post(`${API_BASE_URL}/api/library/documents/`, formData, { headers: multipartHeaders });
      }

      closeDocumentModal();
      fetchData();
      showMessage('تم حفظ الوثيقة بنجاح');
    } catch (err) {
      console.error('Error saving document:', err);
      const errorMessage = err.response?.data?.detail || err.response?.data?.file_url?.[0] || 'حدث خطأ أثناء حفظ الوثيقة';
      showMessage(errorMessage, true);
    }
  };



  const toggleFeatured = async (documentId, currentStatus) => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };
      
      await axios.patch(`${API_BASE_URL}/api/library/documents/${documentId}/`, 
        { is_featured: !currentStatus }, 
        { headers }
      );
      fetchData();
      showMessage('تم تغيير حالة الإبراز بنجاح');
    } catch (err) {
      console.error('Error toggling featured status:', err);
      showMessage('حدث خطأ أثناء تغيير حالة الإبراز', true);
    }
  };

  // Filter documents
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = !searchTerm || 
      doc.title_ar.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.title_en.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.tags.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = !selectedCategory || doc.category?.id.toString() === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Sort documents
  const sortedDocuments = [...filteredDocuments].sort((a, b) => {
    switch (sortBy) {
      case 'download_count':
        return (b.download_count || 0) - (a.download_count || 0);
      case 'publication_date':
        return new Date(b.publication_date) - new Date(a.publication_date);
      case 'title_ar':
        return a.title_ar.localeCompare(b.title_ar);
      default:
        return new Date(b.created_at) - new Date(a.created_at);
    }
  });

  if (loading) return <div className="text-center py-8">جاري التحميل...</div>;
  if (error) return <div className="text-center py-8 text-red-600">{error}</div>;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">إدارة المكتبة</h2>
        <div className="flex gap-2">
          <button
            onClick={() => fetchData()}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            🔄 تحديث
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{successMessage}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setSuccessMessage(null)}
          >
            <svg className="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>إغلاق</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </span>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{error}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setError(null)}
          >
            <svg className="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>إغلاق</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </span>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 space-x-reverse">
          {tabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === tab.key
                  ? 'border-yellow-500 text-yellow-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800">نظرة عامة على المكتبة</h3>
            
            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="bg-blue-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-blue-500 text-white rounded-full p-3 mb-2">
                  <FaBook size={24} />
                </div>
                <div className="text-2xl font-bold text-blue-800">{stats.total_documents}</div>
                <div className="text-blue-700 mt-1">إجمالي الوثائق</div>
              </div>

              <div className="bg-green-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-green-500 text-white rounded-full p-3 mb-2">
                  <FaFolderOpen size={24} />
                </div>
                <div className="text-2xl font-bold text-green-800">{stats.total_categories}</div>
                <div className="text-green-700 mt-1">الفئات</div>
              </div>

              <div className="bg-yellow-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-yellow-500 text-white rounded-full p-3 mb-2">
                  <FaDownload size={24} />
                </div>
                <div className="text-2xl font-bold text-yellow-800">{stats.total_downloads}</div>
                <div className="text-yellow-700 mt-1">إجمالي التحميلات</div>
              </div>

              <div className="bg-purple-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-purple-500 text-white rounded-full p-3 mb-2">
                  <FaStar size={24} />
                </div>
                <div className="text-2xl font-bold text-purple-800">{stats.featured_documents}</div>
                <div className="text-purple-700 mt-1">الوثائق المميزة</div>
              </div>

              <div className="bg-red-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-red-500 text-white rounded-full p-3 mb-2">
                  <FaUpload size={24} />
                </div>
                <div className="text-2xl font-bold text-red-800">{stats.recent_uploads}</div>
                <div className="text-red-700 mt-1">رفع حديث</div>
                <div className="text-sm text-red-600 mt-1">آخر 7 أيام</div>
              </div>
            </div>

            {/* Top Documents */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h4 className="text-lg font-bold text-gray-800 mb-4">الوثائق الأكثر تحميلاً</h4>
              <div className="space-y-3">
                {stats.top_documents.map((doc, index) => (
                  <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="bg-yellow-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </span>
                      <div>
                        <div className="font-medium text-gray-800">{doc.title_ar}</div>
                        <div className="text-sm text-gray-600">{doc.author}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-800">{doc.download_count || 0}</div>
                      <div className="text-sm text-gray-600">تحميل</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-bold text-gray-800">إدارة الفئات ({categories.length})</h3>
              <button
                onClick={() => openCategoryModal()}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
              >
                <FaPlus size={16} />
                إضافة فئة جديدة
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">ID</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالعربية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالإنجليزية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفئة الأب</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الترتيب</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {categories.map(category => (
                      <tr key={category.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{category.id}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{category.name_ar}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{category.name_en}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {category.parent ?
                            categories.find(c => c.id === category.parent)?.name_ar || 'غير موجود' :
                            '—'
                          }
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                            {category.order}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            category.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {category.is_active ? 'نشطة' : 'غير نشطة'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => openCategoryModal(category)}
                            className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg transition-colors flex items-center gap-2"
                            title="تعديل الفئة"
                          >
                            <FaEdit size={14} />
                            <span className="text-sm">تعديل</span>
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {categories.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد فئات مسجلة حالياً
                </div>
              )}
            </div>
          </div>
        )}

        {/* Documents Tab */}
        {activeTab === 'documents' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-bold text-gray-800">إدارة الوثائق ({documents.length})</h3>
              <button
                onClick={() => openDocumentModal()}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
              >
                <FaPlus size={16} />
                إضافة وثيقة جديدة
              </button>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex-1 min-w-64">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث في الوثائق..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  />
                </div>
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
              >
                <option value="">جميع الفئات</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name_ar}
                  </option>
                ))}
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
              >
                <option value="created_at">الأحدث</option>
                <option value="download_count">الأكثر تحميلاً</option>
                <option value="publication_date">تاريخ النشر</option>
                <option value="title_ar">الاسم أبجدياً</option>
              </select>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">العنوان</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفئة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المؤلف</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التحميلات</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">مميز</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الرفع</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {sortedDocuments.map(document => (
                      <tr key={document.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{document.title_ar}</div>
                            <div className="text-sm text-gray-500">{document.title_en}</div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {document.category?.name_ar || '—'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{document.author}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium uppercase">
                            {document.file_type}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                            {document.download_count || 0}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <button
                            onClick={() => toggleFeatured(document.id, document.is_featured)}
                            className={`p-1 rounded transition-colors ${
                              document.is_featured
                                ? 'text-yellow-500 hover:text-yellow-700'
                                : 'text-gray-400 hover:text-yellow-500'
                            }`}
                            title={document.is_featured ? 'إلغاء الإبراز' : 'جعل مميز'}
                          >
                            <FaStar size={16} />
                          </button>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(document.created_at).toLocaleDateString('ar-EG')}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <a
                              href={document.file_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="bg-green-100 hover:bg-green-200 text-green-700 px-2 py-1 rounded-lg transition-colors flex items-center gap-1"
                              title="عرض الملف"
                            >
                              <FaEye size={14} />
                              <span className="text-xs">عرض</span>
                            </a>
                            <button
                              onClick={() => openDocumentModal(document)}
                              className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-2 py-1 rounded-lg transition-colors flex items-center gap-1"
                              title="تعديل الوثيقة"
                            >
                              <FaEdit size={14} />
                              <span className="text-xs">تعديل</span>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {sortedDocuments.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  {searchTerm || selectedCategory ? 'لا توجد نتائج للبحث' : 'لا توجد وثائق مسجلة حالياً'}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Access Logs Tab */}
        {activeTab === 'access-logs' && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800">سجلات الوصول ({accessLogs.length})</h3>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الطالب</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوثيقة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد التحميلات</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر وصول</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {accessLogs.map(log => (
                      <tr key={log.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {log.student?.user?.email || log.student?.user_email || '—'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {log.document?.title_ar || '—'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                            {log.download_count || 0}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(log.accessed_at).toLocaleDateString('ar-EG')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {accessLogs.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد سجلات وصول حالياً
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Category Modal */}
      {showCategoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              {editingCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
            </h3>

            <form onSubmit={handleCategorySubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الاسم بالعربية
                </label>
                <input
                  type="text"
                  value={categoryForm.name_ar}
                  onChange={(e) => setCategoryForm({...categoryForm, name_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الاسم بالإنجليزية
                </label>
                <input
                  type="text"
                  value={categoryForm.name_en}
                  onChange={(e) => setCategoryForm({...categoryForm, name_en: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الفئة الأب
                </label>
                <select
                  value={categoryForm.parent || ''}
                  onChange={(e) => setCategoryForm({...categoryForm, parent: e.target.value || null})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                >
                  <option value="">لا يوجد (فئة رئيسية)</option>
                  {categories.filter(c => c.id !== editingCategory?.id).map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name_ar}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الترتيب
                </label>
                <input
                  type="number"
                  value={categoryForm.order}
                  onChange={(e) => setCategoryForm({...categoryForm, order: parseInt(e.target.value) || 0})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  min="0"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="category_is_active"
                  checked={categoryForm.is_active}
                  onChange={(e) => setCategoryForm({...categoryForm, is_active: e.target.checked})}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="category_is_active" className="mr-2 block text-sm text-gray-900">
                  فئة نشطة
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  {editingCategory ? 'تحديث' : 'إضافة'}
                </button>
                <button
                  type="button"
                  onClick={closeCategoryModal}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Document Modal */}
      {showDocumentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              {editingDocument ? 'تعديل الوثيقة' : 'إضافة وثيقة جديدة'}
            </h3>

            <form onSubmit={handleDocumentSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    العنوان بالعربية
                  </label>
                  <input
                    type="text"
                    value={documentForm.title_ar}
                    onChange={(e) => setDocumentForm({...documentForm, title_ar: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    العنوان بالإنجليزية
                  </label>
                  <input
                    type="text"
                    value={documentForm.title_en}
                    onChange={(e) => setDocumentForm({...documentForm, title_en: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الفئة
                  </label>
                  <select
                    value={documentForm.category_id}
                    onChange={(e) => setDocumentForm({...documentForm, category_id: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  >
                    <option value="">اختر الفئة</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name_ar}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    المؤلف
                  </label>
                  <input
                    type="text"
                    value={documentForm.author}
                    onChange={(e) => setDocumentForm({...documentForm, author: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الوصف
                </label>
                <textarea
                  value={documentForm.description_ar}
                  onChange={(e) => setDocumentForm({...documentForm, description_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  rows="3"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {editingDocument ? 'تغيير الملف (اختياري)' : 'رفع الملف'}
                  </label>
                  <input
                    type="file"
                    onChange={handleFileSelect}
                    accept=".pdf,.doc,.docx,.txt"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required={!editingDocument}
                  />
                  {selectedFile && (
                    <div className="mt-2 text-sm text-green-600">
                      ملف محدد: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                    </div>
                  )}
                  {editingDocument && !selectedFile && (
                    <div className="mt-2 text-sm text-gray-600">
                      الملف الحالي: {editingDocument.file_url ? 'موجود' : 'غير متوفر'}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    نوع الملف
                  </label>
                  <select
                    value={documentForm.file_type}
                    onChange={(e) => setDocumentForm({...documentForm, file_type: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  >
                    <option value="pdf">PDF</option>
                    <option value="doc">DOC</option>
                    <option value="docx">DOCX</option>
                    <option value="ppt">PPT</option>
                    <option value="pptx">PPTX</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ النشر
                  </label>
                  <input
                    type="date"
                    value={documentForm.publication_date}
                    onChange={(e) => setDocumentForm({...documentForm, publication_date: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الكلمات المفتاحية (مفصولة بفواصل)
                </label>
                <input
                  type="text"
                  value={documentForm.tags}
                  onChange={(e) => setDocumentForm({...documentForm, tags: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  placeholder="قانون، دستور، تشريع"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="document_is_featured"
                  checked={documentForm.is_featured}
                  onChange={(e) => setDocumentForm({...documentForm, is_featured: e.target.checked})}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="document_is_featured" className="mr-2 block text-sm text-gray-900">
                  وثيقة مميزة
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  {editingDocument ? 'تحديث' : 'إضافة'}
                </button>
                <button
                  type="button"
                  onClick={closeDocumentModal}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default LibraryManagement;
