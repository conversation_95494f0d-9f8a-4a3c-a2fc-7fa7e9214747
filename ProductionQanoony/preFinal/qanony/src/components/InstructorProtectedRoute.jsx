import React, { useContext } from 'react';
import { Navigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const InstructorProtectedRoute = ({ children }) => {
  const { user } = useContext(AuthContext);

  // Debug info
  console.log('🔍 InstructorProtectedRoute - User:', user);
  console.log('🔍 InstructorProtectedRoute - Is Instructor:', user?.is_instructor);

  // Check if user is logged in
  if (!user) {
    console.log('❌ No user found, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // Check if user is an instructor
  if (!user.is_instructor) {
    console.log('❌ User is not an instructor, redirecting...');
    console.log('- is_student:', user.is_student);
    console.log('- is_admin:', user.is_admin);

    // If user is not an instructor, redirect to appropriate page
    if (user.is_student) {
      return <Navigate to="/student-profile" replace />;
    } else if (user.is_admin) {
      return <Navigate to="/admin-user" replace />;
    } else {
      return <Navigate to="/" replace />;
    }
  }

  console.log('✅ User is instructor, allowing access');

  return children;
};

export default InstructorProtectedRoute;
