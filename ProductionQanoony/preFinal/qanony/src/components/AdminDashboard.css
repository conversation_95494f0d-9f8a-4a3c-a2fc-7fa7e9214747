.admin-container {
  max-width: 800px;
  margin: 40px auto;
  background: #fff;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  text-align: center;
  direction: rtl;
}

.apps-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 18px;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
}

.apps-table th,
.apps-table td {
  padding: 12px 10px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.apps-table th {
  background: #f7f7f7;
  font-weight: bold;
  font-size: 16px;
  color: #333;
}

.apps-table tr:nth-child(even) {
  background: #fafbfc;
}

.approve-btn {
  background: #27ae60;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 7px 18px;
  margin: 0 2px;
  cursor: pointer;
  font-weight: bold;
  font-size: 15px;
  transition: background 0.2s;
}

.reject-btn {
  background: #e74c3c;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 7px 18px;
  margin: 0 2px;
  cursor: pointer;
  font-weight: bold;
  font-size: 15px;
  transition: background 0.2s;
}

.approve-btn:hover {
  background: #219150;
}

.reject-btn:hover {
  background: #c0392b;
}

.details-btn {
  background: #2980b9;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 7px 16px;
  margin: 0 2px;
  cursor: pointer;
  font-weight: bold;
  font-size: 15px;
  transition: background 0.2s;
  display: inline-flex;
  align-items: center;
}

.details-btn:hover {
  background: #1c5d8c;
}

.cv-link {
  color: #2980b9;
  font-weight: bold;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s;
}

.cv-link:hover {
  color: #1c5d8c;
}

.apps-table td[title] {
  cursor: help;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(210px, 1fr));
  gap: 24px;
  margin-bottom: 36px;
  margin-top: 0;
}

.stat-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(250, 204, 21, 0.10), 0 1.5px 6px 0 rgba(0,0,0,0.04);
  padding: 28px 18px 22px 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s, transform 0.2s;
  position: relative;
  min-height: 160px;
  cursor: pointer;
}

.stat-card:hover {
  box-shadow: 0 8px 32px 0 rgba(250, 204, 21, 0.18), 0 3px 12px 0 rgba(0,0,0,0.08);
  transform: translateY(-6px) scale(1.03);
}

.stat-card .icon-circle {
  background: linear-gradient(135deg, #facc15 60%, #fffbe6 100%);
  color: #fff;
  border-radius: 50%;
  width: 54px;
  height: 54px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 14px;
  box-shadow: 0 2px 8px 0 rgba(250, 204, 21, 0.18);
  font-size: 2rem;
}

.stat-value {
  font-size: 2.3rem;
  font-weight: bold;
  color: #222;
  margin-bottom: 4px;
  letter-spacing: 1px;
}

.stat-label {
  font-size: 1.08rem;
  color: #888;
  font-weight: 500;
  margin-top: 2px;
}

.stat-card.gold .icon-circle {
  background: linear-gradient(135deg, #facc15 70%, #fffbe6 100%);
  color: #fff;
}

.stat-card.gold .stat-value,
.stat-card.gold .stat-label {
  color: #facc15;
}

.stat-card.green .icon-circle {
  background: linear-gradient(135deg, #27ae60 70%, #eafaf1 100%);
  color: #fff;
}
.stat-card.green .stat-value {
  color: #27ae60;
}

.stat-card.red .icon-circle {
  background: linear-gradient(135deg, #e74c3c 70%, #fdecea 100%);
  color: #fff;
}
.stat-card.red .stat-value {
  color: #e74c3c;
}

.stat-card.blue .icon-circle {
  background: linear-gradient(135deg, #2980ef 70%, #eaf1fb 100%);
  color: #fff;
}
.stat-card.blue .stat-value {
  color: #2980ef;
}

@media (max-width: 700px) {
  .stats-row {
    grid-template-columns: 1fr;
    gap: 18px;
  }
  .stat-card {
    min-height: 120px;
    padding: 18px 10px 14px 10px;
  }
  .stat-value {
    font-size: 1.5rem;
  }
}

.admin-sidebar {
  width: 180px;
  background: #fff;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  padding: 0 0 18px 0;
  display: flex;
  flex-direction: column;
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1002;
  transition: transform 0.22s;
  border-left: none;
}
.admin-sidebar .sidebar-header {
  padding: 18px 12px 8px 12px;
  font-size: 1.08rem;
  font-weight: 600;
  color: #222;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f2f2f2;
}
.admin-sidebar .sidebar-title {
  font-size: 1.08rem;
  font-weight: 600;
}
.admin-sidebar .sidebar-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #aaa;
  cursor: pointer;
  display: none;
}
.admin-sidebar .sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: 10px;
}
.sidebar-tab {
  display: flex;
  align-items: center;
  gap: 7px;
  padding: 9px 16px;
  font-size: 0.98rem;
  color: #444;
  border-radius: 7px 0 0 7px;
  cursor: pointer;
  transition: background 0.15s, color 0.15s, box-shadow 0.15s;
  font-weight: 400;
}
.sidebar-tab.active, .sidebar-tab:hover {
  background: #f5f5f7;
  color: #facc15;
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(250, 204, 21, 0.07);
}
.sidebar-icon {
  font-size: 1.08rem;
  display: flex;
  align-items: center;
}

.admin-main-content {
  flex: 1;
  margin-right: 180px;
  padding: 18px 0 32px 0;
  min-width: 0;
}
.admin-welcome {
  font-size: 1.1rem;
  color: #222;
  margin-bottom: 22px;
  font-weight: 500;
  text-align: right;
}

.sidebar-toggle {
  display: none;
  position: fixed;
  top: 14px;
  right: 14px;
  z-index: 1100;
  background: #fff;
  border: 1.5px solid #facc15;
  border-radius: 7px;
  padding: 5px 10px;
  font-size: 1.2rem;
  color: #facc15;
  cursor: pointer;
  box-shadow: 0 1px 4px 0 rgba(250, 204, 21, 0.07);
}

@media (max-width: 900px) {
  .admin-sidebar {
    transform: translateX(110%);
    box-shadow: none;
    border-left: none;
    width: 80vw;
    max-width: 320px;
  }
  .admin-sidebar.open {
    transform: translateX(0);
    box-shadow: 0 0 0 9999px rgba(0,0,0,0.13);
  }
  .admin-sidebar .sidebar-close {
    display: block;
  }
  .admin-main-content {
    margin-right: 0;
    padding: 18px 4px 10px 4px;
  }
  .sidebar-toggle {
    display: block;
  }
}

.modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 6px 32px rgba(0,0,0,0.18);
  padding: 24px;
  min-width: 340px;
  max-width: 500px;
  position: relative;
}
.modal-content img {
  max-width: 90vw;
  max-height: 80vh;
  border-radius: 8px;
  margin-bottom: 8px;
}
.modal-close {
  background: #e74c3c;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 7px 18px;
  font-weight: bold;
  font-size: 15px;
  cursor: pointer;
  margin-top: 12px;
  transition: background 0.2s;
}
.modal-close:hover {
  background: #c0392b;
}

.admin-main-content h2 {
  margin-bottom: 18px;
  margin-top: 18px;
} 