import React, { useState, useEffect } from 'react';
import axios from 'axios';
import CountUp from 'react-countup';
import RenewalRequests from './RenewalRequests';
import {
  FaMoneyBillWave,
  FaUsers,
  FaUserCheck,
  FaUserTimes,
  FaClock,
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaCheck,
  FaTimes,
  FaChartLine
} from 'react-icons/fa';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const SubscriptionManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState(null);
  const [plans, setPlans] = useState([]);
  const [subscriptions, setSubscriptions] = useState([]);
  const [renewalRequests, setRenewalRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Plan modals state
  const [showPlanModal, setShowPlanModal] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);
  const [planForm, setPlanForm] = useState({
    name: '',
    duration_days: '',
    price: '',
    is_active: true
  });
  const [planLoading, setPlanLoading] = useState(false);



  // Filtering and search state
  const [statusFilter, setStatusFilter] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredSubscriptions, setFilteredSubscriptions] = useState([]);

  useEffect(() => {
    fetchData();
  }, []);

  // Filter subscriptions when data or filters change
  useEffect(() => {
    let filtered = subscriptions;

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(sub => sub.status === statusFilter);
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(sub =>
        sub.user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        sub.user.first_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        sub.user.last_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredSubscriptions(filtered);
  }, [subscriptions, statusFilter, searchQuery]);

  // Toast notification function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  // Plan management functions
  const openPlanModal = (plan = null) => {
    if (plan) {
      setEditingPlan(plan);
      setPlanForm({
        name: plan.name,
        duration_days: plan.duration_days,
        price: plan.price,
        is_active: plan.is_active
      });
    } else {
      setEditingPlan(null);
      setPlanForm({
        name: '',
        duration_days: '',
        price: '',
        is_active: true
      });
    }
    setShowPlanModal(true);
  };

  const closePlanModal = () => {
    setShowPlanModal(false);
    setEditingPlan(null);
    setPlanForm({
      name: '',
      duration_days: '',
      price: '',
      is_active: true
    });
  };

  const handlePlanSubmit = async (e) => {
    e.preventDefault();
    setPlanLoading(true);

    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      const payload = {
        name: planForm.name,
        duration_days: parseInt(planForm.duration_days),
        price: parseFloat(planForm.price),
        is_active: planForm.is_active
      };

      if (editingPlan) {
        // Update existing plan
        await axios.put(`${API_BASE_URL}/api/subscriptions/admin/plans/${editingPlan.id}/`, payload, { headers });
      } else {
        // Create new plan
        await axios.post(`${API_BASE_URL}/api/subscriptions/admin/plans/`, payload, { headers });
      }

      closePlanModal();
      fetchData(); // Refresh data
      showMessage(editingPlan ? 'تم تحديث الخطة بنجاح' : 'تم إضافة الخطة بنجاح');
    } catch (err) {
      console.error('Error saving plan:', err);
      showMessage('حدث خطأ أثناء حفظ الخطة', true);
    } finally {
      setPlanLoading(false);
    }
  };

  const handleDeletePlan = async (planId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الخطة؟')) {
      return;
    }

    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      await axios.delete(`${API_BASE_URL}/api/subscriptions/admin/plans/${planId}/`, { headers });
      fetchData(); // Refresh data
      showMessage('تم حذف الخطة بنجاح');
    } catch (err) {
      console.error('Error deleting plan:', err);
      showMessage('حدث خطأ أثناء حذف الخطة', true);
    }
  };



  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      // Fetch stats
      const statsResponse = await axios.get(`${API_BASE_URL}/api/subscriptions/admin/stats/`, { headers });
      setStats(statsResponse.data);

      // Fetch plans
      const plansResponse = await axios.get(`${API_BASE_URL}/api/subscriptions/admin/plans/`, { headers });
      setPlans(plansResponse.data);

      // Fetch subscriptions
      const subsResponse = await axios.get(`${API_BASE_URL}/api/subscriptions/admin/subscriptions/`, { headers });
      setSubscriptions(subsResponse.data);

      // Filter renewal requests
      const renewals = subsResponse.data.filter(sub => sub.renewal_status === 'pending');
      setRenewalRequests(renewals);

    } catch (err) {
      console.error('Error fetching subscription data:', err);
      setError('تعذر جلب بيانات الاشتراكات');
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ title, value, icon, color, bgColor, textColor }) => (
    <div className={`${bgColor} rounded-lg p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-shadow`}>
      <div className="flex items-center justify-between">
        <div>
          <p className={`${textColor} text-sm font-medium mb-1`}>{title}</p>
          <p className="text-2xl font-bold text-gray-800">
            <CountUp end={value} duration={2} separator="," />
          </p>
        </div>
        <div className={`${color} text-white rounded-full p-3`}>
          {icon}
        </div>
      </div>
    </div>
  );

  const tabs = [
    { key: 'overview', label: 'نظرة عامة', icon: <FaChartLine /> },
    { key: 'plans', label: 'خطط الاشتراك', icon: <FaMoneyBillWave /> },
    { key: 'subscriptions', label: 'الاشتراكات', icon: <FaUsers /> },
    { key: 'renewals', label: 'طلبات التجديد', icon: <FaClock /> },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-yellow-600 text-lg">جاري تحميل بيانات الاشتراكات...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center p-4">
        {error}
        <button 
          onClick={fetchData} 
          className="ml-2 text-yellow-600 hover:underline"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Toast Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{successMessage}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setSuccessMessage(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{error}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setError(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-800">💳 إدارة الاشتراكات</h2>
        <button
          onClick={fetchData}
          className="bg-yellow-400 hover:bg-yellow-500 text-yellow-900 px-4 py-2 rounded-lg font-semibold transition-colors"
        >
          🔄 تحديث
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="flex gap-2 border-b border-gray-200 p-4 overflow-x-auto">
          {tabs.map(tab => (
            <button
              key={tab.key}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-semibold transition-colors whitespace-nowrap ${
                activeTab === tab.key 
                  ? 'bg-yellow-400 text-yellow-900 shadow' 
                  : 'bg-transparent text-yellow-900 hover:bg-yellow-100'
              }`}
              onClick={() => setActiveTab(tab.key)}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        <div className="p-6">
          {activeTab === 'overview' && stats && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                  title="إجمالي الاشتراكات"
                  value={stats.basic_stats.total_subscriptions}
                  icon={<FaUsers size={24} />}
                  color="bg-blue-500"
                  bgColor="bg-blue-50"
                  textColor="text-blue-700"
                />
                <StatCard
                  title="الاشتراكات النشطة"
                  value={stats.basic_stats.active_subscriptions}
                  icon={<FaUserCheck size={24} />}
                  color="bg-green-500"
                  bgColor="bg-green-50"
                  textColor="text-green-700"
                />
                <StatCard
                  title="الاشتراكات المنتهية"
                  value={stats.basic_stats.expired_subscriptions}
                  icon={<FaUserTimes size={24} />}
                  color="bg-red-500"
                  bgColor="bg-red-50"
                  textColor="text-red-700"
                />
                <StatCard
                  title="طلبات التجديد"
                  value={stats.basic_stats.pending_renewals}
                  icon={<FaClock size={24} />}
                  color="bg-yellow-500"
                  bgColor="bg-yellow-50"
                  textColor="text-yellow-700"
                />
              </div>

              {/* Revenue Card */}
              <div className="bg-gradient-to-r from-green-400 to-green-600 rounded-lg p-6 text-white shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">إجمالي الإيرادات</h3>
                    <p className="text-3xl font-bold">
                      <CountUp end={stats.basic_stats.total_revenue} duration={2} separator="," /> ج.م
                    </p>
                  </div>
                  <FaMoneyBillWave size={48} className="opacity-80" />
                </div>
              </div>

              {/* Plan Statistics */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4">📊 إحصائيات الخطط</h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-300 px-4 py-2 text-right font-semibold">اسم الخطة</th>
                        <th className="border border-gray-300 px-4 py-2 text-right font-semibold">السعر</th>
                        <th className="border border-gray-300 px-4 py-2 text-right font-semibold">الاشتراكات النشطة</th>
                        <th className="border border-gray-300 px-4 py-2 text-right font-semibold">إجمالي الاشتراكات</th>
                        <th className="border border-gray-300 px-4 py-2 text-right font-semibold">معدل النشاط</th>
                      </tr>
                    </thead>
                    <tbody>
                      {stats.plan_stats.map(plan => (
                        <tr key={plan.id} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-4 py-2 font-medium">{plan.name}</td>
                          <td className="border border-gray-300 px-4 py-2">{plan.price} ج.م</td>
                          <td className="border border-gray-300 px-4 py-2">
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                              {plan.active_count}
                            </span>
                          </td>
                          <td className="border border-gray-300 px-4 py-2">{plan.total_count}</td>
                          <td className="border border-gray-300 px-4 py-2">
                            <div className="flex items-center gap-2">
                              <div className="bg-gray-200 rounded-full h-2 flex-1">
                                <div 
                                  className="bg-green-500 h-2 rounded-full transition-all duration-1000"
                                  style={{ 
                                    width: `${plan.total_count > 0 ? (plan.active_count / plan.total_count) * 100 : 0}%` 
                                  }}
                                ></div>
                              </div>
                              <span className="text-sm text-gray-600">
                                {plan.total_count > 0 ? Math.round((plan.active_count / plan.total_count) * 100) : 0}%
                              </span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>




            </div>
          )}

          {activeTab === 'plans' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold text-gray-800">خطط الاشتراك</h3>
                <button
                  onClick={() => openPlanModal()}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
                >
                  <FaPlus size={16} />
                  إضافة خطة جديدة
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {plans.map(plan => (
                  <div key={plan.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow hover:shadow-lg transition-shadow">
                    <div className="flex justify-between items-start mb-4">
                      <h4 className="text-lg font-bold text-gray-800">{plan.name}</h4>
                      <div className="flex gap-2">
                        <button
                          onClick={() => openPlanModal(plan)}
                          className="text-blue-500 hover:text-blue-700 p-1 rounded transition-colors"
                          title="تعديل الخطة"
                        >
                          <FaEdit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeletePlan(plan.id)}
                          className="text-red-500 hover:text-red-700 p-1 rounded transition-colors"
                          title="حذف الخطة"
                        >
                          <FaTrash size={16} />
                        </button>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <p className="text-2xl font-bold text-green-600">{plan.price} ج.م</p>
                      <p className="text-gray-600">{plan.duration_days} يوم</p>
                      <div className={`inline-block px-2 py-1 rounded text-sm ${
                        plan.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {plan.is_active ? 'نشطة' : 'غير نشطة'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'subscriptions' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold text-gray-800">جميع الاشتراكات ({filteredSubscriptions.length} من {subscriptions.length})</h3>
                <div className="flex gap-2">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                  >
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="expired">منتهي</option>
                    <option value="pending_renewal">في انتظار التجديد</option>
                  </select>
                  <input
                    type="text"
                    placeholder="البحث بالإيميل..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm w-48"
                  />
                  {(statusFilter || searchQuery) && (
                    <button
                      onClick={() => {
                        setStatusFilter('');
                        setSearchQuery('');
                      }}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                    >
                      مسح الفلاتر
                    </button>
                  )}
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخطة</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ البداية</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ النهاية</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredSubscriptions.map(subscription => (
                        <tr key={subscription.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{subscription.user_email}</div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {subscription.plan ? subscription.plan.name : 'غير محدد'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {subscription.plan ? `${subscription.plan.price} ج.م` : ''}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(subscription.start_date).toLocaleDateString('ar-EG')}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(subscription.end_date).toLocaleDateString('ar-EG')}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              subscription.status === 'active'
                                ? 'bg-green-100 text-green-800'
                                : subscription.status === 'expired'
                                ? 'bg-red-100 text-red-800'
                                : subscription.status === 'pending_renewal'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {subscription.status === 'active' && 'نشط'}
                              {subscription.status === 'expired' && 'منتهي'}
                              {subscription.status === 'pending_renewal' && 'في انتظار التجديد'}
                              {subscription.status === 'denied' && 'مرفوض'}
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                            <span className="text-gray-500 text-sm">
                              {subscription.created_at ? new Date(subscription.created_at).toLocaleDateString('ar-EG') : 'غير محدد'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {subscriptions.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد اشتراكات حالياً
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'renewals' && (
            <div className="space-y-6">
              <h3 className="text-xl font-bold text-gray-800">طلبات التجديد ({renewalRequests.length})</h3>
              <RenewalRequests renewalRequests={renewalRequests} onRefresh={fetchData} />
            </div>
          )}
        </div>
      </div>

      {/* Plan Modal */}
      {showPlanModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              {editingPlan ? 'تعديل الخطة' : 'إضافة خطة جديدة'}
            </h3>

            <form onSubmit={handlePlanSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  اسم الخطة
                </label>
                <input
                  type="text"
                  value={planForm.name}
                  onChange={(e) => setPlanForm({...planForm, name: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المدة (بالأيام)
                </label>
                <input
                  type="number"
                  value={planForm.duration_days}
                  onChange={(e) => setPlanForm({...planForm, duration_days: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  min="1"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  السعر (ج.م)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={planForm.price}
                  onChange={(e) => setPlanForm({...planForm, price: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  min="0"
                  required
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={planForm.is_active}
                  onChange={(e) => setPlanForm({...planForm, is_active: e.target.checked})}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="mr-2 block text-sm text-gray-900">
                  خطة نشطة
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  disabled={planLoading}
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors disabled:opacity-50"
                >
                  {planLoading ? 'جاري الحفظ...' : (editingPlan ? 'تحديث' : 'إضافة')}
                </button>
                <button
                  type="button"
                  onClick={closePlanModal}
                  disabled={planLoading}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors disabled:opacity-50"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionManagement;
