import React, { useState } from 'react';
import axios from 'axios';
import { 
  FaCheck, 
  FaTimes, 
  FaEye, 
  FaClock, 
  FaUser,
  FaMoneyBillWave,
  FaCalendarAlt,
  FaImage
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

// Helper function to get secure image URL
const getSecureImageUrl = (subscriptionId) => {
  const token = localStorage.getItem('access');
  return `${API_BASE_URL}/api/subscriptions/secure/image/${subscriptionId}/?token=${token}`;
};

const RenewalRequests = ({ renewalRequests, onRefresh }) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [actionLoading, setActionLoading] = useState(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(null);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Toast notification function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  const handleAction = async (subscriptionId, action, reason = '') => {
    setActionLoading(subscriptionId);
    try {
      const token = localStorage.getItem('access');
      const payload = { action };
      if (action === 'deny' && reason) {
        payload.denial_reason = reason;
      }

      await axios.post(
        `${API_BASE_URL}/api/subscriptions/renewal-action/${subscriptionId}/`,
        payload,
        { headers: { 'Authorization': `Bearer ${token}` } }
      );

      // Refresh data
      onRefresh();

      // Close modal if open
      setShowRejectModal(null);
      setRejectionReason('');

      showMessage(action === 'approve' ? 'تمت الموافقة على الطلب بنجاح' : 'تم رفض الطلب بنجاح');
    } catch (err) {
      console.error('Error processing renewal request:', err);
      showMessage('حدث خطأ أثناء معالجة الطلب', true);
    } finally {
      setActionLoading(null);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (renewalRequests.length === 0) {
    return (
      <div className="text-center py-12">
        <FaClock className="mx-auto mb-4 text-gray-400" size={48} />
        <h3 className="text-lg font-semibold text-gray-600 mb-2">لا توجد طلبات تجديد</h3>
        <p className="text-gray-500">جميع طلبات التجديد تم معالجتها</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Toast Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{successMessage}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setSuccessMessage(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{error}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setError(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}

      <div className="grid gap-6">
        {renewalRequests.map(request => (
          <div key={request.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow hover:shadow-lg transition-shadow">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* User Info */}
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <FaUser className="text-blue-500" />
                  <div>
                    <h4 className="font-semibold text-gray-800">
                      {request.user.first_name} {request.user.last_name}
                    </h4>
                    <p className="text-sm text-gray-600">{request.user.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <FaCalendarAlt className="text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">تاريخ الطلب</p>
                    <p className="text-sm text-gray-600">{formatDate(request.renewal_requested_at)}</p>
                  </div>
                </div>
              </div>

              {/* Plan Info */}
              <div className="space-y-3">
                {request.plan && (
                  <div className="flex items-center gap-3">
                    <FaMoneyBillWave className="text-green-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">الخطة المطلوبة</p>
                      <p className="font-semibold text-gray-800">{request.plan.name}</p>
                      <p className="text-sm text-green-600">{request.plan.price} ج.م - {request.plan.duration_days} يوم</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <FaClock className="text-yellow-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">الحالة</p>
                    <span className="inline-block px-2 py-1 rounded text-sm bg-yellow-100 text-yellow-800">
                      قيد المراجعة
                    </span>
                  </div>
                </div>
              </div>

              {/* Payment Screenshot & Actions */}
              <div className="space-y-3">
                {request.renewal_screenshot && (
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">إثبات الدفع</p>
                    <div className="relative group">
                      <img
                        src={getSecureImageUrl(request.id)}
                        alt="إثبات الدفع"
                        className="w-full h-32 object-cover rounded border border-gray-200 cursor-pointer hover:opacity-80 transition-opacity"
                        onClick={() => setSelectedImage(getSecureImageUrl(request.id))}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded flex items-center justify-center">
                        <button
                          onClick={() => setSelectedImage(getSecureImageUrl(request.id))}
                          className="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-3 py-1 rounded-full text-sm font-medium transition-opacity duration-200 flex items-center gap-2"
                        >
                          <FaEye size={14} />
                          عرض بالحجم الكامل
                        </button>
                      </div>
                      <div className="absolute top-2 right-2 flex gap-1">
                        <button
                          onClick={() => setSelectedImage(getSecureImageUrl(request.id))}
                          className="bg-blue-500 hover:bg-blue-600 text-white p-1 rounded text-xs"
                          title="عرض بالحجم الكامل"
                        >
                          <FaEye size={12} />
                        </button>
                        <a
                          href={getSecureImageUrl(request.id)}
                          download
                          className="bg-green-500 hover:bg-green-600 text-white p-1 rounded text-xs"
                          title="تحميل الصورة"
                        >
                          <FaImage size={12} />
                        </a>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      انقر للعرض بالحجم الكامل أو التحميل
                    </p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <button
                    onClick={() => handleAction(request.id, 'approve')}
                    disabled={actionLoading === request.id}
                    className="flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded font-semibold transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
                  >
                    <FaCheck size={14} />
                    {actionLoading === request.id ? 'جاري المعالجة...' : 'موافقة'}
                  </button>
                  <button
                    onClick={() => setShowRejectModal(request.id)}
                    disabled={actionLoading === request.id}
                    className="flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded font-semibold transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
                  >
                    <FaTimes size={14} />
                    رفض
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-6xl max-h-full w-full">
            <div className="bg-white rounded-lg p-4 shadow-2xl">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold text-gray-800">إثبات الدفع - عرض بالحجم الكامل</h3>
                <div className="flex gap-2">
                  <a
                    href={selectedImage}
                    download
                    className="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm flex items-center gap-2"
                  >
                    <FaImage size={14} />
                    تحميل الصورة
                  </a>
                  <button
                    onClick={() => setSelectedImage(null)}
                    className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm flex items-center gap-2"
                  >
                    <FaTimes size={14} />
                    إغلاق
                  </button>
                </div>
              </div>
              <div className="max-h-[70vh] overflow-auto">
                <img
                  src={selectedImage}
                  alt="إثبات الدفع"
                  className="w-full h-auto object-contain rounded border border-gray-200"
                />
              </div>
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  يمكنك تحميل الصورة أو إغلاق النافذة بالضغط على الأزرار أعلاه
                </p>
              </div>
            </div>
          </div>
          {/* Click outside to close */}
          <div
            className="absolute inset-0 -z-10"
            onClick={() => setSelectedImage(null)}
          ></div>
        </div>
      )}

      {/* Rejection Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-4">رفض طلب التجديد</h3>
            <p className="text-gray-600 mb-4">يرجى إدخال سبب الرفض:</p>
            <textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              className="w-full border border-gray-300 rounded p-3 mb-4 h-24 resize-none"
              placeholder="اكتب سبب الرفض هنا..."
            />
            <div className="flex gap-3">
              <button
                onClick={() => handleAction(showRejectModal, 'deny', rejectionReason)}
                disabled={!rejectionReason.trim() || actionLoading === showRejectModal}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded font-semibold transition-colors disabled:opacity-50"
              >
                {actionLoading === showRejectModal ? 'جاري الرفض...' : 'تأكيد الرفض'}
              </button>
              <button
                onClick={() => {
                  setShowRejectModal(null);
                  setRejectionReason('');
                }}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded font-semibold transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RenewalRequests;
