import React, { useContext, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { AuthContext } from '../context/AuthContext';
import { useToast, useApiErrorHandler } from '../components/UI/Toast';
import { handleApiError } from '../utils/errorHandler';
import SEOHead from './SEO/SEOHead';
import './LoginForm.css';
import { Link, useNavigate } from 'react-router-dom';

const schema = yup.object().shape({
  email: yup.string().email('صيغة البريد الإلكتروني غير صحيحة').required('البريد الإلكتروني مطلوب'),
  password: yup.string().required('كلمة المرور مطلوبة'),
});

const LoginForm = () => {
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: yupResolver(schema),
  });
  const { login, loading } = useContext(AuthContext);
  const navigate = useNavigate();
  const { showSuccess, showError } = useToast();
  const { handleError } = useApiErrorHandler();

  const [serverError, setServerError] = useState(null);
  const [success, setSuccess] = useState(false);

  const onSubmit = async (data) => {
    try {
      setServerError(null);
      const { success, error, user: loggedInUser } = await login(data);

      if (success && loggedInUser) {
        // showSuccess(`مرحباً ${loggedInUser.first_name}! تم تسجيل الدخول بنجاح`); // معطل مؤقتاً

        // توجيه المستخدم حسب نوعه
        if (loggedInUser.is_superuser) {
          navigate('/admin-user');
        } else if (loggedInUser.is_instructor) {
          navigate('/instructor-dashboard');
        } else if (loggedInUser.is_student) {
          navigate('/student-profile');
        } else {
          navigate('/');
        }
      } else {
        // معالجة الأخطاء باستخدام Error Handler
        if (error) {
          const errorInfo = handleApiError({ response: { data: error } }, 'تسجيل الدخول');
          setServerError(errorInfo.message);
          showError(errorInfo.message);
        } else {
          setServerError('بيانات الدخول غير صحيحة');
          showError('بيانات الدخول غير صحيحة');
        }
      }
    } catch (err) {
      handleError(err, 'تسجيل الدخول');
      setServerError('حدث خطأ أثناء تسجيل الدخول');
    }
  };

  if (success) {
    return (
      <>
        <SEOHead
          title="تم تسجيل الدخول بنجاح - منصة قانوني التعليمية"
          description="تم تسجيل دخولك بنجاح في منصة قانوني التعليمية. استمتع بالمحاضرات التفاعلية والمحتوى التعليمي المتميز."
          keywords="تسجيل دخول ناجح، منصة قانوني، تعليم قانوني"
          url="https://qanony.com/login"
          noIndex={true}
        />
        <div className="login-success">
          <h2>تم تسجيل الدخول بنجاح</h2>
        </div>
      </>
    );
  }

  return (
    <>
      <SEOHead
        title="تسجيل الدخول - منصة قانوني التعليمية"
        description="سجل دخولك إلى منصة قانوني التعليمية للوصول إلى المحاضرات التفاعلية، الاختبارات، المكتبة القانونية، وجميع الخدمات التعليمية المتميزة."
        keywords="تسجيل الدخول، منصة قانوني، تعليم قانوني، كلية الحقوق، دخول الطلاب، تسجيل دخول المنصة"
        url="https://qanony.com/login"
        type="website"
      />
    <form className="login-form" onSubmit={handleSubmit(onSubmit)}>
      <h2 className="title">تسجيل الدخول</h2>

      <div className="input-group">
        <label htmlFor="email">البريد الإلكتروني</label>
        <input id="email" type="email" {...register('email')} />
        {errors.email && <span className="error">{errors.email.message}</span>}
      </div>

      <div className="input-group">
        <label htmlFor="password">كلمة المرور</label>
        <input id="password" type="password" {...register('password')} />
        {errors.password && <span className="error">{errors.password.message}</span>}
      </div>

      {serverError && <p className="server-error">{serverError}</p>}

      <button
        type="submit"
        disabled={loading}
        className={`w-full py-3 px-4 rounded-md text-white font-semibold transition-colors ${
          loading
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-yellow-400 hover:bg-yellow-500 text-yellow-900'
        }`}
      >
        {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
      </button>

      <p className="switch-link">
        ليس لديك حساب؟ <Link to="/register">إنشاء حساب جديد</Link>
      </p>
      <p className="switch-link">
        <Link to="/password-reset">نسيت كلمة المرور؟</Link>
      </p>
    </form>
    </>
  );
};

export default LoginForm; 