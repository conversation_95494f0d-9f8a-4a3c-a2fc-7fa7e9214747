.verify-container {
  max-width: 400px;
  margin: 60px auto;
  background: #fff;
  padding: 30px 35px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  direction: rtl;
  text-align: center;
}

.title {
  margin-bottom: 20px;
  color: var(--text);
}

.verify-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.verify-form label {
  text-align: right;
  font-weight: 600;
  color: var(--secondary-text);
}

.verify-form input {
  padding: 10px 14px;
  border: 1px solid var(--secondary-bg);
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
}

.verify-form input:focus {
  outline: 2px solid var(--primary);
  border-color: var(--primary);
}

.verify-form button {
  padding: 12px;
  background: var(--primary);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.verify-form button:hover {
  background: #eab308;
}

.success {
  color: green;
}

.error {
  color: red;
} 