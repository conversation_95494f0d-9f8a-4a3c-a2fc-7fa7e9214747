import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  FaBell,
  FaEnvelope,
  FaMobile,
  FaDesktop,
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaPaperPlane,
  FaUsers,
  FaChartLine,
  FaCheck,
  FaTimes,
  FaClock,
  FaSearch,
  FaFilter
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const NotificationsManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Templates state
  const [templates, setTemplates] = useState([]);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [templateForm, setTemplateForm] = useState({
    name: '',
    subject_ar: '',
    content_ar: '',
    notification_type: 'in_app',
    is_active: true
  });

  // Notifications state
  const [notifications, setNotifications] = useState([]);
  const [showSendModal, setShowSendModal] = useState(false);
  const [sendForm, setSendForm] = useState({
    recipients: [],
    template_id: '',
    subject_ar: '',
    content_ar: '',
    notification_type: 'in_app',
    scheduled_at: ''
  });

  // Users for sending notifications
  const [users, setUsers] = useState([]);

  // Stats state
  const [stats, setStats] = useState({
    total_templates: 0,
    active_templates: 0,
    total_notifications: 0,
    sent_notifications: 0,
    pending_notifications: 0,
    failed_notifications: 0,
    recent_notifications: []
  });

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');

  // Notification details modal state
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState(null);

  const tabs = [
    { key: 'overview', label: 'نظرة عامة', icon: <FaChartLine /> },
    { key: 'templates', label: 'قوالب الإشعارات', icon: <FaEnvelope /> },
    { key: 'notifications', label: 'الإشعارات', icon: <FaBell /> },
    { key: 'send', label: 'إرسال إشعار', icon: <FaPaperPlane /> }
  ];

  const notificationTypes = [
    { value: 'in_app', label: 'داخل التطبيق', icon: <FaDesktop /> },
    { value: 'email', label: 'بريد إلكتروني', icon: <FaEnvelope /> },
    { value: 'sms', label: 'رسالة نصية', icon: <FaMobile /> }
  ];

  useEffect(() => {
    fetchData();
  }, []);

  // Show message function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    // Clear message after 5 seconds
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      // Fetch templates
      const templatesRes = await axios.get(`${API_BASE_URL}/api/notifications/templates/`, { headers });
      setTemplates(Array.isArray(templatesRes.data) ? templatesRes.data : templatesRes.data.results || []);

      // Fetch notifications
      const notificationsRes = await axios.get(`${API_BASE_URL}/api/notifications/`, { headers });
      setNotifications(Array.isArray(notificationsRes.data) ? notificationsRes.data : notificationsRes.data.results || []);

      // Fetch users for sending notifications
      try {
        const users = [];

        // Fetch instructors
        try {
          const instructorsRes = await axios.get(`${API_BASE_URL}/api/auth/instructors/`, { headers });
          const instructors = instructorsRes.data.results || instructorsRes.data;
          instructors.forEach(instructor => {
            users.push({
              id: instructor.id,
              email: instructor.email,
              name: `${instructor.first_name} ${instructor.last_name}`,
              type: 'instructor'
            });
          });
        } catch (err) {
          console.log('Instructors not available:', err);
        }

        // Fetch students from profiles
        try {
          const studentsRes = await axios.get(`${API_BASE_URL}/api/students/profiles/`, { headers });
          const students = studentsRes.data.results || studentsRes.data;
          students.forEach(student => {
            if (student.user) {
              users.push({
                id: student.user.id || student.user,
                email: student.user.email || `Student ${student.id}`,
                name: student.user.first_name ? `${student.user.first_name} ${student.user.last_name}` : `Student ${student.id}`,
                type: 'student'
              });
            }
          });
        } catch (err) {
          console.log('Students not available:', err);
        }

        setUsers(users);
      } catch (err) {
        console.log('Error fetching users:', err);
        setUsers([]);
      }

      // Calculate stats
      const templatesData = Array.isArray(templatesRes.data) ? templatesRes.data : templatesRes.data.results || [];
      const notificationsData = Array.isArray(notificationsRes.data) ? notificationsRes.data : notificationsRes.data.results || [];

      const totalTemplates = templatesData.length;
      const activeTemplates = templatesData.filter(t => t.is_active).length;
      const totalNotifications = notificationsData.length;
      const sentNotifications = notificationsData.filter(n => n.status === 'sent').length;
      const pendingNotifications = notificationsData.filter(n => n.status === 'pending').length;
      const failedNotifications = notificationsData.filter(n => n.status === 'failed').length;

      setStats({
        total_templates: totalTemplates,
        active_templates: activeTemplates,
        total_notifications: totalNotifications,
        sent_notifications: sentNotifications,
        pending_notifications: pendingNotifications,
        failed_notifications: failedNotifications,
        recent_notifications: notificationsData
          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
          .slice(0, 5)
      });

    } catch (err) {
      console.error('Error fetching notifications data:', err);
      setError('تعذر جلب بيانات الإشعارات');
    } finally {
      setLoading(false);
    }
  };

  // Template management functions
  const openTemplateModal = (template = null) => {
    if (template) {
      setEditingTemplate(template);
      setTemplateForm({
        name: template.name,
        subject_ar: template.subject_ar,
        content_ar: template.content_ar,
        notification_type: template.notification_type,
        is_active: template.is_active
      });
    } else {
      setEditingTemplate(null);
      setTemplateForm({
        name: '',
        subject_ar: '',
        content_ar: '',
        notification_type: 'in_app',
        is_active: true
      });
    }
    setShowTemplateModal(true);
  };

  const closeTemplateModal = () => {
    setShowTemplateModal(false);
    setEditingTemplate(null);
    setTemplateForm({
      name: '',
      subject_ar: '',
      content_ar: '',
      notification_type: 'in_app',
      is_active: true
    });
  };

  const handleTemplateSubmit = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      if (editingTemplate) {
        await axios.patch(`${API_BASE_URL}/api/notifications/templates/${editingTemplate.id}/`, templateForm, { headers });
      } else {
        await axios.post(`${API_BASE_URL}/api/notifications/templates/`, templateForm, { headers });
      }

      closeTemplateModal();
      fetchData();
      showMessage('تم حفظ القالب بنجاح');
    } catch (err) {
      console.error('Error saving template:', err);
      showMessage('حدث خطأ أثناء حفظ القالب', true);
    }
  };

  const handleDeleteTemplate = async (templateId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا القالب؟')) {
      return;
    }

    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };
      
      await axios.delete(`${API_BASE_URL}/api/notifications/templates/${templateId}/`, { headers });
      fetchData();
      showMessage('تم حذف القالب بنجاح');
    } catch (err) {
      console.error('Error deleting template:', err);
      showMessage('حدث خطأ أثناء حذف القالب', true);
    }
  };

  // Send notification functions
  const openSendModal = () => {
    setSendForm({
      recipients: [],
      template_id: '',
      subject_ar: '',
      content_ar: '',
      notification_type: 'in_app',
      scheduled_at: ''
    });
    setShowSendModal(true);
  };

  const closeSendModal = () => {
    setShowSendModal(false);
    setSendForm({
      recipients: [],
      template_id: '',
      subject_ar: '',
      content_ar: '',
      notification_type: 'in_app',
      scheduled_at: ''
    });
  };

  const handleSendNotification = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      await axios.post(`${API_BASE_URL}/api/notifications/send/`, sendForm, { headers });
      
      closeSendModal();
      fetchData();
      showMessage('تم إرسال الإشعار بنجاح');
    } catch (err) {
      console.error('Error sending notification:', err);
      showMessage('حدث خطأ أثناء إرسال الإشعار', true);
    }
  };

  const handleTemplateSelect = (templateId) => {
    const template = templates.find(t => t.id === parseInt(templateId));
    if (template) {
      setSendForm({
        ...sendForm,
        template_id: templateId,
        subject_ar: template.subject_ar,
        content_ar: template.content_ar,
        notification_type: template.notification_type
      });
    }
  };

  // Show notification details
  const showNotificationDetails = (notification) => {
    setSelectedNotification(notification);
    setShowDetailsModal(true);
  };

  const closeDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedNotification(null);
  };

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = !searchTerm || 
      notification.subject_ar.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.content_ar.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.recipient?.email?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || notification.status === statusFilter;
    const matchesType = !typeFilter || notification.notification_type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  if (loading) return <div className="text-center py-8">جاري التحميل...</div>;
  if (error) return <div className="text-center py-8 text-red-600">{error}</div>;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">إدارة الإشعارات</h2>
        <div className="flex gap-2">
          <button
            onClick={() => fetchData()}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            🔄 تحديث
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{successMessage}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setSuccessMessage(null)}
          >
            <svg className="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>إغلاق</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </span>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{error}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setError(null)}
          >
            <svg className="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>إغلاق</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </span>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 space-x-reverse">
          {tabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === tab.key
                  ? 'border-yellow-500 text-yellow-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800">نظرة عامة على الإشعارات</h3>
            
            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4">
              <div className="bg-blue-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-blue-500 text-white rounded-full p-3 mb-2">
                  <FaEnvelope size={24} />
                </div>
                <div className="text-2xl font-bold text-blue-800">{stats.total_templates}</div>
                <div className="text-blue-700 mt-1">إجمالي القوالب</div>
              </div>

              <div className="bg-green-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-green-500 text-white rounded-full p-3 mb-2">
                  <FaCheck size={24} />
                </div>
                <div className="text-2xl font-bold text-green-800">{stats.active_templates}</div>
                <div className="text-green-700 mt-1">القوالب النشطة</div>
              </div>

              <div className="bg-purple-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-purple-500 text-white rounded-full p-3 mb-2">
                  <FaBell size={24} />
                </div>
                <div className="text-2xl font-bold text-purple-800">{stats.total_notifications}</div>
                <div className="text-purple-700 mt-1">إجمالي الإشعارات</div>
              </div>

              <div className="bg-teal-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-teal-500 text-white rounded-full p-3 mb-2">
                  <FaPaperPlane size={24} />
                </div>
                <div className="text-2xl font-bold text-teal-800">{stats.sent_notifications}</div>
                <div className="text-teal-700 mt-1">تم الإرسال</div>
              </div>

              <div className="bg-yellow-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-yellow-500 text-white rounded-full p-3 mb-2">
                  <FaClock size={24} />
                </div>
                <div className="text-2xl font-bold text-yellow-800">{stats.pending_notifications}</div>
                <div className="text-yellow-700 mt-1">قيد الانتظار</div>
              </div>

              <div className="bg-red-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-red-500 text-white rounded-full p-3 mb-2">
                  <FaTimes size={24} />
                </div>
                <div className="text-2xl font-bold text-red-800">{stats.failed_notifications}</div>
                <div className="text-red-700 mt-1">فشل الإرسال</div>
              </div>
            </div>

            {/* Recent Notifications */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h4 className="text-lg font-bold text-gray-800 mb-4">الإشعارات الحديثة</h4>
              <div className="space-y-3">
                {stats.recent_notifications.length > 0 ? (
                  stats.recent_notifications.map((notification, index) => (
                    <div key={notification.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${
                          notification.notification_type === 'email' ? 'bg-blue-100 text-blue-600' :
                          notification.notification_type === 'sms' ? 'bg-green-100 text-green-600' :
                          'bg-purple-100 text-purple-600'
                        }`}>
                          {notification.notification_type === 'email' ? <FaEnvelope size={16} /> :
                           notification.notification_type === 'sms' ? <FaMobile size={16} /> :
                           <FaDesktop size={16} />}
                        </div>
                        <div>
                          <div className="font-medium text-gray-800">{notification.subject_ar}</div>
                          <div className="text-sm text-gray-600">
                            إلى: {notification.recipient?.email || 'غير محدد'}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          notification.status === 'sent' ? 'bg-green-100 text-green-800' :
                          notification.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {notification.status === 'sent' ? 'تم الإرسال' :
                           notification.status === 'pending' ? 'قيد الانتظار' : 'فشل'}
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {new Date(notification.created_at).toLocaleDateString('ar-EG')}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    لا توجد إشعارات حديثة
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Templates Tab */}
        {activeTab === 'templates' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-bold text-gray-800">إدارة قوالب الإشعارات ({templates.length})</h3>
              <button
                onClick={() => openTemplateModal()}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
              >
                <FaPlus size={16} />
                إضافة قالب جديد
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم القالب</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الموضوع</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">نوع الإشعار</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {templates.map(template => (
                      <tr key={template.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{template.name}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{template.subject_ar}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center gap-2">
                            {template.notification_type === 'email' ? <FaEnvelope className="text-blue-500" /> :
                             template.notification_type === 'sms' ? <FaMobile className="text-green-500" /> :
                             <FaDesktop className="text-purple-500" />}
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              template.notification_type === 'email' ? 'bg-blue-100 text-blue-800' :
                              template.notification_type === 'sms' ? 'bg-green-100 text-green-800' :
                              'bg-purple-100 text-purple-800'
                            }`}>
                              {notificationTypes.find(t => t.value === template.notification_type)?.label}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            template.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {template.is_active ? 'نشط' : 'غير نشط'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <button
                              onClick={() => openTemplateModal(template)}
                              className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                              title="تعديل"
                            >
                              <FaEdit size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteTemplate(template.id)}
                              className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                              title="حذف"
                            >
                              <FaTrash size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {templates.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد قوالب مسجلة حالياً
                </div>
              )}
            </div>
          </div>
        )}

        {/* Notifications Tab */}
        {activeTab === 'notifications' && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800">إدارة الإشعارات ({notifications.length})</h3>

            {/* Search and Filter */}
            <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex-1 min-w-64">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث في الإشعارات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
              >
                <option value="">جميع الحالات</option>
                <option value="pending">قيد الانتظار</option>
                <option value="sent">تم الإرسال</option>
                <option value="failed">فشل</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
              >
                <option value="">جميع الأنواع</option>
                <option value="in_app">داخل التطبيق</option>
                <option value="email">بريد إلكتروني</option>
                <option value="sms">رسالة نصية</option>
              </select>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستلم</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الموضوع</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الإنشاء</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الإرسال</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredNotifications.map(notification => (
                      <tr key={notification.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {notification.recipient?.email || 'غير محدد'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="max-w-xs truncate" title={notification.subject_ar}>
                            {notification.subject_ar}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center gap-2">
                            {notification.notification_type === 'email' ? <FaEnvelope className="text-blue-500" /> :
                             notification.notification_type === 'sms' ? <FaMobile className="text-green-500" /> :
                             <FaDesktop className="text-purple-500" />}
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              notification.notification_type === 'email' ? 'bg-blue-100 text-blue-800' :
                              notification.notification_type === 'sms' ? 'bg-green-100 text-green-800' :
                              'bg-purple-100 text-purple-800'
                            }`}>
                              {notificationTypes.find(t => t.value === notification.notification_type)?.label}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            notification.status === 'sent' ? 'bg-green-100 text-green-800' :
                            notification.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {notification.status === 'sent' ? 'تم الإرسال' :
                             notification.status === 'pending' ? 'قيد الانتظار' : 'فشل'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(notification.created_at).toLocaleDateString('ar-EG')}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {notification.sent_at
                            ? new Date(notification.sent_at).toLocaleDateString('ar-EG')
                            : '—'
                          }
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                            title="عرض التفاصيل"
                            onClick={() => showNotificationDetails(notification)}
                          >
                            <FaEye size={16} />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {filteredNotifications.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  {searchTerm || statusFilter || typeFilter ? 'لا توجد نتائج للبحث' : 'لا توجد إشعارات حالياً'}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Send Notification Tab */}
        {activeTab === 'send' && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800">إرسال إشعار جديد</h3>

            <form onSubmit={handleSendNotification} className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recipients Selection */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-800">اختيار المستلمين</h4>
                  <div className="border border-gray-300 rounded-lg p-4 max-h-64 overflow-y-auto">
                    {users.length > 0 ? (
                      users.map(user => (
                        <label key={user.id} className="flex items-center space-x-3 space-x-reverse py-2">
                          <input
                            type="checkbox"
                            checked={sendForm.recipients.includes(user.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSendForm({
                                  ...sendForm,
                                  recipients: [...sendForm.recipients, user.id]
                                });
                              } else {
                                setSendForm({
                                  ...sendForm,
                                  recipients: sendForm.recipients.filter(id => id !== user.id)
                                });
                              }
                            }}
                            className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                          />
                          <div className="flex flex-col">
                            <span className="text-sm text-gray-900">{user.name}</span>
                            <span className="text-xs text-gray-500">{user.email}</span>
                          </div>
                          <span className={`text-xs px-2 py-1 rounded ${
                            user.type === 'instructor' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {user.type === 'instructor' ? 'مدرس' : 'طالب'}
                          </span>
                        </label>
                      ))
                    ) : (
                      <div className="text-center py-4 text-gray-500">
                        لا توجد مستخدمين متاحين
                      </div>
                    )}
                  </div>
                  <div className="text-sm text-gray-600">
                    تم اختيار {sendForm.recipients.length} مستخدم
                  </div>
                </div>

                {/* Notification Content */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-800">محتوى الإشعار</h4>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      استخدام قالب (اختياري)
                    </label>
                    <select
                      value={sendForm.template_id}
                      onChange={(e) => handleTemplateSelect(e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    >
                      <option value="">اختر قالب أو اكتب محتوى مخصص</option>
                      {templates.filter(t => t.is_active).map(template => (
                        <option key={template.id} value={template.id}>
                          {template.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      نوع الإشعار
                    </label>
                    <select
                      value={sendForm.notification_type}
                      onChange={(e) => setSendForm({...sendForm, notification_type: e.target.value})}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                      required
                    >
                      {notificationTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الموضوع
                    </label>
                    <input
                      type="text"
                      value={sendForm.subject_ar}
                      onChange={(e) => setSendForm({...sendForm, subject_ar: e.target.value})}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      المحتوى
                    </label>
                    <textarea
                      value={sendForm.content_ar}
                      onChange={(e) => setSendForm({...sendForm, content_ar: e.target.value})}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                      rows="4"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      جدولة الإرسال (اختياري)
                    </label>
                    <input
                      type="datetime-local"
                      value={sendForm.scheduled_at}
                      onChange={(e) => setSendForm({...sendForm, scheduled_at: e.target.value})}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    />
                  </div>
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  disabled={sendForm.recipients.length === 0}
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
                >
                  <FaPaperPlane size={16} />
                  إرسال الإشعار
                </button>
              </div>
            </form>
          </div>
        )}
      </div>

      {/* Template Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              {editingTemplate ? 'تعديل القالب' : 'إضافة قالب جديد'}
            </h3>

            <form onSubmit={handleTemplateSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  اسم القالب
                </label>
                <input
                  type="text"
                  value={templateForm.name}
                  onChange={(e) => setTemplateForm({...templateForm, name: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  نوع الإشعار
                </label>
                <select
                  value={templateForm.notification_type}
                  onChange={(e) => setTemplateForm({...templateForm, notification_type: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                >
                  {notificationTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الموضوع
                </label>
                <input
                  type="text"
                  value={templateForm.subject_ar}
                  onChange={(e) => setTemplateForm({...templateForm, subject_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المحتوى
                </label>
                <textarea
                  value={templateForm.content_ar}
                  onChange={(e) => setTemplateForm({...templateForm, content_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  rows="4"
                  required
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="template_is_active"
                  checked={templateForm.is_active}
                  onChange={(e) => setTemplateForm({...templateForm, is_active: e.target.checked})}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="template_is_active" className="mr-2 block text-sm text-gray-900">
                  قالب نشط
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  {editingTemplate ? 'تحديث' : 'إضافة'}
                </button>
                <button
                  type="button"
                  onClick={closeTemplateModal}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Notification Details Modal */}
      {showDetailsModal && selectedNotification && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              تفاصيل الإشعار
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المستلم
                </label>
                <div className="p-3 bg-gray-50 rounded-lg">
                  {selectedNotification.recipient?.email || 'غير محدد'}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  نوع الإشعار
                </label>
                <div className="p-3 bg-gray-50 rounded-lg flex items-center gap-2">
                  {selectedNotification.notification_type === 'email' ? <FaEnvelope className="text-blue-500" /> :
                   selectedNotification.notification_type === 'sms' ? <FaMobile className="text-green-500" /> :
                   <FaDesktop className="text-purple-500" />}
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    selectedNotification.notification_type === 'email' ? 'bg-blue-100 text-blue-800' :
                    selectedNotification.notification_type === 'sms' ? 'bg-green-100 text-green-800' :
                    'bg-purple-100 text-purple-800'
                  }`}>
                    {notificationTypes.find(t => t.value === selectedNotification.notification_type)?.label}
                  </span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الموضوع
                </label>
                <div className="p-3 bg-gray-50 rounded-lg">
                  {selectedNotification.subject_ar}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المحتوى
                </label>
                <div className="p-3 bg-gray-50 rounded-lg whitespace-pre-wrap">
                  {selectedNotification.content_ar}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الحالة
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      selectedNotification.status === 'sent' ? 'bg-green-100 text-green-800' :
                      selectedNotification.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {selectedNotification.status === 'sent' ? 'تم الإرسال' :
                       selectedNotification.status === 'pending' ? 'قيد الانتظار' : 'فشل'}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ الإنشاء
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    {new Date(selectedNotification.created_at).toLocaleString('ar-EG')}
                  </div>
                </div>
              </div>

              {selectedNotification.sent_at && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ الإرسال
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    {new Date(selectedNotification.sent_at).toLocaleString('ar-EG')}
                  </div>
                </div>
              )}

              {selectedNotification.scheduled_at && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ الجدولة
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    {new Date(selectedNotification.scheduled_at).toLocaleString('ar-EG')}
                  </div>
                </div>
              )}

              {selectedNotification.template && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    القالب المستخدم
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    {selectedNotification.template.name}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end pt-4">
              <button
                onClick={closeDetailsModal}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationsManagement;
