.register-form {
  max-width: 800px;
  margin: 40px auto;
  background: #fff;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  direction: rtl;
}

.title {
  margin-bottom: 25px;
  text-align: center;
  color: var(--text);
  font-size: 1.75rem;
}

.form-section {
  background: #F9F9F9;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.form-section h3 {
  color: var(--primary);
  margin-bottom: 20px;
  font-size: 1.25rem;
  font-weight: 600;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--primary);
}

.input-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.input-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.input-row .input-group {
  flex: 1;
  margin-bottom: 0;
}

label {
  margin-bottom: 6px;
  font-weight: 600;
  color: var(--secondary-text);
}

input, select {
  padding: 10px 14px;
  border: 1px solid var(--secondary-bg);
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  background-color: #fff;
}

input:focus, select:focus {
  outline: 2px solid var(--primary);
  border-color: var(--primary);
}

input[type="file"] {
  padding: 8px;
  border: 2px dashed var(--secondary-bg);
  background: #fff;
  cursor: pointer;
  transition: border-color 0.3s;
}

input[type="file"]:hover {
  border-color: var(--primary);
}

.error {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
}

.server-error {
  color: #e74c3c;
  text-align: center;
  margin-bottom: 12px;
  padding: 8px;
  background: #fde8e8;
  border-radius: 4px;
}

.submit-btn {
  width: 100%;
  padding: 12px;
  background: var(--primary);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s, transform 0.2s;
  margin-top: 24px;
}

.submit-btn:hover {
  background: #eab308;
  transform: translateY(-1px);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.register-success {
  text-align: center;
  padding: 40px 20px;
  color: var(--primary);
}

.register-success h2 {
  font-size: 1.5rem;
  margin-bottom: 12px;
}

@media (max-width: 768px) {
  .register-form {
    padding: 24px;
    margin: 20px;
  }

  .input-row {
    flex-direction: column;
    gap: 0;
  }

  .input-row .input-group {
    margin-bottom: 16px;
  }

  .form-section {
    padding: 16px;
  }
} 