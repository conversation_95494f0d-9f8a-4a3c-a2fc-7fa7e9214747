import React, { useContext, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const AdminRedirect = ({ children }) => {
  const { user } = useContext(AuthContext);

  // إذا كان المستخدم superuser، أعد توجيهه إلى dashboard الأدمن
  if (user && user.is_superuser) {
    return <Navigate to="/admin-user" replace />;
  }

  // إذا لم يكن superuser، اعرض المحتوى العادي
  return children;
};

export default AdminRedirect;
