import React, { useState, useEffect } from 'react';
import axios from 'axios';
import CountUp from 'react-countup';
import StudentApplicationRequests from './StudentApplicationRequests';
import CreateInstructor from './CreateInstructor';
import {
  FaU<PERSON><PERSON>,
  FaUserGraduate,
  FaChalkboardTeacher,
  FaUserCheck,
  FaUserTimes,
  FaClock,
  FaSearch,
  FaCheck,
  FaTimes,
  FaDownload,
  FaUserPlus
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const UserManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [students, setStudents] = useState([]);
  const [instructors, setInstructors] = useState([]);
  const [studentApps, setStudentApps] = useState([]);
  const [instructorApps, setInstructorApps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [studentSearchTerm, setStudentSearchTerm] = useState('');
  const [instructorSearchTerm, setInstructorSearchTerm] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      // Fetch student applications
      const studentAppsResponse = await axios.get(`${API_BASE_URL}/api/auth/student-applications/`, { headers });
      console.log('Student Apps Response:', studentAppsResponse.data);
      console.log('Student Apps Count:', studentAppsResponse.data?.count ?? 'No count field');
      console.log('Student Apps Results:', studentAppsResponse.data?.results || 'No results field');
      console.log('Student Apps Results Length:', studentAppsResponse.data?.results?.length || 0);
      // Handle paginated response - extract results array
      const studentAppsData = studentAppsResponse.data?.results || studentAppsResponse.data || [];
      console.log('Final Student Apps Data:', studentAppsData);
      setStudentApps(Array.isArray(studentAppsData) ? studentAppsData : []);

      // Fetch instructor applications
      const instructorAppsResponse = await axios.get(`${API_BASE_URL}/api/auth/instructor-applications/`, { headers });
      console.log('Instructor Apps Response:', instructorAppsResponse.data);
      // Handle paginated response - extract results array
      const instructorAppsData = instructorAppsResponse.data?.results || instructorAppsResponse.data || [];
      setInstructorApps(Array.isArray(instructorAppsData) ? instructorAppsData : []);

      // Fetch students (from student profiles)
      const studentsResponse = await axios.get(`${API_BASE_URL}/api/students/profiles/`, { headers });
      console.log('Students Response:', studentsResponse.data);
      // Handle paginated response - extract results array
      const studentsData = studentsResponse.data?.results || studentsResponse.data || [];
      setStudents(Array.isArray(studentsData) ? studentsData : []);

      // Fetch instructors with detailed info
      const instructorsResponse = await axios.get(`${API_BASE_URL}/api/auth/instructors/detailed/`, { headers });
      console.log('Instructors Response:', instructorsResponse.data);
      // Handle paginated response - extract results array
      const instructorsData = instructorsResponse.data?.results || instructorsResponse.data || [];
      setInstructors(Array.isArray(instructorsData) ? instructorsData : []);

    } catch (err) {
      console.error('Error fetching user data:', err);
      setError('تعذر جلب بيانات المستخدمين');
      // Set empty arrays on error to prevent filter errors
      setStudentApps([]);
      setInstructorApps([]);
      setStudents([]);
      setInstructors([]);
    } finally {
      setLoading(false);
    }
  };

  const handleApplicationAction = async (applicationId, action, type) => {
    try {
      const token = localStorage.getItem('access');
      const endpoint = type === 'student'
        ? `${API_BASE_URL}/api/auth/student-application/${applicationId}/${action}/`
        : `${API_BASE_URL}/api/auth/instructor-application/${applicationId}/${action}/`;

      await axios.post(endpoint, {}, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      // Show success message
      alert(action === 'approve' ? 'تم قبول الطلب بنجاح' : 'تم رفض الطلب بنجاح');

      // Refresh data
      fetchData();
    } catch (err) {
      console.error('Error processing application:', err);
      alert('حدث خطأ أثناء معالجة الطلب');
    }
  };





  // Export functions
  const exportStudents = () => {
    try {
      const csvContent = "data:text/csv;charset=utf-8,"
        + "ID,الإيميل,الجامعة,السنة الدراسية,رقم الطالب,تاريخ التسجيل\n"
        + students.map(student =>
          `${student.id},"${student.user_email || ''}","${student.university_name || ''}","${student.academic_year_name || ''}","${student.student_id || ''}","${student.created_at ? new Date(student.created_at).toLocaleDateString('ar-EG') : ''}"`
        ).join("\n");

      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", "students.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('تم تصدير قائمة الطلاب بنجاح');
    } catch (err) {
      console.error('Error exporting students:', err);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  const exportInstructors = () => {
    try {
      const csvContent = "data:text/csv;charset=utf-8,"
        + "ID,الاسم,الإيميل,التخصص,الحالة,تاريخ التسجيل\n"
        + instructors.map(instructor =>
          `${instructor.id},"${instructor.user ? `${instructor.user.first_name} ${instructor.user.last_name}` : ''}","${instructor.user?.email || instructor.email || ''}","${instructor.specialty || ''}","${instructor.is_approved ? 'نشط' : 'قيد المراجعة'}","${instructor.created_at ? new Date(instructor.created_at).toLocaleDateString('ar-EG') : ''}"`
        ).join("\n");

      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", "instructors.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('تم تصدير قائمة المدرسين بنجاح');
    } catch (err) {
      console.error('Error exporting instructors:', err);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // Filter functions - with safety checks
  const filteredStudents = Array.isArray(students) ? students.filter(student =>
    !studentSearchTerm ||
    (student.user_email && student.user_email.toLowerCase().includes(studentSearchTerm.toLowerCase())) ||
    (student.university_name && student.university_name.toLowerCase().includes(studentSearchTerm.toLowerCase()))
  ) : [];

  const filteredInstructors = Array.isArray(instructors) ? instructors.filter(instructor =>
    !instructorSearchTerm ||
    (instructor.user?.email && instructor.user.email.toLowerCase().includes(instructorSearchTerm.toLowerCase())) ||
    (instructor.email && instructor.email.toLowerCase().includes(instructorSearchTerm.toLowerCase())) ||
    (instructor.specialty && instructor.specialty.toLowerCase().includes(instructorSearchTerm.toLowerCase()))
  ) : [];

  const StatCard = ({ title, value, icon, color, bgColor, textColor }) => (
    <div className={`${bgColor} rounded-lg p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-shadow`}>
      <div className="flex items-center justify-between">
        <div>
          <p className={`${textColor} text-sm font-medium mb-1`}>{title}</p>
          <p className="text-2xl font-bold text-gray-800">
            <CountUp end={value} duration={2} separator="," />
          </p>
        </div>
        <div className={`${color} text-white rounded-full p-3`}>
          {icon}
        </div>
      </div>
    </div>
  );

  const tabs = [
    { key: 'overview', label: 'نظرة عامة', icon: <FaUsers /> },
    { key: 'student-apps', label: 'طلبات الطلاب', icon: <FaUserGraduate /> },
    { key: 'instructor-apps', label: 'طلبات المدرسين', icon: <FaChalkboardTeacher /> },
    { key: 'students', label: 'الطلاب', icon: <FaUsers /> },
    { key: 'instructors', label: 'المدرسين', icon: <FaChalkboardTeacher /> },
    { key: 'create-instructor', label: 'إضافة مدرس', icon: <FaUserPlus /> },
  ];

  // Filter applications - with safety checks
  const filteredStudentApps = Array.isArray(studentApps) ? studentApps.filter(app => {
    const matchesSearch = app.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.user?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.user?.last_name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterStatus === 'all' ||
                         (filterStatus === 'pending' && !app.is_approved && !app.rejection_reason) ||
                         (filterStatus === 'approved' && app.is_approved) ||
                         (filterStatus === 'rejected' && app.rejection_reason);

    return matchesSearch && matchesFilter;
  }) : [];

  const filteredInstructorApps = Array.isArray(instructorApps) ? instructorApps.filter(app => {
    const matchesSearch = app.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.user?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.user?.last_name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterStatus === 'all' ||
                         (filterStatus === 'pending' && !app.is_approved && !app.rejection_reason) ||
                         (filterStatus === 'approved' && app.is_approved) ||
                         (filterStatus === 'rejected' && app.rejection_reason);

    return matchesSearch && matchesFilter;
  }) : [];

  // Calculate stats - with safety checks
  const studentStats = {
    total: Array.isArray(studentApps) ? studentApps.length : 0,
    pending: Array.isArray(studentApps) ? studentApps.filter(app => !app.is_approved && !app.rejection_reason).length : 0,
    approved: Array.isArray(studentApps) ? studentApps.filter(app => app.is_approved).length : 0,
    rejected: Array.isArray(studentApps) ? studentApps.filter(app => app.rejection_reason).length : 0,
  };

  const instructorStats = {
    total: Array.isArray(instructorApps) ? instructorApps.length : 0,
    pending: Array.isArray(instructorApps) ? instructorApps.filter(app => !app.is_approved && !app.rejection_reason).length : 0,
    approved: Array.isArray(instructorApps) ? instructorApps.filter(app => app.is_approved).length : 0,
    rejected: Array.isArray(instructorApps) ? instructorApps.filter(app => app.rejection_reason).length : 0,
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-yellow-600 text-lg">جاري تحميل بيانات المستخدمين...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center p-4">
        {error}
        <button 
          onClick={fetchData} 
          className="ml-2 text-yellow-600 hover:underline"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-800">👥 إدارة المستخدمين</h2>
        <button 
          onClick={fetchData}
          className="bg-yellow-400 hover:bg-yellow-500 text-yellow-900 px-4 py-2 rounded-lg font-semibold transition-colors"
        >
          🔄 تحديث
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="flex gap-2 border-b border-gray-200 p-4 overflow-x-auto">
          {tabs.map(tab => (
            <button
              key={tab.key}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-semibold transition-colors whitespace-nowrap ${
                activeTab === tab.key 
                  ? 'bg-yellow-400 text-yellow-900 shadow' 
                  : 'bg-transparent text-yellow-900 hover:bg-yellow-100'
              }`}
              onClick={() => setActiveTab(tab.key)}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                  title="طلبات الطلاب"
                  value={studentStats.total}
                  icon={<FaUserGraduate size={24} />}
                  color="bg-blue-500"
                  bgColor="bg-blue-50"
                  textColor="text-blue-700"
                />
                <StatCard
                  title="طلبات المدرسين"
                  value={instructorStats.total}
                  icon={<FaChalkboardTeacher size={24} />}
                  color="bg-green-500"
                  bgColor="bg-green-50"
                  textColor="text-green-700"
                />
                <StatCard
                  title="الطلاب المسجلين"
                  value={students.length}
                  icon={<FaUsers size={24} />}
                  color="bg-purple-500"
                  bgColor="bg-purple-50"
                  textColor="text-purple-700"
                />
                <StatCard
                  title="المدرسين النشطين"
                  value={instructors.length}
                  icon={<FaChalkboardTeacher size={24} />}
                  color="bg-orange-500"
                  bgColor="bg-orange-50"
                  textColor="text-orange-700"
                />
              </div>

              {/* Pending Applications Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-800 mb-4">طلبات الطلاب قيد المراجعة</h3>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-3xl font-bold text-yellow-600">{studentStats.pending}</p>
                      <p className="text-sm text-gray-600">طلب قيد المراجعة</p>
                    </div>
                    <FaClock className="text-yellow-500" size={32} />
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-800 mb-4">طلبات المدرسين قيد المراجعة</h3>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-3xl font-bold text-yellow-600">{instructorStats.pending}</p>
                      <p className="text-sm text-gray-600">طلب قيد المراجعة</p>
                    </div>
                    <FaClock className="text-yellow-500" size={32} />
                  </div>
                </div>
              </div>
            </div>
          )}

          {(activeTab === 'student-apps' || activeTab === 'instructor-apps') && (
            <div className="space-y-6">
              {/* Search and Filter */}
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث بالاسم أو الإيميل..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  />
                </div>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="pending">قيد المراجعة</option>
                  <option value="approved">مقبول</option>
                  <option value="rejected">مرفوض</option>
                </select>
              </div>

              {/* Student Applications */}
              {activeTab === 'student-apps' && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-bold text-gray-800">طلبات الطلاب ({filteredStudentApps.length})</h3>
                    <div className="flex gap-4">
                      <select
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 text-sm"
                      >
                        <option value="all">جميع الطلبات</option>
                        <option value="pending">قيد المراجعة</option>
                        <option value="approved">مقبولة</option>
                        <option value="rejected">مرفوضة</option>
                      </select>
                    </div>
                  </div>

                  <StudentApplicationRequests
                    applications={filteredStudentApps}
                    onRefresh={fetchData}
                  />
                </div>
              )}

              {activeTab === 'instructor-apps' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-bold text-gray-800">طلبات المدرسين ({filteredInstructorApps.length})</h3>

                  {filteredInstructorApps.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      لا توجد طلبات مدرسين حالياً
                    </div>
                  ) : (
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم</th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإيميل</th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التخصص</th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">النبذة</th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">السيرة الذاتية</th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {filteredInstructorApps.map(app => (
                              <tr key={app.id} className="hover:bg-gray-50">
                                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {app.user ? `${app.user.first_name} ${app.user.last_name}` :
                                   (app.first_name || app.last_name) ? `${app.first_name || ''} ${app.last_name || ''}`.trim() : '—'}
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {app.user?.email || app.email || '—'}
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {app.specialty || '—'}
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 max-w-xs truncate">
                                  {app.bio || '—'}
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {app.cv ? (
                                    <a href={app.cv} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                      عرض السيرة الذاتية
                                    </a>
                                  ) : '—'}
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    app.is_approved
                                      ? 'bg-green-100 text-green-800'
                                      : app.rejection_reason
                                      ? 'bg-red-100 text-red-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}>
                                    {app.is_approved ? 'مقبول' : app.rejection_reason ? 'مرفوض' : 'قيد المراجعة'}
                                  </span>
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                  {!app.is_approved && !app.rejection_reason && (
                                    <div className="flex gap-2">
                                      <button
                                        onClick={() => handleApplicationAction(app.id, 'approve', 'instructor')}
                                        className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs"
                                      >
                                        <FaCheck className="inline mr-1" /> موافقة
                                      </button>
                                      <button
                                        onClick={() => handleApplicationAction(app.id, 'reject', 'instructor')}
                                        className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs"
                                      >
                                        <FaTimes className="inline mr-1" /> رفض
                                      </button>
                                    </div>
                                  )}
                                  {app.is_approved && (
                                    <span className="text-green-600 text-xs">تم القبول</span>
                                  )}
                                  {app.rejection_reason && (
                                    <span className="text-red-600 text-xs">مرفوض</span>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {activeTab === 'students' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold text-gray-800">قائمة الطلاب ({filteredStudents.length} من {Array.isArray(students) ? students.length : 0})</h3>
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="البحث بالإيميل أو الجامعة..."
                    value={studentSearchTerm}
                    onChange={(e) => setStudentSearchTerm(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm w-48"
                  />
                  <button
                    onClick={exportStudents}
                    className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm"
                  >
                    <FaDownload className="inline mr-1" /> تصدير
                  </button>
                </div>
              </div>

              {students.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا يوجد طلاب مسجلين حالياً
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">ID</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإيميل</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الجامعة</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">السنة الدراسية</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الطالب</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ التسجيل</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredStudents.map(student => (
                          <tr key={student.id} className="hover:bg-gray-50">
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{student.id}</td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{student.user_email || '—'}</td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{student.university_name || '—'}</td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{student.academic_year_name || '—'}</td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{student.student_id || '—'}</td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                              {student.created_at ? new Date(student.created_at).toLocaleDateString('ar-EG') : '—'}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="text-center text-gray-500 text-sm">
                                لا توجد إجراءات متاحة
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'create-instructor' && (
            <CreateInstructor
              onSuccess={(data) => {
                console.log('Instructor created successfully:', data);
                // Refresh data
                fetchData();
                // Optionally switch to instructors tab
                // setActiveTab('instructors');
              }}
            />
          )}

          {activeTab === 'instructors' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold text-gray-800">قائمة المدرسين ({filteredInstructors.length} من {Array.isArray(instructors) ? instructors.length : 0})</h3>
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="البحث بالإيميل أو التخصص..."
                    value={instructorSearchTerm}
                    onChange={(e) => setInstructorSearchTerm(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm w-48"
                  />
                  <button
                    onClick={exportInstructors}
                    className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm"
                  >
                    <FaDownload className="inline mr-1" /> تصدير
                  </button>
                </div>
              </div>

              {instructors.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا يوجد مدرسين نشطين حالياً
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">ID</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإيميل</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التخصص</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ التسجيل</th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredInstructors.map(instructor => (
                          <tr key={instructor.id} className="hover:bg-gray-50">
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{instructor.id}</td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                              {instructor.full_name || `${instructor.first_name || ''} ${instructor.last_name || ''}`.trim() || '—'}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                              {instructor.email || '—'}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{instructor.specialty || '—'}</td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                instructor.is_approved
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {instructor.is_approved ? 'نشط' : 'قيد المراجعة'}
                              </span>
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                              {instructor.created_at ? new Date(instructor.created_at).toLocaleDateString('ar-EG') : '—'}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="text-center text-gray-500 text-sm">
                                لا توجد إجراءات متاحة
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserManagement;
