import React, { useState } from 'react';
import axios from 'axios';
import { 
  <PERSON>a<PERSON>ser, 
  FaEnvelope, 
  FaPhone, 
  FaLock, 
  FaEye, 
  FaEyeSlash,
  FaCheck,
  FaTimes,
  FaSpinner,
  FaUserPlus,
  FaGraduationCap,
  FaInfoCircle
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const CreateInstructor = ({ onSuccess, onCancel }) => {
  const [formData, setFormData] = useState({
    email: '',
    first_name: '',
    last_name: '',
    phone_number: '',
    specialty: '',
    bio: '',
    auto_generate_password: true,
    custom_password: '',
    send_email_notification: true
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [createdInstructor, setCreatedInstructor] = useState(null);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem('access');
      const response = await axios.post(
        `${API_BASE_URL}/api/auth/instructors/create/`,
        formData,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      console.log('✅ Instructor created:', response.data);
      setSuccess(response.data.message);
      setCreatedInstructor(response.data);
      
      // Reset form
      setFormData({
        email: '',
        first_name: '',
        last_name: '',
        phone_number: '',
        specialty: '',
        bio: '',
        auto_generate_password: true,
        custom_password: '',
        send_email_notification: true
      });

      // Call success callback
      if (onSuccess) {
        onSuccess(response.data);
      }

    } catch (err) {
      console.error('❌ Error creating instructor:', err);
      const errorMessage = err.response?.data?.message || 
                          err.response?.data?.errors || 
                          'حدث خطأ أثناء إنشاء المدرس';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaUserPlus className="text-blue-500" size={24} />
          <h2 className="text-2xl font-bold text-gray-800">إضافة مدرس جديد</h2>
        </div>
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700 p-2"
            title="إغلاق"
          >
            <FaTimes size={20} />
          </button>
        )}
      </div>

      {/* Success Message */}
      {success && createdInstructor && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <FaCheck className="text-green-500 mt-1" />
            <div className="flex-1">
              <h3 className="font-semibold text-green-800 mb-2">{success}</h3>
              
              {/* Instructor Details */}
              <div className="bg-white rounded p-3 mb-3">
                <h4 className="font-medium text-gray-800 mb-2">بيانات المدرس:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div><strong>الاسم:</strong> {createdInstructor.instructor?.first_name} {createdInstructor.instructor?.last_name}</div>
                  <div><strong>الإيميل:</strong> {createdInstructor.instructor?.email}</div>
                  <div><strong>الهاتف:</strong> {createdInstructor.instructor?.phone_number || 'غير محدد'}</div>
                  <div><strong>تاريخ الإنشاء:</strong> {new Date(createdInstructor.instructor?.date_joined).toLocaleDateString('ar-EG')}</div>
                </div>
              </div>

              {/* Credentials */}
              <div className="bg-yellow-50 border border-yellow-200 rounded p-3 mb-3">
                <h4 className="font-medium text-yellow-800 mb-2">بيانات الدخول:</h4>
                <div className="text-sm space-y-1">
                  <div><strong>الإيميل:</strong> {createdInstructor.credentials?.email}</div>
                  <div><strong>كلمة السر:</strong> <code className="bg-gray-100 px-2 py-1 rounded">{createdInstructor.credentials?.password}</code></div>
                  <div><strong>رابط الدخول:</strong> <a href={createdInstructor.credentials?.login_url} className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">{createdInstructor.credentials?.login_url}</a></div>
                </div>
              </div>

              {/* Next Steps */}
              <div className="bg-blue-50 border border-blue-200 rounded p-3">
                <h4 className="font-medium text-blue-800 mb-2">الخطوات التالية:</h4>
                <ul className="text-sm space-y-1">
                  {createdInstructor.next_steps?.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-blue-500 mt-1">•</span>
                      <span>{step}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <FaTimes className="text-red-500 mt-1" />
            <div>
              <h3 className="font-semibold text-red-800 mb-1">خطأ في إنشاء المدرس</h3>
              <div className="text-red-700">
                {typeof error === 'object' ? (
                  <ul className="space-y-1">
                    {Object.entries(error).map(([field, messages]) => (
                      <li key={field}>
                        <strong>{field}:</strong> {Array.isArray(messages) ? messages.join(', ') : messages}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>{error}</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FaEnvelope className="inline mr-2" />
              البريد الإلكتروني *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="<EMAIL>"
            />
          </div>

          {/* Phone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FaPhone className="inline mr-2" />
              رقم الهاتف
            </label>
            <input
              type="tel"
              name="phone_number"
              value={formData.phone_number}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="01234567890"
            />
          </div>

          {/* First Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FaUser className="inline mr-2" />
              الاسم الأول *
            </label>
            <input
              type="text"
              name="first_name"
              value={formData.first_name}
              onChange={handleInputChange}
              required
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="أحمد"
            />
          </div>

          {/* Last Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FaUser className="inline mr-2" />
              اسم العائلة *
            </label>
            <input
              type="text"
              name="last_name"
              value={formData.last_name}
              onChange={handleInputChange}
              required
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="محمد"
            />
          </div>
        </div>

        {/* Professional Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Specialty */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FaGraduationCap className="inline mr-2" />
              التخصص
            </label>
            <input
              type="text"
              name="specialty"
              value={formData.specialty}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="القانون المدني، القانون الجنائي، إلخ"
            />
          </div>
        </div>

        {/* Bio */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FaInfoCircle className="inline mr-2" />
            نبذة تعريفية
          </label>
          <textarea
            name="bio"
            value={formData.bio}
            onChange={handleInputChange}
            rows={3}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="نبذة مختصرة عن المدرس وخبراته..."
          />
        </div>

        {/* Password Settings */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">إعدادات كلمة السر</h3>

          {/* Auto Generate Password */}
          <div className="mb-4">
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                name="auto_generate_password"
                checked={formData.auto_generate_password}
                onChange={handleInputChange}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">
                توليد كلمة سر آمنة تلقائياً (مُوصى به)
              </span>
            </label>
            <p className="text-xs text-gray-500 mt-1 mr-7">
              سيتم توليد كلمة سر قوية مكونة من 12 حرف تحتوي على أحرف وأرقام ورموز
            </p>
          </div>

          {/* Custom Password */}
          {!formData.auto_generate_password && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaLock className="inline mr-2" />
                كلمة السر المخصصة *
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="custom_password"
                  value={formData.custom_password}
                  onChange={handleInputChange}
                  required={!formData.auto_generate_password}
                  minLength={8}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 pr-10 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="أدخل كلمة سر قوية (8 أحرف على الأقل)"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                يجب أن تحتوي على 8 أحرف على الأقل مع مزيج من الأحرف والأرقام
              </p>
            </div>
          )}
        </div>

        {/* Notification Settings */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">إعدادات الإشعارات</h3>

          <div>
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                name="send_email_notification"
                checked={formData.send_email_notification}
                onChange={handleInputChange}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">
                إرسال بيانات الدخول عبر البريد الإلكتروني
              </span>
            </label>
            <p className="text-xs text-gray-500 mt-1 mr-7">
              سيتم إرسال إيميل ترحيبي يحتوي على بيانات الدخول وتعليمات الاستخدام
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 pt-4">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
          >
            {loading ? (
              <>
                <FaSpinner className="animate-spin" />
                جاري الإنشاء...
              </>
            ) : (
              <>
                <FaUserPlus />
                إنشاء المدرس
              </>
            )}
          </button>

          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={loading}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
          )}
        </div>

        {/* Help Text */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <FaInfoCircle className="text-yellow-600 mt-1" />
            <div className="text-sm text-yellow-800">
              <h4 className="font-semibold mb-2">ملاحظات مهمة:</h4>
              <ul className="space-y-1">
                <li>• سيحصل المدرس على صلاحيات إدارية كاملة</li>
                <li>• يجب على المدرس تغيير كلمة السر في أول تسجيل دخول</li>
                <li>• تأكد من صحة البريد الإلكتروني قبل الإرسال</li>
                <li>• احتفظ ببيانات الدخول في مكان آمن</li>
              </ul>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CreateInstructor;
