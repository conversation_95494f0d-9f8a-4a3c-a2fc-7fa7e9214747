import React, { useState } from 'react';
import axios from 'axios';
import { 
  FaCheck, 
  FaTimes, 
  FaEye, 
  FaClock, 
  FaUser,
  FaMoneyBillWave,
  FaCalendarAlt,
  FaImage,
  FaUniversity,
  FaGraduationCap
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

// Helper function to get secure image URL with proper headers
const getSecureImageUrl = (applicationId) => {
  return `${API_BASE_URL}/api/auth/secure/student-application-image/${applicationId}/`;
};

// Helper function to create image element with auth headers
const createAuthenticatedImage = (applicationId, onLoad, onError) => {
  const token = localStorage.getItem('access');

  // Create a new XMLHttpRequest to fetch the image with auth headers
  const xhr = new XMLHttpRequest();
  xhr.open('GET', getSecureImageUrl(applicationId), true);
  xhr.setRequestHeader('Authorization', `Bearer ${token}`);
  xhr.responseType = 'blob';

  xhr.onload = function() {
    if (xhr.status === 200) {
      const blob = xhr.response;
      const imageUrl = URL.createObjectURL(blob);
      onLoad(imageUrl);
    } else {
      onError('فشل في تحميل الصورة');
    }
  };

  xhr.onerror = function() {
    onError('خطأ في الشبكة');
  };

  xhr.send();
};

// Component for authenticated image display
const AuthenticatedImage = ({ applicationId, className, onClick, alt }) => {
  const [imageSrc, setImageSrc] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  React.useEffect(() => {
    createAuthenticatedImage(
      applicationId,
      (imageUrl) => {
        setImageSrc(imageUrl);
        setLoading(false);
      },
      (errorMsg) => {
        console.error('Error loading image:', errorMsg);
        setError(true);
        setLoading(false);
      }
    );

    // Cleanup function to revoke object URL
    return () => {
      if (imageSrc) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [applicationId]);

  if (loading) {
    return (
      <div className={`${className} bg-gray-200 flex items-center justify-center`}>
        <div className="text-gray-500 text-sm">جاري التحميل...</div>
      </div>
    );
  }

  if (error || !imageSrc) {
    return (
      <div className={`${className} bg-gray-100 flex items-center justify-center border-2 border-dashed border-gray-300`}>
        <div className="text-gray-500 text-sm text-center">
          <FaImage className="mx-auto mb-2" />
          فشل في تحميل الصورة
        </div>
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      onClick={onClick}
    />
  );
};

const StudentApplicationRequests = ({ applications, onRefresh }) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [actionLoading, setActionLoading] = useState(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(null);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Toast notification function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  const handleAction = async (applicationId, action, reason = '') => {
    setActionLoading(applicationId);
    try {
      const token = localStorage.getItem('access');
      const payload = {};
      if (action === 'reject' && reason) {
        payload.rejection_reason = reason;
      }

      await axios.post(
        `${API_BASE_URL}/api/auth/student-application/${applicationId}/${action}/`,
        payload,
        { headers: { 'Authorization': `Bearer ${token}` } }
      );

      // Refresh data
      onRefresh();

      // Close modal if open
      setShowRejectModal(null);
      setRejectionReason('');

      showMessage(action === 'approve' ? 'تمت الموافقة على الطلب بنجاح' : 'تم رفض الطلب بنجاح');
    } catch (err) {
      console.error('Error processing application:', err);
      showMessage('حدث خطأ أثناء معالجة الطلب', true);
    } finally {
      setActionLoading(null);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (applications.length === 0) {
    return (
      <div className="text-center py-12">
        <FaClock className="mx-auto mb-4 text-gray-400" size={48} />
        <h3 className="text-lg font-semibold text-gray-600 mb-2">لا توجد طلبات التحاق</h3>
        <p className="text-gray-500">جميع طلبات الالتحاق تم معالجتها</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Toast Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{successMessage}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setSuccessMessage(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{error}</span>
          <span
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setError(null)}
          >
            <FaTimes />
          </span>
        </div>
      )}

      <div className="grid gap-6">
        {applications.map(application => (
          <div key={application.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow hover:shadow-lg transition-shadow">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* User Info */}
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <FaUser className="text-blue-500" />
                  <div>
                    <h4 className="font-semibold text-gray-800">
                      {application.user.first_name} {application.user.last_name}
                    </h4>
                    <p className="text-sm text-gray-600">{application.user.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <FaCalendarAlt className="text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">تاريخ الطلب</p>
                    <p className="text-sm text-gray-600">{formatDate(application.created_at)}</p>
                  </div>
                </div>

                {/* Student Profile Info */}
                {application.student_profile && (
                  <>
                    <div className="flex items-center gap-3">
                      <FaUniversity className="text-purple-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">الجامعة</p>
                        <p className="text-sm text-gray-600">{application.student_profile.university_name || 'غير محدد'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <FaGraduationCap className="text-indigo-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">السنة الدراسية</p>
                        <p className="text-sm text-gray-600">{application.student_profile.academic_year_name || 'غير محدد'}</p>
                      </div>
                    </div>
                  </>
                )}
              </div>

              {/* Plan Info */}
              <div className="space-y-3">
                {application.selected_plan_name && (
                  <div className="flex items-center gap-3">
                    <FaMoneyBillWave className="text-green-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">الخطة المطلوبة</p>
                      <p className="font-semibold text-gray-800">{application.selected_plan_name}</p>
                      <p className="text-sm text-green-600">{application.selected_plan_price} ج.م</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <FaClock className="text-yellow-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">الحالة</p>
                    <span className={`inline-block px-2 py-1 rounded text-sm ${
                      application.is_approved 
                        ? 'bg-green-100 text-green-800' 
                        : application.rejection_reason 
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {application.is_approved 
                        ? 'تمت الموافقة' 
                        : application.rejection_reason 
                        ? 'مرفوض'
                        : 'قيد المراجعة'
                      }
                    </span>
                  </div>
                </div>

                {application.rejection_reason && (
                  <div className="bg-red-50 p-3 rounded border border-red-200">
                    <p className="text-sm font-medium text-red-700">سبب الرفض:</p>
                    <p className="text-sm text-red-600">{application.rejection_reason}</p>
                  </div>
                )}
              </div>

              {/* Payment Screenshot & Actions */}
              <div className="space-y-3">
                {application.payment_screenshot && (
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">إثبات الدفع</p>
                    <div className="relative group">
                      <AuthenticatedImage
                        applicationId={application.id}
                        alt="إثبات الدفع"
                        className="w-full h-40 object-cover rounded-lg border-2 border-gray-200 cursor-pointer hover:opacity-80 hover:border-blue-400 transition-all duration-200 shadow-sm hover:shadow-md"
                        onClick={() => {
                          // Create authenticated image for modal
                          createAuthenticatedImage(
                            application.id,
                            (imageUrl) => setSelectedImage(imageUrl),
                            (error) => console.error('Error loading modal image:', error)
                          );
                        }}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded flex items-center justify-center">
                        <button
                          onClick={() => {
                            createAuthenticatedImage(
                              application.id,
                              (imageUrl) => setSelectedImage(imageUrl),
                              (error) => console.error('Error loading modal image:', error)
                            );
                          }}
                          className="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-3 py-1 rounded-full text-sm font-medium transition-opacity duration-200 flex items-center gap-2"
                        >
                          <FaEye size={14} />
                          عرض بالحجم الكامل
                        </button>
                      </div>
                      <div className="absolute top-2 right-2 flex gap-1">
                        <button
                          onClick={() => {
                            createAuthenticatedImage(
                              application.id,
                              (imageUrl) => setSelectedImage(imageUrl),
                              (error) => console.error('Error loading modal image:', error)
                            );
                          }}
                          className="bg-blue-500 hover:bg-blue-600 text-white p-1 rounded text-xs"
                          title="عرض بالحجم الكامل"
                        >
                          <FaEye size={12} />
                        </button>
                        <button
                          onClick={() => {
                            // Download image with authentication
                            createAuthenticatedImage(
                              application.id,
                              (imageUrl) => {
                                const link = document.createElement('a');
                                link.href = imageUrl;
                                link.download = `payment_screenshot_${application.id}.jpg`;
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                                URL.revokeObjectURL(imageUrl);
                              },
                              (error) => console.error('Error downloading image:', error)
                            );
                          }}
                          className="bg-green-500 hover:bg-green-600 text-white p-1 rounded text-xs"
                          title="تحميل الصورة"
                        >
                          <FaImage size={12} />
                        </button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                      <FaEye size={10} />
                      انقر على الصورة للعرض بالحجم الكامل
                    </p>
                  </div>
                )}

                {/* Action Buttons - Only show if not processed */}
                {!application.is_approved && !application.rejection_reason && (
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleAction(application.id, 'approve')}
                      disabled={actionLoading === application.id}
                      className="flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded font-semibold transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
                    >
                      <FaCheck size={14} />
                      {actionLoading === application.id ? 'جاري المعالجة...' : 'موافقة'}
                    </button>
                    <button
                      onClick={() => setShowRejectModal(application.id)}
                      disabled={actionLoading === application.id}
                      className="flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded font-semibold transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
                    >
                      <FaTimes size={14} />
                      رفض
                    </button>
                  </div>
                )}

                {/* Show status for processed applications */}
                {(application.is_approved || application.rejection_reason) && (
                  <div className="text-center py-2">
                    <span className={`text-sm font-medium ${
                      application.is_approved ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {application.is_approved ? '✅ تمت الموافقة على الطلب' : '❌ تم رفض الطلب'}
                    </span>
                    {application.approved_at && (
                      <p className="text-xs text-gray-500 mt-1">
                        {formatDate(application.approved_at)}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-7xl max-h-full w-full">
            <div className="bg-white rounded-lg p-4 shadow-2xl">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold text-gray-800">إثبات الدفع - عرض بالحجم الكامل</h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = selectedImage;
                      link.download = 'payment_screenshot.jpg';
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                    className="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm flex items-center gap-2"
                  >
                    <FaImage size={14} />
                    تحميل الصورة
                  </button>
                  <button
                    onClick={() => {
                      if (selectedImage) {
                        URL.revokeObjectURL(selectedImage);
                      }
                      setSelectedImage(null);
                    }}
                    className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm flex items-center gap-2"
                  >
                    <FaTimes size={14} />
                    إغلاق
                  </button>
                </div>
              </div>
              <div className="max-h-[75vh] overflow-auto bg-gray-100 rounded-lg p-2">
                <img
                  src={selectedImage}
                  alt="إثبات الدفع"
                  className="w-full h-auto object-contain rounded border border-gray-200 bg-white"
                  style={{ minHeight: '300px', maxWidth: '100%' }}
                />
              </div>
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  يمكنك تحميل الصورة أو إغلاق النافذة بالضغط على الأزرار أعلاه
                </p>
              </div>
            </div>
          </div>
          {/* Click outside to close */}
          <div
            className="absolute inset-0 -z-10"
            onClick={() => {
              if (selectedImage) {
                URL.revokeObjectURL(selectedImage);
              }
              setSelectedImage(null);
            }}
          ></div>
        </div>
      )}

      {/* Rejection Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-4">رفض طلب الالتحاق</h3>
            <p className="text-gray-600 mb-4">يرجى إدخال سبب الرفض:</p>
            <textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              className="w-full border border-gray-300 rounded p-3 mb-4 h-24 resize-none"
              placeholder="اكتب سبب الرفض هنا..."
            />
            <div className="flex gap-3">
              <button
                onClick={() => handleAction(showRejectModal, 'reject', rejectionReason)}
                disabled={!rejectionReason.trim() || actionLoading === showRejectModal}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded font-semibold transition-colors disabled:opacity-50"
              >
                {actionLoading === showRejectModal ? 'جاري الرفض...' : 'تأكيد الرفض'}
              </button>
              <button
                onClick={() => {
                  setShowRejectModal(null);
                  setRejectionReason('');
                }}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded font-semibold transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentApplicationRequests;
