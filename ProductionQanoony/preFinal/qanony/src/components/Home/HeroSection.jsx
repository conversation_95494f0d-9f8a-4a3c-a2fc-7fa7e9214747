// هذا المكون ضروري للواجهة الرئيسية ولا يجب التعليق عليه أو حذفه
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

export default function HeroSection() {
  return (
    <div className="relative isolate overflow-hidden bg-gradient-to-r from-yellow-300 to-yellow-400">
      <div className="container py-24 sm:py-32">
        <div className="mx-auto max-w-7xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-8"
          >
            <h1 className="text-4xl font-bold tracking-tight text-dark sm:text-6xl font-cairo">
              منصة{' '}
              <span className="text-yellow-400 inline-block">
                قانوني
              </span>
              {' '}التعليمية
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600 font-tajawal max-w-3xl mx-auto">
              نقدم لطلاب كلية الحقوق تجربة تعليمية متكاملة من خلال محتوى قانوني عالي الجودة،
              مكتبة قوانين شاملة، وفرص توظيف متميزة. انضم إلينا اليوم وكن جزءاً من مستقبل
              التعليم القانوني.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Link
                to="/register"
                className="btn-primary text-lg"
              >
                ابدأ الآن مجاناً
              </Link>
              <Link
                to="/about"
                className="text-sm font-semibold leading-6 text-dark hover:text-yellow-400 transition-colors"
              >
                تعرف علينا أكثر <span aria-hidden="true">←</span>
              </Link>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Background pattern */}
      <div className="absolute inset-x-0 top-0 -z-10 transform-gpu overflow-hidden blur-3xl" aria-hidden="true">
        <div
          className="relative aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-yellow-300 to-yellow-400 opacity-20"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>
      <div className="absolute inset-x-0 bottom-0 -z-10 transform-gpu overflow-hidden blur-3xl" aria-hidden="true">
        <div
          className="relative aspect-[1155/678] w-[36.125rem] translate-x-1/3 bg-gradient-to-tr from-yellow-300 to-yellow-400 opacity-20"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>
    </div>
  );
} 