import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

export default function PricingCard({ tier }) {
  return (
    <motion.div
      variants={item}
      whileHover={{ y: -8, scale: 1.03 }}
      transition={{ type: 'spring', stiffness: 300 }}
      className="relative flex flex-col rounded-3xl p-8 ring-1 bg-white/60 ring-gray-200 xl:p-10"
    >
      <div className="flex-1">
        <h3 id={`tier-${tier.id}`} className="text-2xl font-bold tracking-tight text-dark">
          {tier.name}
        </h3>
        <p className="mt-4 text-base leading-6 text-gray-600">
          اشتراك لمدة {tier.duration_days} يوم
        </p>
        <p className="mt-6 flex items-baseline gap-x-1">
          <span className="text-4xl font-bold tracking-tight text-dark">
            {tier.price}
          </span>
          <span className="text-sm font-semibold leading-6 text-gray-600">
            جنيه
          </span>
        </p>
      </div>
      <Link
        to={tier.href}
        aria-describedby={`tier-${tier.id}`}
        className="mt-8 block w-full text-center btn-primary"
      >
        ابدأ الآن
      </Link>
    </motion.div>
  );
} 