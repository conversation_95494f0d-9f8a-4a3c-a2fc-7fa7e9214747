import React, { useContext } from 'react';
import { AuthContext } from '../../context/AuthContext';
import SEOHead from '../SEO/SEOHead';
import HeroSlider from './HeroSlider';
import FeaturesSection from './FeaturesSection';
import TestimonialsSection from './TestimonialsSection';
import LegalSubjectsSection from './LegalSubjectsSection';
import PricingSection from './PricingSection';
import CallToActionSection from './CallToActionSection';
import AboutSection from './AboutSection';

export default function HomePage() {
  const { user } = useContext(AuthContext);

  // عرض الصفحة الرئيسية العادية لجميع المستخدمين
  // الأدمن يقدر يوصل للوحة التحكم من الهيدر أو الرابط المباشر

  return (
    <>
      <SEOHead
        title="منصة قانوني التعليمية - أفضل منصة تعليمية لطلاب كلية الحقوق في مصر"
        description="منصة قانوني التعليمية الرائدة في مصر لطلاب كلية الحقوق. محاضرات تفاعلية من أفضل الخبراء، اختبارات متخصصة، مكتبة قانونية شاملة، وفرص توظيف متميزة. احصل على تعليم قانوني متميز يؤهلك للنجاح."
        keywords="قانوني، كلية الحقوق، تعليم قانوني، محاضرات قانونية، اختبارات قانونية، مكتبة قانونية، توظيف محامين، دراسة القانون، طلاب الحقوق، منصة تعليمية، التعليم الإلكتروني، القانون المصري، المحاماة، الاستشارات القانونية، تعليم عن بعد، دورات قانونية"
        url="https://qanony.com/"
        type="website"
      />
      <div className="min-h-screen bg-background">
        <main className="space-y-0">
          <HeroSlider />
          <div className="space-y-16 py-16">
            <FeaturesSection />
            <TestimonialsSection />
            <CallToActionSection />
            <LegalSubjectsSection />
            <PricingSection />
            <AboutSection />
          </div>
        </main>
      </div>
    </>
  );
}