import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  ScaleIcon, 
  BuildingLibraryIcon, 
  UserGroupIcon, 
  DocumentTextIcon,
  ShieldCheckIcon,
  BanknotesIcon,
  GlobeAltIcon,
  CogIcon
} from '@heroicons/react/24/outline';

const subjects = [
  {
    id: 1,
    title: "القانون المدني",
    subtitle: "أساسيات القانون المدني",
    description: "دراسة شاملة للقانون المدني المصري، بما في ذلك العقود والالتزامات والمسؤولية المدنية والملكية والحقوق العينية.",
    icon: ScaleIcon,
    color: "from-blue-500 to-blue-600",
    image: "https://images.unsplash.com/photo-*************-d10d557cf95f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  },
  {
    id: 2,
    title: "القانون الجنائي",
    subtitle: "الجرائم والعقوبات",
    description: "دراسة متعمقة للقانون الجنائي، أنواع الجرائم والعقوبات، إجراءات المحاكمة الجنائية، والدفاع الجنائي.",
    icon: ShieldCheckIcon,
    color: "from-red-500 to-red-600",
    image: "https://images.unsplash.com/photo-**********-6726b3ff858f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2011&q=80"
  },
  {
    id: 3,
    title: "القانون التجاري",
    subtitle: "التجارة والأعمال",
    description: "دراسة القانون التجاري والشركات التجارية، العقود التجارية، الأوراق التجارية، والإفلاس.",
    icon: BanknotesIcon,
    color: "from-green-500 to-green-600",
    image: "https://images.unsplash.com/photo-**********-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  },
  {
    id: 4,
    title: "القانون الدستوري",
    subtitle: "الدستور والحقوق",
    description: "دراسة القانون الدستوري، حقوق الإنسان والحريات العامة، النظام السياسي، والرقابة الدستورية.",
    icon: DocumentTextIcon,
    color: "from-purple-500 to-purple-600",
    image: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"
  },
  {
    id: 5,
    title: "القانون الدولي",
    subtitle: "القانون الدولي العام",
    description: "دراسة القانون الدولي العام، العلاقات الدولية، المنظمات الدولية، وحل النزاعات الدولية.",
    icon: GlobeAltIcon,
    color: "from-indigo-500 to-indigo-600",
    image: "https://images.unsplash.com/photo-1521791136064-7986c2920216?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80"
  },
  {
    id: 6,
    title: "قانون العمل",
    subtitle: "علاقات العمل",
    description: "دراسة قانون العمل، عقود العمل، التأمينات الاجتماعية، النقابات العمالية، وحل نزاعات العمل.",
    icon: UserGroupIcon,
    color: "from-yellow-300 to-yellow-400",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"
  }
];

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

export default function LegalSubjectsSection() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="container">
        <div className="mx-auto max-w-2xl text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-base font-semibold leading-7 text-yellow-400">المواد الدراسية</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-dark sm:text-4xl font-cairo">
              اكتشف عالم القانون
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600 font-tajawal">
              مجموعة شاملة من المواد القانونية المحدثة، مقدمة من نخبة من الأساتذة والمحامين المتخصصين
            </p>
          </motion.div>
        </div>

        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {subjects.map((subject) => (
            <motion.div
              key={subject.id}
              variants={item}
              className="group relative bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
            >
              {/* Background Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={subject.image}
                  alt={subject.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                
                {/* Icon */}
                <div className={`absolute top-4 right-4 p-3 rounded-full bg-gradient-to-r ${subject.color} text-white shadow-lg`}>
                  <subject.icon className="w-6 h-6" />
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-dark font-cairo mb-2">{subject.title}</h3>
                <p className="text-sm text-yellow-400 font-medium mb-3">{subject.subtitle}</p>
                <p className="text-gray-600 font-tajawal text-sm leading-relaxed mb-6">
                  {subject.description}
                </p>

                <Link
                  to="/login"
                  className={`block w-full text-center py-3 px-4 rounded-lg bg-gradient-to-r ${subject.color} text-white font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105`}
                >
                  ابدأ الدراسة
                </Link>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-yellow-300 to-yellow-400 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold font-cairo mb-4">هل تريد المزيد من المواد؟</h3>
            <p className="text-lg mb-6 font-tajawal">
              اكتشف مكتبتنا الشاملة من المواد القانونية والمراجع العلمية
            </p>
            <Link
              to="/login"
              className="inline-block bg-white text-yellow-900 font-semibold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors duration-300"
            >
              استكشف المكتبة
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
} 