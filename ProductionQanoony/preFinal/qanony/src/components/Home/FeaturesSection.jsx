// هذا المكون ضروري للواجهة الرئيسية ولا يجب التعليق عليه أو حذفه
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useContext } from 'react';
import { AuthContext } from '../../context/AuthContext';
import {
  AcademicCapIcon,
  BookOpenIcon,
  BriefcaseIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  DevicePhoneMobileIcon,
  SparklesIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  ChartBarIcon,
  HeartIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'محتوى تعليمي متميز',
    description:
      'دروس وشروحات مفصلة للمواد القانونية، مع أمثلة عملية وتمارين تفاعلية تساعد في فهم المفاهيم القانونية.',
    icon: AcademicCapIcon,
    color: 'from-blue-500 to-blue-600',
    stats: '500+ محاضرة'
  },
  {
    name: 'مكتبة قوانين شاملة',
    description:
      'مجموعة واسعة من القوانين والتشريعات المصرية والعربية، محدثة باستمرار ومنظمة بشكل سهل الوصول.',
    icon: BookOpenIcon,
    color: 'from-green-500 to-green-600',
    stats: '1000+ مرجع'
  },
  {
    name: 'فرص توظيف متميزة',
    description:
      'بوابة توظيف متخصصة تربط الطلاب والخريجين بأفضل المكاتب والشركات القانونية في المنطقة.',
    icon: BriefcaseIcon,
    color: 'from-purple-500 to-purple-600',
    stats: '200+ فرصة'
  },
  {
    name: 'مجتمع قانوني نشط',
    description:
      'منصة تفاعلية للنقاش وتبادل الخبرات بين الطلاب والمحامين والأساتذة في مختلف المجالات القانونية.',
    icon: UserGroupIcon,
    color: 'from-yellow-300 to-yellow-400',
    stats: '5,000+ عضو'
  },
  {
    name: 'مساعد ذكي للقانون',
    description:
      'مساعد ذكي متخصص في القانون يساعدك في فهم المواد الصعبة والإجابة على استفساراتك القانونية.',
    icon: ChatBubbleLeftRightIcon,
    color: 'from-indigo-500 to-indigo-600',
    stats: '24/7 متاح'
  },
  {
    name: 'تعلم في أي وقت',
    description:
      'احصل على المحتوى التعليمي في أي وقت ومن أي مكان، مع إمكانية التحميل للاستخدام بدون إنترنت.',
    icon: ClockIcon,
    color: 'from-red-500 to-red-600',
    stats: 'متاح دائماً'
  },
  {
    name: 'تطبيق موبايل متطور',
    description:
      'تطبيق موبايل متطور يمكنك من الوصول للمحتوى التعليمي والتفاعل مع المجتمع القانوني من هاتفك.',
    icon: DevicePhoneMobileIcon,
    color: 'from-teal-500 to-teal-600',
    stats: 'Coming Soon'
  },
  {
    name: 'شهادات معتمدة',
    description:
      'احصل على شهادات معتمدة بعد إكمال المواد الدراسية، معترف بها من المؤسسات القانونية.',
    icon: ShieldCheckIcon,
    color: 'from-yellow-300 to-yellow-400',
    stats: 'معتمدة رسمياً'
  }
];

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

export default function FeaturesSection() {
  const { user } = useContext(AuthContext);

  return (
    <div className="bg-gradient-to-br from-gray-50 to-gray-100 py-24 sm:py-32">
      <div className="container">
        <div className="mx-auto max-w-2xl lg:text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center justify-center mb-4">
              <SparklesIcon className="w-8 h-8 text-yellow-400" />
            </div>
            <h2 className="text-base font-semibold leading-7 text-yellow-400">لماذا قانوني؟</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-dark sm:text-4xl font-cairo">
              كل ما تحتاجه في مكان واحد
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600 font-tajawal">
              نوفر لك تجربة تعليمية متكاملة تجمع بين المحتوى الأكاديمي عالي الجودة والتطبيق العملي،
              مع فرص مهنية متميزة لبناء مستقبلك في المجال القانوني.
            </p>
          </motion.div>
        </div>
        
        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none"
        >
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 xl:grid-cols-3">
            {features.map((feature) => (
              <motion.div
                key={feature.name}
                variants={item}
                className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
              >
                <div className={`inline-flex p-3 rounded-xl bg-gradient-to-r ${feature.color} text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-6 h-6" />
                </div>
                
                <h3 className="text-xl font-bold text-dark font-cairo mb-3">
                  {feature.name}
                </h3>
                
                <p className="text-gray-600 font-tajawal leading-relaxed mb-4">
                  {feature.description}
                </p>
                
                <div className="inline-block px-3 py-1 bg-yellow-400/10 text-yellow-400 text-sm font-semibold rounded-full">
                  {feature.stats}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Additional Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-20 grid grid-cols-1 md:grid-cols-4 gap-8"
        >
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <GlobeAltIcon className="w-8 h-8 text-yellow-400" />
            </div>
            <div className="text-3xl font-bold text-yellow-400 mb-2">15+</div>
            <div className="text-gray-600 font-tajawal">جامعة مشاركة</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <ChartBarIcon className="w-8 h-8 text-yellow-400" />
            </div>
            <div className="text-3xl font-bold text-yellow-400 mb-2">98%</div>
            <div className="text-gray-600 font-tajawal">معدل النجاح</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <HeartIcon className="w-8 h-8 text-yellow-400" />
            </div>
            <div className="text-3xl font-bold text-yellow-400 mb-2">24/7</div>
            <div className="text-gray-600 font-tajawal">دعم فني</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <SparklesIcon className="w-8 h-8 text-yellow-400" />
            </div>
            <div className="text-3xl font-bold text-yellow-400 mb-2">100%</div>
            <div className="text-gray-600 font-tajawal">ضمان الجودة</div>
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-yellow-300 to-yellow-400 rounded-2xl p-8 text-white relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-10 rounded-full translate-y-12 -translate-x-12"></div>

            <div className="relative z-10">
              <h3 className="text-3xl font-bold font-cairo mb-4">
                {user ? "مرحباً بك مرة أخرى!" : "جاهز لبدء رحلتك القانونية؟"}
              </h3>
              <p className="text-lg mb-6 font-tajawal">
                {user
                  ? "استكمل تعلمك واستكشف المزيد من المحتوى المتميز"
                  : "انضم إلى أكثر من 5000 طالب واحصل على تجربة تعليمية لا تُنسى"
                }
              </p>

              {!user && (
                <div className="flex items-center justify-center gap-6 mb-6 text-sm">
                  <div className="flex items-center gap-2">
                    <HeartIcon className="w-5 h-5" />
                    <span>تجربة مجانية 7 أيام</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ShieldCheckIcon className="w-5 h-5" />
                    <span>بدون التزام</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <SparklesIcon className="w-5 h-5" />
                    <span>إلغاء في أي وقت</span>
                  </div>
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to={user ? (user.is_superuser ? "/admin-user" : user.is_instructor ? "/instructor-profile" : "/student-profile") : "/register"}
                  className="inline-block bg-white text-yellow-900 font-bold py-4 px-8 rounded-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  {user ? (user.is_superuser ? "انتقل إلى لوحة التحكم" : user.is_instructor ? "انتقل إلى بروفايل المدرس" : "انتقل إلى الملف الدراسي") : "ابدأ الآن مجاناً 🚀"}
                </Link>

                {!user && (
                  <Link
                    to="/about"
                    className="inline-block border-2 border-white text-white font-semibold py-4 px-8 rounded-lg hover:bg-white hover:text-yellow-900 transition-all duration-300"
                  >
                    تعرف علينا أكثر
                  </Link>
                )}
              </div>

              {!user && (
                <p className="text-sm mt-4 opacity-90">
                  💡 <strong>نصيحة:</strong> سجل الآن واحصل على خصم 30% على الخطة السنوية
                </p>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
} 