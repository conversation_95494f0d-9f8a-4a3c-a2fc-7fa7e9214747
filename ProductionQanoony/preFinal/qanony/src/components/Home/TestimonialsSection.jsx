import React from 'react';
import { motion } from 'framer-motion';
import { StarIcon } from '@heroicons/react/24/solid';
import { ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';

const testimonials = [
  {
    id: 1,
    name: "أحم<PERSON> محمد",
    role: "طالب حقوق - السنة الثالثة",
    university: "جامعة القاهرة",
    content: "منصة قانوني غيرت حياتي الدراسية تماماً! المحتوى منظم ومفصل، والأساتذة متعاونين جداً. نجحت في جميع المواد بفضل المنصة.",
    rating: 5
  },
  {
    id: 2,
    name: "فاطمة علي",
    role: "محامية ناشئة",
    university: "خريجة جامعة عين شمس",
    content: "بعد التخرج، وجدت في منصة قانوني فرصة ذهبية للتعلم المستمر. المكتبة القانونية شاملة ومحدثة، والمنتديات مفيدة جداً.",
    rating: 5
  },
  {
    id: 3,
    name: "محمد حسن",
    role: "مستشار قانوني",
    university: "خبرة 15 سنة",
    content: "أستخدم منصة قانوني لتدريب الموظفين الجدد في مكتبي. المحتوى احترافي ومتوافق مع أحدث التشريعات. أنصح بها بشدة.",
    rating: 5
  },
  {
    id: 4,
    name: "سارة أحمد",
    role: "طالبة حقوق - السنة الرابعة",
    university: "جامعة الإسكندرية",
    content: "المنصة ساعدتني في فهم المواد الصعبة مثل القانون المدني والقانون التجاري. الشروحات واضحة والأمثلة عملية.",
    rating: 5
  },
  {
    id: 5,
    name: "علي محمود",
    role: "محامي متخصص في القانون الجنائي",
    university: "خبرة 8 سنوات",
    content: "منصة قانوني مصدر موثوق للمعلومات القانونية. أستخدمها للاطلاع على أحدث التعديلات والتشريعات الجديدة.",
    rating: 5
  },
  {
    id: 6,
    name: "نور الدين",
    role: "طالب حقوق - السنة الثانية",
    university: "جامعة المنصورة",
    content: "المنصة سهلة الاستخدام ومحتواها غني. أحب التفاعل مع الطلاب الآخرين في المنتديات وتبادل الخبرات.",
    rating: 5
  }
];

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

export default function TestimonialsSection() {
  return (
    <div className="bg-gradient-to-br from-gray-50 to-gray-100 py-24 sm:py-32">
      <div className="container">
        <div className="mx-auto max-w-2xl text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-base font-semibold leading-7 text-yellow-400">آراء طلابنا</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-dark sm:text-4xl font-cairo">
              ماذا يقولون عن منصة قانوني؟
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600 font-tajawal">
              انضم أكثر من 5,000 طالب ومحامي لمنصة قانوني واستفادوا من محتواها التعليمي المتميز
            </p>
          </motion.div>
        </div>

        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {testimonials.map((testimonial) => (
            <motion.div
              key={testimonial.id}
              variants={item}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
            >
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-dark font-cairo mb-1">{testimonial.name}</h3>
                <p className="text-sm text-yellow-400 font-medium mb-1">{testimonial.role}</p>
                <p className="text-xs text-gray-500">{testimonial.university}</p>
              </div>

              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <StarIcon key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>

              <div className="relative">
                <ChatBubbleLeftRightIcon className="absolute -top-2 -right-2 w-8 h-8 text-yellow-400/20" />
                <p className="text-gray-600 font-tajawal leading-relaxed text-sm">
                  "{testimonial.content}"
                </p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-20 grid grid-cols-1 md:grid-cols-4 gap-8 text-center"
        >
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <div className="text-3xl font-bold text-yellow-400 mb-2">5,000+</div>
            <div className="text-gray-600 font-tajawal">طالب مسجل</div>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <div className="text-3xl font-bold text-yellow-400 mb-2">500+</div>
            <div className="text-gray-600 font-tajawal">محاضرة تفاعلية</div>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <div className="text-3xl font-bold text-yellow-400 mb-2">50+</div>
            <div className="text-gray-600 font-tajawal">أستاذ متخصص</div>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <div className="text-3xl font-bold text-yellow-400 mb-2">95%</div>
            <div className="text-gray-600 font-tajawal">معدل الرضا</div>
          </div>
        </motion.div>
      </div>
    </div>
  );
} 