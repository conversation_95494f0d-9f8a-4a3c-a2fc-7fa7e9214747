.about-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60px 20px;
  gap: 40px;
  direction: rtl;
}

.about-section .text {
  flex: 1;
}

.about-section h2 {
  font-size: 32px;
  margin-bottom: 16px;
  color: var(--primary);
}

.cta-btn {
  display: inline-block;
  background: var(--primary);
  color: #fff;
  padding: 10px 20px;
  border-radius: 6px;
  text-decoration: none;
  margin-top: 20px;
  transition: background 0.3s;
}

.cta-btn:hover {
  background: #eab308;
}

.illustration img {
  max-width: 260px;
}

@media (max-width: 768px) {
  .about-section {
    flex-direction: column;
    text-align: center;
  }
  .illustration img {
    max-width: 180px;
  }
} 