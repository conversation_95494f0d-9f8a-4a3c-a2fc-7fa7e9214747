import React, { useContext, useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  ArrowRightIcon, 
  CheckCircleIcon, 
  ClockIcon, 
  UserGroupIcon,
  StarIcon,
  GiftIcon,
  FireIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { AuthContext } from '../../context/AuthContext';

const CallToActionSection = () => {
  const { user } = useContext(AuthContext);
  const [timeLeft, setTimeLeft] = useState({
    hours: 47,
    minutes: 23,
    seconds: 45
  });

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 };
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 };
        }
        return prev;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Don't show CTA if user is already logged in
  if (user) {
    return null;
  }

  const benefits = [
    "وصول فوري لأكثر من 500 محاضرة",
    "مساعد ذكي متخصص في القانون",
    "شهادات معتمدة بعد إكمال المواد",
    "فرص توظيف حصرية",
    "مجتمع قانوني نشط",
    "دعم فني 24/7"
  ];

  return (
    <div className="relative bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 py-20 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-72 h-72 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute top-0 right-0 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute bottom-0 left-1/2 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      <div className="relative container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          {/* Urgency Banner */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="inline-flex items-center gap-2 bg-red-500 bg-opacity-20 backdrop-blur-sm border border-red-400 rounded-full px-6 py-3 mb-8"
          >
            <FireIcon className="w-5 h-5 text-red-400" />
            <span className="text-red-200 font-semibold">عرض محدود الوقت</span>
            <FireIcon className="w-5 h-5 text-red-400" />
          </motion.div>

          {/* Main Headline */}
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-4xl md:text-6xl font-bold text-white mb-6 font-cairo"
          >
            ابدأ رحلتك القانونية
            <span className="block text-yellow-400">اليوم مجاناً!</span>
          </motion.h2>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-xl text-gray-200 mb-8 font-tajawal max-w-2xl mx-auto"
          >
            انضم إلى أكثر من 5000 طالب وخريج واحصل على تعليم قانوني متميز مع فرص مهنية حصرية
          </motion.p>

          {/* Countdown Timer */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
            className="bg-black bg-opacity-30 backdrop-blur-sm rounded-2xl p-6 mb-8 max-w-md mx-auto"
          >
            <div className="flex items-center justify-center gap-2 mb-4">
              <ClockIcon className="w-6 h-6 text-yellow-400" />
              <span className="text-yellow-400 font-semibold">العرض ينتهي خلال:</span>
            </div>
            <div className="flex justify-center gap-4">
              <div className="text-center">
                <div className="bg-yellow-400 text-yellow-900 rounded-lg px-3 py-2 font-bold text-2xl">
                  {timeLeft.hours.toString().padStart(2, '0')}
                </div>
                <div className="text-gray-300 text-sm mt-1">ساعة</div>
              </div>
              <div className="text-center">
                <div className="bg-yellow-400 text-yellow-900 rounded-lg px-3 py-2 font-bold text-2xl">
                  {timeLeft.minutes.toString().padStart(2, '0')}
                </div>
                <div className="text-gray-300 text-sm mt-1">دقيقة</div>
              </div>
              <div className="text-center">
                <div className="bg-yellow-400 text-yellow-900 rounded-lg px-3 py-2 font-bold text-2xl">
                  {timeLeft.seconds.toString().padStart(2, '0')}
                </div>
                <div className="text-gray-300 text-sm mt-1">ثانية</div>
              </div>
            </div>
          </motion.div>

          {/* Benefits Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
            className="grid md:grid-cols-2 gap-4 mb-8 max-w-2xl mx-auto"
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.5 + index * 0.1 }}
                className="flex items-center gap-3 text-left"
              >
                <CheckCircleIcon className="w-6 h-6 text-green-400 flex-shrink-0" />
                <span className="text-gray-200">{benefit}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.8 }}
            className="flex flex-col sm:flex-row gap-4 justify-center mb-8"
          >
            <Link
              to="/register"
              className="group inline-flex items-center justify-center gap-3 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-yellow-900 font-bold py-4 px-8 rounded-xl text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
            >
              <GiftIcon className="w-6 h-6" />
              ابدأ مجاناً الآن
              <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            
            <Link
              to="/about"
              className="inline-flex items-center justify-center gap-3 border-2 border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-yellow-900 font-semibold py-4 px-8 rounded-xl text-lg transition-all duration-300"
            >
              <SparklesIcon className="w-6 h-6" />
              تعرف علينا أكثر
            </Link>
          </motion.div>

          {/* Social Proof */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 1 }}
            className="flex flex-wrap justify-center items-center gap-8 text-gray-300"
          >
            <div className="flex items-center gap-2">
              <UserGroupIcon className="w-5 h-5 text-yellow-400" />
              <span className="font-semibold">5,000+ طالب نشط</span>
            </div>
            <div className="flex items-center gap-2">
              <StarIcon className="w-5 h-5 text-yellow-400" />
              <span className="font-semibold">تقييم 4.9/5</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircleIcon className="w-5 h-5 text-green-400" />
              <span className="font-semibold">معتمد رسمياً</span>
            </div>
          </motion.div>

          {/* Risk-Free Guarantee */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 1.2 }}
            className="mt-8 bg-green-500 bg-opacity-20 backdrop-blur-sm border border-green-400 rounded-xl p-4 max-w-md mx-auto"
          >
            <div className="flex items-center justify-center gap-2 mb-2">
              <CheckCircleIcon className="w-6 h-6 text-green-400" />
              <span className="text-green-200 font-semibold">ضمان استرداد المال</span>
            </div>
            <p className="text-green-300 text-sm">
              إذا لم تكن راضياً خلال 30 يوم، سنسترد أموالك كاملة
            </p>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default CallToActionSection;
