import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon, UserGroupIcon, AcademicCapIcon } from '@heroicons/react/24/outline';
import { AuthContext } from '../../context/AuthContext';

const slides = [
  {
    id: 1,
    title: "حقق حلمك في المحاماة مع منصة قانوني",
    subtitle: "طريقك المضمون لتصبح محامياً متميزاً في مصر",
    description: "اكتشف أسرار النجاح في المحاماة مع أقوى منصة تعليمية قانونية! محاضرات حصرية من كبار المحامين، مكتبة قانونية ضخمة، واختبارات تفاعلية تضمن لك التفوق والنجاح في مسيرتك المهنية.",
    image: "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    role: "محامي ناجح",
    cta: "ابدأ رحلة النجاح مجاناً",
    ctaSecondary: "اكتشف قصص النجاح",
    stats: [
      { icon: AcademicCapIcon, value: "500+", label: "محاضرة حصرية" },
      { icon: StarIcon, value: "4.9", label: "تقييم ممتاز" },
      { icon: UserGroupIcon, value: "50+", label: "خبير قانوني" }
    ]
  },
  {
    id: 2,
    title: "تفوق في دراستك مع أقوى المحاضرات القانونية",
    subtitle: "من الصفر إلى الخبرة مع نخبة أساتذة القانون",
    description: "لا تضيع وقتك في البحث! احصل على كنز المعرفة القانونية في مكان واحد. محاضرات مبسطة وشاملة تغطي كل ما تحتاجه لتتفوق في دراستك وتصبح محامياً محترفاً بأسرع وقت ممكن.",
    image: "https://images.unsplash.com/photo-1521791136064-7986c2920216?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80",
    role: "طالب متفوق",
    cta: "احصل على 7 أيام مجاناً",
    ctaSecondary: "شاهد المحاضرات المجانية",
    stats: [
      { icon: UserGroupIcon, value: "50+", label: "أستاذ متخصص" },
      { icon: AcademicCapIcon, value: "20+", label: "مادة شاملة" },
      { icon: StarIcon, value: "95%", label: "معدل التفوق" }
    ]
  },
  {
    id: 3,
    title: "اضمن مستقبلك المهني في عالم القانون",
    subtitle: "من التخرج إلى الوظيفة المثالية في خطوات بسيطة",
    description: "لماذا تبحث عن وظيفة بينما يمكن للوظائف أن تبحث عنك؟ انضم لشبكتنا المهنية واحصل على أفضل الفرص في أكبر مكاتب المحاماة والشركات. نحن نربطك بأصحاب العمل المناسبين لمهاراتك وطموحاتك.",
    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2011&q=80",
    role: "محامي محترف",
    cta: "ابني شبكتك المهنية الآن",
    ctaSecondary: "استكشف الفرص الذهبية",
    stats: [
      { icon: UserGroupIcon, value: "200+", label: "فرصة ذهبية" },
      { icon: AcademicCapIcon, value: "85%", label: "نجاح التوظيف" },
      { icon: StarIcon, value: "15+", label: "مكتب شريك" }
    ]
  }
];

export default function HeroSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const { user } = useContext(AuthContext);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  return (
    <div className="relative h-[500px] md:h-[600px] lg:h-[700px] overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 mb-0">

      {/* Slides */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0"
        >
          <div 
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{ backgroundImage: `url(${slides[currentSlide].image})` }}
          >
            <div className="absolute inset-0 bg-black bg-opacity-50"></div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center h-full">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
                className="space-y-4"
              >
                <div className="inline-block px-4 py-2 bg-yellow-400 bg-opacity-20 backdrop-blur-sm rounded-full border border-yellow-400">
                  <span className="text-yellow-400 font-semibold">{slides[currentSlide].role}</span>
                </div>
                
                <h1 className="text-5xl md:text-7xl font-bold font-cairo leading-tight text-white">
                  {slides[currentSlide].title}
                </h1>
                
                <h2 className="text-2xl md:text-3xl font-semibold text-yellow-400">
                  {slides[currentSlide].subtitle}
                </h2>
                
                <p className="text-lg md:text-xl text-gray-200 font-tajawal max-w-2xl mx-auto leading-relaxed">
                  {slides[currentSlide].description}
                </p>

                {/* Statistics */}
                <div className="flex flex-wrap justify-center gap-6 mt-6 mb-6">
                  {slides[currentSlide].stats.map((stat, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 + index * 0.1 }}
                      className="text-center"
                    >
                      <div className="flex items-center justify-center mb-1">
                        <stat.icon className="w-5 h-5 text-yellow-400 mr-2" />
                        <span className="text-xl font-bold text-white">{stat.value}</span>
                      </div>
                      <p className="text-xs text-gray-300">{stat.label}</p>
                    </motion.div>
                  ))}
                </div>

                <div className="pt-6 space-y-4">
                  {/* Primary CTA */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link
                      to={user ? (user.is_staff ? "/admin-user" : "/student-profile") : "/register"}
                      className="inline-block bg-yellow-400 hover:bg-yellow-500 text-yellow-900 font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200"
                    >
                      {user ? "انتقل إلى لوحة التحكم" : slides[currentSlide].cta}
                    </Link>

                    {!user && (
                      <Link
                        to="/about"
                        className="inline-block border-2 border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-yellow-900 font-semibold py-4 px-8 rounded-lg text-lg transition-colors duration-200"
                      >
                        {slides[currentSlide].ctaSecondary}
                      </Link>
                    )}
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-black bg-opacity-50 hover:bg-opacity-75 text-white rounded-full transition-colors duration-200"
      >
        <ChevronLeftIcon className="w-6 h-6" />
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-black bg-opacity-50 hover:bg-opacity-75 text-white rounded-full transition-colors duration-200"
      >
        <ChevronRightIcon className="w-6 h-6" />
      </button>

      {/* Dots Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? 'bg-yellow-400 scale-125'
                : 'bg-white bg-opacity-50 hover:bg-opacity-75'
            }`}
          />
        ))}
      </div>
    </div>
  );
} 