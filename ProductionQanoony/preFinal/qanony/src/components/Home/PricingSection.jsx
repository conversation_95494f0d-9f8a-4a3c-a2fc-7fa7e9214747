import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import apiClient from '../../api';
import PricingCard from './PricingCard';
import FeaturedPricingCard from './FeaturedPricingCard';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

// NOTE: If you are seeing an "Element type is invalid" error here,
// it is likely an issue with your local development environment.
// Please try the following steps:
// 1. Restart your React development server.
// 2. If the error persists, delete the `node_modules` directory and the `package-lock.json` file.
// 3. Run `npm install` to reinstall dependencies.
// This type of error often occurs when the module cache is corrupted.

const defaultFeatures = {
  free: [
    'الوصول إلى المقالات الأساسية',
    'مشاهدة المحاضرات المجانية',
    'المشاركة في المنتدى العام',
    'تحميل محدود للوثائق القانونية',
  ],
  pro: [],
  enterprise: [
    'جميع مميزات الخطة الاحترافية',
    'لوحة تحكم للمؤسسة',
    'تقارير تقدم الطلاب',
    'تخصيص المحتوى',
  ],
};

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

export default function PricingSection() {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        const response = await apiClient.get(`/api/subscriptions/plans/`);

        // Transform API data to match our UI needs
        const transformedPlans = response.data.map((plan, index) => {
          // The second card (index 1) will always be featured.
          const isFeatured = index === 1;
          
          return {
            ...plan,
            featured: isFeatured,
            href: plan.price === 0 ? '/register' : `/register?plan=${plan.id}`,
          };
        });
        setPlans(transformedPlans);
        setLoading(false);
      } catch (err) {
        console.error('PricingSection: Error fetching plans:', err);
        setError('حدث خطأ في تحميل الخطط. يرجى المحاولة مرة أخرى.');
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  if (loading) {
    return (
      <div className="bg-background py-24 sm:py-32">
        <div className="container">
          <div className="text-center">
            <p className="text-lg text-gray-600">جاري تحميل الخطط...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-background py-24 sm:py-32">
        <div className="container">
          <div className="text-center">
            <p className="text-lg text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background py-24 sm:py-32">
      <div className="container">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-base font-semibold leading-7 text-yellow-400">الأسعار</h2>
          <p className="mt-2 text-4xl font-bold tracking-tight text-dark sm:text-5xl font-cairo">
            اختر الخطة المناسبة لك
          </p>
          <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
            ابدأ مجاناً واختبر جودة المحتوى، ثم اختر الخطة التي تناسب احتياجاتك التعليمية
          </p>

          {/* Special Offer Banner */}
          <div className="mt-6 inline-flex items-center gap-2 bg-red-50 border border-red-200 rounded-full px-6 py-2">
            <span className="text-red-600 font-semibold text-sm">🔥 عرض محدود:</span>
            <span className="text-red-700 text-sm">خصم 30% على الخطة السنوية</span>
          </div>
        </div>
        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className="isolate mx-auto mt-16 grid max-w-md grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3"
        >
          {plans.map((tier) =>
            tier.featured ? (
              <FeaturedPricingCard key={tier.id} tier={tier} />
            ) : (
              <PricingCard key={tier.id} tier={tier} />
            )
          )}
        </motion.div>
      </div>
    </div>
  );
}