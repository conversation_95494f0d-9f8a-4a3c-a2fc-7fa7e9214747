import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

export default function FeaturedPricingCard({ tier }) {
  return (
    <motion.div
      variants={item}
      whileHover={{ y: -8, scale: 1.03 }}
      transition={{ type: 'spring', stiffness: 300 }}
      className="relative flex flex-col rounded-3xl p-8 z-10 bg-gradient-to-r from-yellow-300 to-yellow-400 text-gray-900 shadow-2xl xl:p-10"
    >
      <p className="absolute top-0 -translate-y-1/2 self-center rounded-full bg-gray-800 px-3 py-1 text-xs font-semibold tracking-wide text-white">
        الخطة الموصى بها
      </p>

      <div className="flex-1">
        <h3 id={`tier-${tier.id}`} className="text-2xl font-bold tracking-tight text-gray-900">
          {tier.name}
        </h3>
        <p className="mt-4 text-base leading-6 text-yellow-900">
          اشتراك لمدة {tier.duration_days} يوم
        </p>
        <p className="mt-6 flex items-baseline gap-x-1">
          <span className="text-4xl font-bold tracking-tight text-gray-900">
            {tier.price}
          </span>
          <span className="text-sm font-semibold leading-6 text-yellow-900">
            جنيه
          </span>
        </p>
      </div>
      <Link
        to={tier.href}
        aria-describedby={`tier-${tier.id}`}
        className="mt-8 block w-full text-center rounded-md px-3 py-2 text-sm font-semibold text-gray-900 bg-white hover:bg-gray-100 shadow-lg transition-colors duration-200"
      >
        ابدأ الآن
      </Link>
    </motion.div>
  );
} 