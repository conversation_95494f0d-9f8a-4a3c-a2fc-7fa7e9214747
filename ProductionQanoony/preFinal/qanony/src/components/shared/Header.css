/* ... existing code ... */
.mobile-menu-dropdown {
  position: absolute;
  top: 100%; /* Position it right below the header */
  left: 0;
  right: 0;
  background-color: #ffffff;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  z-index: 1000;
  overflow: hidden;
}

.mobile-nav-link {
  display: block;
  padding: 12px 16px;
  font-size: 1rem;
  color: #343a40;
  text-decoration: none;
  border-radius: 8px;
  transition: background-color 0.2s, color 0.2s;
  text-align: right;
}

.mobile-nav-link:hover {
  background-color: #f8f9fa;
  color: #facc15;
}