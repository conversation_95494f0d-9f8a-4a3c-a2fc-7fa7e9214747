/* --- <PERSON><PERSON><PERSON> <PERSON>er with <PERSON> Hover --- */

.main-footer {
  background-color: #111827; /* Dark gray/black */
  color: #FFFFFF;
  padding: 4rem 0 0; /* More vertical space */
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  /* Responsive grid: 4 columns on desktop, 2 on tablet, 1 on mobile */
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2.5rem;
}

.footer-section {
  text-align: right;
  padding-left: 2rem; /* Space for the separator */
  position: relative;
}

/* Vertical separator lines */
.footer-section:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 80%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.1);
}

.footer-section h3 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #facc15; /* Gold border */
  display: inline-block;
  color: #facc15; /* Gold text */
}

.about p, .links-grid a, .contact-item {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
}

.about p {
  line-height: 1.8;
}

.links-grid {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.links-grid a {
  text-decoration: none;
  transition: color 0.3s ease, transform 0.3s ease;
  display: inline-block;
  color: rgba(255, 255, 255, 0.8);
}

.links-grid a:hover {
  color: #facc15; /* Gold on hover */
  text-decoration: none;
  transform: translateX(-3px);
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.contact-item .icon {
  font-size: 1.2rem;
  min-width: 1.2rem;
  color: #facc15; /* Gold icons */
}

.social-icons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-icons a {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.5rem;
  transition: transform 0.3s ease, color 0.3s ease;
}

.social-icons a:hover {
  transform: scale(1.2);
  color: #facc15; /* Gold on hover */
}

.footer-bottom {
  margin-top: 3rem;
  padding: 1.5rem 2rem;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.3); /* Darker background */
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  border-top: 1px solid rgba(184, 148, 31, 0.2); /* Subtle gold border */
}

.footer-bottom .social-links a {
  color: #666;
  transition: color 0.3s ease;
}

.footer-bottom .social-links a:hover {
  color: #facc15; /* Gold icons */
}

.footer-bottom .footer-links a:hover {
  color: #facc15; /* Gold on hover */
}

/* On smaller screens, remove vertical separators */
@media (max-width: 767px) {
  .footer-section:not(:last-child)::after {
    display: none;
  }
  .footer-section {
    padding-left: 0;
    text-align: center;
  }
  .links-grid, .contact-details {
    align-items: center;
  }
} 