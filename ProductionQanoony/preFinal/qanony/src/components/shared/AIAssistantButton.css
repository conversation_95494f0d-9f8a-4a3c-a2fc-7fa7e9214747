.ai-assistant-btn {
  position: fixed;
  bottom: 32px;
  left: 32px;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #facc15;
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  cursor: pointer;
  transition: box-shadow 0.2s;
}
.ai-assistant-btn:hover {
  box-shadow: 0 8px 24px rgba(250, 204, 21, 0.18);
}

.ai-assistant-popup {
  position: fixed;
  bottom: 110px;
  left: 32px;
  width: 370px;
  max-width: 95vw;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  z-index: 1100;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: ai-popup-in 0.2s;
}
@keyframes ai-popup-in {
  from { transform: translateY(40px) scale(0.95); opacity: 0; }
  to { transform: none; opacity: 1; }
}

.ai-assistant-close {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #fff;
  border: none;
  font-size: 1.5rem;
  color: #facc15;
  cursor: pointer;
  z-index: 1200;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: background 0.2s;
}
.ai-assistant-close:hover {
  background: #f7f7f7;
} 