import React from 'react';
import { isArraySafe, safeMap } from '../../utils/arrayHelpers';

/**
 * مكون آمن لعرض arrays مع معالجة الأخطاء
 */
const SafeArrayRenderer = ({ 
  data, 
  renderItem, 
  emptyMessage = 'لا توجد بيانات للعرض',
  errorMessage = 'خطأ في عرض البيانات',
  context = 'SafeArrayRenderer',
  className = '',
  loadingComponent = null,
  loading = false
}) => {
  
  // إذا كان في حالة تحميل
  if (loading) {
    return loadingComponent || (
      <div className={`text-center py-4 ${className}`}>
        <div className="text-gray-500">جاري التحميل...</div>
      </div>
    );
  }

  // تحقق من أن البيانات آمنة
  if (!isArraySafe(data, context)) {
    console.error(`${context}: Data is not safe for rendering:`, data);
    return (
      <div className={`text-center py-4 text-red-500 ${className}`}>
        {errorMessage}
      </div>
    );
  }

  // إذا كانت البيانات فارغة
  if (data.length === 0) {
    return (
      <div className={`text-center py-4 text-gray-500 ${className}`}>
        {emptyMessage}
      </div>
    );
  }

  // عرض البيانات بأمان
  try {
    return (
      <div className={className}>
        {safeMap(data, renderItem, context)}
      </div>
    );
  } catch (error) {
    console.error(`${context}: Error rendering items:`, error);
    return (
      <div className={`text-center py-4 text-red-500 ${className}`}>
        {errorMessage}
      </div>
    );
  }
};

/**
 * Hook مخصص للتعامل مع arrays بأمان
 */
export const useSafeArray = (data, context = 'useSafeArray') => {
  const isValid = isArraySafe(data, context);
  const safeData = isValid ? data : [];
  
  return {
    data: safeData,
    isValid,
    isEmpty: safeData.length === 0,
    length: safeData.length,
    map: (fn) => safeMap(safeData, fn, context),
    filter: (fn) => {
      try {
        return safeData.filter(fn);
      } catch (error) {
        console.error(`${context}: Error in filter:`, error);
        return [];
      }
    },
    find: (fn) => {
      try {
        return safeData.find(fn);
      } catch (error) {
        console.error(`${context}: Error in find:`, error);
        return undefined;
      }
    }
  };
};

/**
 * مكون للعرض الشرطي الآمن
 */
export const SafeConditionalRender = ({ 
  condition, 
  children, 
  fallback = null,
  context = 'SafeConditionalRender'
}) => {
  try {
    return condition ? children : fallback;
  } catch (error) {
    console.error(`${context}: Error in conditional render:`, error);
    return fallback;
  }
};

/**
 * مكون wrapper آمن لأي محتوى
 */
export const SafeWrapper = ({ 
  children, 
  fallback = <div>خطأ في العرض</div>,
  context = 'SafeWrapper'
}) => {
  try {
    return children;
  } catch (error) {
    console.error(`${context}: Error in wrapper:`, error);
    return fallback;
  }
};

export default SafeArrayRenderer;
