import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FaFacebook, FaTwitter, FaLinkedin, FaPhone, FaEnvelope, FaMapMarkerAlt, FaInstagram, FaYoutube } from 'react-icons/fa';
import './Footer.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';

const Footer = () => {
  const [contactInfo, setContactInfo] = useState(null);

  useEffect(() => {
    const fetchContactInfo = async () => {
      try {
        const res = await fetch(`${API_BASE_URL}/api/auth/contact-info/`);
        if (res.ok) {
          const data = await res.json();
          setContactInfo(data);
        }
      } catch (e) {
        console.error('Failed to fetch contact info', e);
      }
    };

    fetchContactInfo();
  }, []);

  return (
    <footer className="main-footer">
      <div className="footer-content">
        {/* القسم الأول: معلومات عن الموقع */}
        <div className="footer-section about">
          <h3>قانوني</h3>
          <p>منصة تعليمية متخصصة لطلاب كلية الحقوق، نقدم محتوى قانوني عالي الجودة ومصادر تعليمية متميزة.</p>
        </div>

        {/* القسم الثاني: روابط سريعة */}
        <div className="footer-section quick-links">
          <h3>روابط سريعة</h3>
          <div className="links-grid">
            <Link to="/">الرئيسية</Link>
            <Link to="/student-profile">المواد</Link>
            <Link to="/library">مكتبة القوانين</Link>
            <Link to="/careers">بوابة التوظيف</Link>
            <Link to="/about">عن المنصة</Link>
            <Link to="/join-instructor">انضم إلينا كمعلم</Link>
            <Link to="/contact">اتصل بنا</Link>
          </div>
        </div>

        {/* القسم الثالث: معلومات الاتصال */}
        <div className="footer-section contact-info">
          <h3>تواصل معنا</h3>
          <div className="contact-details">
            {contactInfo?.phone_number && (
              <div className="contact-item">
                <FaPhone className="icon" />
                <span>{contactInfo.phone_number}</span>
              </div>
            )}
            {contactInfo?.email && (
              <div className="contact-item">
                <FaEnvelope className="icon" />
                <span>{contactInfo.email}</span>
              </div>
            )}
            {contactInfo?.address && (
              <div className="contact-item">
                <FaMapMarkerAlt className="icon" />
                <span>{contactInfo.address}</span>
              </div>
            )}
            {contactInfo?.working_hours && (
              <div className="contact-item">
                <span style={{fontSize: '0.9em', color: '#ccc'}}>
                  ساعات العمل: {contactInfo.working_hours}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* القسم الرابع: وسائل التواصل الاجتماعي */}
        <div className="footer-section social">
          <h3>تابعنا</h3>
          <div className="social-icons">
            {contactInfo?.facebook_url && (
              <a href={contactInfo.facebook_url} target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                <FaFacebook />
              </a>
            )}
            {contactInfo?.twitter_url && (
              <a href={contactInfo.twitter_url} target="_blank" rel="noopener noreferrer" aria-label="Twitter">
                <FaTwitter />
              </a>
            )}
            {contactInfo?.instagram_url && (
              <a href={contactInfo.instagram_url} target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                <FaInstagram />
              </a>
            )}
            {contactInfo?.linkedin_url && (
              <a href={contactInfo.linkedin_url} target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                <FaLinkedin />
              </a>
            )}
            {contactInfo?.youtube_url && (
              <a href={contactInfo.youtube_url} target="_blank" rel="noopener noreferrer" aria-label="YouTube">
                <FaYoutube />
              </a>
            )}
          </div>
        </div>
      </div>
      
      {/* شريط الحقوق */}
      <div className="footer-bottom">
        <p>جميع الحقوق محفوظة &copy; {new Date().getFullYear()} قانوني</p>
      </div>
    </footer>
  );
};

export default Footer; 