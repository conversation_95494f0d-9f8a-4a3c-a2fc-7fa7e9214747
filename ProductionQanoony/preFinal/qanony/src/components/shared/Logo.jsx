import React from 'react';

const Logo = ({ className = "w-8 h-8", color = "text-yellow-400" }) => {
  return (
    <div className={className} style={{ background: '#ffffff' }}>
      <img
        src="/images/image.png"
        alt="Qanony Academy Logo"
        className="w-full h-full object-contain"
        style={{
          maxWidth: '100%',
          height: 'auto',
          background: '#ffffff'
        }}
        onError={(e) => {
          console.error('Logo image failed to load:', e);
          // Fallback to text if image fails
          e.target.style.display = 'none';
          e.target.nextSibling.style.display = 'block';
        }}
      />
      {/* Fallback text logo */}
      <div
        className={`${color} font-bold text-center hidden`}
        style={{
          fontSize: 'clamp(0.5rem, 2vw, 1rem)',
          background: '#ffffff'
        }}
      >
        قانوني
      </div>
    </div>
  );
};

export default Logo;