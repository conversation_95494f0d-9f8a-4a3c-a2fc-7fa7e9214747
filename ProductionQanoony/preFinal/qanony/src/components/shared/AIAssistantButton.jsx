import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../../context/AuthContext';
import ChatWindow from '../ChatWindow/ChatWindow';
import './AIAssistantButton.css';

// Classic justice scale SVG icon (gold)
const JusticeScaleIcon = () => (
  <svg width="32" height="32" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="29" y="8" width="6" height="36" rx="3" fill="#facc15"/>
    <rect x="24" y="44" width="16" height="6" rx="3" fill="#facc15"/>
    <rect x="28" y="50" width="8" height="6" rx="3" fill="#facc15"/>
    <line x1="32" y1="14" x2="12" y2="32" stroke="#facc15" strokeWidth="2.5"/>
    <line x1="32" y1="14" x2="52" y2="32" stroke="#facc15" strokeWidth="2.5"/>
    <ellipse cx="12" cy="32" rx="7" ry="3.5" fill="#fff" stroke="#facc15" strokeWidth="2"/>
    <ellipse cx="52" cy="32" rx="7" ry="3.5" fill="#fff" stroke="#facc15" strokeWidth="2"/>
    <line x1="12" y1="32" x2="12" y2="38" stroke="#facc15" strokeWidth="2"/>
    <line x1="52" y1="32" x2="52" y2="38" stroke="#facc15" strokeWidth="2"/>
    <ellipse cx="12" cy="38" rx="5" ry="2.5" fill="#fff" stroke="#facc15" strokeWidth="1.5"/>
    <ellipse cx="52" cy="38" rx="5" ry="2.5" fill="#fff" stroke="#facc15" strokeWidth="1.5"/>
  </svg>
);

const AIAssistantButton = () => {
  const { user } = useContext(AuthContext);
  const [open, setOpen] = useState(false);
  // Persist open/close state in localStorage
  useEffect(() => {
    const stored = localStorage.getItem('ai_assistant_closed');
    if (stored === 'true') setOpen(false);
  }, []);
  const handleOpen = () => {
    setOpen(true);
    localStorage.setItem('ai_assistant_closed', 'false');
  };
  const handleClose = () => {
    setOpen(false);
    localStorage.setItem('ai_assistant_closed', 'true');
  };
  // Only show if user is logged in
  if (!user) return null;
  return (
    <>
      {!open && (
        <button className="ai-assistant-btn" onClick={handleOpen} aria-label="مساعد الذكاء الاصطناعي">
          <JusticeScaleIcon />
        </button>
      )}
      {open && (
        <div className="ai-assistant-popup">
          <button className="ai-assistant-close" onClick={handleClose}>&times;</button>
          {/* You can pass props to ChatWindow for AI mode, or use a placeholder for now */}
          <div style={{width: 350, height: 500}}>
            <ChatWindow aiMode={true} />
          </div>
        </div>
      )}
    </>
  );
};

export default AIAssistantButton; 