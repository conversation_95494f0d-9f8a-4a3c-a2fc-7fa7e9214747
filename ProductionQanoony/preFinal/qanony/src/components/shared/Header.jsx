import { Fragment, useState, useContext, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Dialog, Menu, Transition } from '@headlessui/react';
import {
  Bars3Icon,
  XMarkIcon,
  UserCircleIcon,
  AcademicCapIcon,
  BookOpenIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  BellIcon
} from '@heroicons/react/24/outline';
import apiClient from '../../api';
import { AuthContext } from '../../context/AuthContext';
import { useToast, useApiErrorHandler } from '../UI/Toast';
import { getErrorMessage } from '../../utils/errorHandler';
import ErrorBoundary from '../UI/ErrorBoundary';
import { motion, AnimatePresence } from 'framer-motion';
import Logo from './Logo';
import './Header.css';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

// دالة مساعدة للتأكد من أن notifications دايماً array
function ensureNotificationsArray(data) {
  if (Array.isArray(data)) return data;
  if (data && Array.isArray(data.results)) return data.results;
  if (data && Array.isArray(data.data)) return data.data;
  return [];
}

function HeaderComponent({ onToggleSidebar }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  const { user, logout, isAuthenticated } = useContext(AuthContext);
  const { handleError } = useApiErrorHandler();

  // Notifications state - تأكد من أن القيمة الافتراضية array
  const [notifications, setNotifications] = useState([]);
  const [notifMenuOpen, setNotifMenuOpen] = useState(false);
  const notifMenuWasOpen = useRef(false);

  useEffect(() => {
    if (isAuthenticated) {
      // Try to fetch notifications
      apiClient.get('/api/notifications/', { params: { page_size: 10 } })
        .then((res) => {
          // استخدام الدالة المساعدة للتأكد من أن البيانات array
          const notificationsData = ensureNotificationsArray(res.data);
          setNotifications(notificationsData);
        })
        .catch((error) => {
          console.error('Header: Failed to fetch notifications:', error);
          setNotifications([]); // تعيين array فارغ في حالة الخطأ

          // معالجة الأخطاء باستخدام Error Handler
          if (error.response?.status === 401) {
            console.log('Header: User token is invalid, redirecting to login');
            // Clear authentication if token is invalid
            localStorage.removeItem('access');
            localStorage.removeItem('refresh');
            localStorage.removeItem('user');
            // Don't reload the page, just clear the state
            window.location.href = '/login';
          } else {
            // لا نعرض خطأ للمستخدم هنا لأن الإشعارات ليست ضرورية لعمل الصفحة
            console.warn('Failed to load notifications, but continuing normally');
          }
        });
    } else {
      setNotifications([]);
    }
  }, [isAuthenticated]);

  // Mark all as read when menu opens
  useEffect(() => {
    if (notifMenuOpen && !notifMenuWasOpen.current) {
      notifMenuWasOpen.current = true;

      // التأكد من أن notifications هو array قبل استخدام filter
      if (Array.isArray(notifications)) {
        const unread = notifications.filter(n => n && !n.read_at);
        if (unread.length > 0) {
          Promise.all(
            unread.map(n =>
              apiClient.post(`/api/notifications/${n.id}/mark_as_read/`)
            )
          ).then(() => {
            setNotifications(prev =>
              Array.isArray(prev) ? prev.map(n => n.read_at ? n : { ...n, read_at: new Date().toISOString() }) : []
            );
          }).catch(error => {
            console.error('Error marking notifications as read:', error);
          });
        }
      }
    }
    if (!notifMenuOpen) {
      notifMenuWasOpen.current = false;
    }
  }, [notifMenuOpen]); // إزالة notifications من dependencies لتجنب infinite loop

  // حساب عدد الإشعارات غير المقروءة مع حماية من الأخطاء
  const unreadCount = (() => {
    try {
      if (!Array.isArray(notifications)) return 0;
      return notifications.filter((n) => n && typeof n === 'object' && !n.read_at).length;
    } catch (error) {
      console.error('Error calculating unread count:', error);
      return 0;
    }
  })();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <header className="bg-white relative">
      <nav className="container mx-auto flex items-center justify-between py-4" aria-label="Global">
        <div className="flex lg:flex-1">
          <Link to="/" className="-m-1.5 p-1.5 flex items-center space-x-3 space-x-reverse">
            <span className="text-2xl font-bold text-yellow-400">قانوني</span>
          </Link>
        </div>
        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 bg-white text-yellow-400 border border-yellow-400 hover:bg-yellow-400 hover:text-white transition-colors duration-200"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            <span className="sr-only">Open main menu</span>
            {mobileMenuOpen ? (
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            ) : (
              <Bars3Icon className="h-6 w-6" aria-hidden="true" />
            )}
          </button>
        </div>
        <div className="hidden lg:flex lg:gap-x-8">
          {user?.is_instructor ? (
            <>
              <Link to="/instructor-dashboard" className="nav-link">
                لوحة تحكم المدرس
              </Link>
              <Link to="/instructor-profile" className="nav-link">
                بروفايل المدرس
              </Link>
            </>
          ) : null}
          {user?.is_superuser && (
            <Link to="/admin-user" className="nav-link">
              لوحة التحكم
            </Link>
          )}
          <Link to="/student-profile" className="nav-link">
            الملف الدراسي
          </Link>
          <Link to="/library" className="nav-link">
            مكتبة القوانين
          </Link>
          <Link to="/jobs" className="nav-link">
            بوابة التوظيف
          </Link>
          <Link to="/forum" className="nav-link">
            المنتدى
          </Link>
          <Link to="/about" className="nav-link">
            عن المنصة
          </Link>
        </div>
        <div className="hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4">
          {isAuthenticated ? (
            <>
              {/* Notifications */}
              <Menu as="div" className="relative">
                <Menu.Button
                  className="relative flex items-center gap-1 px-2 py-1 bg-white rounded-md text-dark hover:bg-yellow-400 hover:text-white transition-colors"
                  onClick={() => setNotifMenuOpen((open) => !open)}
                  onBlur={() => setNotifMenuOpen(false)}
                >
                  <BellIcon className="h-6 w-6" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-600 text-white rounded-full text-xs h-4 w-4 flex items-center justify-center">
                      {unreadCount}
                    </span>
                  )}
                </Menu.Button>
                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="absolute left-0 z-[60] mt-2 w-80 origin-top-left rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none max-h-96 overflow-y-auto" style={{direction:'rtl'}}>
                    {!Array.isArray(notifications) || notifications.length === 0 ? (
                      <div className="px-4 py-3 text-sm text-gray-500">لا توجد إشعارات</div>
                    ) : (
                      notifications.filter(n => n && typeof n === 'object' && n.id).map((n) => (
                        <Menu.Item key={n.id}>
                          {({ active }) => (
                            <div className={classNames(active ? 'bg-gray-50' : '', 'block px-4 py-2 text-sm text-dark text-right whitespace-normal')}>
                              <p className="font-semibold">{n.subject_ar}</p>
                              <p className="text-xs text-gray-600">{n.content_ar}</p>
                            </div>
                          )}
                        </Menu.Item>
                      ))
                    )}
                  </Menu.Items>
                </Transition>
              </Menu>

              {/* Account menu */}
              <Menu as="div" className="relative">
                <Menu.Button className="flex items-center gap-1 px-2 py-1 bg-white rounded-md text-yellow-400 border border-yellow-400 hover:bg-yellow-400 hover:text-white transition-colors">
                  <UserCircleIcon className="h-6 w-6" />
                  <span className="text-xs font-medium">{user?.first_name || user?.email}</span>
                </Menu.Button>
                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="absolute left-0 z-[60] mt-2 w-48 origin-top-left rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/student-profile"
                          className={classNames(
                            active ? 'bg-gray-50' : '',
                            'flex px-4 py-2 text-sm text-dark hover:text-yellow-400 items-center gap-2'
                          )}
                        >
                          <UserCircleIcon className="h-5 w-5" />
                          الملف الشخصي
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          to="/settings"
                          className={classNames(
                            active ? 'bg-gray-50' : '',
                            'flex px-4 py-2 text-sm text-dark hover:text-yellow-400 items-center gap-2'
                          )}
                        >
                          <CogIcon className="h-5 w-5" />
                          الإعدادات
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={handleLogout}
                          className={classNames(
                            active ? 'bg-gray-50' : '',
                            'flex w-full px-4 py-2 text-sm text-dark hover:text-yellow-400 items-center gap-2'
                          )}
                        >
                          <ArrowRightOnRectangleIcon className="h-5 w-5" />
                          تسجيل الخروج
                        </button>
                      )}
                    </Menu.Item>
                  </Menu.Items>
                </Transition>
              </Menu>
            </>
          ) : (
            <div className="flex items-center gap-4">
              <Link
                to="/login"
                className="text-sm font-semibold leading-6 text-dark hover:text-yellow-400 transition-colors"
              >
                تسجيل الدخول
              </Link>
              <Link
                to="/register"
                className="btn-primary"
              >
                إنشاء حساب
              </Link>
            </div>
          )}
        </div>
      </nav>

      {/* Mobile menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ x: '100%', opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: '100%', opacity: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10"
            style={{ boxShadow: '0 0 0 9999px rgba(0,0,0,0.2)' }}
          >
            <div className="flex items-center justify-between">
              <Link to="/" className="-m-1.5 p-1.5 flex items-center space-x-3 space-x-reverse">
                <span className="text-2xl font-bold text-yellow-400">قانوني</span>
              </Link>
              <button
                type="button"
                className="-m-2.5 rounded-md p-2.5 text-gray-700"
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="sr-only">Close menu</span>
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
            {/* Notifications in burger menu */}
            {isAuthenticated && (
              <div className="flex items-center gap-3 mt-6 mb-2">
                <button
                  className="relative flex items-center gap-1 px-2 py-1 bg-white rounded-md text-dark border border-yellow-400 hover:bg-yellow-400 hover:text-white transition-colors"
                  onClick={() => setNotifMenuOpen((open) => !open)}
                  style={{ minWidth: 44 }}
                >
                  <BellIcon className="h-6 w-6" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-600 text-white rounded-full text-xs h-4 w-4 flex items-center justify-center">
                      {unreadCount}
                    </span>
                  )}
                </button>
                <span className="text-sm text-gray-700">الإشعارات</span>
              </div>
            )}
            {/* Notifications dropdown in mobile */}
            {notifMenuOpen && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none max-h-80 overflow-y-auto mb-4"
                style={{ direction: 'rtl', marginTop: 8 }}
              >
                {!Array.isArray(notifications) || notifications.length === 0 ? (
                  <div className="px-4 py-3 text-sm text-gray-500">لا توجد إشعارات</div>
                ) : (
                  notifications.filter(n => n && typeof n === 'object' && n.id).map((n) => (
                    <div key={n.id} className="block px-4 py-2 text-sm text-dark text-right whitespace-normal border-b border-gray-100 last:border-b-0">
                      <p className="font-semibold">{n.subject_ar || 'إشعار'}</p>
                      <p className="text-xs text-gray-600">{n.content_ar || 'محتوى الإشعار'}</p>
                    </div>
                  ))
                )}
              </motion.div>
            )}
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-gray-500/10">
                <div className="space-y-2 py-6">
                  {user?.is_instructor ? (
                    <>
                      <Link
                        to="/instructor-dashboard"
                        className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        لوحة تحكم المدرس
                      </Link>
                      <Link
                        to="/instructor-profile"
                        className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        بروفايل المدرس
                      </Link>
                    </>
                  ) : (
                    <Link
                      to="/student-profile"
                      className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      الملف الدراسي
                    </Link>
                  )}
                  <Link
                    to="/library"
                    className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    مكتبة القوانين
                  </Link>
                  <Link
                    to="/jobs"
                    className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    بوابة التوظيف
                  </Link>
                  <Link
                    to="/forum"
                    className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    المنتدى
                  </Link>
                  <Link
                    to="/about"
                    className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    عن المنصة
                  </Link>
                </div>
                <div className="py-6">
                  {isAuthenticated ? (
                    <div className="space-y-2">
                      <div className="px-3 py-2 text-sm text-gray-500">
                        مرحباً، {user?.first_name || user?.email}
                      </div>
                      <Link
                        to="/student-profile"
                        className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        الملف الشخصي
                      </Link>
                      <Link
                        to="/settings"
                        className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        الإعدادات
                      </Link>
                      <button
                        onClick={() => {
                          handleLogout();
                          setMobileMenuOpen(false);
                        }}
                        className="-mx-3 block w-full text-right rounded-lg px-3 py-2 text-base font-bold leading-7 text-red-600 hover:bg-red-50 hover:text-red-800 transition-colors"
                      >
                        تسجيل الخروج
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Link
                        to="/login"
                        className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        تسجيل الدخول
                      </Link>
                      <Link
                        to="/register"
                        className="btn-primary block text-center"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        إنشاء حساب
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}

// تصدير Header مع Error Boundary
export default function Header(props) {
  return (
    <ErrorBoundary>
      <HeaderComponent {...props} />
    </ErrorBoundary>
  );
}