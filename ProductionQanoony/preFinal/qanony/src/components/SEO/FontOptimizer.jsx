import { useEffect } from 'react';

/**
 * Font optimization utilities for better performance and SEO
 */

/**
 * Preload critical fonts
 */
export const preloadFonts = () => {
  const fonts = [
    {
      href: 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap',
      family: 'Cairo'
    },
    {
      href: 'https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap',
      family: 'Tajawal'
    }
  ];

  fonts.forEach(font => {
    // Preconnect to font domains
    const preconnectLink = document.createElement('link');
    preconnectLink.rel = 'preconnect';
    preconnectLink.href = 'https://fonts.googleapis.com';
    preconnectLink.crossOrigin = 'anonymous';
    document.head.appendChild(preconnectLink);

    const preconnectGstaticLink = document.createElement('link');
    preconnectGstaticLink.rel = 'preconnect';
    preconnectGstaticLink.href = 'https://fonts.gstatic.com';
    preconnectGstaticLink.crossOrigin = 'anonymous';
    document.head.appendChild(preconnectGstaticLink);

    // Preload font CSS
    const preloadLink = document.createElement('link');
    preloadLink.rel = 'preload';
    preloadLink.as = 'style';
    preloadLink.href = font.href;
    document.head.appendChild(preloadLink);

    // Load font CSS
    const fontLink = document.createElement('link');
    fontLink.rel = 'stylesheet';
    fontLink.href = font.href;
    fontLink.media = 'print';
    fontLink.onload = function() {
      this.media = 'all';
    };
    document.head.appendChild(fontLink);
  });
};

/**
 * Font loading optimization with font-display: swap
 */
export const optimizeFontDisplay = () => {
  const style = document.createElement('style');
  style.textContent = `
    /* Optimize font loading with font-display: swap */
    @font-face {
      font-family: 'Cairo';
      font-display: swap;
      font-weight: 300 700;
      src: url('https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8.woff2') format('woff2');
      unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
    }
    
    @font-face {
      font-family: 'Tajawal';
      font-display: swap;
      font-weight: 300 700;
      src: url('https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLrZjiLlJ-g.woff2') format('woff2');
      unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
    }

    /* Fallback fonts for better performance */
    .font-cairo {
      font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .font-tajawal {
      font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    /* Prevent invisible text during font swap period */
    .font-loading {
      font-display: swap;
    }

    /* Critical text styling to prevent layout shift */
    h1, h2, h3, h4, h5, h6 {
      font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-display: swap;
    }

    body, p, span, div {
      font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-display: swap;
    }
  `;
  document.head.appendChild(style);
};

/**
 * Font loading detection and optimization
 */
export const detectFontLoading = () => {
  if ('fonts' in document) {
    // Use Font Loading API if available
    const fonts = [
      new FontFace('Cairo', 'url(https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8.woff2)', {
        weight: '300 700',
        display: 'swap'
      }),
      new FontFace('Tajawal', 'url(https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLrZjiLlJ-g.woff2)', {
        weight: '300 700',
        display: 'swap'
      })
    ];

    fonts.forEach(font => {
      font.load().then(loadedFont => {
        document.fonts.add(loadedFont);
        console.log(`Font ${loadedFont.family} loaded successfully`);
      }).catch(error => {
        console.warn(`Failed to load font: ${error}`);
      });
    });

    // Monitor font loading status
    document.fonts.ready.then(() => {
      console.log('All fonts loaded');
      document.body.classList.add('fonts-loaded');
    });
  }
};

/**
 * Reduce font loading impact on performance
 */
export const reduceFontLoadingImpact = () => {
  // Add CSS to prevent layout shift during font loading
  const style = document.createElement('style');
  style.textContent = `
    /* Prevent layout shift during font loading */
    .font-loading-fallback {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 16px;
      line-height: 1.5;
    }

    /* Smooth transition when fonts load */
    .fonts-loaded * {
      transition: font-family 0.1s ease-in-out;
    }

    /* Optimize text rendering */
    body {
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Prevent flash of unstyled text */
    .font-display-swap {
      font-display: swap;
    }
  `;
  document.head.appendChild(style);
};

/**
 * Font subset optimization for Arabic content
 */
export const optimizeArabicFonts = () => {
  const style = document.createElement('style');
  style.textContent = `
    /* Optimized Arabic font loading */
    @font-face {
      font-family: 'Cairo-Arabic';
      font-display: swap;
      src: url('https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8.woff2') format('woff2');
      unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
    }

    @font-face {
      font-family: 'Cairo-Latin';
      font-display: swap;
      src: url('https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIkTp2mxdt0.woff2') format('woff2');
      unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
    }

    /* Use appropriate font subset based on content */
    .arabic-text {
      font-family: 'Cairo-Arabic', 'Cairo', sans-serif;
    }

    .latin-text {
      font-family: 'Cairo-Latin', 'Cairo', sans-serif;
    }
  `;
  document.head.appendChild(style);
};

/**
 * React hook for font optimization
 */
export const useFontOptimization = () => {
  useEffect(() => {
    // Initialize font optimizations
    optimizeFontDisplay();
    reduceFontLoadingImpact();
    optimizeArabicFonts();
    detectFontLoading();

    // Preload fonts after initial render
    const timer = setTimeout(() => {
      preloadFonts();
    }, 100);

    return () => clearTimeout(timer);
  }, []);
};

/**
 * Font Optimizer Component
 */
const FontOptimizer = ({ children }) => {
  useFontOptimization();
  return children;
};

export default FontOptimizer;
