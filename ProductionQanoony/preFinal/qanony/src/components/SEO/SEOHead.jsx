import React from 'react';
import { Helmet } from 'react-helmet-async';

const SEOHead = ({
  title = "منصة قانوني التعليمية - أفضل منصة تعليمية لطلاب كلية الحقوق في مصر",
  description = "منصة قانوني التعليمية الرائدة في مصر لطلاب كلية الحقوق. محاضرات تفاعلية، اختبارات، مكتبة قانونية شاملة، وفرص توظيف متميزة. انضم لأكثر من 5000 طالب واحصل على تعليم قانوني متميز.",
  keywords = "قانوني، كلية الحقوق، تعليم قانوني، محاضرات قانونية، اختبارات قانونية، مكتبة قانونية، توظيف محامين، دراسة القانون، طلاب الحقوق، منصة تعليمية، التعليم الإلكتروني، القانون المصري، المحاماة، الاستشارات القانونية",
  image = "https://qanony.com/og-image.jpg",
  url = "https://qanony.com/",
  type = "website",
  author = "منصة قانوني التعليمية",
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  noIndex = false,
  noFollow = false,
  canonical
}) => {
  const robotsContent = `${noIndex ? 'noindex' : 'index'}, ${noFollow ? 'nofollow' : 'follow'}`;
  const fullTitle = title.includes('قانوني') ? title : `${title} - منصة قانوني التعليمية`;
  const keywordsString = Array.isArray(keywords) ? keywords.join(', ') : keywords;
  const tagsString = Array.isArray(tags) ? tags.join(', ') : tags;

  return (
    <Helmet>
      {/* Primary Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="title" content={fullTitle} />
      <meta name="description" content={description} />
      <meta name="keywords" content={keywordsString} />
      <meta name="author" content={author} />
      <meta name="robots" content={robotsContent} />
      <meta name="language" content="Arabic" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      {!canonical && <link rel="canonical" href={url} />}
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={fullTitle} />
      <meta property="og:site_name" content="منصة قانوني التعليمية" />
      <meta property="og:locale" content="ar_EG" />
      
      {/* Article specific meta tags */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && section && (
        <meta property="article:section" content={section} />
      )}
      {type === 'article' && tagsString && (
        <meta property="article:tag" content={tagsString} />
      )}
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={url} />
      <meta property="twitter:title" content={fullTitle} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={image} />
      <meta property="twitter:image:alt" content={fullTitle} />
      
      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#1e40af" />
      <meta name="msapplication-TileColor" content="#1e40af" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="قانوني" />
      
      {/* Structured Data for Educational Content */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": fullTitle,
          "description": description,
          "url": url,
          "inLanguage": "ar",
          "isPartOf": {
            "@type": "WebSite",
            "name": "منصة قانوني التعليمية",
            "url": "https://qanony.com"
          },
          "about": {
            "@type": "Thing",
            "name": "تعليم القانون",
            "description": "تعليم القانون لطلاب كلية الحقوق"
          },
          "keywords": keywordsString,
          "author": {
            "@type": "Organization",
            "name": author
          },
          "publisher": {
            "@type": "Organization",
            "name": "منصة قانوني التعليمية",
            "logo": {
              "@type": "ImageObject",
              "url": "https://qanony.com/logo.png"
            }
          }
        })}
      </script>
    </Helmet>
  );
};

export default SEOHead;
