import React, { useState, useRef, useEffect } from 'react';

/**
 * Optimized Image component with lazy loading and performance features
 */
const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  loading = 'lazy',
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+',
  priority = false,
  sizes,
  srcSet,
  onLoad,
  onError,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(priority ? src : placeholder);
  const imgRef = useRef(null);

  useEffect(() => {
    if (!priority && 'IntersectionObserver' in window) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              setCurrentSrc(src);
              observer.unobserve(entry.target);
            }
          });
        },
        {
          rootMargin: '50px',
        }
      );

      if (imgRef.current) {
        observer.observe(imgRef.current);
      }

      return () => {
        if (imgRef.current) {
          observer.unobserve(imgRef.current);
        }
      };
    } else if (!priority) {
      // Fallback for browsers without IntersectionObserver
      setCurrentSrc(src);
    }
  }, [src, priority]);

  const handleLoad = (e) => {
    setIsLoaded(true);
    if (onLoad) onLoad(e);
  };

  const handleError = (e) => {
    setHasError(true);
    if (onError) onError(e);
  };

  const imageClasses = `
    ${className}
    ${isLoaded ? 'opacity-100' : 'opacity-0'}
    transition-opacity duration-300
    ${hasError ? 'bg-gray-200' : ''}
  `.trim();

  return (
    <div 
      className="relative overflow-hidden"
      style={{ 
        width: width || 'auto', 
        height: height || 'auto',
        aspectRatio: width && height ? `${width}/${height}` : undefined
      }}
    >
      <img
        ref={imgRef}
        src={currentSrc}
        alt={alt}
        width={width}
        height={height}
        loading={priority ? 'eager' : loading}
        className={imageClasses}
        sizes={sizes}
        srcSet={srcSet}
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
      
      {!isLoaded && !hasError && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center"
          aria-hidden="true"
        >
          <svg 
            className="w-8 h-8 text-gray-400" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path 
              fillRule="evenodd" 
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" 
              clipRule="evenodd" 
            />
          </svg>
        </div>
      )}
      
      {hasError && (
        <div 
          className="absolute inset-0 bg-gray-200 flex items-center justify-center"
          aria-hidden="true"
        >
          <span className="text-gray-500 text-sm">فشل تحميل الصورة</span>
        </div>
      )}
    </div>
  );
};

/**
 * Hero Image component with optimized loading
 */
export const HeroImage = ({ src, alt, className = '', ...props }) => {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={`w-full h-full object-cover ${className}`}
      priority={true}
      loading="eager"
      sizes="100vw"
      {...props}
    />
  );
};

/**
 * Card Image component with lazy loading
 */
export const CardImage = ({ src, alt, className = '', ...props }) => {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={`w-full h-48 object-cover ${className}`}
      loading="lazy"
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      {...props}
    />
  );
};

/**
 * Avatar Image component
 */
export const AvatarImage = ({ src, alt, size = 'md', className = '', ...props }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={`${sizeClasses[size]} rounded-full object-cover ${className}`}
      loading="lazy"
      width={size === 'sm' ? 32 : size === 'md' ? 48 : size === 'lg' ? 64 : 96}
      height={size === 'sm' ? 32 : size === 'md' ? 48 : size === 'lg' ? 64 : 96}
      {...props}
    />
  );
};

/**
 * Responsive Image component with multiple breakpoints
 */
export const ResponsiveImage = ({ 
  src, 
  alt, 
  breakpoints = {
    mobile: { width: 400, quality: 75 },
    tablet: { width: 768, quality: 80 },
    desktop: { width: 1200, quality: 85 }
  },
  className = '',
  ...props 
}) => {
  // Generate srcSet based on breakpoints
  const generateSrcSet = () => {
    return Object.entries(breakpoints)
      .map(([key, { width, quality }]) => {
        // In a real implementation, you'd use an image optimization service
        // For now, we'll use the original src
        return `${src} ${width}w`;
      })
      .join(', ');
  };

  const generateSizes = () => {
    return Object.entries(breakpoints)
      .map(([key, { width }], index, array) => {
        if (index === array.length - 1) {
          return `${width}px`;
        }
        return `(max-width: ${width}px) ${width}px`;
      })
      .join(', ');
  };

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={className}
      srcSet={generateSrcSet()}
      sizes={generateSizes()}
      {...props}
    />
  );
};

/**
 * Background Image component with optimization
 */
export const BackgroundImage = ({ 
  src, 
  alt, 
  children, 
  className = '',
  overlay = false,
  overlayOpacity = 0.5,
  ...props 
}) => {
  return (
    <div className={`relative ${className}`} {...props}>
      <OptimizedImage
        src={src}
        alt={alt}
        className="absolute inset-0 w-full h-full object-cover"
        priority={true}
      />
      
      {overlay && (
        <div 
          className="absolute inset-0 bg-black"
          style={{ opacity: overlayOpacity }}
          aria-hidden="true"
        />
      )}
      
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default OptimizedImage;
