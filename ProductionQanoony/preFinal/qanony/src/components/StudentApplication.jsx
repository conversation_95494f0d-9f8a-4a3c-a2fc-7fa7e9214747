import React, { useEffect, useState, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import './StudentApplication.css';

const StudentApplication = () => {
  const { loading } = useContext(AuthContext);

  const [appData, setAppData] = useState(null); // null -> not submitted
  const [statusLoading, setStatusLoading] = useState(true);
  const [file, setFile] = useState(null);
  const [error, setError] = useState(null);
  const [successMsg, setSuccessMsg] = useState(null);

  const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

  const fetchStatus = async () => {
    setStatusLoading(true);
    setError(null);
    console.log('🔍 Fetching student application status...');
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/auth/student-application/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      console.log('✅ Status response:', data);
      setAppData(data);
    } catch (e) {
      console.error('❌ Error fetching status:', e);
      console.error('❌ Error response:', e.response?.data);
      setError('فشل تحميل الحالة');
    } finally {
      setStatusLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) return;
    setError(null);
    setSuccessMsg(null);

    console.log('🚀 Starting student application submission...');
    console.log('📁 File:', file);
    console.log('🔑 Token:', localStorage.getItem('access') ? 'Present' : 'Missing');

    const form = new FormData();
    form.append('payment_screenshot', file);

    try {
      console.log('📤 Sending request to:', `${API_BASE_URL}/api/auth/student-application/`);
      const { data } = await axios.post(
        `${API_BASE_URL}/api/auth/student-application/`,
        form,
        {
          headers: { 'Content-Type': 'multipart/form-data', 'Authorization': `Bearer ${localStorage.getItem('access')}` },
        }
      );
      console.log('✅ Success response:', data);
      setAppData(data);
      setSuccessMsg('تم إرسال الطلب بنجاح');
    } catch (err) {
      console.error('❌ Error submitting application:', err);
      console.error('❌ Error response:', err.response?.data);
      console.error('❌ Error status:', err.response?.status);
      const data = err.response?.data;
      setError(data?.non_field_errors?.[0] || data?.detail || 'تعذر الإرسال');
    }
  };

  if (statusLoading) return <p style={{ textAlign: 'center' }}>جاري التحميل ...</p>;

  return (
    <div className="app-container">
      <h2 className="title">طلب التحاق طالب</h2>

      {appData ? (
        <div className="status-card">
          <p>
            حالة الطلب:{' '}
            {appData.is_approved ? (
              <span className="approved">مقبول</span>
            ) : appData.rejection_reason ? (
              <span className="rejected">مرفوض</span>
            ) : (
              <span className="pending">قيد المراجعة</span>
            )}
          </p>
          {appData.rejection_reason && <p>السبب: {appData.rejection_reason}</p>}
        </div>
      ) : (
        <form className="upload-form" onSubmit={handleSubmit}>
          <label htmlFor="file">صورة إثبات الدفع</label>
          <input
            id="file"
            type="file"
            accept="image/*"
            onChange={(e) => setFile(e.target.files[0])}
          />
          {error && <p className="error">{error}</p>}
          {successMsg && <p className="success">{successMsg}</p>}
          <button type="submit" disabled={!file || loading}>
            {loading ? 'جارٍ الإرسال...' : 'إرسال الطلب'}
          </button>
        </form>
      )}
    </div>
  );
};

export default StudentApplication; 