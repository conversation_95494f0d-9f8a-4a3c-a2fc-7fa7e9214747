import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Header from '../shared/Header';
import Footer from '../shared/Footer';
import { AnimatePresence, motion } from 'framer-motion';
import { useContext } from 'react';
import { AuthContext } from '../../context/AuthContext';

const Layout = (props) => {
  const location = useLocation();
  const { user } = useContext(AuthContext);

  return (
    <div className="app-wrapper">
      <Header />
      <main className="relative z-10" style={{ minHeight: 'calc(100vh - 120px)' }}>
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -15 }}
            transition={{ duration: 0.25 }}
          >
            {props.children}
            <Outlet />
          </motion.div>
        </AnimatePresence>
      </main>
      <Footer />
    </div>
  );
};

export default Layout; 