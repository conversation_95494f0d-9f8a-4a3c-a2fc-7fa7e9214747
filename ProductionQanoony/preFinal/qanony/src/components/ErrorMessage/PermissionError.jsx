import React from 'react';
import { AlertTriangle, Mail, CreditCard, User } from 'lucide-react';

const PermissionError = ({ error, type = 'general' }) => {
  const getErrorContent = () => {
    if (error?.response?.status === 403) {
      return {
        icon: <AlertTriangle className="w-8 h-8 text-yellow-500" />,
        title: 'غير مصرح بالوصول',
        message: 'يجب توثيق البريد الإلكتروني أو وجود اشتراك نشط للوصول إلى هذا المحتوى',
        suggestions: [
          {
            icon: <Mail className="w-5 h-5 text-blue-500" />,
            text: 'تحقق من توثيق البريد الإلكتروني',
            action: 'verify-email'
          },
          {
            icon: <CreditCard className="w-5 h-5 text-green-500" />,
            text: 'تحقق من حالة الاشتراك',
            action: 'check-subscription'
          },
          {
            icon: <User className="w-5 h-5 text-purple-500" />,
            text: 'تواصل مع الدعم الفني',
            action: 'contact-support'
          }
        ]
      };
    }

    if (error?.response?.status === 404) {
      return {
        icon: <AlertTriangle className="w-8 h-8 text-orange-500" />,
        title: 'المحتوى غير موجود',
        message: 'لم يتم العثور على المحتوى المطلوب',
        suggestions: [
          {
            icon: <User className="w-5 h-5 text-blue-500" />,
            text: 'تحقق من صحة الرابط',
            action: 'check-url'
          }
        ]
      };
    }

    return {
      icon: <AlertTriangle className="w-8 h-8 text-red-500" />,
      title: 'حدث خطأ',
      message: error?.message || 'حدث خطأ غير متوقع',
      suggestions: [
        {
          icon: <User className="w-5 h-5 text-blue-500" />,
          text: 'حاول مرة أخرى',
          action: 'retry'
        }
      ]
    };
  };

  const { icon, title, message, suggestions } = getErrorContent();

  const handleAction = (action) => {
    switch (action) {
      case 'verify-email':
        // Navigate to email verification
        window.location.href = '/verify-email';
        break;
      case 'check-subscription':
        // Navigate to subscription page
        window.location.href = '/settings';
        break;
      case 'contact-support':
        // Open support contact
        window.location.href = '/contact';
        break;
      case 'retry':
        // Reload the page
        window.location.reload();
        break;
      default:
        break;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200">
      <div className="mb-4">
        {icon}
      </div>
      
      <h3 className="text-xl font-semibold text-gray-800 mb-2 text-center">
        {title}
      </h3>
      
      <p className="text-gray-600 text-center mb-6 max-w-md">
        {message}
      </p>
      
      {suggestions && suggestions.length > 0 && (
        <div className="space-y-3 w-full max-w-sm">
          <p className="text-sm font-medium text-gray-700 text-center mb-3">
            الحلول المقترحة:
          </p>
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => handleAction(suggestion.action)}
              className="w-full flex items-center justify-center gap-3 p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors duration-200"
            >
              {suggestion.icon}
              <span className="text-gray-700">{suggestion.text}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default PermissionError;
