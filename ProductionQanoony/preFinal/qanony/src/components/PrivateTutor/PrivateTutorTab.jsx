import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './PrivateTutorTab.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const formatDate = (dateString) => {
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  return new Date(dateString).toLocaleDateString('ar-EG', options);
};

const PrivateTutorTab = () => {
  const [sessions, setSessions] = useState([]);
  const [instructors, setInstructors] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showBookingForm, setShowBookingForm] = useState(false);
  const [formData, setFormData] = useState({
    instructor: '',
    subject: '',
    scheduled_at: '',
    duration_minutes: 60,
    student_notes: ''
  });

  // Move fetch functions before useEffect
  const fetchSessions = async () => {
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/courses/private-tutor/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      console.log('Sessions API response:', data);

      // تأكد من أن البيانات array
      if (Array.isArray(data)) {
        setSessions(data);
      } else if (data && Array.isArray(data.results)) {
        setSessions(data.results);
      } else if (data && typeof data === 'object') {
        // إذا كان object، حول إلى array
        setSessions([]);
        console.warn('Sessions API returned object instead of array:', data);
      } else {
        setSessions([]);
      }
    } catch (err) {
      console.error('Error fetching sessions:', err);
      setError('حدث خطأ في تحميل الجلسات');
      setSessions([]); // تأكد من أن sessions هو array حتى في حالة الخطأ
    }
  };

  const fetchInstructors = async () => {
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/auth/instructors/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      console.log('Instructors API response:', data);

      // تأكد من أن البيانات array
      if (Array.isArray(data)) {
        setInstructors(data);
      } else if (data && Array.isArray(data.results)) {
        setInstructors(data.results);
      } else {
        setInstructors([]);
        console.warn('Instructors API returned unexpected format:', data);
      }
    } catch (err) {
      console.error('Error fetching instructors:', err);
      setError('حدث خطأ في تحميل قائمة المدرسين');
      setInstructors([]);
    }
  };

  const fetchSubjects = async () => {
    try {
      const { data } = await axios.get(`${API_BASE_URL}/api/courses/subjects/`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      setSubjects(Array.isArray(data) ? data : []);
    } catch (err) {
      console.error('Error fetching subjects:', err);
      if (err.response?.status === 403) {
        setError('يجب توثيق البريد الإلكتروني أو وجود اشتراك نشط للوصول إلى المواد');
      } else {
        setError('حدث خطأ في تحميل قائمة المواد');
      }
      setSubjects([]);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);

      try {
        await Promise.all([
          fetchSessions(),
          fetchInstructors(),
          fetchSubjects()
        ]);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('حدث خطأ في تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []); // Empty dependency array is fine now



  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await axios.post(`${API_BASE_URL}/api/courses/private-tutor/`, formData, { headers: { 'Authorization': `Bearer ${localStorage.getItem('access')}` } });
      setShowBookingForm(false);
      fetchSessions();
      setFormData({
        instructor: '',
        subject: '',
        scheduled_at: '',
        duration_minutes: 60,
        student_notes: ''
      });
    } catch (err) {
      setError('حدث خطأ في حجز الجلسة');
    } finally {
      setLoading(false);
    }
  };

  const getStatusClass = (status) => {
    const statusClasses = {
      pending: 'status-pending',
      approved: 'status-approved',
      rejected: 'status-rejected',
      completed: 'status-completed',
      cancelled: 'status-cancelled'
    };
    return statusClasses[status] || 'status-default';
  };

  const getStatusText = (status) => {
    const statusTexts = {
      pending: 'قيد الانتظار',
      approved: 'تمت الموافقة',
      rejected: 'مرفوض',
      completed: 'مكتمل',
      cancelled: 'ملغي'
    };
    return statusTexts[status] || status;
  };

  if (loading) return (
    <div className="loading-message">
      <div>جاري التحميل...</div>
      <div style={{ fontSize: '14px', color: '#666', marginTop: '10px' }}>
        يتم تحميل الجلسات والمدرسين والمواد...
      </div>
    </div>
  );

  if (error) return (
    <div className="error-message">
      <div>{error}</div>
      <button
        onClick={() => window.location.reload()}
        style={{
          marginTop: '10px',
          padding: '8px 16px',
          backgroundColor: '#facc15',
          color: '#78350f',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        إعادة تحميل الصفحة
      </button>
    </div>
  );

  // Debug info (commented out to reduce console spam)
  // console.log('PrivateTutorTab render:', {
  //   sessions: sessions,
  //   sessionsType: typeof sessions,
  //   sessionsIsArray: Array.isArray(sessions),
  //   sessionsLength: sessions?.length,
  //   instructors: instructors?.length,
  //   subjects: subjects?.length,
  //   loading,
  //   error
  // });

  return (
    <div className="private-tutor-container">
      <div className="header-section">
        <h2>مدرسك الخاص</h2>
        <button onClick={() => setShowBookingForm(true)} className="book-button">
          حجز جلسة جديدة
        </button>
      </div>

      {showBookingForm && (
        <div className="booking-form">
          <form onSubmit={handleSubmit}>
            <div className="form-field">
              <label>المدرس</label>
              <select
                name="instructor"
                value={formData.instructor}
                onChange={handleInputChange}
                required
              >
                <option value="">اختر المدرس</option>
                {instructors.map(instructor => (
                  <option key={instructor.id} value={instructor.id}>
                    {instructor.full_name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-field">
              <label>المادة</label>
              <select
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                required
              >
                <option value="">اختر المادة</option>
                {subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>
                    {subject.name_ar}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-field">
              <label>موعد الجلسة</label>
              <input
                type="datetime-local"
                name="scheduled_at"
                value={formData.scheduled_at}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-field">
              <label>مدة الجلسة (دقيقة)</label>
              <input
                type="number"
                name="duration_minutes"
                value={formData.duration_minutes}
                onChange={handleInputChange}
                min="30"
                max="180"
                required
              />
            </div>

            <div className="form-field">
              <label>ملاحظات</label>
              <textarea
                name="student_notes"
                value={formData.student_notes}
                onChange={handleInputChange}
                rows="3"
              />
            </div>

            <div className="form-actions">
              <button type="button" onClick={() => setShowBookingForm(false)} className="cancel-button">
                إلغاء
              </button>
              <button type="submit" className="submit-button">
                تأكيد الحجز
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="sessions-list">
        {!Array.isArray(sessions) ? (
          <div className="error-message">
            خطأ في تحميل البيانات - يرجى إعادة تحميل الصفحة
          </div>
        ) : sessions.length === 0 ? (
          <div className="empty-message">
            لا توجد جلسات مسجلة
          </div>
        ) : (
          sessions.map(session => (
            <div key={session.id} className="session-card">
              <div className="session-info">
                <h3>{session.subject_name}</h3>
                <p>المدرس: {session.instructor_name}</p>
                <p>الموعد: {formatDate(session.scheduled_at)}</p>
                <p>المدة: {session.duration_minutes} دقيقة</p>
                <span className={`status-badge ${getStatusClass(session.status)}`}>
                  {getStatusText(session.status)}
                </span>
              </div>

              {session.meeting_link && session.status === 'approved' && (
                <div className="session-link">
                  <a
                    href={session.meeting_link}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    رابط الجلسة
                  </a>
                </div>
              )}

              {session.instructor_notes && (
                <div className="instructor-notes">
                  <p>
                    <strong>ملاحظات المدرس:</strong> {session.instructor_notes}
                  </p>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default PrivateTutorTab; 