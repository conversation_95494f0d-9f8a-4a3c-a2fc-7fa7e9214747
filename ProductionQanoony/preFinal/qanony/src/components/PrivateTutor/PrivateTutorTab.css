.private-tutor-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-section h2 {
  font-size: 24px;
  color: #333;
}

.book-button,
.submit-button {
  background-color: #facc15;
  color: #78350f;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  font-weight: 700;
}

.book-button:hover,
.submit-button:hover {
  background-color: #fde047;
  color: #a16207;
}

.booking-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.form-field {
  margin-bottom: 15px;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  color: #333;
}

.form-field input,
.form-field select,
.form-field textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-field textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  color: #DC2626;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cancel-button:hover {
  background-color: #FEE2E2;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.session-card {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.session-info h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.session-info p {
  margin: 5px 0;
  color: #666;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  margin-top: 10px;
}

.status-pending {
  background-color: #FEF3C7;
  color: #92400E;
}

.status-approved {
  background-color: #D1FAE5;
  color: #065F46;
}

.status-rejected {
  background-color: #FEE2E2;
  color: #991B1B;
}

.status-completed {
  background-color: #DBEAFE;
  color: #1E40AF;
}

.status-cancelled {
  background-color: #F3F4F6;
  color: #1F2937;
}

.session-link {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.session-link a {
  color: #facc15;
  text-decoration: none;
}

.session-link a:hover {
  text-decoration: underline;
}

.instructor-notes {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.instructor-notes p {
  color: #666;
}

.loading-message {
  text-align: center;
  padding: 20px;
  color: #666;
}

.error-message {
  text-align: center;
  padding: 20px;
  color: #DC2626;
}

.empty-message {
  text-align: center;
  padding: 20px;
  color: #666;
  background-color: #F9FAFB;
  border-radius: 8px;
}

/* Custom styles for datetime-local input */
input[type="datetime-local"] {
  direction: ltr;
  text-align: right;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .private-tutor-container {
    padding: 15px;
  }
  
  .header-section {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
  }
}

.forum-button,
.forum-header,
.forum-title,
.forum-category,
.forum-empty-icon,
.forum-empty-title {
  background-color: #facc15 !important;
  color: #78350f !important;
  border: none;
  border-radius: 12px;
  font-weight: 700;
  transition: background-color 0.2s, color 0.2s;
}

.forum-button:hover {
  background-color: #fde047 !important;
  color: #a16207 !important;
}

.forum-empty-title {
  color: #a16207 !important;
  background: none !important;
} 