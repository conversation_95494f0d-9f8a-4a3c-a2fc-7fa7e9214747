import React from 'react';
import { useToast } from './UI/Toast';
import { runAllTests } from '../test/errorHandlerTest';

/**
 * مكون لاختبار Error Handler
 * يمكن إضافته مؤقتاً لأي صفحة للاختبار
 */
const TestErrorHandler = () => {
  const { showSuccess, showError, showWarning, showInfo } = useToast();

  const testToastTypes = () => {
    showSuccess('تم حفظ البيانات بنجاح!');
    
    setTimeout(() => {
      showError('حدث خطأ في الاتصال بالخادم', {
        canRetry: true,
        onRetry: () => {
          showInfo('جاري إعادة المحاولة...');
        }
      });
    }, 1000);

    setTimeout(() => {
      showWarning('تحذير: انتهت صلاحية الجلسة قريباً');
    }, 2000);

    setTimeout(() => {
      showInfo('تم تحديث البيانات');
    }, 3000);
  };

  const testNetworkError = () => {
    // محاكاة خطأ شبكة
    const networkError = {
      message: 'Network Error',
      code: 'NETWORK_ERROR'
    };
    
    showError('فشل الاتصال بالإنترنت. يرجى التحقق من اتصالك.', {
      canRetry: true,
      onRetry: () => {
        showSuccess('تم استعادة الاتصال!');
      }
    });
  };

  const testValidationError = () => {
    // محاكاة خطأ تحقق من البيانات
    showError('البيانات المدخلة غير صحيحة:', {
      title: 'خطأ في النموذج',
      details: [
        'البريد الإلكتروني مطلوب',
        'كلمة المرور قصيرة جداً',
        'رقم الهاتف غير صحيح'
      ]
    });
  };

  const testPermissionError = () => {
    showError('ليس لديك الصلاحية للوصول إلى هذا المورد', {
      title: 'وصول مرفوض',
      duration: 7000
    });
  };

  const runConsoleTests = () => {
    console.log('🧪 تشغيل اختبارات Error Handler في الكونسول...');
    runAllTests();
    showInfo('تم تشغيل الاختبارات. تحقق من الكونسول للنتائج.');
  };

  return (
    <div className="p-6 bg-gray-50 rounded-lg border border-gray-200">
      <h3 className="text-lg font-bold text-gray-800 mb-4">
        🧪 اختبار Error Handler
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <button
          onClick={testToastTypes}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
        >
          اختبار أنواع الإشعارات
        </button>

        <button
          onClick={testNetworkError}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
        >
          اختبار خطأ الشبكة
        </button>

        <button
          onClick={testValidationError}
          className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 transition-colors"
        >
          اختبار خطأ التحقق
        </button>

        <button
          onClick={testPermissionError}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors"
        >
          اختبار خطأ الصلاحيات
        </button>

        <button
          onClick={runConsoleTests}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors md:col-span-2"
        >
          تشغيل اختبارات الكونسول
        </button>
      </div>

      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
        <h4 className="font-semibold text-blue-800 mb-2">تعليمات الاختبار:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• اضغط على الأزرار لاختبار أنواع مختلفة من الأخطاء</li>
          <li>• راقب الإشعارات التي تظهر في أعلى الشاشة</li>
          <li>• تحقق من الكونسول للاختبارات المفصلة</li>
          <li>• جرب إعادة المحاولة عند ظهور الخيار</li>
        </ul>
      </div>

      <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h4 className="font-semibold text-yellow-800 mb-2">ملاحظة:</h4>
        <p className="text-sm text-yellow-700">
          هذا المكون للاختبار فقط. يجب إزالته من الإنتاج النهائي.
        </p>
      </div>
    </div>
  );
};

export default TestErrorHandler;
