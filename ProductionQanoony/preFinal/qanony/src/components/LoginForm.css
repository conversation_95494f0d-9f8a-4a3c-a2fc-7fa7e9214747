.login-form {
  max-width: 400px;
  margin: 60px auto;
  background: #fff;
  padding: 30px 35px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  direction: rtl;
}

.title {
  margin-bottom: 25px;
  text-align: center;
  color: var(--text);
}

.input-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

label {
  margin-bottom: 6px;
  font-weight: 600;
  color: var(--secondary-text);
}

input {
  padding: 10px 14px;
  border: 1px solid var(--secondary-bg);
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
}

input:focus {
  outline: 2px solid var(--primary);
  border-color: var(--primary);
}

.error {
  color: #d9534f;
  font-size: 11px;
  margin-top: 2px;
  font-weight: 400;
  opacity: 0.85;
}

.server-error {
  color: red;
  text-align: center;
  margin-bottom: 12px;
}

.submit-btn {
  width: 100%;
  padding: 12px;
  background: var(--primary);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.submit-btn:hover {
  background: #eab308;
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.login-success {
  text-align: center;
  padding: 40px 20px;
  color: var(--primary);
}

.switch-link {
  text-align: center;
  margin-top: 18px;
  font-size: 14px;
}

.switch-link a {
  color: var(--primary);
  text-decoration: none;
}

.switch-link a:hover {
  text-decoration: underline;
} 