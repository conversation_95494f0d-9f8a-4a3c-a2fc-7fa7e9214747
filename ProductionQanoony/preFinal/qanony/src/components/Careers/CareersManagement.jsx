import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { ensureArray, processApiResponse, handleApiError } from '../../utils/arrayHelpers';
import {
  FaBriefcase,
  FaBuilding,
  FaUsers,
  FaChartLine,
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaCheck,
  FaTimes,
  FaClock,
  FaMapMarkerAlt,
  FaMoneyBillWave,
  FaCalendarAlt
} from 'react-icons/fa';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

const CareersManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Categories state
  const [categories, setCategories] = useState([]);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [categoryForm, setCategoryForm] = useState({
    name_ar: '',
    name_en: '',
    description_ar: '',
    is_active: true
  });

  // Jobs state
  const [jobs, setJobs] = useState([]);
  const [showJobModal, setShowJobModal] = useState(false);
  const [editingJob, setEditingJob] = useState(null);
  const [jobForm, setJobForm] = useState({
    title_ar: '',
    company_name: '',
    category_id: '',
    job_type: 'full_time',
    location: '',
    description_ar: '',
    requirements_ar: '',
    salary_range: '',
    application_deadline: '',
    contact_email: '',
    contact_phone: '',
    is_active: true
  });

  // Applications state
  const [applications, setApplications] = useState([]);
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState(null);

  // Stats state
  const [stats, setStats] = useState({
    total_categories: 0,
    active_categories: 0,
    total_jobs: 0,
    active_jobs: 0,
    total_applications: 0,
    pending_applications: 0,
    accepted_applications: 0,
    rejected_applications: 0,
    recent_jobs: [],
    recent_applications: []
  });

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');

  const tabs = [
    { key: 'overview', label: 'نظرة عامة', icon: <FaChartLine /> },
    { key: 'categories', label: 'فئات الوظائف', icon: <FaBuilding /> },
    { key: 'jobs', label: 'الوظائف', icon: <FaBriefcase /> },
    { key: 'applications', label: 'طلبات التوظيف', icon: <FaUsers /> }
  ];

  const jobTypes = [
    { value: 'internship', label: 'تدريب' },
    { value: 'training', label: 'تدريب عملي' },
    { value: 'full_time', label: 'دوام كامل' }
  ];

  const applicationStatuses = [
    { value: 'applied', label: 'تم التقديم', color: 'blue' },
    { value: 'reviewed', label: 'تمت المراجعة', color: 'yellow' },
    { value: 'accepted', label: 'تم القبول', color: 'green' },
    { value: 'rejected', label: 'تم الرفض', color: 'red' }
  ];

  useEffect(() => {
    fetchData();
  }, []);

  // Show message function
  const showMessage = (message, isError = false) => {
    if (isError) {
      setError(message);
      setSuccessMessage(null);
    } else {
      setSuccessMessage(message);
      setError(null);
    }
    // Clear message after 5 seconds
    setTimeout(() => {
      setError(null);
      setSuccessMessage(null);
    }, 5000);
  };

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      // Fetch categories
      const categoriesRes = await axios.get(`${API_BASE_URL}/api/careers/categories/`, { headers });
      const categoriesData = processApiResponse(categoriesRes.data, 'Categories');
      setCategories(categoriesData);

      // Fetch jobs
      const jobsRes = await axios.get(`${API_BASE_URL}/api/careers/jobs/`, { headers });
      const jobsData = processApiResponse(jobsRes.data, 'Jobs');
      setJobs(jobsData);

      // Fetch applications
      let applicationsData = [];
      try {
        const applicationsRes = await axios.get(`${API_BASE_URL}/api/careers/applications/`, { headers });
        applicationsData = processApiResponse(applicationsRes.data, 'Applications');
        setApplications(applicationsData);
      } catch (err) {
        console.log('Applications not available:', err);
        setApplications([]);
      }

      // Calculate stats using processed arrays
      const totalCategories = categoriesData.length;
      const activeCategories = categoriesData.filter(c => c.is_active).length;
      const totalJobs = jobsData.length;
      const activeJobs = jobsData.filter(j => j.is_active).length;
      const totalApplications = applicationsData.length;
      const pendingApplications = applicationsData.filter(a => a.status === 'applied').length;
      const acceptedApplications = applicationsData.filter(a => a.status === 'accepted').length;
      const rejectedApplications = applicationsData.filter(a => a.status === 'rejected').length;

      setStats({
        total_categories: totalCategories,
        active_categories: activeCategories,
        total_jobs: totalJobs,
        active_jobs: activeJobs,
        total_applications: totalApplications,
        pending_applications: pendingApplications,
        accepted_applications: acceptedApplications,
        rejected_applications: rejectedApplications,
        recent_jobs: jobsData
          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
          .slice(0, 5),
        recent_applications: applicationsData
          .sort((a, b) => new Date(b.applied_at) - new Date(a.applied_at))
          .slice(0, 5)
      });

    } catch (err) {
      console.error('Error fetching careers data:', err);
      setError('تعذر جلب بيانات الوظائف');
    } finally {
      setLoading(false);
    }
  };

  // Category management functions
  const openCategoryModal = (category = null) => {
    if (category) {
      setEditingCategory(category);
      setCategoryForm({
        name_ar: category.name_ar,
        name_en: category.name_en,
        description_ar: category.description_ar,
        is_active: category.is_active
      });
    } else {
      setEditingCategory(null);
      setCategoryForm({
        name_ar: '',
        name_en: '',
        description_ar: '',
        is_active: true
      });
    }
    setShowCategoryModal(true);
  };

  const closeCategoryModal = () => {
    setShowCategoryModal(false);
    setEditingCategory(null);
    setCategoryForm({
      name_ar: '',
      name_en: '',
      description_ar: '',
      is_active: true
    });
  };

  const handleCategorySubmit = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      if (editingCategory) {
        await axios.patch(`${API_BASE_URL}/api/careers/categories/${editingCategory.id}/`, categoryForm, { headers });
      } else {
        await axios.post(`${API_BASE_URL}/api/careers/categories/`, categoryForm, { headers });
      }

      closeCategoryModal();
      fetchData();
      showMessage('تم حفظ الفئة بنجاح');
    } catch (err) {
      console.error('Error saving category:', err);
      showMessage('حدث خطأ أثناء حفظ الفئة', true);
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
      return;
    }

    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };
      
      await axios.delete(`${API_BASE_URL}/api/careers/categories/${categoryId}/`, { headers });
      fetchData();
      showMessage('تم حذف الفئة بنجاح');
    } catch (err) {
      console.error('Error deleting category:', err);
      showMessage('حدث خطأ أثناء حذف الفئة', true);
    }
  };

  // Job management functions
  const openJobModal = (job = null) => {
    if (job) {
      setEditingJob(job);
      setJobForm({
        title_ar: job.title_ar,
        company_name: job.company_name,
        category_id: job.category?.id || '',
        job_type: job.job_type,
        location: job.location,
        description_ar: job.description_ar,
        requirements_ar: job.requirements_ar,
        salary_range: job.salary_range,
        application_deadline: job.application_deadline,
        contact_email: job.contact_email,
        contact_phone: job.contact_phone,
        is_active: job.is_active
      });
    } else {
      setEditingJob(null);
      setJobForm({
        title_ar: '',
        company_name: '',
        category_id: '',
        job_type: 'full_time',
        location: '',
        description_ar: '',
        requirements_ar: '',
        salary_range: '',
        application_deadline: '',
        contact_email: '',
        contact_phone: '',
        is_active: true
      });
    }
    setShowJobModal(true);
  };

  const closeJobModal = () => {
    setShowJobModal(false);
    setEditingJob(null);
    setJobForm({
      title_ar: '',
      company_name: '',
      category_id: '',
      job_type: 'full_time',
      location: '',
      description_ar: '',
      requirements_ar: '',
      salary_range: '',
      application_deadline: '',
      contact_email: '',
      contact_phone: '',
      is_active: true
    });
  };

  const handleJobSubmit = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      if (editingJob) {
        await axios.patch(`${API_BASE_URL}/api/careers/jobs/${editingJob.id}/`, jobForm, { headers });
      } else {
        await axios.post(`${API_BASE_URL}/api/careers/jobs/`, jobForm, { headers });
      }

      closeJobModal();
      fetchData();
      showMessage('تم حفظ الوظيفة بنجاح');
    } catch (err) {
      console.error('Error saving job:', err);
      showMessage('حدث خطأ أثناء حفظ الوظيفة', true);
    }
  };

  const handleDeleteJob = async (jobId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الوظيفة؟')) {
      return;
    }

    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      await axios.delete(`${API_BASE_URL}/api/careers/jobs/${jobId}/`, { headers });
      fetchData();
      showMessage('تم حذف الوظيفة بنجاح');
    } catch (err) {
      console.error('Error deleting job:', err);
      showMessage('حدث خطأ أثناء حذف الوظيفة', true);
    }
  };

  // Application management functions
  const showApplicationDetails = (application) => {
    setSelectedApplication(application);
    setShowApplicationModal(true);
  };

  const closeApplicationModal = () => {
    setShowApplicationModal(false);
    setSelectedApplication(null);
  };

  const updateApplicationStatus = async (applicationId, newStatus) => {
    try {
      const token = localStorage.getItem('access');
      const headers = { 'Authorization': `Bearer ${token}` };

      await axios.patch(`${API_BASE_URL}/api/careers/applications/${applicationId}/`,
        { status: newStatus },
        { headers }
      );
      fetchData();
      showMessage('تم تحديث حالة الطلب بنجاح');
    } catch (err) {
      console.error('Error updating application status:', err);
      showMessage('حدث خطأ أثناء تحديث حالة الطلب', true);
    }
  };

  if (loading) return <div className="text-center py-8">جاري التحميل...</div>;
  if (error) return <div className="text-center py-8 text-red-600">{error}</div>;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">إدارة الوظائف</h2>
        <div className="flex gap-2">
          <button
            onClick={() => fetchData()}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            🔄 تحديث
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{successMessage}</span>
          <span 
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setSuccessMessage(null)}
          >
            <svg className="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>إغلاق</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </span>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{error}</span>
          <span 
            className="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer"
            onClick={() => setError(null)}
          >
            <svg className="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>إغلاق</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </span>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 space-x-reverse">
          {tabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === tab.key
                  ? 'border-yellow-500 text-yellow-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800">نظرة عامة على الوظائف</h3>
            
            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-blue-500 text-white rounded-full p-3 mb-2">
                  <FaBuilding size={24} />
                </div>
                <div className="text-2xl font-bold text-blue-800">{stats.total_categories}</div>
                <div className="text-blue-700 mt-1">فئات الوظائف</div>
              </div>

              <div className="bg-green-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-green-500 text-white rounded-full p-3 mb-2">
                  <FaBriefcase size={24} />
                </div>
                <div className="text-2xl font-bold text-green-800">{stats.total_jobs}</div>
                <div className="text-green-700 mt-1">إجمالي الوظائف</div>
              </div>

              <div className="bg-yellow-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-yellow-500 text-white rounded-full p-3 mb-2">
                  <FaUsers size={24} />
                </div>
                <div className="text-2xl font-bold text-yellow-800">{stats.total_applications}</div>
                <div className="text-yellow-700 mt-1">طلبات التوظيف</div>
              </div>

              <div className="bg-purple-100 rounded-lg p-6 flex flex-col items-center shadow">
                <div className="bg-purple-500 text-white rounded-full p-3 mb-2">
                  <FaClock size={24} />
                </div>
                <div className="text-2xl font-bold text-purple-800">{stats.pending_applications}</div>
                <div className="text-purple-700 mt-1">قيد المراجعة</div>
              </div>
            </div>

            {/* Recent Jobs and Applications */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Jobs */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h4 className="text-lg font-bold text-gray-800 mb-4">الوظائف الحديثة</h4>
                <div className="space-y-3">
                  {stats.recent_jobs.length > 0 ? (
                    stats.recent_jobs.map((job, index) => (
                      <div key={job.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="bg-blue-100 text-blue-600 p-2 rounded-full">
                            <FaBriefcase size={16} />
                          </div>
                          <div>
                            <div className="font-medium text-gray-800">{job.title_ar}</div>
                            <div className="text-sm text-gray-600">{job.company_name}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                            job.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {job.is_active ? 'نشطة' : 'غير نشطة'}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {new Date(job.created_at).toLocaleDateString('ar-EG')}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      لا توجد وظائف حديثة
                    </div>
                  )}
                </div>
              </div>

              {/* Recent Applications */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h4 className="text-lg font-bold text-gray-800 mb-4">طلبات التوظيف الحديثة</h4>
                <div className="space-y-3">
                  {stats.recent_applications.length > 0 ? (
                    stats.recent_applications.map((application, index) => (
                      <div key={application.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="bg-green-100 text-green-600 p-2 rounded-full">
                            <FaUsers size={16} />
                          </div>
                          <div>
                            <div className="font-medium text-gray-800">
                              {application.student?.user?.email || 'طالب غير محدد'}
                            </div>
                            <div className="text-sm text-gray-600">
                              {application.job?.title_ar || 'وظيفة غير محددة'}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                            applicationStatuses.find(s => s.value === application.status)?.color === 'green' ? 'bg-green-100 text-green-800' :
                            applicationStatuses.find(s => s.value === application.status)?.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                            applicationStatuses.find(s => s.value === application.status)?.color === 'red' ? 'bg-red-100 text-red-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {applicationStatuses.find(s => s.value === application.status)?.label || application.status}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {new Date(application.applied_at).toLocaleDateString('ar-EG')}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      لا توجد طلبات توظيف حديثة
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-bold text-gray-800">إدارة فئات الوظائف ({categories.length})</h3>
              <button
                onClick={() => openCategoryModal()}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
              >
                <FaPlus size={16} />
                إضافة فئة جديدة
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">ID</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالعربية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالإنجليزية</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوصف</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {categories.map(category => (
                      <tr key={category.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{category.id}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{category.name_ar}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{category.name_en}</td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          <div className="max-w-xs truncate" title={category.description_ar}>
                            {category.description_ar}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            category.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {category.is_active ? 'نشطة' : 'غير نشطة'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <button
                              onClick={() => openCategoryModal(category)}
                              className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                              title="تعديل"
                            >
                              <FaEdit size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteCategory(category.id)}
                              className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                              title="حذف"
                            >
                              <FaTrash size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {categories.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد فئات مسجلة حالياً
                </div>
              )}
            </div>
          </div>
        )}

        {/* Jobs Tab */}
        {activeTab === 'jobs' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-bold text-gray-800">إدارة الوظائف ({jobs.length})</h3>
              <button
                onClick={() => openJobModal()}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
              >
                <FaPlus size={16} />
                إضافة وظيفة جديدة
              </button>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex-1 min-w-64">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث في الوظائف..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  />
                </div>
              </div>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
              >
                <option value="">جميع الفئات</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name_ar}
                  </option>
                ))}
              </select>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">العنوان</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الشركة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفئة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الموقع</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الراتب</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر موعد</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {jobs.filter(job => {
                      const matchesSearch = !searchTerm ||
                        job.title_ar.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        job.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        job.location.toLowerCase().includes(searchTerm.toLowerCase());

                      const matchesCategory = !categoryFilter || job.category?.id.toString() === categoryFilter;

                      return matchesSearch && matchesCategory;
                    }).map(job => (
                      <tr key={job.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="font-medium">{job.title_ar}</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{job.company_name}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {job.category?.name_ar || '—'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                            {jobTypes.find(t => t.value === job.job_type)?.label || job.job_type}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center gap-1">
                            <FaMapMarkerAlt className="text-gray-400" size={12} />
                            {job.location}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center gap-1">
                            <FaMoneyBillWave className="text-green-500" size={12} />
                            {job.salary_range}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center gap-1">
                            <FaCalendarAlt className="text-red-500" size={12} />
                            {new Date(job.application_deadline).toLocaleDateString('ar-EG')}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            job.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {job.is_active ? 'نشطة' : 'غير نشطة'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <button
                              onClick={() => openJobModal(job)}
                              className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                              title="تعديل"
                            >
                              <FaEdit size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteJob(job.id)}
                              className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                              title="حذف"
                            >
                              <FaTrash size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {jobs.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد وظائف مسجلة حالياً
                </div>
              )}
            </div>
          </div>
        )}

        {/* Applications Tab */}
        {activeTab === 'applications' && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800">إدارة طلبات التوظيف ({applications.length})</h3>

            {/* Search and Filter */}
            <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex-1 min-w-64">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث في الطلبات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
              >
                <option value="">جميع الحالات</option>
                {applicationStatuses.map(status => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الطالب</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوظيفة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الشركة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ التقديم</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {applications.filter(application => {
                      const matchesSearch = !searchTerm ||
                        application.student?.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        application.job?.title_ar?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        application.job?.company_name?.toLowerCase().includes(searchTerm.toLowerCase());

                      const matchesStatus = !statusFilter || application.status === statusFilter;

                      return matchesSearch && matchesStatus;
                    }).map(application => (
                      <tr key={application.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {application.student?.user?.email || 'طالب غير محدد'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {application.job?.title_ar || 'وظيفة غير محددة'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {application.job?.company_name || '—'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(application.applied_at).toLocaleDateString('ar-EG')}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            applicationStatuses.find(s => s.value === application.status)?.color === 'green' ? 'bg-green-100 text-green-800' :
                            applicationStatuses.find(s => s.value === application.status)?.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                            applicationStatuses.find(s => s.value === application.status)?.color === 'red' ? 'bg-red-100 text-red-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {applicationStatuses.find(s => s.value === application.status)?.label || application.status}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <button
                              onClick={() => showApplicationDetails(application)}
                              className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                              title="عرض التفاصيل"
                            >
                              <FaEye size={16} />
                            </button>
                            <button
                              onClick={() => updateApplicationStatus(application.id, 'accepted')}
                              className="text-green-600 hover:text-green-900 p-1 rounded transition-colors"
                              title="قبول"
                            >
                              <FaCheck size={16} />
                            </button>
                            <button
                              onClick={() => updateApplicationStatus(application.id, 'rejected')}
                              className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                              title="رفض"
                            >
                              <FaTimes size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {applications.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد طلبات توظيف حالياً
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Category Modal */}
      {showCategoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              {editingCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
            </h3>

            <form onSubmit={handleCategorySubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الاسم بالعربية
                </label>
                <input
                  type="text"
                  value={categoryForm.name_ar}
                  onChange={(e) => setCategoryForm({...categoryForm, name_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الاسم بالإنجليزية
                </label>
                <input
                  type="text"
                  value={categoryForm.name_en}
                  onChange={(e) => setCategoryForm({...categoryForm, name_en: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الوصف
                </label>
                <textarea
                  value={categoryForm.description_ar}
                  onChange={(e) => setCategoryForm({...categoryForm, description_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  rows="3"
                  required
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="category_is_active"
                  checked={categoryForm.is_active}
                  onChange={(e) => setCategoryForm({...categoryForm, is_active: e.target.checked})}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="category_is_active" className="mr-2 block text-sm text-gray-900">
                  فئة نشطة
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  {editingCategory ? 'تحديث' : 'إضافة'}
                </button>
                <button
                  type="button"
                  onClick={closeCategoryModal}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Job Modal */}
      {showJobModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              {editingJob ? 'تعديل الوظيفة' : 'إضافة وظيفة جديدة'}
            </h3>

            <form onSubmit={handleJobSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    عنوان الوظيفة
                  </label>
                  <input
                    type="text"
                    value={jobForm.title_ar}
                    onChange={(e) => setJobForm({...jobForm, title_ar: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم الشركة
                  </label>
                  <input
                    type="text"
                    value={jobForm.company_name}
                    onChange={(e) => setJobForm({...jobForm, company_name: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الفئة
                  </label>
                  <select
                    value={jobForm.category_id}
                    onChange={(e) => setJobForm({...jobForm, category_id: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  >
                    <option value="">اختر الفئة</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name_ar}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    نوع الوظيفة
                  </label>
                  <select
                    value={jobForm.job_type}
                    onChange={(e) => setJobForm({...jobForm, job_type: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  >
                    {jobTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الموقع
                  </label>
                  <input
                    type="text"
                    value={jobForm.location}
                    onChange={(e) => setJobForm({...jobForm, location: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  وصف الوظيفة
                </label>
                <textarea
                  value={jobForm.description_ar}
                  onChange={(e) => setJobForm({...jobForm, description_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  rows="4"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المتطلبات
                </label>
                <textarea
                  value={jobForm.requirements_ar}
                  onChange={(e) => setJobForm({...jobForm, requirements_ar: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  rows="3"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    نطاق الراتب
                  </label>
                  <input
                    type="text"
                    value={jobForm.salary_range}
                    onChange={(e) => setJobForm({...jobForm, salary_range: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    placeholder="مثال: 5000-8000 جنيه"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    آخر موعد للتقديم
                  </label>
                  <input
                    type="date"
                    value={jobForm.application_deadline}
                    onChange={(e) => setJobForm({...jobForm, application_deadline: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    البريد الإلكتروني للتواصل
                  </label>
                  <input
                    type="email"
                    value={jobForm.contact_email}
                    onChange={(e) => setJobForm({...jobForm, contact_email: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  رقم الهاتف للتواصل
                </label>
                <input
                  type="tel"
                  value={jobForm.contact_phone}
                  onChange={(e) => setJobForm({...jobForm, contact_phone: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  required
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="job_is_active"
                  checked={jobForm.is_active}
                  onChange={(e) => setJobForm({...jobForm, is_active: e.target.checked})}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="job_is_active" className="mr-2 block text-sm text-gray-900">
                  وظيفة نشطة
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  {editingJob ? 'تحديث' : 'إضافة'}
                </button>
                <button
                  type="button"
                  onClick={closeJobModal}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Application Details Modal */}
      {showApplicationModal && selectedApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              تفاصيل طلب التوظيف
            </h3>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الطالب
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    {selectedApplication.student?.user?.email || 'طالب غير محدد'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الوظيفة
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    {selectedApplication.job?.title_ar || 'وظيفة غير محددة'}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الشركة
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    {selectedApplication.job?.company_name || '—'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    تاريخ التقديم
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    {new Date(selectedApplication.applied_at).toLocaleString('ar-EG')}
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الحالة الحالية
                </label>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    applicationStatuses.find(s => s.value === selectedApplication.status)?.color === 'green' ? 'bg-green-100 text-green-800' :
                    applicationStatuses.find(s => s.value === selectedApplication.status)?.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                    applicationStatuses.find(s => s.value === selectedApplication.status)?.color === 'red' ? 'bg-red-100 text-red-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {applicationStatuses.find(s => s.value === selectedApplication.status)?.label || selectedApplication.status}
                  </span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  خطاب التقديم
                </label>
                <div className="p-3 bg-gray-50 rounded-lg whitespace-pre-wrap">
                  {selectedApplication.cover_letter}
                </div>
              </div>

              {selectedApplication.resume && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    السيرة الذاتية
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <a
                      href={selectedApplication.resume}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 underline flex items-center gap-2"
                    >
                      <FaEye size={16} />
                      عرض السيرة الذاتية
                    </a>
                  </div>
                </div>
              )}

              <div className="flex gap-3 pt-4">
                <button
                  onClick={() => updateApplicationStatus(selectedApplication.id, 'accepted')}
                  className="flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
                >
                  <FaCheck size={16} />
                  قبول
                </button>
                <button
                  onClick={() => updateApplicationStatus(selectedApplication.id, 'rejected')}
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
                >
                  <FaTimes size={16} />
                  رفض
                </button>
                <button
                  onClick={closeApplicationModal}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CareersManagement;
