import React, { useEffect, useContext, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import './EmailVerification.css';

const EmailVerification = () => {
  const { verifyEmail, loading } = useContext(AuthContext);
  const [searchParams] = useSearchParams();
  const [token, setToken] = useState('');
  const [result, setResult] = useState(null); // {success: bool, message: string}
  const navigate = useNavigate();

  // Auto verify if token in URL
  useEffect(() => {
    const urlToken = searchParams.get('token');
    if (urlToken) {
      handleVerify(urlToken);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleVerify = async (verifyToken) => {
    const res = await verifyEmail(verifyToken);
    if (res.success) {
      setResult({ success: true, message: 'تم تأكيد البريد الإلكتروني بنجاح' });
      // redirect after 3 seconds
      setTimeout(() => {
        navigate('/student-application');
      }, 3000);
    } else {
      setResult({ success: false, message: res.error?.detail || 'تعذر التحقق من الرمز' });
    }
  };

  const onSubmit = (e) => {
    e.preventDefault();
    if (token.trim()) {
      handleVerify(token.trim());
    }
  };

  return (
    <div className="verify-container">
      <h2 className="title">تأكيد البريد الإلكتروني</h2>

      {result ? (
        <p className={result.success ? 'success' : 'error'}>{result.message}</p>
      ) : (
        <form onSubmit={onSubmit} className="verify-form">
          <label htmlFor="token">الرمز</label>
          <input
            id="token"
            type="text"
            value={token}
            onChange={(e) => setToken(e.target.value)}
            placeholder="أدخل رمز التفعيل"
          />
          <button type="submit" disabled={loading || !token.trim()}>
            {loading ? 'جاري التحقق...' : 'تحقّق'}
          </button>
        </form>
      )}
    </div>
  );
};

export default EmailVerification; 