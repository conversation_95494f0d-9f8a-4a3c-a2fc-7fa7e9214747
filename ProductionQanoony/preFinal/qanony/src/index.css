@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    direction: rtl;
  }
  
  body {
    @apply bg-background text-dark font-cairo;
  }

  :root {
    --primary: #FACC15;
    --secondary: #555555;
    --background: #F9F9F9;
    --text: #1A1A1A;
    --light-gray: #E0E0E0;
  }
}

@layer components {
  .btn-primary {
    @apply bg-yellow-400 text-yellow-900 px-6 py-2 rounded-lg hover:bg-yellow-500 transition-all duration-300 font-semibold;
  }
  
  .btn-secondary {
    @apply bg-secondary text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-all duration-300 font-semibold;
  }

  .btn-outline-primary {
    @apply border-2 border-yellow-400 text-yellow-400 px-6 py-2 rounded-lg hover:bg-yellow-400 hover:text-yellow-900 transition-all duration-300 font-semibold;
  }

  .btn-outline-secondary {
    @apply border-2 border-secondary text-secondary px-6 py-2 rounded-lg hover:bg-secondary hover:text-white transition-all duration-300 font-semibold;
  }

  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .nav-link {
    @apply relative px-3 py-2 text-dark hover:text-yellow-400 transition-colors duration-200 font-medium;
  }

  .nav-link::after {
    @apply content-[''] absolute right-0 bottom-0 w-0 h-0.5 bg-yellow-400 transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  .nav-link.active {
    @apply text-yellow-400;
  }

  .nav-link.active::after {
    @apply w-full;
  }

  .card {
    @apply bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300;
  }

  .input {
    @apply w-full px-4 py-2 rounded-lg border border-light-gray focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400 transition-all duration-200;
  }

  .label {
    @apply block text-sm font-medium text-dark mb-1;
  }

  .error {
    @apply text-red-600 text-sm mt-1;
  }

  .success {
    @apply text-green-600 text-sm mt-1;
  }
}

/* Blob Animation */
.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}
