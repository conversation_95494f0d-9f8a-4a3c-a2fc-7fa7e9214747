import React, { useState, useEffect } from 'react';
import { 
  useWebSocket, 
  useChatWebSocket, 
  useNotificationsWebSocket, 
  useAIAssistantWebSocket 
} from '../hooks/useWebSocket';

/**
 * Example component showing how to use WebSocket hooks
 */
const WebSocketExample = () => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [notifications, setNotifications] = useState([]);

  // Example 1: Basic WebSocket connection
  const {
    isConnected: isBasicConnected,
    lastMessage: basicLastMessage,
    sendMessage: sendBasicMessage,
    connectionState: basicState,
  } = useWebSocket('ws/example/', {
    onMessage: (data) => {
      console.log('Basic WebSocket message:', data);
    },
    onOpen: () => {
      console.log('Basic WebSocket connected');
    },
    onClose: () => {
      console.log('Basic WebSocket disconnected');
    },
  });

  // Example 2: Chat WebSocket
  const {
    isConnected: isChatConnected,
    lastMessage: chatLastMessage,
    sendChatMessage,
    sendTyping,
    connectionState: chatState,
  } = useChatWebSocket('general', {
    onMessage: (data) => {
      if (data.type === 'chat_message') {
        setMessages(prev => [...prev, data]);
      }
    },
  });

  // Example 3: Notifications WebSocket
  const {
    isConnected: isNotificationsConnected,
    lastMessage: notificationLastMessage,
    connectionState: notificationsState,
  } = useNotificationsWebSocket({
    onMessage: (data) => {
      if (data.type === 'notification') {
        setNotifications(prev => [...prev, data]);
      }
    },
  });

  // Example 4: AI Assistant WebSocket
  const {
    isConnected: isAIConnected,
    lastMessage: aiLastMessage,
    sendAIQuery,
    connectionState: aiState,
  } = useAIAssistantWebSocket({
    onMessage: (data) => {
      console.log('AI Assistant response:', data);
    },
  });

  // Handle sending chat message
  const handleSendMessage = () => {
    if (newMessage.trim() && isChatConnected) {
      sendChatMessage(newMessage);
      setNewMessage('');
    }
  };

  // Handle sending AI query
  const handleAIQuery = () => {
    if (isAIConnected) {
      sendAIQuery('What is the weather today?', { location: 'Cairo' });
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">WebSocket Examples</h1>

      {/* Connection Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold mb-2">Basic WebSocket</h3>
          <div className={`px-2 py-1 rounded text-sm ${
            isBasicConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {basicState}
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold mb-2">Chat WebSocket</h3>
          <div className={`px-2 py-1 rounded text-sm ${
            isChatConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {chatState}
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold mb-2">Notifications</h3>
          <div className={`px-2 py-1 rounded text-sm ${
            isNotificationsConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {notificationsState}
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold mb-2">AI Assistant</h3>
          <div className={`px-2 py-1 rounded text-sm ${
            isAIConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {aiState}
          </div>
        </div>
      </div>

      {/* Chat Example */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">Chat Example</h2>
        
        <div className="border rounded-lg p-4 h-64 overflow-y-auto mb-4 bg-gray-50">
          {messages.map((msg, index) => (
            <div key={index} className="mb-2 p-2 bg-white rounded">
              <div className="text-sm text-gray-600">{msg.timestamp}</div>
              <div>{msg.message}</div>
            </div>
          ))}
        </div>

        <div className="flex gap-2">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Type a message..."
            className="flex-1 border rounded-lg px-3 py-2"
            disabled={!isChatConnected}
          />
          <button
            onClick={handleSendMessage}
            disabled={!isChatConnected || !newMessage.trim()}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            Send
          </button>
        </div>
      </div>

      {/* Notifications Example */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">Notifications ({notifications.length})</h2>
        
        <div className="space-y-2">
          {notifications.slice(-5).map((notification, index) => (
            <div key={index} className="border-l-4 border-blue-500 pl-4 py-2 bg-blue-50">
              <div className="font-medium">{notification.title}</div>
              <div className="text-sm text-gray-600">{notification.message}</div>
              <div className="text-xs text-gray-500">{notification.timestamp}</div>
            </div>
          ))}
          {notifications.length === 0 && (
            <div className="text-gray-500 text-center py-4">No notifications yet</div>
          )}
        </div>
      </div>

      {/* AI Assistant Example */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">AI Assistant Example</h2>
        
        <button
          onClick={handleAIQuery}
          disabled={!isAIConnected}
          className="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 disabled:opacity-50"
        >
          Send AI Query
        </button>

        {aiLastMessage && (
          <div className="mt-4 p-4 bg-purple-50 rounded-lg">
            <div className="font-medium">AI Response:</div>
            <div className="text-sm">{JSON.stringify(aiLastMessage, null, 2)}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WebSocketExample;
