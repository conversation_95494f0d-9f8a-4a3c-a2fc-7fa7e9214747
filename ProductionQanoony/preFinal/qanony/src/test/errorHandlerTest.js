/**
 * اختبارات للـ Error Handler
 */

import { 
  extractErrorMessage, 
  handleApiError, 
  handleFormErrors, 
  getOperationErrorMessage,
  requiresReauth,
  createDisplayError,
  getErrorMessage
} from '../utils/errorHandler';

// محاكاة أخطاء مختلفة للاختبار
const mockErrors = {
  networkError: {
    message: 'Network Error',
    code: 'NETWORK_ERROR'
  },
  
  timeoutError: {
    code: 'ECONNABORTED',
    message: 'timeout of 10000ms exceeded'
  },
  
  unauthorizedError: {
    response: {
      status: 401,
      data: {
        detail: 'Authentication credentials were not provided.'
      }
    }
  },
  
  validationError: {
    response: {
      status: 400,
      data: {
        error: 'خطأ في التحقق من البيانات',
        detail: 'البيانات المرسلة غير صحيحة',
        errors: {
          email: ['هذا الحقل مطلوب'],
          password: ['كلمة المرور قصيرة جداً']
        }
      }
    }
  },
  
  serverError: {
    response: {
      status: 500,
      data: {
        error: 'خطأ داخلي في الخادم',
        detail: 'حدث خطأ غير متوقع'
      }
    }
  },
  
  customError: {
    response: {
      status: 403,
      data: {
        error: 'ممنوع',
        detail: 'ليس لديك الصلاحية للوصول إلى هذا المورد',
        code: 'permission_denied'
      }
    }
  }
};

// دالة لتشغيل الاختبارات
export const runErrorHandlerTests = () => {
  console.log('🧪 بدء اختبارات Error Handler...\n');

  // اختبار 1: خطأ الشبكة
  console.log('1️⃣ اختبار خطأ الشبكة:');
  const networkResult = extractErrorMessage(mockErrors.networkError);
  console.log('النتيجة:', networkResult);
  console.log('✅ متوقع: رسالة خطأ شبكة\n');

  // اختبار 2: خطأ انتهاء المهلة
  console.log('2️⃣ اختبار خطأ انتهاء المهلة:');
  const timeoutResult = extractErrorMessage(mockErrors.timeoutError);
  console.log('النتيجة:', timeoutResult);
  console.log('✅ متوقع: رسالة انتهاء مهلة\n');

  // اختبار 3: خطأ عدم المصادقة
  console.log('3️⃣ اختبار خطأ عدم المصادقة:');
  const authResult = extractErrorMessage(mockErrors.unauthorizedError);
  console.log('النتيجة:', authResult);
  console.log('✅ متوقع: رسالة عدم مصادقة\n');

  // اختبار 4: خطأ التحقق من البيانات
  console.log('4️⃣ اختبار خطأ التحقق من البيانات:');
  const validationResult = handleFormErrors(mockErrors.validationError);
  console.log('النتيجة:', validationResult);
  console.log('✅ متوقع: أخطاء الحقول منفصلة\n');

  // اختبار 5: خطأ الخادم
  console.log('5️⃣ اختبار خطأ الخادم:');
  const serverResult = extractErrorMessage(mockErrors.serverError);
  console.log('النتيجة:', serverResult);
  console.log('✅ متوقع: رسالة خطأ خادم\n');

  // اختبار 6: رسائل العمليات المخصصة
  console.log('6️⃣ اختبار رسائل العمليات:');
  const loginError = getOperationErrorMessage('login', mockErrors.unauthorizedError);
  const fetchError = getOperationErrorMessage('fetch', mockErrors.serverError);
  console.log('خطأ تسجيل الدخول:', loginError);
  console.log('خطأ جلب البيانات:', fetchError);
  console.log('✅ متوقع: رسائل مخصصة للعمليات\n');

  // اختبار 7: فحص الحاجة لإعادة المصادقة
  console.log('7️⃣ اختبار فحص إعادة المصادقة:');
  const needsReauth = requiresReauth(mockErrors.unauthorizedError);
  const noReauth = requiresReauth(mockErrors.validationError);
  console.log('يحتاج إعادة مصادقة (401):', needsReauth);
  console.log('لا يحتاج إعادة مصادقة (400):', noReauth);
  console.log('✅ متوقع: true للأول، false للثاني\n');

  // اختبار 8: إنشاء خطأ للعرض
  console.log('8️⃣ اختبار إنشاء خطأ للعرض:');
  const displayError = createDisplayError(mockErrors.networkError);
  console.log('خطأ العرض:', displayError);
  console.log('✅ متوقع: كائن خطأ مع canRetry = true\n');

  // اختبار 9: الدالة المساعدة السريعة
  console.log('9️⃣ اختبار الدالة المساعدة:');
  const quickMessage = getErrorMessage(mockErrors.customError, 'الوصول للمورد');
  console.log('الرسالة السريعة:', quickMessage);
  console.log('✅ متوقع: رسالة مع السياق\n');

  console.log('🎉 انتهت جميع الاختبارات!');
};

// دالة لاختبار سيناريوهات مختلفة
export const testErrorScenarios = () => {
  console.log('🎭 اختبار سيناريوهات مختلفة...\n');

  // سيناريو 1: مستخدم غير مصرح له
  console.log('📋 سيناريو 1: مستخدم يحاول الوصول لمورد محظور');
  const forbiddenError = {
    response: {
      status: 403,
      data: {
        error: 'ممنوع',
        detail: 'يجب أن يكون لديك اشتراك نشط للوصول إلى هذا المورد'
      }
    }
  };
  console.log('الرسالة:', getErrorMessage(forbiddenError, 'الوصول للمحتوى'));

  // سيناريو 2: بيانات مكررة
  console.log('\n📋 سيناريو 2: محاولة إنشاء بيانات مكررة');
  const duplicateError = {
    response: {
      status: 400,
      data: {
        error: 'بيانات مكررة',
        detail: 'هذا البريد الإلكتروني مستخدم بالفعل'
      }
    }
  };
  console.log('الرسالة:', getErrorMessage(duplicateError, 'إنشاء الحساب'));

  // سيناريو 3: خطأ في رفع الملف
  console.log('\n📋 سيناريو 3: خطأ في رفع الملف');
  const uploadError = {
    response: {
      status: 413,
      data: {
        error: 'ملف كبير جداً',
        detail: 'حجم الملف يتجاوز الحد المسموح (5 ميجابايت)'
      }
    }
  };
  console.log('الرسالة:', getErrorMessage(uploadError, 'رفع الملف'));

  // سيناريو 4: خطأ في الاتصال بخدمة خارجية
  console.log('\n📋 سيناريو 4: خطأ في خدمة خارجية');
  const serviceError = {
    response: {
      status: 502,
      data: {
        error: 'خطأ في البوابة',
        detail: 'خدمة الذكاء الاصطناعي غير متاحة حالياً'
      }
    }
  };
  console.log('الرسالة:', getErrorMessage(serviceError, 'استخدام المساعد الذكي'));

  console.log('\n✨ انتهت اختبارات السيناريوهات!');
};

// دالة لاختبار الأداء
export const testErrorHandlerPerformance = () => {
  console.log('⚡ اختبار أداء Error Handler...\n');

  const iterations = 1000;
  const startTime = performance.now();

  for (let i = 0; i < iterations; i++) {
    extractErrorMessage(mockErrors.validationError);
    handleApiError(mockErrors.serverError, 'test');
    requiresReauth(mockErrors.unauthorizedError);
  }

  const endTime = performance.now();
  const totalTime = endTime - startTime;
  const avgTime = totalTime / iterations;

  console.log(`📊 نتائج الأداء:`);
  console.log(`- عدد التكرارات: ${iterations}`);
  console.log(`- الوقت الإجمالي: ${totalTime.toFixed(2)} مللي ثانية`);
  console.log(`- متوسط الوقت: ${avgTime.toFixed(4)} مللي ثانية`);
  console.log(`- العمليات في الثانية: ${(1000 / avgTime).toFixed(0)}`);

  if (avgTime < 1) {
    console.log('✅ الأداء ممتاز!');
  } else if (avgTime < 5) {
    console.log('⚠️ الأداء مقبول');
  } else {
    console.log('❌ الأداء بطيء، يحتاج تحسين');
  }
};

// تشغيل جميع الاختبارات
export const runAllTests = () => {
  runErrorHandlerTests();
  console.log('\n' + '='.repeat(50) + '\n');
  testErrorScenarios();
  console.log('\n' + '='.repeat(50) + '\n');
  testErrorHandlerPerformance();
};

// تصدير للاستخدام في الكونسول
if (typeof window !== 'undefined') {
  window.testErrorHandler = {
    runErrorHandlerTests,
    testErrorScenarios,
    testErrorHandlerPerformance,
    runAllTests
  };
}
