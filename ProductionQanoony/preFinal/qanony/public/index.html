<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />

    <!-- Primary Meta Tags -->
    <title>منصة قانوني التعليمية - أفضل منصة تعليمية لطلاب كلية الحقوق في مصر</title>
    <meta name="title" content="منصة قانوني التعليمية - أفضل منصة تعليمية لطلاب كلية الحقوق في مصر" />
    <meta name="description" content="منصة قانوني التعليمية الرائدة في مصر لطلاب كلية الحقوق. محاضرات تفاعلية، اختبارات، مكتبة قانونية شاملة، وفرص توظيف متميزة. انضم لأكثر من 5000 طالب واحصل على تعليم قانوني متميز." />
    <meta name="keywords" content="قانوني، كلية الحقوق، تعليم قانوني، محاضرات قانونية، اختبارات قانونية، مكتبة قانونية، توظيف محامين، دراسة القانون، طلاب الحقوق، منصة تعليمية، التعليم الإلكتروني، القانون المصري، المحاماة، الاستشارات القانونية" />
    <meta name="author" content="منصة قانوني التعليمية" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="Arabic" />
    <meta name="revisit-after" content="7 days" />

    <!-- Viewport and Mobile -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="theme-color" content="#1e40af" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="قانوني" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://qanony.com/" />
    <meta property="og:title" content="منصة قانوني التعليمية - أفضل منصة تعليمية لطلاب كلية الحقوق" />
    <meta property="og:description" content="منصة قانوني التعليمية الرائدة في مصر لطلاب كلية الحقوق. محاضرات تفاعلية، اختبارات، مكتبة قانونية شاملة، وفرص توظيف متميزة." />
    <meta property="og:image" content="https://qanony.com/og-image.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="منصة قانوني التعليمية - تعليم قانوني متميز" />
    <meta property="og:site_name" content="منصة قانوني التعليمية" />
    <meta property="og:locale" content="ar_EG" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://qanony.com/" />
    <meta property="twitter:title" content="منصة قانوني التعليمية - أفضل منصة تعليمية لطلاب كلية الحقوق" />
    <meta property="twitter:description" content="منصة قانوني التعليمية الرائدة في مصر لطلاب كلية الحقوق. محاضرات تفاعلية، اختبارات، مكتبة قانونية شاملة." />
    <meta property="twitter:image" content="https://qanony.com/og-image.jpg" />
    <meta property="twitter:image:alt" content="منصة قانوني التعليمية" />

    <!-- Favicons -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='0.9em' font-size='90'>⚖️</text></svg>" />
    <link rel="apple-touch-icon" sizes="180x180" href="%PUBLIC_URL%/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="%PUBLIC_URL%/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="%PUBLIC_URL%/favicon-16x16.png" />

    <!-- Preload Google Fonts for better performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://qanony.com/" />

    <!-- Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "EducationalOrganization",
      "name": "منصة قانوني التعليمية",
      "alternateName": "Qanony Educational Platform",
      "url": "https://qanony.com",
      "logo": "https://qanony.com/logo.png",
      "description": "منصة تعليمية متخصصة في تدريس القانون لطلاب كلية الحقوق في مصر",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "EG",
        "addressLocality": "القاهرة"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+20-************",
        "contactType": "customer service",
        "email": "<EMAIL>",
        "availableLanguage": "Arabic"
      },
      "sameAs": [
        "https://facebook.com/qanony",
        "https://twitter.com/qanony",
        "https://linkedin.com/company/qanony"
      ],
      "offers": {
        "@type": "Offer",
        "category": "Legal Education",
        "description": "دورات تعليمية في القانون"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.9",
        "reviewCount": "5000",
        "bestRating": "5",
        "worstRating": "1"
      }
    }
    </script>

    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Course",
      "name": "دراسة القانون - منصة قانوني",
      "description": "دورة شاملة في القانون تشمل جميع فروع القانون المصري",
      "provider": {
        "@type": "EducationalOrganization",
        "name": "منصة قانوني التعليمية",
        "url": "https://qanony.com"
      },
      "educationalLevel": "University",
      "inLanguage": "ar",
      "courseMode": "online",
      "hasCourseInstance": {
        "@type": "CourseInstance",
        "courseMode": "online",
        "instructor": {
          "@type": "Person",
          "name": "نخبة من المحامين والأساتذة المتخصصين"
        }
      }
    }
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
