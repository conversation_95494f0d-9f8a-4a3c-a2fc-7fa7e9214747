from django.contrib import admin
from .models import JobCategory, JobPosting, JobApplication

@admin.register(JobCategory)
class JobCategoryAdmin(admin.ModelAdmin):
    list_display = ('name_en', 'name_ar', 'is_active')
    search_fields = ('name_en', 'name_ar')
    list_filter = ('is_active',)

@admin.register(JobPosting)
class JobPostingAdmin(admin.ModelAdmin):
    list_display = ('title_ar', 'company_name', 'location', 'application_deadline', 'is_active')
    search_fields = ('title_ar', 'company_name', 'location')
    list_filter = ('is_active', 'company_name', 'location')

@admin.register(JobApplication)
class JobApplicationAdmin(admin.ModelAdmin):
    list_display = ('job', 'student', 'status', 'applied_at')
    search_fields = ('job__title_ar', 'student__user__email')
    list_filter = ('status', 'applied_at')
    readonly_fields = ('job', 'student', 'resume', 'cover_letter', 'applied_at')

    def get_readonly_fields(self, request, obj=None):
        if not request.user.is_superuser:
            return self.readonly_fields + ('status',)
        return self.readonly_fields
