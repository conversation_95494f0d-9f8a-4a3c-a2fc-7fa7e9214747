from rest_framework.routers import DefaultRouter
from .views import JobCategoryViewSet, JobPostingViewSet, JobApplicationViewSet

router = DefaultRouter()
router.register(r'categories', JobCategoryViewSet, basename='jobcategory')
router.register(r'jobs', JobPostingViewSet, basename='jobposting')
router.register(r'applications', JobApplicationViewSet, basename='jobapplication')

urlpatterns = router.urls 