from django.db import models
from authentication.models import CustomUser
from students.models import StudentProfile

# Create your models here.


class JobCategory(models.Model):
    name_ar = models.CharField(max_length=255)
    name_en = models.CharField(max_length=100)
    description_ar = models.TextField()
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name_en


class JobPosting(models.Model):
    JOB_TYPE_CHOICES = [
        ('internship', 'تدريب'),
        ('training', 'تدريب عملي'),
        ('full_time', 'دوام كامل'),
    ]
    title_ar = models.CharField(max_length=255)
    company_name = models.CharField(max_length=255)
    category = models.ForeignKey(
        JobCategory, on_delete=models.CASCADE, related_name='jobs')
    job_type = models.CharField(max_length=20, choices=JOB_TYPE_CHOICES)
    location = models.CharField(max_length=255)
    description_ar = models.TextField()
    requirements_ar = models.TextField()
    salary_range = models.CharField(max_length=100)
    application_deadline = models.DateField()
    contact_email = models.EmailField()
    contact_phone = models.CharField(max_length=30)
    is_active = models.BooleanField(default=True)
    posted_by = models.ForeignKey(
        CustomUser, on_delete=models.SET_NULL, null=True, related_name='posted_jobs')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.title_ar} - {self.company_name}"


class JobApplication(models.Model):
    STATUS_CHOICES = [
        ('applied', 'تم التقديم'),
        ('reviewed', 'تمت المراجعة'),
        ('accepted', 'تم القبول'),
        ('rejected', 'تم الرفض'),
    ]
    student = models.ForeignKey(
        StudentProfile, on_delete=models.CASCADE, related_name='job_applications')
    job = models.ForeignKey(
        JobPosting, on_delete=models.CASCADE, related_name='applications')
    cover_letter = models.TextField()
    resume = models.FileField()
    applied_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='applied')

    def __str__(self):
        return f"{self.student.user.email} - {self.job.title_ar}"
