from rest_framework import serializers
from .models import <PERSON><PERSON>ate<PERSON><PERSON>, JobPosting, JobApplication
from authentication.serializers import ProfileSerializer
from students.serializers import StudentProfileSerializer
from students.models import StudentProfile

class JobCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = JobCategory
        fields = ['id', 'name_ar', 'name_en', 'description_ar', 'is_active']

class JobPostingSerializer(serializers.ModelSerializer):
    category = JobCategorySerializer(read_only=True)
    category_id = serializers.PrimaryKeyRelatedField(queryset=JobCategory.objects.all(), source='category', write_only=True)
    posted_by = ProfileSerializer(read_only=True)

    class Meta:
        model = JobPosting
        fields = [
            'id', 'title_ar', 'company_name', 'category', 'category_id', 'job_type', 'location',
            'description_ar', 'requirements_ar', 'salary_range', 'application_deadline',
            'contact_email', 'contact_phone', 'is_active', 'posted_by', 'created_at'
        ]
        read_only_fields = ['id', 'posted_by', 'created_at']

class JobApplicationSerializer(serializers.ModelSerializer):
    student = StudentProfileSerializer(read_only=True)
    student_id = serializers.PrimaryKeyRelatedField(queryset=StudentProfile.objects.all(), source='student', write_only=True)
    job = JobPostingSerializer(read_only=True)
    job_id = serializers.PrimaryKeyRelatedField(queryset=JobPosting.objects.all(), source='job', write_only=True)
    resume = serializers.FileField()

    class Meta:
        model = JobApplication
        fields = [
            'id', 'student', 'student_id', 'job', 'job_id', 'cover_letter', 'resume',
            'applied_at', 'status'
        ]
        read_only_fields = ['id', 'applied_at']

    def validate_resume(self, value):
        allowed_types = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
        if hasattr(value, 'content_type') and value.content_type not in allowed_types:
            raise serializers.ValidationError('نوع السيرة الذاتية غير مدعوم')
        return value

    def validate_status(self, value):
        allowed_statuses = ['applied', 'reviewed', 'accepted', 'rejected']
        if value not in allowed_statuses:
            raise serializers.ValidationError('حالة الطلب غير صالحة')
        return value 