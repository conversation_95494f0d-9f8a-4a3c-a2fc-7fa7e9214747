# JobCategory Endpoints

## List Job Categories
- URL: /api/careers/categories/
- Method: GET
- Description: List all active job categories.
- Permissions: Public
- Request Example:
    GET /api/careers/categories/
- Response Example:
    [
        {"id": 1, "name_en": "Legal", "name_ar": "قانون", "description_ar": "desc1", "is_active": true}
    ]

## Retrieve Job Category
- URL: /api/careers/categories/{id}/
- Method: GET
- Description: Retrieve a job category by ID.
- Permissions: Public
- Request Example:
    GET /api/careers/categories/1/
- Response Example:
    {"id": 1, "name_en": "Legal", "name_ar": "قانون", "description_ar": "desc1", "is_active": true}

## Create Job Category
- URL: /api/careers/categories/
- Method: POST
- Description: Create a new job category.
- Permissions: Admin only
- Request Example:
    {"name_en": "HR", "name_ar": "موارد بشرية", "description_ar": "desc", "is_active": true}
- Response Example:
    {"id": 2, "name_en": "HR", "name_ar": "موارد بشرية", "description_ar": "desc", "is_active": true}

## Update Job Category
- URL: /api/careers/categories/{id}/
- Method: PUT/PATCH
- Description: Update a job category.
- Permissions: Admin only
- Request Example:
    {"name_en": "Legal Updated", "name_ar": "قانون", "description_ar": "desc1", "is_active": true}
- Response Example:
    {"id": 1, "name_en": "Legal Updated", "name_ar": "قانون", "description_ar": "desc1", "is_active": true}

## Delete Job Category
- URL: /api/careers/categories/{id}/
- Method: DELETE
- Description: Delete a job category.
- Permissions: Admin only
- Request Example:
    DELETE /api/careers/categories/2/
- Response Example:
    204 No Content 