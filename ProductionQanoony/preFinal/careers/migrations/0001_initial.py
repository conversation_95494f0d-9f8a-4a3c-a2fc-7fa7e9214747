# Generated by Django 4.2.7 on 2025-06-28 12:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='JobCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name_ar', models.Char<PERSON>ield(max_length=255)),
                ('name_en', models.CharField(max_length=100)),
                ('description_ar', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='JobPosting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_ar', models.Char<PERSON><PERSON>(max_length=255)),
                ('company_name', models.Char<PERSON>ield(max_length=255)),
                ('job_type', models.CharField(choices=[('internship', 'تدريب'), ('training', 'تدريب عملي'), ('full_time', 'دوام كامل')], max_length=20)),
                ('location', models.CharField(max_length=255)),
                ('description_ar', models.TextField()),
                ('requirements_ar', models.TextField()),
                ('salary_range', models.CharField(max_length=100)),
                ('application_deadline', models.DateField()),
                ('contact_email', models.EmailField(max_length=254)),
                ('contact_phone', models.CharField(max_length=30)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='jobs', to='careers.jobcategory')),
                ('posted_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_jobs', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='JobApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cover_letter', models.TextField()),
                ('resume', models.FileField(upload_to='')),
                ('applied_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('applied', 'تم التقديم'), ('reviewed', 'تمت المراجعة'), ('accepted', 'تم القبول'), ('rejected', 'تم الرفض')], default='applied', max_length=20)),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='careers.jobposting')),
            ],
        ),
    ]
