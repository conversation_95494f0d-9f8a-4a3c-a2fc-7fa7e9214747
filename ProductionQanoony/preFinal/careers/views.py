from django.shortcuts import render
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from .models import JobCategory, JobPosting, JobApplication
from .serializers import JobCategorySerializer, JobPostingSerializer, JobApplicationSerializer
from authentication.permissions import IsEmailVerified
from rest_framework.permissions import IsAdminUser
from django.utils import timezone
from ai_assistant.permissions import IsStaffOrActiveSubscriptionOrDenied

# Custom permission: Only employers/admins can modify jobs
class IsEmployerOrAdmin(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user.is_authenticated and (getattr(request.user, 'is_staff', False) or getattr(request.user, 'is_instructor', False))
    def has_object_permission(self, request, view, obj):
        # Only owner (posted_by) or admin can update/delete
        if request.method in permissions.SAFE_METHODS:
            return True
        return (request.user.is_authenticated and (
            request.user.is_staff or (getattr(request.user, 'is_instructor', False) and obj.posted_by == request.user)
        ))

# Custom permission: Only students can apply/view their applications
class IsStudent(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and getattr(request.user, 'is_student', False)

class JobCategoryViewSet(viewsets.ModelViewSet):
    queryset = JobCategory.objects.filter(is_active=True)
    serializer_class = JobCategorySerializer
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['name_en']

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdminUser(), IsStaffOrActiveSubscriptionOrDenied()]
        return [permissions.AllowAny(), IsStaffOrActiveSubscriptionOrDenied()]

class JobPostingViewSet(viewsets.ModelViewSet):
    queryset = JobPosting.objects.filter(is_active=True)
    serializer_class = JobPostingSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title_ar', 'company_name', 'location', 'description_ar', 'requirements_ar']
    ordering_fields = ['created_at', 'application_deadline', 'company_name']

    def get_permissions(self):
        if self.action == 'create':
            return [IsEmailVerified(), permissions.IsAuthenticated(), IsEmployerOrAdmin(), IsStaffOrActiveSubscriptionOrDenied()]
        if self.action in ['update', 'partial_update', 'destroy']:
            return [IsEmailVerified(), IsEmployerOrAdmin(), IsStaffOrActiveSubscriptionOrDenied()]
        return [permissions.AllowAny(), IsStaffOrActiveSubscriptionOrDenied()]

    def perform_create(self, serializer):
        serializer.save(posted_by=self.request.user)

    @action(detail=False, methods=['get'], url_path='my-jobs', permission_classes=[IsEmailVerified, IsEmployerOrAdmin, IsStaffOrActiveSubscriptionOrDenied])
    def my_jobs(self, request):
        jobs = JobPosting.objects.filter(posted_by=request.user)
        serializer = self.get_serializer(jobs, many=True)
        return Response(serializer.data)

class JobApplicationViewSet(viewsets.ModelViewSet):
    queryset = JobApplication.objects.all()
    serializer_class = JobApplicationSerializer
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['applied_at', 'status']

    def get_permissions(self):
        if self.action in ['create']:
            return [IsEmailVerified(), IsStudent(), IsStaffOrActiveSubscriptionOrDenied()]
        if self.action in ['list', 'retrieve']:
            return [IsEmailVerified(), IsStaffOrActiveSubscriptionOrDenied()]
        return [IsAdminUser(), IsStaffOrActiveSubscriptionOrDenied()]

    def get_queryset(self):
        user = self.request.user
        if user.is_staff or getattr(user, 'is_instructor', False):
            return JobApplication.objects.all()
        elif getattr(user, 'is_student', False):
            return JobApplication.objects.filter(student__user=user)
        return JobApplication.objects.none()

    def perform_create(self, serializer):
        serializer.save(student=self.request.user.student_profile)
