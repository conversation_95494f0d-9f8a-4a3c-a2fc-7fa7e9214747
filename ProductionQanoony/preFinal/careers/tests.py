from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import JobCategory, JobPosting, JobApplication
from django.core.files.uploadedfile import SimpleUploadedFile
from students.models import StudentProfile
from unittest.mock import patch

User = get_user_model()

class JobCategoryAPITests(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.admin = User.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='adminpass')
        cls.admin.email_verified = True
        cls.admin.save()
        cls.user = User.objects.create_user(email='<EMAIL>', first_name='Normal', last_name='User', password='userpass')
        cls.user.email_verified = True
        cls.user.save()
        cls.cat1 = JobCategory.objects.create(name_en='Legal', name_ar='قانون', description_ar='desc1', is_active=True)
        cls.cat2 = JobCategory.objects.create(name_en='Finance', name_ar='مالية', description_ar='desc2', is_active=True)
        cls.cat3 = JobCategory.objects.create(name_en='Inactive', name_ar='غير نشط', description_ar='desc3', is_active=False)

    def setUp(self):
        self.client = APIClient()

    def test_list_categories_public(self):
        url = reverse('jobcategory-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Only active categories
        self.assertEqual(len(response.data), 2)
        names = [cat['name_en'] for cat in response.data]
        self.assertIn('Legal', names)
        self.assertIn('Finance', names)
        self.assertNotIn('Inactive', names)

    def test_retrieve_category(self):
        url = reverse('jobcategory-detail', args=[self.cat1.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name_en'], 'Legal')

    def test_create_category_admin_only(self):
        url = reverse('jobcategory-list')
        data = {'name_en': 'HR', 'name_ar': 'موارد بشرية', 'description_ar': 'desc', 'is_active': True}
        # Unauthenticated
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        # Authenticated non-admin
        self.client.force_authenticate(user=self.user)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        # Admin
        self.client.force_authenticate(user=self.admin)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(JobCategory.objects.filter(name_en='HR').count(), 1)

    def test_update_category_admin_only(self):
        url = reverse('jobcategory-detail', args=[self.cat1.id])
        data = {'name_en': 'Legal Updated', 'name_ar': 'قانون', 'description_ar': 'desc1', 'is_active': True}
        # Unauthenticated
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        # Non-admin
        self.client.force_authenticate(user=self.user)
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        # Admin
        self.client.force_authenticate(user=self.admin)
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.cat1.refresh_from_db()
        self.assertEqual(self.cat1.name_en, 'Legal Updated')

    def test_delete_category_admin_only(self):
        url = reverse('jobcategory-detail', args=[self.cat2.id])
        # Unauthenticated
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        # Non-admin
        self.client.force_authenticate(user=self.user)
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        # Admin
        self.client.force_authenticate(user=self.admin)
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(JobCategory.objects.filter(id=self.cat2.id).exists())

    def test_ordering(self):
        url = reverse('jobcategory-list') + '?ordering=name_en'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        names = [cat['name_en'] for cat in response.data]
        self.assertEqual(names, sorted(names))

class JobPostingAPITests(APITestCase):
    @classmethod
    def setUpTestData(cls):
        User = get_user_model()
        cls.admin = User.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='adminpass')
        cls.admin.email_verified = True
        cls.admin.save()
        cls.employer = User.objects.create_user(email='<EMAIL>', first_name='Emp', last_name='Loyer', password='emppass', is_instructor=True)
        cls.employer.email_verified = True
        cls.employer.save()
        cls.student = User.objects.create_user(email='<EMAIL>', first_name='Stu', last_name='Dent', password='studpass', is_student=True)
        cls.student.email_verified = True
        cls.student.save()
        cls.cat = JobCategory.objects.create(name_en='IT', name_ar='تقنية', description_ar='desc', is_active=True)
        cls.job1 = JobPosting.objects.create(
            title_ar='مبرمج', company_name='TechCorp', category=cls.cat, job_type='full_time',
            location='Cairo', description_ar='برمجة', requirements_ar='Python', salary_range='10k-15k',
            application_deadline='2030-12-31', contact_email='<EMAIL>', contact_phone='0100000000',
            is_active=True, posted_by=cls.employer
        )
        cls.job2 = JobPosting.objects.create(
            title_ar='مصمم', company_name='DesignInc', category=cls.cat, job_type='internship',
            location='Alex', description_ar='تصميم', requirements_ar='Photoshop', salary_range='5k-7k',
            application_deadline='2030-11-30', contact_email='<EMAIL>', contact_phone='0111111111',
            is_active=True, posted_by=cls.admin
        )
        cls.inactive_job = JobPosting.objects.create(
            title_ar='محاسب', company_name='FinanceLLC', category=cls.cat, job_type='full_time',
            location='Giza', description_ar='محاسبة', requirements_ar='Excel', salary_range='8k-10k',
            application_deadline='2030-10-31', contact_email='<EMAIL>', contact_phone='0122222222',
            is_active=False, posted_by=cls.admin
        )

    def setUp(self):
        self.client = APIClient()

    def test_list_jobs_public(self):
        url = '/api/careers/jobs/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Only active jobs
        self.assertEqual(len(response.data), 2)
        titles = [job['title_ar'] for job in response.data]
        self.assertIn('مبرمج', titles)
        self.assertIn('مصمم', titles)
        self.assertNotIn('محاسب', titles)

    def test_retrieve_job(self):
        url = f'/api/careers/jobs/{self.job1.id}/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title_ar'], 'مبرمج')

    def test_search_jobs(self):
        url = '/api/careers/jobs/?search=مصمم'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['title_ar'], 'مصمم')

    def test_ordering_jobs(self):
        url = '/api/careers/jobs/?ordering=company_name'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        companies = [job['company_name'] for job in response.data]
        self.assertEqual(companies, sorted(companies))

    def test_create_job_permissions(self):
        url = '/api/careers/jobs/'
        data = {
            'title_ar': 'مدير', 'company_name': 'ManageIt', 'category_id': self.cat.id, 'job_type': 'full_time',
            'location': 'Cairo', 'description_ar': 'إدارة', 'requirements_ar': 'خبرة', 'salary_range': '20k-25k',
            'application_deadline': '2031-01-01', 'contact_email': '<EMAIL>', 'contact_phone': '0133333333',
            'is_active': True
        }
        # Unauthenticated
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        # Student
        self.client.force_authenticate(user=self.student)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        # Employer
        self.client.force_authenticate(user=self.employer)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Admin
        self.client.force_authenticate(user=self.admin)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_update_job_permissions(self):
        url = f'/api/careers/jobs/{self.job1.id}/'
        data = {
            'title_ar': 'مبرمج أول', 'company_name': 'TechCorp', 'category_id': self.cat.id, 'job_type': 'full_time',
            'location': 'Cairo', 'description_ar': 'برمجة متقدمة', 'requirements_ar': 'Python', 'salary_range': '12k-18k',
            'application_deadline': '2030-12-31', 'contact_email': '<EMAIL>', 'contact_phone': '0100000000',
            'is_active': True
        }
        # Unauthenticated
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        # Student
        self.client.force_authenticate(user=self.student)
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        # Employer (owner)
        self.client.force_authenticate(user=self.employer)
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Admin
        self.client.force_authenticate(user=self.admin)
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_delete_job_permissions(self):
        url = f'/api/careers/jobs/{self.job2.id}/'
        # Unauthenticated
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        # Student
        self.client.force_authenticate(user=self.student)
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        # Employer (not owner)
        self.client.force_authenticate(user=self.employer)
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        # Admin
        self.client.force_authenticate(user=self.admin)
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_my_jobs_action(self):
        url = '/api/careers/jobs/my-jobs/'
        # Employer
        self.client.force_authenticate(user=self.employer)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['company_name'], 'TechCorp')
        # Admin
        self.client.force_authenticate(user=self.admin)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)

class JobApplicationAPITests(APITestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.cloudinary_patcher = patch('cloudinary.models.CloudinaryField.pre_save', return_value='dummy_resume_url')
        cls.cloudinary_patcher.start()

    @classmethod
    def tearDownClass(cls):
        cls.cloudinary_patcher.stop()
        super().tearDownClass()

    @classmethod
    def setUpTestData(cls):
        User = get_user_model()
        cls.admin = User.objects.create_superuser(email='<EMAIL>', first_name='Admin', last_name='User', password='adminpass')
        cls.admin.email_verified = True
        cls.admin.save()
        cls.employer = User.objects.create_user(email='<EMAIL>', first_name='Emp', last_name='Loyer', password='emppass', is_instructor=True)
        cls.employer.email_verified = True
        cls.employer.save()
        cls.student = User.objects.create_user(email='<EMAIL>', first_name='Stu', last_name='Dent', password='studpass', is_student=True)
        cls.student.email_verified = True
        cls.student.save()
        cls.cat = JobCategory.objects.create(name_en='HR', name_ar='موارد', description_ar='desc', is_active=True)
        cls.job = JobPosting.objects.create(
            title_ar='HR Specialist', company_name='HRInc', category=cls.cat, job_type='full_time',
            location='Cairo', description_ar='موارد بشرية', requirements_ar='خبرة', salary_range='10k-15k',
            application_deadline='2030-12-31', contact_email='<EMAIL>', contact_phone='0100000000',
            is_active=True, posted_by=cls.employer
        )
        # Create student profile
        cls.student_profile = StudentProfile.objects.create(user=cls.student, university=None, academic_year=None, student_id='S999')
        # Create application
        cls.application = JobApplication.objects.create(
            student=cls.student_profile, job=cls.job, cover_letter='I am interested',
            resume='resume.pdf', status='applied'
        )

    def setUp(self):
        self.client = APIClient()

    def get_resume_file(self, name='resume.pdf', content_type='application/pdf'):
        return SimpleUploadedFile(name, b'PDF content', content_type=content_type)

    def test_student_can_apply(self):
        url = '/api/careers/applications/'
        self.client.force_authenticate(user=self.student)
        data = {
            'job_id': self.job.id,
            'student_id': self.student_profile.id,
            'cover_letter': 'Looking forward',
            'resume': self.get_resume_file(),
        }
        response = self.client.post(url, data, format='multipart')
        if response.status_code != status.HTTP_201_CREATED:
            print('APPLY ERROR:', response.data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['status'], 'applied')

    def test_resume_file_validation(self):
        url = '/api/careers/applications/'
        self.client.force_authenticate(user=self.student)
        data = {
            'job_id': self.job.id,
            'cover_letter': 'Looking forward',
            'resume': self.get_resume_file(name='resume.txt', content_type='text/plain'),
        }
        response = self.client.post(url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('resume', response.data)

    def test_unauthenticated_cannot_apply(self):
        url = '/api/careers/applications/'
        data = {
            'job_id': self.job.id,
            'cover_letter': 'Looking forward',
            'resume': self.get_resume_file(),
        }
        response = self.client.post(url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_student_can_list_own_applications(self):
        url = '/api/careers/applications/'
        self.client.force_authenticate(user=self.student)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)
        for app in response.data:
            self.assertEqual(str(app['student']['user']), str(self.student.id))

    def test_employer_can_list_applications_for_their_jobs(self):
        url = '/api/careers/applications/'
        self.client.force_authenticate(user=self.employer)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Employer sees all applications for their jobs
        self.assertTrue(any(app['job']['company_name'] == 'HRInc' for app in response.data))

    def test_admin_can_list_all_applications(self):
        url = '/api/careers/applications/'
        self.client.force_authenticate(user=self.admin)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)

    def test_student_cannot_update_status(self):
        url = f'/api/careers/applications/{self.application.id}/'
        self.client.force_authenticate(user=self.student)
        response = self.client.patch(url, {'status': 'accepted'})
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_admin_can_update_status(self):
        url = f'/api/careers/applications/{self.application.id}/'
        self.client.force_authenticate(user=self.admin)
        response = self.client.patch(url, {'status': 'accepted'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'accepted')

    def test_student_can_retrieve_own_application(self):
        url = f'/api/careers/applications/{self.application.id}/'
        self.client.force_authenticate(user=self.student)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(str(response.data['student']['user']), str(self.student.id))

    def test_unauthenticated_cannot_list_or_retrieve(self):
        url = f'/api/careers/applications/{self.application.id}/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        url = '/api/careers/applications/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
